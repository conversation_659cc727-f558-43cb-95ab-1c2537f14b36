"""
窗口切换验证工具

此工具用于验证所有窗口切换相关代码的正确性，包括：
1. 检查所有to_*_slot函数的实现
2. 验证窗口类型的一致性
3. 检测潜在的切换错误
4. 生成切换关系图

作者: [作者名]
日期: [日期]
"""

import ast
import os
import sys
import inspect
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from window_manager import WindowType


@dataclass
class SwitchFunction:
    """切换函数信息"""
    name: str
    file_path: str
    line_number: int
    target_window: Optional[WindowType]
    source_code: str
    has_error_handling: bool
    calls_window_manager: bool


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    switch_functions: List[SwitchFunction]
    coverage_report: Dict[str, any]


class WindowSwitchValidator:
    """窗口切换验证器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.ctrl_dir = self.project_root / "ctrl"
        self.switch_functions: List[SwitchFunction] = []
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def validate_all(self) -> ValidationResult:
        """执行完整验证"""
        print("🔍 开始窗口切换验证...")
        
        # 1. 扫描所有切换函数
        self._scan_switch_functions()
        
        # 2. 验证函数实现
        self._validate_function_implementations()
        
        # 3. 检查窗口类型一致性
        self._validate_window_type_consistency()
        
        # 4. 验证错误处理
        self._validate_error_handling()
        
        # 5. 生成覆盖率报告
        coverage_report = self._generate_coverage_report()
        
        # 6. 输出结果
        self._print_validation_results()
        
        return ValidationResult(
            is_valid=len(self.errors) == 0,
            errors=self.errors,
            warnings=self.warnings,
            switch_functions=self.switch_functions,
            coverage_report=coverage_report
        )
    
    def _scan_switch_functions(self) -> None:
        """扫描所有切换函数"""
        print("📂 扫描切换函数...")
        
        # 扫描ctrl目录下的所有Python文件
        for py_file in self.ctrl_dir.glob("*_slot.py"):
            self._scan_file_for_switch_functions(py_file)
        
        print(f"✅ 找到 {len(self.switch_functions)} 个切换函数")
    
    def _scan_file_for_switch_functions(self, file_path: Path) -> None:
        """扫描单个文件中的切换函数"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    if node.name.startswith('to_') and node.name.endswith('_slot'):
                        switch_func = self._analyze_switch_function(
                            node, file_path, content
                        )
                        if switch_func:
                            self.switch_functions.append(switch_func)
        
        except Exception as e:
            self.errors.append(f"扫描文件 {file_path} 时出错: {e}")
    
    def _analyze_switch_function(self, node: ast.FunctionDef, 
                                file_path: Path, content: str) -> Optional[SwitchFunction]:
        """分析切换函数"""
        try:
            # 提取函数源码
            lines = content.split('\n')
            start_line = node.lineno - 1
            end_line = node.end_lineno if hasattr(node, 'end_lineno') else start_line + 10
            source_code = '\n'.join(lines[start_line:end_line])
            
            # 分析目标窗口类型
            target_window = self._extract_target_window(node, source_code)
            
            # 检查是否有错误处理
            has_error_handling = self._has_error_handling(node)
            
            # 检查是否调用window_manager
            calls_window_manager = self._calls_window_manager(node, source_code)
            
            return SwitchFunction(
                name=node.name,
                file_path=str(file_path),
                line_number=node.lineno,
                target_window=target_window,
                source_code=source_code,
                has_error_handling=has_error_handling,
                calls_window_manager=calls_window_manager
            )
        
        except Exception as e:
            self.warnings.append(f"分析函数 {node.name} 时出错: {e}")
            return None
    
    def _extract_target_window(self, node: ast.FunctionDef, source_code: str) -> Optional[WindowType]:
        """提取目标窗口类型"""
        # 从函数名推断
        function_name = node.name
        if function_name == "to_mesh_slot":
            return WindowType.MESH
        elif function_name == "to_pre_slot":
            return WindowType.PRE
        elif function_name == "to_connection_slot":
            return WindowType.CONNECTION
        elif function_name == "to_analysis_slot":
            return WindowType.ANALYSIS
        elif function_name == "to_constrain_slot":
            return WindowType.CONSTRAIN
        elif function_name == "to_result_slot":
            return WindowType.RESULT
        elif function_name == "to_vibration_slot":
            return WindowType.VIBRATION
        elif function_name == "to_main_slot":
            return WindowType.MAIN
        
        # 从源码中查找WindowType引用
        for window_type in WindowType:
            if f"WindowType.{window_type.name}" in source_code:
                return window_type
        
        return None
    
    def _has_error_handling(self, node: ast.FunctionDef) -> bool:
        """检查是否有错误处理"""
        for child in ast.walk(node):
            if isinstance(child, (ast.Try, ast.ExceptHandler)):
                return True
        return False
    
    def _calls_window_manager(self, node: ast.FunctionDef, source_code: str) -> bool:
        """检查是否调用window_manager"""
        return "window_manager.switch_to" in source_code
    
    def _validate_function_implementations(self) -> None:
        """验证函数实现"""
        print("🔧 验证函数实现...")
        
        for func in self.switch_functions:
            # 检查是否调用了window_manager
            if not func.calls_window_manager:
                self.errors.append(
                    f"函数 {func.name} ({func.file_path}:{func.line_number}) "
                    f"没有调用 window_manager.switch_to"
                )
            
            # 检查目标窗口类型
            if func.target_window is None:
                self.warnings.append(
                    f"函数 {func.name} ({func.file_path}:{func.line_number}) "
                    f"无法确定目标窗口类型"
                )
    
    def _validate_window_type_consistency(self) -> None:
        """验证窗口类型一致性"""
        print("🎯 验证窗口类型一致性...")
        
        # 检查函数名与目标窗口类型的一致性
        for func in self.switch_functions:
            if func.target_window:
                expected_name = f"to_{func.target_window.name.lower()}_slot"
                if func.name != expected_name:
                    self.warnings.append(
                        f"函数名 {func.name} 与目标窗口类型 {func.target_window.name} 不一致，"
                        f"建议使用 {expected_name}"
                    )
    
    def _validate_error_handling(self) -> None:
        """验证错误处理"""
        print("🛡️ 验证错误处理...")
        
        for func in self.switch_functions:
            if not func.has_error_handling:
                self.warnings.append(
                    f"函数 {func.name} ({func.file_path}:{func.line_number}) "
                    f"缺少错误处理机制"
                )
    
    def _generate_coverage_report(self) -> Dict[str, any]:
        """生成覆盖率报告"""
        print("📊 生成覆盖率报告...")
        
        # 统计各种指标
        total_functions = len(self.switch_functions)
        functions_with_error_handling = sum(1 for f in self.switch_functions if f.has_error_handling)
        functions_with_window_manager = sum(1 for f in self.switch_functions if f.calls_window_manager)
        functions_with_target = sum(1 for f in self.switch_functions if f.target_window is not None)
        
        # 统计每个窗口类型的覆盖情况
        window_type_coverage = {}
        for window_type in WindowType:
            functions_for_type = [f for f in self.switch_functions if f.target_window == window_type]
            window_type_coverage[window_type.name] = {
                'function_count': len(functions_for_type),
                'functions': [f.name for f in functions_for_type]
            }
        
        return {
            'total_functions': total_functions,
            'error_handling_coverage': functions_with_error_handling / total_functions if total_functions > 0 else 0,
            'window_manager_usage': functions_with_window_manager / total_functions if total_functions > 0 else 0,
            'target_identification': functions_with_target / total_functions if total_functions > 0 else 0,
            'window_type_coverage': window_type_coverage,
            'files_scanned': len(set(f.file_path for f in self.switch_functions))
        }
    
    def _print_validation_results(self) -> None:
        """打印验证结果"""
        print("\n" + "="*60)
        print("🎯 窗口切换验证结果")
        print("="*60)
        
        # 总体状态
        if len(self.errors) == 0:
            print("✅ 验证通过！")
        else:
            print("❌ 验证失败！")
        
        print(f"\n📊 统计信息:")
        print(f"   切换函数总数: {len(self.switch_functions)}")
        print(f"   错误数量: {len(self.errors)}")
        print(f"   警告数量: {len(self.warnings)}")
        
        # 错误详情
        if self.errors:
            print(f"\n❌ 错误详情:")
            for i, error in enumerate(self.errors, 1):
                print(f"   {i}. {error}")
        
        # 警告详情
        if self.warnings:
            print(f"\n⚠️ 警告详情:")
            for i, warning in enumerate(self.warnings, 1):
                print(f"   {i}. {warning}")
        
        # 函数列表
        print(f"\n📋 切换函数列表:")
        for func in self.switch_functions:
            status_icons = []
            if func.calls_window_manager:
                status_icons.append("✅")
            else:
                status_icons.append("❌")
            
            if func.has_error_handling:
                status_icons.append("🛡️")
            else:
                status_icons.append("⚠️")
            
            target = func.target_window.name if func.target_window else "未知"
            print(f"   {''.join(status_icons)} {func.name} -> {target}")
    
    def generate_switch_diagram(self, output_file: str = "window_switch_diagram.md") -> None:
        """生成窗口切换关系图"""
        print(f"📈 生成切换关系图: {output_file}")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("# 窗口切换关系图\n\n")
            f.write("```mermaid\n")
            f.write("graph TD\n")
            
            # 添加所有窗口节点
            for window_type in WindowType:
                f.write(f"    {window_type.name}[{window_type.name}]\n")
            
            # 添加切换关系
            for func in self.switch_functions:
                if func.target_window:
                    # 从函数名推断源窗口
                    source_window = self._infer_source_window(func.file_path)
                    if source_window:
                        f.write(f"    {source_window} --> {func.target_window.name}\n")
            
            f.write("```\n\n")
            
            # 添加函数详情表格
            f.write("## 切换函数详情\n\n")
            f.write("| 函数名 | 文件 | 目标窗口 | 错误处理 | 状态 |\n")
            f.write("|--------|------|----------|----------|------|\n")
            
            for func in self.switch_functions:
                target = func.target_window.name if func.target_window else "未知"
                error_handling = "✅" if func.has_error_handling else "❌"
                status = "✅" if func.calls_window_manager else "❌"
                file_name = os.path.basename(func.file_path)
                
                f.write(f"| {func.name} | {file_name} | {target} | {error_handling} | {status} |\n")
    
    def _infer_source_window(self, file_path: str) -> Optional[str]:
        """从文件路径推断源窗口"""
        file_name = os.path.basename(file_path)
        if "main_slot" in file_name:
            return "MAIN"
        elif "mesh_slot" in file_name:
            return "MESH"
        elif "analysis_slot" in file_name:
            return "ANALYSIS"
        elif "result_slot" in file_name:
            return "RESULT"
        # 可以添加更多映射
        return None


def main():
    """主函数"""
    validator = WindowSwitchValidator(str(project_root))
    result = validator.validate_all()
    
    # 生成切换关系图
    validator.generate_switch_diagram()
    
    # 返回退出码
    return 0 if result.is_valid else 1


if __name__ == "__main__":
    sys.exit(main())
