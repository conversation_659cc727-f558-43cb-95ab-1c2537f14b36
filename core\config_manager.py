"""
配置管理器模块

此模块提供了统一的配置管理功能，包括：
1. 配置的读取和保存
2. 配置验证
3. 配置版本管理
4. 默认配置提供

作者: [作者名]
日期: [日期]
"""

import os
import json
import logging
import copy
from typing import Dict, Any, Optional, List, Callable
from PySide6.QtCore import QObject, Signal

# 获取模块日志记录器
logger = logging.getLogger(__name__)

# 配置验证器类型
ValidatorType = Callable[[Dict[str, Any]], List[str]]

class ConfigSignals(QObject):
    """配置信号类，用于在配置变更时发出信号"""
    config_changed = Signal(dict)  # 配置变更信号
    config_error = Signal(str)     # 配置错误信号

class ConfigManager:
    """配置管理器，实现单例模式"""
    
    # 单例实例
    _instance = None
    
    # 当前配置版本
    CURRENT_VERSION = "1.0.0"
    
    # 默认配置文件路径
    DEFAULT_CONFIG_PATH = "config/settings.json"
    
    def __new__(cls, config_path=None):
        """创建或返回单例实例
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config_path=None):
        """初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        # 避免重复初始化
        if self._initialized:
            return
            
        self._config_path = config_path or self.DEFAULT_CONFIG_PATH
        self._config = {}
        self._default_config = self._get_default_config()
        self._validators = []
        self._signals = ConfigSignals()
        self._initialized = True
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self._config_path), exist_ok=True)
        
        # 加载配置
        self.load_config()
        
        logger.info("配置管理器初始化完成")
    
    @property
    def signals(self) -> ConfigSignals:
        """获取信号对象"""
        return self._signals
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置字典
        """
        return {
            "version": self.CURRENT_VERSION,
            "ansys": {
                "start_file": os.path.normpath("D:\\app-many\\ANC23\\ANSYS Inc\\v232\\Framework\\bin\\Win64\\RunWB2.exe"),
                "work_dir": os.getcwd().replace('\\', '/'),
                "project_file": os.path.normpath("D:\\data\\all-XM\\autoworkbench\\damox23R2.wbpj")
            },
            "analysis": {
                "time_step": 0.001,
                "end_time": 0.1,
                "stiffness_coefficient": 0.0,
                "mass_coefficient": 0.0
            },
            "constrain": {
                "force_output_folder": "",
                "rotation_speed": 0.0
            },
            "ui": {
                "theme": "default",
                "language": "zh_CN",
                "window_state": {}
            },
            "splash_screen": {
                "enabled": True,
                "show_fade_in": True,
                "fade_in_duration": 500,
                "fade_out_duration": 300,
                "minimum_display_time": 2000,
                "show_progress": True,
                "show_rotation_animation": True,
                "show_company_info": True,
                "custom_background": None,
                "colors": {
                    "primary": "#3498db",
                    "secondary": "#2ecc71",
                    "text": "#34495e",
                    "progress_background": "#ecf0f1"
                },
                "fonts": {
                    "title_size": 16,
                    "version_size": 10,
                    "status_size": 9,
                    "font_family": "Arial"
                },
                "layout": {
                    "width": 480,
                    "height": 320,
                    "logo_size": 64,
                    "logo_y": 40,
                    "title_y": 120,
                    "progress_y": 220,
                    "status_y": 260
                }
            },
            "mesh": {
                "mesh_parameters": {},
                "current_mesh_id": None,
                "default_settings": {
                    "element_type": "四面体",
                    "quality_settings": {
                        "skewness": 0.9,
                        "aspect_ratio": 20.0,
                        "smoothing_iterations": 3,
                        "auto_sizing": True,
                        "capture_curvature": True,
                        "capture_proximity": False
                    }
                },
                "modal_analysis": {
                    "default_mode_count": 5,
                    "mode_count_range": [0, 200],
                    "default_freq_range": [0.0, 1000.0],
                    "max_freq_limit": 100000.0,
                    "freq_limit_enabled": True,
                    "convergence_tolerance": 1e-6
                }
            }
        }
    
    def register_validator(self, validator: ValidatorType) -> None:
        """注册配置验证器
        
        Args:
            validator: 验证函数，接收配置字典，返回错误信息列表
        """
        self._validators.append(validator)
        logger.debug(f"已注册新的配置验证器，当前验证器数量: {len(self._validators)}")
    
    def _validate_config(self, config: Dict[str, Any]) -> List[str]:
        """验证配置
        
        Args:
            config: 要验证的配置字典
            
        Returns:
            List[str]: 错误信息列表，如果没有错误则为空列表
        """
        errors = []
        
        # 验证版本
        if "version" not in config:
            errors.append("配置缺少版本信息")
        
        # 验证必需的顶级键
        required_keys = ["ansys", "analysis", "constrain", "ui", "mesh"]
        for key in required_keys:
            if key not in config:
                errors.append(f"配置缺少必需的部分: {key}")

        # 验证网格配置
        if "mesh" in config:
            mesh_config = config["mesh"]

            # 验证网格参数结构
            if "mesh_parameters" not in mesh_config:
                errors.append("网格配置缺少mesh_parameters部分")

            if "default_settings" not in mesh_config:
                errors.append("网格配置缺少default_settings部分")

            if "modal_analysis" not in mesh_config:
                errors.append("网格配置缺少modal_analysis部分")
        
        # 运行自定义验证器
        for validator in self._validators:
            try:
                validator_errors = validator(config)
                if validator_errors:
                    errors.extend(validator_errors)
            except Exception as e:
                errors.append(f"验证器错误: {str(e)}")
                logger.error(f"配置验证器异常: {str(e)}", exc_info=True)
        
        if errors:
            logger.warning(f"配置验证失败，发现 {len(errors)} 个错误")
            for error in errors:
                logger.warning(f"配置错误: {error}")
        else:
            logger.debug("配置验证通过")
            
        return errors
    
    def _upgrade_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """升级配置到最新版本
        
        Args:
            config: 要升级的配置字典
            
        Returns:
            Dict[str, Any]: 升级后的配置字典
        """
        # 获取配置版本
        version = config.get("version", "0.0.0")
        
        # 如果版本已经是最新的，直接返回
        if version == self.CURRENT_VERSION:
            return config
        
        # 记录升级操作
        logger.info(f"正在升级配置，从版本 {version} 到 {self.CURRENT_VERSION}")
        
        # 创建配置副本
        new_config = copy.deepcopy(config)
        
        # 在这里添加版本升级逻辑
        # 如果配置中没有网格部分，添加默认网格配置
        if "mesh" not in new_config:
            default_config = self._get_default_config()
            new_config["mesh"] = default_config["mesh"]
            logger.info("添加了默认网格配置")

        # 设置新版本
        new_config["version"] = self.CURRENT_VERSION
        
        return new_config
    
    def load_config(self) -> None:
        """从文件加载配置"""
        try:
            # 如果配置文件不存在，使用默认配置
            if not os.path.exists(self._config_path):
                logger.info(f"配置文件不存在，使用默认配置: {self._config_path}")
                self._config = copy.deepcopy(self._default_config)
                return
            
            # 读取配置文件
            with open(self._config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 升级配置
            config = self._upgrade_config(config)
            
            # 验证配置
            errors = self._validate_config(config)
            if errors:
                # 如果有错误，发出错误信号
                error_msg = "配置验证失败:\n" + "\n".join(errors)
                self._signals.config_error.emit(error_msg)
                
                # 使用默认配置填充缺失的部分
                for key, value in self._default_config.items():
                    if key not in config:
                        config[key] = value
            
            # 设置配置
            self._config = config
            logger.info("配置加载成功")
            
        except Exception as e:
            # 如果加载失败，使用默认配置
            logger.error(f"加载配置失败: {str(e)}", exc_info=True)
            self._config = copy.deepcopy(self._default_config)
            
            # 发出错误信号
            self._signals.config_error.emit(f"加载配置失败: {str(e)}")
    
    def save_config(self) -> bool:
        """保存配置到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 验证配置
            errors = self._validate_config(self._config)
            if errors:
                error_msg = "保存配置失败，验证错误:\n" + "\n".join(errors)
                logger.error(error_msg)
                self._signals.config_error.emit(error_msg)
                return False
            
            # 确保配置目录存在
            config_dir = os.path.dirname(self._config_path)
            os.makedirs(config_dir, exist_ok=True)
            logger.debug(f"确保配置目录存在: {config_dir}")
            
            # 写入配置文件
            with open(self._config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            
            logger.info(f"成功保存配置到: {self._config_path}")
            return True
            
        except Exception as e:
            error_msg = f"保存配置失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            self._signals.config_error.emit(error_msg)
            return False
    
    def get(self, key: str, default=None) -> Any:
        """获取配置值
        
        Args:
            key: 配置键，支持点号分隔的路径，如 "ansys.start_file"
            default: 默认值，如果键不存在则返回此值
            
        Returns:
            Any: 配置值或默认值
        """
        # 分割键路径
        parts = key.split('.')
        
        # 遍历配置字典
        value = self._config
        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值
        
        Args:
            key: 配置键，支持点号分隔的路径，如 "ansys.start_file"
            value: 配置值
        """
        # 分割键路径
        parts = key.split('.')
        
        # 遍历配置字典
        config = self._config
        for i, part in enumerate(parts[:-1]):
            # 如果路径不存在，创建新的字典
            if part not in config or not isinstance(config[part], dict):
                config[part] = {}
            config = config[part]
        
        # 设置值
        config[parts[-1]] = value
        
        # 发出配置变更信号
        self._signals.config_changed.emit(self._config)
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置
        
        Returns:
            Dict[str, Any]: 配置字典的副本
        """
        return copy.deepcopy(self._config)
    
    def set_all(self, config: Dict[str, Any]) -> None:
        """设置所有配置
        
        Args:
            config: 配置字典
        """
        # 验证配置
        errors = self._validate_config(config)
        if errors:
            error_msg = "设置配置失败，验证错误:\n" + "\n".join(errors)
            logger.error(error_msg)
            self._signals.config_error.emit(error_msg)
            return
        
        # 更新配置
        self._config = copy.deepcopy(config)
        logger.info("配置已完全更新")
        
        # 发出配置变更信号
        self._signals.config_changed.emit(self._config)
        logger.debug("已发送配置变更信号")
    
    def reset_to_default(self) -> None:
        """重置为默认配置"""
        logger.info("正在重置配置为默认值")
        self._config = copy.deepcopy(self._default_config)
        self._signals.config_changed.emit(self._config)
        if self.save_config():
            logger.info("配置已成功重置为默认值")
        else:
            logger.warning("配置已重置为默认值，但保存失败")
    
    def get_config_path(self) -> str:
        """获取配置文件路径
        
        Returns:
            str: 配置文件路径
        """
        return self._config_path
    
    def set_config_path(self, path: str) -> None:
        """设置配置文件路径
        
        Args:
            path: 新的配置文件路径
        """
        if not path:
            logger.error("无法设置空的配置路径")
            return
            
        logger.info(f"更改配置文件路径: {self._config_path} -> {path}")
        self._config_path = path
        self.load_config()

    # 网格配置相关的便捷方法
    def get_mesh_config(self) -> Dict[str, Any]:
        """获取网格配置

        Returns:
            Dict[str, Any]: 网格配置字典
        """
        return self.get("mesh", {})

    def set_mesh_config(self, mesh_config: Dict[str, Any]) -> None:
        """设置网格配置

        Args:
            mesh_config: 网格配置字典
        """
        self.set("mesh", mesh_config)

    def get_mesh_parameters(self) -> Dict[str, Any]:
        """获取网格参数配置

        Returns:
            Dict[str, Any]: 网格参数字典
        """
        return self.get("mesh.mesh_parameters", {})

    def set_mesh_parameters(self, mesh_parameters: Dict[str, Any]) -> None:
        """设置网格参数配置

        Args:
            mesh_parameters: 网格参数字典
        """
        self.set("mesh.mesh_parameters", mesh_parameters)

    def get_current_mesh_id(self) -> Optional[str]:
        """获取当前选中的网格ID

        Returns:
            Optional[str]: 当前网格ID，如果没有则返回None
        """
        return self.get("mesh.current_mesh_id")

    def set_current_mesh_id(self, mesh_id: Optional[str]) -> None:
        """设置当前选中的网格ID

        Args:
            mesh_id: 网格ID
        """
        self.set("mesh.current_mesh_id", mesh_id)

    def get_mesh_default_settings(self) -> Dict[str, Any]:
        """获取网格默认设置

        Returns:
            Dict[str, Any]: 网格默认设置字典
        """
        return self.get("mesh.default_settings", {})

    def get_modal_analysis_config(self) -> Dict[str, Any]:
        """获取模态分析配置

        Returns:
            Dict[str, Any]: 模态分析配置字典
        """
        return self.get("mesh.modal_analysis", {})

    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置

        Returns:
            Dict[str, Any]: 完整的配置字典
        """
        return copy.deepcopy(self._config)