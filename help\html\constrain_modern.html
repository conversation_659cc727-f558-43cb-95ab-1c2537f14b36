<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>约束设置界面 - 监控点管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-indigo-600 to-purple-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-indigo-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    📍 约束设置界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-indigo-100">
                    监控点管理系统 | 边界条件定义与智能文件处理
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-indigo-500 bg-opacity-20 text-indigo-100 text-sm font-semibold px-4 py-2 rounded-full border border-indigo-400">
                        📁 文件导入 | ✅ 数据验证 | 🧹 智能清理 | 👁️ 实时预览
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                约束设置界面是一个综合性的边界条件定义模块，不仅包含传统的力、位移、远程位移和旋转等约束设置，还集成了强大的监控点管理系统。该界面为用户提供了完整的约束定义和监控点管理解决方案。
            </p>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-blue-800">🔒 传统约束设置</h3>
                    </div>
                    <ul class="space-y-2 text-blue-600">
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span>力约束设置 - 定义力的时间历程和作用位置</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span>位移约束设置 - 设置固定约束和位移边界条件</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span>远程位移约束设置 - 配置远程位移参考点和约束面</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
                            <span>旋转约束设置 - 定义旋转速度和旋转轴</span>
                        </li>
                    </ul>
                </div>
                
                <div class="bg-purple-50 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-purple-600 text-white rounded-full flex items-center justify-center mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-purple-800">📍 监控点管理系统</h3>
                    </div>
                    <ul class="space-y-2 text-purple-600">
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            <span>监控点文件导入 - 支持JSON和TXT格式</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            <span>数据格式验证 - 自动检查坐标数据完整性</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            <span>实时数据预览 - 表格形式显示监控点信息</span>
                        </li>
                        <li class="flex items-center">
                            <span class="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
                            <span>自动清理机制 - 智能管理监控点文件版本</span>
                        </li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Monitor Point Management System -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">📍 监控点管理系统详解</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">提供完整的监控点数据处理能力，支持多种文件格式和智能验证</p>
            </div>
            
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📁</span>文件格式支持
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-3">JSON格式示例</h4>
                                <div class="bg-gray-100 p-4 rounded-lg text-sm font-mono">
                                    <pre class="text-gray-700">{
  "monitor_points": [
    {
      "name": "Point_1",
      "coordinates": [0.0, 0.0, 0.0],
      "id": 1,
      "created_time": "2025-06-23T00:00:00",
      "source": "user_input"
    }
  ],
  "monitor_points_count": 1,
  "monitor_points_source": "manual"
}</pre>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-3">TXT格式示例</h4>
                                <div class="bg-gray-100 p-4 rounded-lg text-sm font-mono">
                                    <pre class="text-gray-700"># 监控点坐标文件
# X, Y, Z
0.0, 0.0, 0.0
1.0, 1.0, 1.0
-0.5, 0.5, 2.0</pre>
                                </div>
                                <p class="text-sm text-gray-600 mt-3">简单的坐标列表格式，每行一个监控点，支持注释行（以#开头）</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">✅</span>数据验证机制
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                    <span class="text-blue-600 mr-3">📋</span>
                                    <span><strong>格式验证</strong> - 检查文件格式和语法正确性</span>
                                </div>
                                <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                    <span class="text-green-600 mr-3">🔢</span>
                                    <span><strong>坐标验证</strong> - 确保坐标数据为有效数值</span>
                                </div>
                            </div>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                    <span class="text-purple-600 mr-3">🔍</span>
                                    <span><strong>完整性检查</strong> - 验证必需字段的存在</span>
                                </div>
                                <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                                    <span class="text-orange-600 mr-3">🔄</span>
                                    <span><strong>重复检测</strong> - 自动识别和处理重复的监控点</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🧹</span>自动清理机制
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-4 gap-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-blue-800 mb-2">智能识别</h4>
                                <p class="text-sm text-blue-600">自动识别监控点文件模式</p>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-green-800 mb-2">时间排序</h4>
                                <p class="text-sm text-green-600">按时间戳排序，保留最新文件</p>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-purple-800 mb-2">数量控制</h4>
                                <p class="text-sm text-purple-600">支持自定义保留文件数量</p>
                            </div>
                            <div class="text-center p-4 bg-orange-50 rounded-lg">
                                <div class="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                </div>
                                <h4 class="font-semibold text-orange-800 mb-2">安全保护</h4>
                                <p class="text-sm text-orange-600">异常处理，确保清理过程安全</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Constraint Types Explanation -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔒 约束类型说明</h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="space-y-6">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">⚡ 力约束</h3>
                        <ul class="space-y-2 text-sm text-blue-600">
                            <li>• <strong>力文件选择</strong> - 导入力的时间历程数据</li>
                            <li>• <strong>力的方向</strong> - 定义力的作用方向</li>
                            <li>• <strong>作用位置</strong> - 选择力的施加位置</li>
                        </ul>
                    </div>

                    <div class="bg-green-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-green-800 mb-4">📍 位移约束</h3>
                        <ul class="space-y-2 text-sm text-green-600">
                            <li>• <strong>约束方向</strong> - 选择限制的自由度</li>
                            <li>• <strong>约束值</strong> - 设置位移量</li>
                        </ul>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-purple-800 mb-4">🎯 远程位移</h3>
                        <ul class="space-y-2 text-sm text-purple-600">
                            <li>• <strong>参考点</strong> - 设置远程位移的参考点</li>
                            <li>• <strong>约束面</strong> - 选择受约束的面</li>
                            <li>• <strong>位移量</strong> - 设置位移大小</li>
                        </ul>
                    </div>

                    <div class="bg-orange-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-orange-800 mb-4">🔄 旋转约束</h3>
                        <ul class="space-y-2 text-sm text-orange-600">
                            <li>• <strong>旋转速度</strong> - 设置转速（r/min）</li>
                            <li>• <strong>旋转轴</strong> - 定义旋转轴方向</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Monitor Point Management Operation Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔧 监控点管理操作指南</h2>

            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📋 完整操作流程</h3>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📂 访问监控点管理界面</h4>
                            <p class="text-gray-600 text-sm">在主界面选择"约束设置"模块，切换到"监控点管理"选项卡（Tab 5）。界面包含文件导入区域、数据预览表格和操作按钮。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📁 导入监控点文件</h4>
                            <p class="text-gray-600 text-sm mb-2">点击"导入"按钮，选择监控点文件：</p>
                            <ul class="text-xs text-gray-500 space-y-1">
                                <li>• <strong>JSON格式</strong> - 包含完整监控点信息的结构化数据</li>
                                <li>• <strong>TXT格式</strong> - 简单的坐标列表，每行一个监控点</li>
                            </ul>
                            <p class="text-gray-600 text-sm mt-2">系统会自动检测文件格式并进行相应处理。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">✅ 数据验证和预览</h4>
                            <p class="text-gray-600 text-sm mb-2">文件导入后，系统会自动执行以下验证：</p>
                            <ul class="text-xs text-gray-500 space-y-1">
                                <li>• 检查文件格式和语法正确性</li>
                                <li>• 验证坐标数据的有效性</li>
                                <li>• 确认必需字段的完整性</li>
                                <li>• 在表格中显示监控点预览</li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📊 查看监控点数据</h4>
                            <p class="text-gray-600 text-sm mb-2">在数据预览表格中查看导入的监控点信息：</p>
                            <ul class="text-xs text-gray-500 space-y-1">
                                <li>• <strong>名称</strong> - 监控点的标识名称</li>
                                <li>• <strong>X坐标</strong> - X方向坐标值</li>
                                <li>• <strong>Y坐标</strong> - Y方向坐标值</li>
                                <li>• <strong>Z坐标</strong> - Z方向坐标值</li>
                                <li>• <strong>ID</strong> - 监控点的唯一标识符</li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">5</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔄 应用监控点设置</h4>
                            <p class="text-gray-600 text-sm mb-2">确认数据无误后，点击"应用"按钮将监控点设置应用到当前项目。系统会：</p>
                            <ul class="text-xs text-gray-500 space-y-1">
                                <li>• 保存监控点配置到项目文件</li>
                                <li>• 更新相关的约束设置</li>
                                <li>• 触发自动清理机制</li>
                            </ul>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">6</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🧹 自动文件清理</h4>
                            <p class="text-gray-600 text-sm mb-2">系统会自动执行文件清理操作：</p>
                            <ul class="text-xs text-gray-500 space-y-1">
                                <li>• 识别监控点文件模式</li>
                                <li>• 按时间戳排序文件</li>
                                <li>• 保留最新的文件版本</li>
                                <li>• 清理过期的临时文件</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Traditional Constraint Settings Operation -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔒 传统约束设置操作</h2>

            <div class="bg-gradient-to-r from-blue-50 to-green-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📋 约束设置流程</h3>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="p-4 bg-white rounded-lg shadow-sm">
                            <h4 class="font-semibold text-blue-800 mb-2">⚡ 力约束设置</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 点击"选择文件"导入力的时间历程数据</li>
                                <li>• 确认力的方向和大小</li>
                                <li>• 选择力的作用位置和面</li>
                            </ul>
                        </div>

                        <div class="p-4 bg-white rounded-lg shadow-sm">
                            <h4 class="font-semibold text-green-800 mb-2">📍 位移约束设置</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 选择需要约束的自由度（X、Y、Z方向）</li>
                                <li>• 输入位移值或设置为固定约束</li>
                                <li>• 确认约束的作用位置</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="p-4 bg-white rounded-lg shadow-sm">
                            <h4 class="font-semibold text-purple-800 mb-2">🎯 远程位移设置</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 定义远程位移的参考点位置</li>
                                <li>• 选择受约束的面或节点</li>
                                <li>• 设置位移参数和约束类型</li>
                            </ul>
                        </div>

                        <div class="p-4 bg-white rounded-lg shadow-sm">
                            <h4 class="font-semibold text-orange-800 mb-2">🔄 旋转约束设置</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 输入旋转速度（r/min）</li>
                                <li>• 定义旋转轴的方向和位置</li>
                                <li>• 设置旋转中心点</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题解答</h2>

            <div class="mb-8">
                <h3 class="text-xl font-semibold text-indigo-800 mb-4">📍 监控点管理相关问题</h3>

                <div class="space-y-4">
                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">📁</span>支持哪些监控点文件格式？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">系统支持两种主要格式：</p>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-semibold text-blue-800 mb-2">JSON格式</h4>
                                    <p class="text-sm text-blue-600">包含完整监控点信息的结构化数据，支持名称、坐标、ID、创建时间等字段</p>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h4 class="font-semibold text-green-800 mb-2">TXT格式</h4>
                                    <p class="text-sm text-green-600">简单的坐标列表，每行格式为 "X, Y, Z"，支持注释行（以#开头）</p>
                                </div>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">⚠️</span>监控点文件导入失败怎么办？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">请检查以下几个方面：</p>
                            <div class="grid md:grid-cols-2 gap-4">
                                <div class="space-y-2">
                                    <div class="flex items-center p-3 bg-red-50 rounded-lg">
                                        <span class="text-red-600 mr-3">📝</span>
                                        <span class="text-sm"><strong>文件编码</strong> - 确保文件编码为UTF-8格式</span>
                                    </div>
                                    <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                                        <span class="text-orange-600 mr-3">🔍</span>
                                        <span class="text-sm"><strong>语法检查</strong> - 验证JSON文件语法正确性</span>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                        <span class="text-blue-600 mr-3">🔢</span>
                                        <span class="text-sm"><strong>数据验证</strong> - 检查坐标数据是否为有效数值</span>
                                    </div>
                                    <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                        <span class="text-green-600 mr-3">🔐</span>
                                        <span class="text-sm"><strong>权限检查</strong> - 确认文件路径和权限正确</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">🧹</span>自动清理机制如何工作？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">系统会自动执行以下清理操作：</p>
                            <div class="grid md:grid-cols-4 gap-3">
                                <div class="text-center p-3 bg-blue-50 rounded-lg">
                                    <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">1</div>
                                    <p class="text-xs text-blue-600">识别监控点文件的命名模式</p>
                                </div>
                                <div class="text-center p-3 bg-green-50 rounded-lg">
                                    <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2">2</div>
                                    <p class="text-xs text-green-600">按时间戳对文件进行排序</p>
                                </div>
                                <div class="text-center p-3 bg-purple-50 rounded-lg">
                                    <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">3</div>
                                    <p class="text-xs text-purple-600">保留最新的文件版本</p>
                                </div>
                                <div class="text-center p-3 bg-orange-50 rounded-lg">
                                    <div class="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">4</div>
                                    <p class="text-xs text-orange-600">清理超过保留数量的旧文件</p>
                                </div>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">📝</span>如何创建正确的监控点文件？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">建议按照以下步骤：</p>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                    <span class="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs mr-3">1</span>
                                    <span class="text-sm">参考项目中提供的示例文件格式</span>
                                </div>
                                <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                    <span class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs mr-3">2</span>
                                    <span class="text-sm">确保坐标数据的单位一致</span>
                                </div>
                                <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                    <span class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-xs mr-3">3</span>
                                    <span class="text-sm">为监控点提供有意义的名称</span>
                                </div>
                                <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                    <span class="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs mr-3">4</span>
                                    <span class="text-sm">保存文件时选择UTF-8编码</span>
                                </div>
                            </div>
                        </div>
                    </details>
                </div>
            </div>

            <div>
                <h3 class="text-xl font-semibold text-blue-800 mb-4">🔒 传统约束设置问题</h3>

                <div class="space-y-4">
                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">📄</span>力文件格式要求是什么？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">力文件应为文本格式，包含时间和力值两列数据，用空格或逗号分隔。</p>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">格式要求：</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 第一行应包含列标题</li>
                                    <li>• 确保数据单位正确且时间步长一致</li>
                                    <li>• 使用标准的数值格式</li>
                                </ul>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">⚡</span>如何处理约束冲突？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">检查是否在同一位置施加了相互矛盾的约束，确保约束之间的独立性。</p>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">解决方法：</h4>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>• 必要时可以调整约束位置或类型</li>
                                    <li>• 避免过约束问题</li>
                                    <li>• 检查约束的物理合理性</li>
                                </ul>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">🎯</span>远程位移参考点如何选择？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">参考点通常选择在约束面的中心或特征点位置。</p>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">选择原则：</h4>
                                <ul class="text-sm text-purple-600 space-y-1">
                                    <li>• 应避免选择会导致不合理变形的位置</li>
                                    <li>• 确保参考点与约束面之间有合理的距离</li>
                                    <li>• 考虑结构的对称性和载荷分布</li>
                                </ul>
                            </div>
                        </div>
                    </details>

                    <details class="feature-accordion section-card overflow-hidden">
                        <summary class="font-bold text-lg text-gray-800">
                            <span class="flex items-center">
                                <span class="text-2xl mr-4">🔄</span>旋转约束设置有什么注意事项？
                            </span>
                        </summary>
                        <div class="feature-accordion-content text-gray-600">
                            <p class="mb-3">旋转约束设置需要特别注意以下几个方面：</p>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">注意事项：</h4>
                                <ul class="text-sm text-orange-600 space-y-1">
                                    <li>• 旋转速度必须为正数</li>
                                    <li>• 旋转轴方向要明确定义</li>
                                    <li>• 确保旋转中心点位置合理</li>
                                    <li>• 注意单位换算，系统使用r/min作为旋转速度单位</li>
                                </ul>
                            </div>
                        </div>
                    </details>
                </div>
            </div>
        </section>

        <!-- Important Notes and Tips -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 力文件格式必须符合要求</li>
                    <li>• 确保约束不会相互冲突</li>
                    <li>• 检查约束的单位是否正确</li>
                    <li>• 旋转速度必须为正数</li>
                    <li>• 远程位移的参考点位置要合理</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用技巧</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• <strong>文件管理</strong> - 定期检查监控点文件，确保数据的准确性和时效性</li>
                    <li>• <strong>数据备份</strong> - 重要的监控点配置建议进行备份</li>
                    <li>• <strong>格式统一</strong> - 团队协作时统一使用相同的文件格式和命名规范</li>
                    <li>• <strong>验证检查</strong> - 导入后务必检查预览数据的正确性</li>
                    <li>• <strong>版本控制</strong> - 利用自动清理机制，但重要版本可手动备份</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">监控点管理系统 - 智能化边界条件定义解决方案</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
