# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'modal_result_comparison_redesigned.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON>Brush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QCheckBox, QGroupBox,
    QHBoxLayout, QLabel, QListWidget, QListWidgetItem,
    QPushButton, QRadioButton, QSizePolicy, QSpacerItem,
    QVBoxLayout, QWidget)

class Ui_ModalResultComparison(object):
    def setupUi(self, ModalResultComparison):
        if not ModalResultComparison.objectName():
            ModalResultComparison.setObjectName(u"ModalResultComparison")
        ModalResultComparison.resize(1200, 800)
        self.horizontalLayout_main = QHBoxLayout(ModalResultComparison)
        self.horizontalLayout_main.setSpacing(10)
        self.horizontalLayout_main.setObjectName(u"horizontalLayout_main")
        self.horizontalLayout_main.setContentsMargins(10, 10, 10, 10)
        self.groupBox_control_panel = QGroupBox(ModalResultComparison)
        self.groupBox_control_panel.setObjectName(u"groupBox_control_panel")
        self.groupBox_control_panel.setMaximumSize(QSize(380, 16777215))
        self.groupBox_control_panel.setStyleSheet(u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}")
        self.verticalLayout_control = QVBoxLayout(self.groupBox_control_panel)
        self.verticalLayout_control.setSpacing(15)
        self.verticalLayout_control.setObjectName(u"verticalLayout_control")
        self.groupBox_mesh_results = QGroupBox(self.groupBox_control_panel)
        self.groupBox_mesh_results.setObjectName(u"groupBox_mesh_results")
        self.verticalLayout_mesh_list = QVBoxLayout(self.groupBox_mesh_results)
        self.verticalLayout_mesh_list.setObjectName(u"verticalLayout_mesh_list")
        self.label_mesh_info = QLabel(self.groupBox_mesh_results)
        self.label_mesh_info.setObjectName(u"label_mesh_info")
        self.label_mesh_info.setStyleSheet(u"font-weight: normal; color: #666;")

        self.verticalLayout_mesh_list.addWidget(self.label_mesh_info)

        self.listWidget_mesh_results = QListWidget(self.groupBox_mesh_results)
        self.listWidget_mesh_results.setObjectName(u"listWidget_mesh_results")
        self.listWidget_mesh_results.setMinimumSize(QSize(0, 180))
        self.listWidget_mesh_results.setSelectionMode(QAbstractItemView.MultiSelection)
        self.listWidget_mesh_results.setStyleSheet(u"QListWidget {\n"
"    border: 1px solid #ddd;\n"
"    border-radius: 4px;\n"
"    background-color: #fafafa;\n"
"}\n"
"QListWidget::item {\n"
"    padding: 8px;\n"
"    border-bottom: 1px solid #eee;\n"
"}\n"
"QListWidget::item:selected {\n"
"    background-color: #e3f2fd;\n"
"    color: #1976d2;\n"
"}")

        self.verticalLayout_mesh_list.addWidget(self.listWidget_mesh_results)


        self.verticalLayout_control.addWidget(self.groupBox_mesh_results)

        self.groupBox_modal_options = QGroupBox(self.groupBox_control_panel)
        self.groupBox_modal_options.setObjectName(u"groupBox_modal_options")
        self.verticalLayout_modal_options = QVBoxLayout(self.groupBox_modal_options)
        self.verticalLayout_modal_options.setObjectName(u"verticalLayout_modal_options")
        self.checkBox_show_frequencies = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_frequencies.setObjectName(u"checkBox_show_frequencies")
        self.checkBox_show_frequencies.setChecked(True)

        self.verticalLayout_modal_options.addWidget(self.checkBox_show_frequencies)

        self.checkBox_show_mode_shapes = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_mode_shapes.setObjectName(u"checkBox_show_mode_shapes")
        self.checkBox_show_mode_shapes.setChecked(True)

        self.verticalLayout_modal_options.addWidget(self.checkBox_show_mode_shapes)

        self.checkBox_show_mesh_info = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_mesh_info.setObjectName(u"checkBox_show_mesh_info")
        self.checkBox_show_mesh_info.setChecked(True)

        self.verticalLayout_modal_options.addWidget(self.checkBox_show_mesh_info)


        self.verticalLayout_control.addWidget(self.groupBox_modal_options)

        self.groupBox_chart_type = QGroupBox(self.groupBox_control_panel)
        self.groupBox_chart_type.setObjectName(u"groupBox_chart_type")
        self.verticalLayout_chart_type = QVBoxLayout(self.groupBox_chart_type)
        self.verticalLayout_chart_type.setObjectName(u"verticalLayout_chart_type")
        self.radioButton_frequency_comparison = QRadioButton(self.groupBox_chart_type)
        self.radioButton_frequency_comparison.setObjectName(u"radioButton_frequency_comparison")
        self.radioButton_frequency_comparison.setChecked(True)

        self.verticalLayout_chart_type.addWidget(self.radioButton_frequency_comparison)

        self.radioButton_mode_distribution = QRadioButton(self.groupBox_chart_type)
        self.radioButton_mode_distribution.setObjectName(u"radioButton_mode_distribution")

        self.verticalLayout_chart_type.addWidget(self.radioButton_mode_distribution)

        self.radioButton_mesh_convergence = QRadioButton(self.groupBox_chart_type)
        self.radioButton_mesh_convergence.setObjectName(u"radioButton_mesh_convergence")

        self.verticalLayout_chart_type.addWidget(self.radioButton_mesh_convergence)


        self.verticalLayout_control.addWidget(self.groupBox_chart_type)

        self.verticalLayout_buttons = QVBoxLayout()
        self.verticalLayout_buttons.setObjectName(u"verticalLayout_buttons")
        self.btn_update_chart = QPushButton(self.groupBox_control_panel)
        self.btn_update_chart.setObjectName(u"btn_update_chart")
        self.btn_update_chart.setMinimumSize(QSize(0, 40))
        self.btn_update_chart.setStyleSheet(u"QPushButton {\n"
"    background-color: #2196f3;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #1976d2;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #0d47a1;\n"
"}")

        self.verticalLayout_buttons.addWidget(self.btn_update_chart)

        self.btn_export_results = QPushButton(self.groupBox_control_panel)
        self.btn_export_results.setObjectName(u"btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(0, 40))
        self.btn_export_results.setStyleSheet(u"QPushButton {\n"
"    background-color: #4caf50;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #388e3c;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #2e7d32;\n"
"}")

        self.verticalLayout_buttons.addWidget(self.btn_export_results)


        self.verticalLayout_control.addLayout(self.verticalLayout_buttons)

        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_control.addItem(self.verticalSpacer)


        self.horizontalLayout_main.addWidget(self.groupBox_control_panel)

        self.groupBox_chart_display = QGroupBox(ModalResultComparison)
        self.groupBox_chart_display.setObjectName(u"groupBox_chart_display")
        self.groupBox_chart_display.setStyleSheet(u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}")
        self.verticalLayout_chart = QVBoxLayout(self.groupBox_chart_display)
        self.verticalLayout_chart.setSpacing(10)
        self.verticalLayout_chart.setObjectName(u"verticalLayout_chart")
        self.horizontalLayout_chart_toolbar = QHBoxLayout()
        self.horizontalLayout_chart_toolbar.setObjectName(u"horizontalLayout_chart_toolbar")
        self.label_chart_title = QLabel(self.groupBox_chart_display)
        self.label_chart_title.setObjectName(u"label_chart_title")
        self.label_chart_title.setStyleSheet(u"font-size: 14px; font-weight: bold; color: #333;")

        self.horizontalLayout_chart_toolbar.addWidget(self.label_chart_title)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_chart_toolbar.addItem(self.horizontalSpacer)

        self.btn_save_chart = QPushButton(self.groupBox_chart_display)
        self.btn_save_chart.setObjectName(u"btn_save_chart")
        self.btn_save_chart.setMaximumSize(QSize(100, 30))
        self.btn_save_chart.setStyleSheet(u"QPushButton {\n"
"    background-color: #ff9800;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-size: 11px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #f57c00;\n"
"}")

        self.horizontalLayout_chart_toolbar.addWidget(self.btn_save_chart)


        self.verticalLayout_chart.addLayout(self.horizontalLayout_chart_toolbar)

        self.widget_chart_container = QWidget(self.groupBox_chart_display)
        self.widget_chart_container.setObjectName(u"widget_chart_container")
        self.widget_chart_container.setMinimumSize(QSize(600, 500))
        self.widget_chart_container.setStyleSheet(u"QWidget {\n"
"    border: 2px dashed #cccccc;\n"
"    border-radius: 8px;\n"
"    background-color: #fafafa;\n"
"}")
        self.verticalLayout_chart_content = QVBoxLayout(self.widget_chart_container)
        self.verticalLayout_chart_content.setObjectName(u"verticalLayout_chart_content")
        self.label_chart_placeholder = QLabel(self.widget_chart_container)
        self.label_chart_placeholder.setObjectName(u"label_chart_placeholder")
        self.label_chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_chart_placeholder.setStyleSheet(u"color: #999999; \n"
"font-size: 14px; \n"
"line-height: 1.6;\n"
"border: none;")

        self.verticalLayout_chart_content.addWidget(self.label_chart_placeholder)


        self.verticalLayout_chart.addWidget(self.widget_chart_container)


        self.horizontalLayout_main.addWidget(self.groupBox_chart_display)


        self.retranslateUi(ModalResultComparison)

        QMetaObject.connectSlotsByName(ModalResultComparison)
    # setupUi

    def retranslateUi(self, ModalResultComparison):
        ModalResultComparison.setWindowTitle(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u6790\u7ed3\u679c\u5bf9\u6bd4", None))
        self.groupBox_control_panel.setTitle(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u6790\u7ed3\u679c\u63a7\u5236", None))
        self.groupBox_mesh_results.setTitle(QCoreApplication.translate("ModalResultComparison", u"\u7f51\u683c\u7ed3\u679c\u5217\u8868", None))
        self.label_mesh_info.setText(QCoreApplication.translate("ModalResultComparison", u"\u9009\u62e9\u8981\u5bf9\u6bd4\u7684\u7f51\u683c\u7ed3\u679c:", None))
        self.groupBox_modal_options.setTitle(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u6790\u9009\u9879", None))
        self.checkBox_show_frequencies.setText(QCoreApplication.translate("ModalResultComparison", u"\u663e\u793a\u9891\u7387\u6570\u503c", None))
        self.checkBox_show_mode_shapes.setText(QCoreApplication.translate("ModalResultComparison", u"\u663e\u793a\u6a21\u6001\u9636\u6570", None))
        self.checkBox_show_mesh_info.setText(QCoreApplication.translate("ModalResultComparison", u"\u663e\u793a\u7f51\u683c\u4fe1\u606f", None))
        self.groupBox_chart_type.setTitle(QCoreApplication.translate("ModalResultComparison", u"\u56fe\u8868\u7c7b\u578b", None))
        self.radioButton_frequency_comparison.setText(QCoreApplication.translate("ModalResultComparison", u"\u9891\u7387\u5bf9\u6bd4\u56fe", None))
        self.radioButton_mode_distribution.setText(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u5e03\u56fe", None))
        self.radioButton_mesh_convergence.setText(QCoreApplication.translate("ModalResultComparison", u"\u7f51\u683c\u6536\u655b\u6027\u5206\u6790", None))
        self.btn_update_chart.setText(QCoreApplication.translate("ModalResultComparison", u"\u66f4\u65b0\u56fe\u8868", None))
        self.btn_export_results.setText(QCoreApplication.translate("ModalResultComparison", u"\u5bfc\u51fa\u7ed3\u679c", None))
        self.groupBox_chart_display.setTitle(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u6790\u7ed3\u679c\u56fe\u8868", None))
        self.label_chart_title.setText(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u9891\u7387\u5bf9\u6bd4\u5206\u6790", None))
        self.btn_save_chart.setText(QCoreApplication.translate("ModalResultComparison", u"\u4fdd\u5b58\u56fe\u8868", None))
        self.label_chart_placeholder.setText(QCoreApplication.translate("ModalResultComparison", u"\u6a21\u6001\u5206\u6790\u7ed3\u679c\u56fe\u8868\u5c06\u5728\u6b64\u5904\u663e\u793a\n"
"\n"
"\ud83d\udcca \u53ef\u7528\u6570\u636e\u7c7b\u578b\uff1a\n"
"\u2022 \u7f51\u683c\u540d\u79f0\u548c\u5c3a\u5bf8\u4fe1\u606f\n"
"\u2022 \u7f51\u683c\u8282\u70b9/\u5355\u5143\u6570\u91cf\n"
"\u2022 \u6a21\u6001\u9636\u6570 (1-N\u9636)\n"
"\u2022 \u5404\u9636\u6a21\u6001\u9891\u7387\u7ed3\u679c\n"
"\n"
"\ud83d\udca1 \u9009\u62e9\u5de6\u4fa7\u7f51\u683c\u7ed3\u679c\u5e76\u70b9\u51fb\u201c\u66f4\u65b0\u56fe\u8868\u201d\u5f00\u59cb\u5206\u6790", None))
    # retranslateUi
