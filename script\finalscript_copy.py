# -*- coding: utf-8 -*-
import os
import math
import csv
import logging
import json


# The following objects are assumed to be available in the runtime environment
# ExtAPI, Model, DataModel, etc.

class LoggerManager:
    """
    Manages logging configuration and provides a logger instance.
    """
    def __init__(self, target_directory):
        if not os.path.exists(target_directory):
            try:
                os.makedirs(target_directory)
                print("Log directory created: ", target_directory)
            except Exception as e:
                print("Failed to create target directory {}: {}".format(target_directory,e))
                raise
        
        if not os.access(target_directory, os.W_OK):
            raise PermissionError("Target directory is not writable: {}".format(target_directory))
        
        self.log_file_path = os.path.join(target_directory, "ansys_workbench.log")
        try:
            self.logger = self.setup_logger(self.log_file_path)
        except Exception as e:
            print("Error initializing logger: {}".format(e))
            raise

    
    def setup_logger(self, log_file):
        logger = logging.getLogger('AnsysWorkbenchLogger')
        if not logger.handlers:  
            logger.setLevel(logging.DEBUG)
            fh = logging.FileHandler(log_file)
            fh.setLevel(logging.DEBUG)
            ch = logging.StreamHandler()
            ch.setLevel(logging.ERROR)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            fh.setFormatter(formatter)
            ch.setFormatter(formatter)
            logger.addHandler(fh)
            logger.addHandler(ch)
        return logger

    
    def get_logger(self):
        return self.logger

# --------------------------------------------------------------------
# Added MeshManager
# --------------------------------------------------------------------
class MeshManager(object):
    """
    Responsible for collecting Body IDs, unified and local mesh generation, exporting mesh info and screenshots
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model   = ExtAPI.DataModel.Project.Model
        self.all_body_ids = []

    # Collect all geometry body IDs
    def store_all_bodies(self):
        self.all_body_ids = []
        for asm in self.ExtAPI.DataModel.GeoData.Assemblies:
            for part in asm.Parts:
                for body in part.Bodies:
                    self.all_body_ids.append(body.Id)
        self.logger.info("All Body IDs cached, total count: %d", len(self.all_body_ids))
        return self.all_body_ids

    # Global automatic mesh + Sizing
    def generate_allbody_mesh(self, element_size_m=0.01):
        mesh = self.Model.Mesh
        mesh.ClearGeneratedData()
        # Clear old methods/Sizing
        for child in list(mesh.Children):
            child.Delete()

        sel = self.ExtAPI.SelectionManager.CreateSelectionInfo(
            SelectionTypeEnum.GeometryEntities)
        sel.Ids = self.all_body_ids

        method = mesh.AddAutomaticMethod()
        method.Location = sel

        sizing = mesh.AddSizing()
        sizing.Location = sel
        sizing.ElementSize = Quantity(element_size_m, "m")

        mesh.GenerateMesh()
        self.logger.info("Global mesh generation completed (ElementSize = %.4f m)", element_size_m)

    # Apply local Sizing to individual named selection
    def generate_local_sizing(self, named_dict, sel_name, elem_size_m):
        ids = named_dict.get(sel_name, [])
        if not ids:
            self.logger.warning("Named selection %s not found, skipping local mesh", sel_name)
            return

        mesh = self.Model.Mesh
        sel  = self.ExtAPI.SelectionManager.CreateSelectionInfo(
            SelectionTypeEnum.GeometryEntities)
        sel.Ids = ids
        sizing = mesh.AddSizing()
        sizing.Location = sel
        sizing.ElementSize = Quantity(elem_size_m, "m")
        mesh.Update()      # Only update mesh tree; do not recalculate immediately

    # Export mesh information and screenshots
    def export_mesh_info_png(self, png_path):
        mesh = self.Model.Mesh
        mesh.Activate()

        # 简易截屏设置
        setting = Ansys.Mechanical.Graphics.GraphicsImageExportSettings()
        Graphics.Camera.SetSpecificViewOrientation(ViewOrientationType.Iso)
        Graphics.Camera.SceneHeight = Quantity(1.5, 'm')
        Graphics.ViewOptions.ShowRuler = False
        Graphics.ViewOptions.ShowTriad = False
        Graphics.ViewOptions.ShowLegend = False
        Graphics.ExportImage(png_path, GraphicsImageExportFormat.PNG, setting)
        Graphics.ViewOptions.ShowRuler = True
        Graphics.ViewOptions.ShowTriad = True
        Graphics.ViewOptions.ShowLegend = True
        
        

        n_ele  = mesh.Elements
        n_node = mesh.Nodes
        self.logger.info("Screenshot saved to %s, Elements=%d, Nodes=%d", png_path, n_ele, n_node)
        return n_ele, n_node


class SelectionManagerWrapper:
    """
    Encapsulates selection operations and named selections retrieval.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model

    def get_named_selections_dict(self):
        named_selections_dict = {
            ns.Name: ns.Ids for ns in self.Model.NamedSelections.Children
        }
        self.logger.info("Retrieved all named selections.")
        for name, ids in named_selections_dict.iteritems():
            self.logger.debug("Named Selection ID: %s, Name: %s", ids, name)
        return named_selections_dict

    def create_selection(self, entity_ids, entity_type=SelectionTypeEnum.GeometryEntities):
        selection = self.ExtAPI.SelectionManager.CreateSelectionInfo(entity_type)
        selection.Ids = entity_ids
        return selection


class FileIO:
    """
    Handles file I/O operations, such as reading force files.
    """
    def __init__(self, logger):
        self.logger = logger

    def read_force_file(self, file_name):
        time_list = []
        force_list = []
        if not os.path.exists(file_name):
            self.logger.warning("File does not exist: %s", file_name)
            return time_list, force_list
        try:
            with open(file_name, "r") as file:
                reader = csv.reader(file, delimiter=' ')
                for row in reader:
                    if not row or row[0].startswith(('"', '(')):
                        continue
                    if len(row) >= 3:
                        try:
                            force = float(row[1])
                            t = float(row[2])
                            force_list.append(Quantity("{0} [N]".format(force)))
                            time_list.append(Quantity("{0} [s]".format(t)))
                        except ValueError:
                            self.logger.warning("Invalid data in file: %s", file_name)
            self.logger.info("Read force file: %s", file_name)
            return time_list, force_list
        except Exception as e:
            self.logger.error("Error occurred while reading file %s: %s", file_name, str(e), exc_info=True)
            return time_list, force_list


class GeometryHelper:
    """
    Stores node coordinates and finds the closest nodes to given target points.
    """
    def __init__(self, logger):
        self.logger = logger

    def store_node_coordinates(self, analysis):
        try:
            mesh = analysis.MeshData
            node_ids = mesh.NodeIds
            node_by_id = mesh.NodeById
            node_coordinates = {
                node_id: (node_by_id(node_id).X, node_by_id(node_id).Y, node_by_id(node_id).Z)
                for node_id in node_ids
            }
            self.logger.info("Stored coordinates for all nodes.")
            return node_coordinates
        except Exception as e:
            self.logger.error("Error in store_node_coordinates: %s", str(e), exc_info=True)
            return {}

    def find_closest_node(self, node_coordinates, tx, ty, tz):
        try:
            min_dist = float('inf')
            closest_id = None
            for node_id, (x, y, z) in node_coordinates.items():
                dx = x - tx
                dy = y - ty
                dz = z - tz
                dist_sq = dx*dx + dy*dy + dz*dz
                if dist_sq < min_dist:
                    min_dist = dist_sq
                    closest_id = node_id
            self.logger.info("Found closest node ID: %s for point (%s,%s,%s).", closest_id, tx, ty, tz)
            return closest_id
        except Exception as e:
            self.logger.error("Error in find_closest_node: %s", str(e), exc_info=True)
            return None

    def find_closest_nodes(self, node_coordinates, target_points):
        closest_nodes = {}
        try:
            for (tx, ty, tz) in target_points:
                cn = self.find_closest_node(node_coordinates, tx, ty, tz)
                closest_nodes[(tx, ty, tz)] = cn
            self.logger.info("Processed all target points to find closest nodes.")
            return closest_nodes
        except Exception as e:
            self.logger.error("Error in find_closest_nodes: %s", str(e), exc_info=True)
            return closest_nodes

class ConnectionManager:
    """
    Manages connection groups and joints.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model
        self.connection_group = None
        
    # ------------------【 bearing zone 】------------------ #
    def load_bearing_config(self, config_file):
        """
        Reads a JSON file that contains multiple bearing configurations.
        Example JSON structure:
        {
          "bearings": [
            {
              "ref_name": "zhou1-dianji",
              "mob_name": "dianji-zhou1",
              "RotationPlane": "X-Y Plane",
              "k11": 1000.0,
              "k22": 2000.0,
              "k12": 0.0,
              "k21": 0.0,
              "c11": 1.0,
              "c22": 2.0,
              "c12": 0.0,
              "c21": 0.0
            },
            {
              "ref_name": "zhou2-dianji",
              "mob_name": "dianji-zhou2",
              "RotationPlane": "Y-Z Plane",
              "k11": 500.0,
              ...
            }
          ]
        }
        """
        if not os.path.exists(config_file):
            self.logger.warning("Bearing config file not found: %s", config_file)
            return []

        try:
            with open(config_file, "r") as f:
                data = json.load(f)
            bearings = data.get("bearings", [])
            self.logger.info("Loaded %d bearing configurations from %s.", len(bearings), config_file)
            return bearings
        except Exception as e:
            self.logger.error("Error reading bearing config file %s: %s", config_file, str(e), exc_info=True)
            return []
    
    def create_bearing_in_batch(self, named_selections_dict, bearing_list):
        """
        Loops through the bearing_list and calls add_bearing_connection() for each item.
        """
        for item in bearing_list:
            ref_name = item.get("ref_name")
            mob_name = item.get("mob_name")
            if not (ref_name and mob_name):
                self.logger.warning("Skipping invalid bearing config: %s", item)
                continue

            self.add_bearing_connection(
                named_selections_dict,
                item,
                ref_name, 
                mob_name
            )
    
    def add_bearing_connection(self, named_selections_dict, setting_dict, ref_name, mob_name):
        """
        Original bearing creation method, retained from older code with minimal changes.
        """
        try:
            self.logger.info("Adding bearing connection for: %s <-> %s", ref_name, mob_name)

            bearing = self.Model.Connections.AddBearing()
            bearing.ConnectionType = ConnectionScopingType.BodyToBody
            bearing.RenameBasedOnDefinition()

            # setting RotationPlane
            plane_setting = setting_dict.get("RotationPlane", "X-Y Plane")
            if plane_setting == "X-Y Plane":
                bearing.ReferenceRotationPlane = RotationPlane.XY
            elif plane_setting == "Y-Z Plane":
                bearing.ReferenceRotationPlane = RotationPlane.YZ
            else:
                bearing.ReferenceRotationPlane = RotationPlane.XZ

            # Stiffness
            bearing.StiffnessK11.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k11", 0.0))))
            bearing.StiffnessK22.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k22", 0.0))))
            bearing.StiffnessK12.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k12", 0.0))))
            bearing.StiffnessK21.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k21", 0.0))))

            # Damping
            bearing.DampingC11.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c11", 0.0))))
            bearing.DampingC22.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c22", 0.0))))
            bearing.DampingC12.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c12", 0.0))))
            bearing.DampingC21.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c21", 0.0))))

            # Reference & Mobile
            selection_ref = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_ref.Ids = named_selections_dict.get(ref_name, [])
            bearing.ReferenceLocation = selection_ref

            selection_mob = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_mob.Ids = named_selections_dict.get(mob_name, [])
            bearing.MobileLocation = selection_mob
            if plane_setting == "X-Y Plane":
                bearing.MobileZCoordinate = bearing.ReferenceZCoordinate
            elif plane_setting == "Y-Z Plane":
                bearing.MobileXCoordinate = bearing.ReferenceXCoordinate
            else:
                bearing.MobileYCoordinate = bearing.ReferenceYCoordinate

            self.logger.info("Bearing connection added successfully.")
            return bearing
        except Exception as e:
            self.logger.error("Error in add_bearing_connection: %s", str(e), exc_info=True)
            return None
    # ------------------【 bearing zone 】------------------ #
    
    # ------------------【 bushing zone 】------------------ #
    def load_bushing_config(self, config_file):
        """
        Example JSON structure:
        {
          "bushings": [
            {
              "mob_name": "bushing_1",
              "connection_type": "BodyToGround",
              "stiffness_matrix": {
                "X": { "0": 5000000, "1": 5000000, "2": 0 },
                "Y": { "1": 0, "2": 0 },
                "Z": { "2": 0 }
              },
              "damping_matrix": {
                "X": { "0": 0, "1": 0, "2": 0 },
                "Y": { "1": 0, "2": 0 },
                "Z": { "2": 0 }
              }
            },
            ...
          ]
        }
        """
        if not os.path.exists(config_file):
            self.logger.warning("Bushing config file not found: %s", config_file)
            return []

        try:
            with open(config_file, "r") as f:
                data = json.load(f)
            bushing_list = data.get("bushings", [])
            self.logger.info("Loaded %d bushing configs from %s.", len(bushing_list), config_file)
            return bushing_list
        except Exception as e:
            self.logger.error("Error reading bushing config file %s: %s", config_file, str(e), exc_info=True)
            return []

    def create_bushing_joints_in_batch(self, named_selections_dict, bushing_list):
        """
        Loops through the bushing_list from JSON and calls add_bushing_joint() for each item.
        """
        for item in bushing_list:
            mob_name = item.get("mob_name")
            if not mob_name:
                self.logger.warning("Skipping invalid bushing config (missing mob_name): %s", item)
                continue

            connection_type = item.get("connection_type", "BodyToGround")
            stiffness_matrix = item.get("stiffness_matrix", {})
            damping_matrix = item.get("damping_matrix", {})

            self.add_bushing_joint(
                named_selections_dict,
                mob_name,
                stiffness_matrix,
                damping_matrix,
                connection_type=connection_type
            )

    def add_bushing_joint(self, named_selections_dict, mob_name, stiffness_matrix, damping_matrix, connection_type="BodyToGround"):

        try:
            # connection_group
            if not self.connection_group:
                connections = self.Model.Connections
                self.connection_group = connections.AddConnectionGroup()
                self.connection_group.Name = "Joints_bushing"
                self.logger.info("Connection group 'Joints_bushing' created.")
            else:
                self.logger.info("Using existing connection group 'Joints_bushing'.")

            joint = self.connection_group.AddJoint()
            joint.RenameBasedOnDefinition()
            joint.Type = JointType.Bushing

            if connection_type == "BodyToBody":
                joint.ConnectionType = JointScopingType.BodyToBody
            else:
                joint.ConnectionType = JointScopingType.BodyToGround

            # Mobile location
            selection_mob = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_mob.Ids = named_selections_dict.get(mob_name, [])
            joint.MobileLocation = selection_mob

            # get BushingWorksheet
            worksheet = joint.BushingWorksheet

            self.set_bushing_parameters(worksheet, stiffness_matrix, is_stiffness=True)

            self.set_bushing_parameters(worksheet, damping_matrix, is_stiffness=False)

            self.logger.info("Bushing joint added successfully for reference: %s, type=%s", mob_name, connection_type)
            return joint
        except Exception as e:
            self.logger.error("Error adding bushing joint: %s", str(e), exc_info=True)
            return None

    def set_bushing_parameters(self, worksheet, parameter_matrix, is_stiffness=True):
        try:
            for direction, indices in parameter_matrix.iteritems():
                for str_index, value in indices.iteritems():
                    index = int(str_index)
                    method_name = ""
                    if is_stiffness:
                        if direction == 'X':
                            method_name = 'SetBushingStiffnessPerUnitX'
                        elif direction == 'Y':
                            method_name = 'SetBushingStiffnessPerUnitY'
                        elif direction == 'Z':
                            method_name = 'SetBushingStiffnessPerUnitZ'
                    else:
                        if direction == 'X':
                            method_name = 'SetBushingDampingPerUnitX'
                        elif direction == 'Y':
                            method_name = 'SetBushingDampingPerUnitY'
                        elif direction == 'Z':
                            method_name = 'SetBushingDampingPerUnitZ'

                    if not method_name:
                        self.logger.warning("Invalid direction: %s", direction)
                        continue

                   
                    valid_indices = []
                    if direction == 'X':
                        valid_indices = [0, 1, 2]
                    elif direction == 'Y':
                        valid_indices = [1, 2]
                    elif direction == 'Z':
                        valid_indices = [2]

                    if index not in valid_indices:
                        self.logger.warning("Invalid index %s for direction %s. Skipping.", index, direction)
                        continue

                    set_method = getattr(worksheet, method_name, None)
                    if set_method:
                        set_method(index, value)
                        param_type = "Stiffness" if is_stiffness else "Damping"
                        self.logger.debug("Set %s for %s direction at index %s to %s.",
                                          param_type, direction, index, value)
                    else:
                        self.logger.warning("Method %s not found on worksheet.", method_name)
        except Exception as e:
            param_type = "Stiffness" if is_stiffness else "Damping"
            self.logger.error("Error setting %s parameters: %s", param_type, str(e), exc_info=True)
    # ------------------【 bushing zone 】------------------ #







class AnalysisManager:
    """
    Manages adding various constraints (displacement, bearing, forces) to the analysis.
    """
    def __init__(self, logger, ExtAPI, file_io):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model
        self.file_io = file_io
        
    # ------------------【 displacement zone 】------------------ #
    def create_displacement_constraint(self, analysis, named_selections_dict, name, value_dict):
        """
        Create a displacement constraint with specified parameters.
        """
        try:
            self.logger.info("Creating displacement constraint for %s.", name)
            
            # Get the selection based on the named selection dictionary
            selection = self._create_selection(named_selections_dict, name)
            if not selection:
                return

            # Create the displacement constraint
            displacement = analysis.AddDisplacement()
            displacement.Location = selection

            # Set values for each direction
            for direction, value in value_dict.items():
                self._set_displacement_component(displacement, direction, value)

            self.logger.info("Displacement constraint for %s created successfully.", name)
        except Exception as e:
            self.logger.error("Error creating displacement constraint for %s: %s", name, str(e), exc_info=True)

    def _set_displacement_component(self, constraint, direction, value):
        """
        Helper method to set displacement component values for a constraint.
        """
        try:
            component_map = {
                "X": constraint.XComponent,
                "Y": constraint.YComponent,
                "Z": constraint.ZComponent
            }
            if direction in component_map:
                component = component_map[direction]
                if value == "Free":
                    component.Output.DefinitionType = VariableDefinitionType.Free
                elif isinstance(value, (int, float)):
                    # Use the correct units based on the direction
                    unit = "rad" if "Rot" in direction else "m"
                    component.Output.DiscreteValues = [Quantity("{} [{}]".format(value, unit))]
                else:
                    self.logger.warning("Invalid value type for %s: %s", direction, value)
            else:
                self.logger.warning("Direction %s is not valid for displacement components.", direction)
        except Exception as e:
            self.logger.error("Error setting displacement component %s: %s", direction, str(e), exc_info=True)


    # ------------------【 displacement zone 】------------------ #
    
    # ------------------【 remote_displacement zone 】------------------ #
    def create_remote_displacement(self, analysis, named_selections_dict, name, value_dict):
        """
        Create a remote displacement with specified parameters.
        """
        try:
            self.logger.info("Creating remote displacement for %s.", name)
            
            # Get the selection based on the named selection dictionary
            selection = self._create_selection(named_selections_dict, name)
            if not selection:
                return

            # Create the remote displacement
            remote_disp = analysis.AddRemoteDisplacement()
            remote_disp.Location = selection

            # Set values for each direction
            for direction, value in value_dict.items():
                self._set_remote_displacement_component(remote_disp, direction, value)

            # Set behavior to rigid
            remote_disp.Behavior = LoadBehavior.Rigid
            self.logger.info("Remote displacement for %s created successfully.", name)
        except Exception as e:
            self.logger.error("Error creating remote displacement for %s: %s", name, str(e), exc_info=True)

    def _set_remote_displacement_component(self, constraint, direction, value):
        """
        Helper method to set displacement component values for a constraint.
        """
        try:
            component_map = {
                "X": constraint.XComponent,
                "Y": constraint.YComponent,
                "Z": constraint.ZComponent,
                "XRot": constraint.RotationX,
                "YRot": constraint.RotationY,
                "ZRot": constraint.RotationZ
            }
            if direction in component_map:
                component = component_map[direction]
                if value == "Free":
                    component.Output.DefinitionType = VariableDefinitionType.Free
                elif isinstance(value, (int, float)):
                    # Use the correct units based on the direction
                    unit = "rad" if "Rot" in direction else "m"
                    component.Output.DiscreteValues = [Quantity("{} [{}]".format(value, unit))]
                else:
                    self.logger.warning("Invalid value type for %s: %s", direction, value)
            else:
                self.logger.warning("Direction %s is not valid for remote displacement components.", direction)
        except Exception as e:
            self.logger.error("Error setting remote displacement component %s: %s", direction, str(e), exc_info=True)
    
    # ------------------【 remote_displacement zone 】------------------ #


    def _create_selection(self, named_selections_dict, name):
        """
        Helper method to create a selection based on the named selection dictionary.
        """
        try:
            selection = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection.Ids = named_selections_dict.get(name, [])
            if not selection.Ids:
                self.logger.warning("No IDs found for named selection: %s", name)
            return selection
        except Exception as e:
            self.logger.error("Error creating selection for %s: %s", name, str(e), exc_info=True)
            return None

    # ------------------【 analysis_settings zone 】------------------ #
    def configure_analysis_settings(self, analysis, **settings):
        """
        Configure analysis settings with flexible flags and parameters.
        """
        try:
            analysis.AnalysisSettings.Activate()
            analysis.AnalysisSettings.AutomaticTimeStepping = AutomaticTimeStepping.Off
            analysis.AnalysisSettings.TimeStep = Quantity(settings.get("time_step", "1.1111e-4 [s]"))
            analysis.AnalysisSettings.StepEndTime = Quantity(settings.get("end_time", "0.32 [s]"))
            analysis.AnalysisSettings.StiffnessCoefficient = settings.get("StiffnessCoefficient", 0.00015939669)
            analysis.AnalysisSettings.MassCoefficient = settings.get("MassCoefficient", 5.634898)
            
            self.logger.info(
                "Analysis settings configured: TimeStep=%s, EndTime=%s, StiffnessCoefficient=%s, MassCoefficient=%s",
                settings.get("time_step"),
                settings.get("end_time"),
                settings.get("StiffnessCoefficient"),
                settings.get("MassCoefficient"),
            )

            # Configure flags
            self._set_analysis_flag(analysis, "Stress", settings.get("Stress_flag", False))
            self._set_analysis_flag(analysis, "Strain", settings.get("Strain_flag", False))
            self._set_analysis_flag(analysis, "ContactData", settings.get("ContactData_flag", False))
            self._set_analysis_flag(analysis, "CalculateVolumeEnergy", settings.get("CalculateVolumeEnergy_flag", False))
            self._set_analysis_flag(analysis, "EulerAngles", settings.get("EulerAngles_flag", False))
        except Exception as e:
            self.logger.error("Error configuring analysis settings: %s", str(e), exc_info=True)

    def _set_analysis_flag(self, analysis, flag_name, flag_value):
        """
        Set a specific analysis flag to the given value.
        """
        try:
            setattr(analysis.AnalysisSettings, flag_name, flag_value)
            self.logger.info("Analysis settings configured: %s set to %s.", flag_name, flag_value)
        except Exception as e:
            self.logger.error("Error setting analysis flag %s: %s", flag_name, str(e), exc_info=True)
    # ------------------【 analysis_settings zone 】------------------ #
    
    # ------------------【 forces zone 】------------------ #
    def apply_forces(self, analysis, target_directory,named_selections_dict, force_files_map):
        """
        Reads forces from a file according to force_files_map and adds force loads to the corresponding named selection.
        force_files_map format:
        {
          "yl-wall": ["yl-fx.out", "yl-fy.out", "yl-fz.out"],
          "wk-wall": ["wk-fx.out", "wk-fy.out", "wk-fz.out"],
          ...
        }
        """
        # Change working directory
        current_directory = os.getcwd()
        self.logger.info("Current working directory: %s", current_directory)
        
        try:
            os.chdir(target_directory)
            self.logger.info("Successfully changed to target folder: %s", os.getcwd())
        except OSError:
            self.logger.error("Failed to change to target folder: %s", target_directory)
        
        with Transaction():
            for selection_name, force_files in force_files_map.iteritems():
                named_selection_location = named_selections_dict.get(selection_name)
                if not named_selection_location:
                    self.logger.warning("Named selection not found: %s", selection_name)
                    continue

                force_data_map = {"fx": [], "fy": [], "fz": []}
                time_data = None
                for force_file in force_files:
                    # Reading files with file_io
                    t_data, f_data = self.file_io.read_force_file(force_file)
                    if t_data and f_data:
                        direction = force_file.split('-')[-1].split('.')[0]
                        if direction in force_data_map:
                            force_data_map[direction] = f_data
                            if not time_data:
                                time_data = t_data
                    else:
                        self.logger.warning("Failed to read file %s, skipping this direction.", force_file)

                if any(force_data_map.values()):
                    try:
                        force_constraint = analysis.AddForce()
                        selection_force = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
                        selection_force.Ids = named_selection_location
                        force_constraint.Location = selection_force
                        force_constraint.DefineBy = LoadDefineBy.Components

                        if "fx" in force_data_map and force_data_map["fx"]:
                            force_constraint.XComponent.Inputs[0].DiscreteValues = time_data
                            force_constraint.XComponent.Output.DiscreteValues = force_data_map["fx"]
                        if "fy" in force_data_map and force_data_map["fy"]:
                            force_constraint.YComponent.Inputs[0].DiscreteValues = time_data
                            force_constraint.YComponent.Output.DiscreteValues = force_data_map["fy"]
                        if "fz" in force_data_map and force_data_map["fz"]:
                            force_constraint.ZComponent.Inputs[0].DiscreteValues = time_data
                            force_constraint.ZComponent.Output.DiscreteValues = force_data_map["fz"]

                        self.logger.info("Force constraints added for selection: %s", selection_name)
                    except Exception as e:
                        self.logger.error("Error adding force constraints for %s: %s", selection_name, str(e), exc_info=True)
    # ------------------【 forces zone 】------------------ #

    # ------------------【 acceleration_probe zone 】------------------ #
    def add_acceleration_probe(self, solution, coordinate_system):
        """
        Add an acceleration probe to the analysis solution at the specified coordinate system.
        """
        try:
            acceleration_probe = solution.AddAccelerationProbe()
            acceleration_probe.LocationMethod = LocationDefinitionMethod.CoordinateSystem
            acceleration_probe.Orientation = coordinate_system
            acceleration_probe.CoordinateSystemSelection = coordinate_system
            logger.info("Acceleration probe added.")
            return acceleration_probe
        except Exception as e:
            logger.error("Error adding acceleration probe: %s", str(e), exc_info=True)
            return None
    # ------------------【 acceleration_probe zone 】------------------ #
    
    # ------------------【 Rotationalvlocity zone 】------------------ #
    def add_Rotationalvlocity(self, analysis, named_selections_dict, rotation_speed, rotation_axis = "z"):
        RotationalVelocity=analysis.AddRotationalVelocity()
        selection = self._create_selection(named_selections_dict, "rotor")
        if not selection:
            return
        RotationalVelocity.Location = selection
        RotationalVelocity.DefineBy = LoadDefineBy.Components
        if rotation_axis == "x":
            RotationalVelocity.XComponent.Output.DiscreteValues = [Quantity('{} [rev min^-1]'.format(str(rotation_speed)))]
        elif rotation_axis == "y":
            RotationalVelocity.YComponent.Output.DiscreteValues = [Quantity('{} [rev min^-1]'.format(str(rotation_speed)))]
        else:
            RotationalVelocity.ZComponent.Output.DiscreteValues = [Quantity('{} [rev min^-1]'.format(str(rotation_speed)))]
        pass
    # ------------------【 Rotationalvlocity zone 】------------------ #
    
class MainApp:
    """
    Main application class coordinating all operations.
    """
    def __init__(self, ExtAPI):
        self.ExtAPI = ExtAPI
        self.Model=ExtAPI.DataModel.Project.Model
        try:
            target_directory = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
            self.logger = LoggerManager(target_directory).get_logger()
            self.logger.info("Logger initialized successfully.")
        except Exception as e:
            print("Failed to initialize logger: {}".format(e))
            raise

        self.analysis = ExtAPI.DataModel.Project.Model.Analyses[0]
        self.file_io = FileIO(self.logger)
        self.geometry_helper = GeometryHelper(self.logger)
        self.analysis_manager = AnalysisManager(self.logger, ExtAPI, self.file_io)
        self.selection_mgr = SelectionManagerWrapper(self.logger, ExtAPI)
        self.connection_manager = ConnectionManager(self.logger, ExtAPI)
        # Add this line
        self.mesh_mgr = MeshManager(self.logger, ExtAPI)
    
    
    def run_mesh_setting(self):
        """
        Reads mesh_config.json, generates global/local mesh, and exports info.
        """
        self.logger.info("[Mesh] Starting mesh generation...")
        
        # 1. Load mesh configuration
        cfg_path = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/mesh_config_last.json"
        try:
            with open(cfg_path, "r") as f:
                config = json.load(f)
        except Exception as e:
            self.logger.error("Failed to load mesh_config.json: %s", e)
            return

        element_size = config.get("element_size")

        # 2. Global Meshing
        self.mesh_mgr.store_all_bodies()
        self.mesh_mgr.generate_allbody_mesh(element_size)


        # 4. Final Generation and Export
        self.logger.info("Generating final mesh...")
        self.Model.Mesh.GenerateMesh()
    
    
    def clear_previous_setup(self):
        
        self.logger.info("--- starting cleanup of previous settings ---")

        items_to_delete = [item for item in self.analysis.Children]
        if items_to_delete:
            self.logger.info("Deleting %d old items from the analysis environment...", len(items_to_delete))
            for item in items_to_delete:
                try:
                    item.Delete()
                except Exception as e:
                    self.logger.warning("Error deleting item %s: %s", item.Name, e)
 
        connection_items_to_delete = [conn for conn in self.Model.Connections.Children]
        if connection_items_to_delete:
            self.logger.info("Deleting %d old connection items...", len(connection_items_to_delete))
            for item in connection_items_to_delete:
                if item.Name == "Contacts":
                    print("baoliu Contacts")
                else:
                    item.Delete()

        self.logger.info("--- Cleanup of previous settings complete  ---")
    
    # 分析设置
    def run_analysis_setting(self):
        self.logger.info("Starting run_analysis_setting...")
        
        # 1) 从外部 JSON 读取分析配置
        ansys_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\analysis_config_latest.json"
        analysis_params = {}
        
        if os.path.exists(ansys_result_path):
            import codecs
            try:
                with codecs.open(ansys_result_path, "r", "utf-8-sig") as f:
                    data = json.load(f)
                analysis_params = data.get("analysis_settings", {})
                self.logger.info("Loaded analysis settings from %s.", ansys_result_path)
            except Exception as e:
                self.logger.error("Error reading analysis config file %s: %s", ansys_result_path, str(e), exc_info=True)
        else:
            self.logger.warning("Analysis config file not found: %s. Using default parameters.", ansys_result_path)

        default_params = {
            "Stress_flag": True,
            "Strain_flag": True,
            "ContactData_flag": True,
            "CalculateVolumeEnergy_flag": True,
            "EulerAngles_flag": True,
            "time_step": "0.00011111 [s]",
            "end_time": "0.32 [s]",
            "StiffnessCoefficient": 0.00015939669,
            "MassCoefficient": 5.634898
        }

        for key, default_val in default_params.items():
            if key not in analysis_params:
                analysis_params[key] = default_val
    
        try:
            self.analysis_manager.configure_analysis_settings(
                analysis=self.analysis,
                Stress_flag=analysis_params.get("Stress_flag"),
                Strain_flag=analysis_params.get("Strain_flag"),
                ContactData_flag=analysis_params.get("ContactData_flag"),
                CalculateVolumeEnergy_flag=analysis_params.get("CalculateVolumeEnergy_flag"),
                EulerAngles_flag=analysis_params.get("EulerAngles_flag"),
                time_step=analysis_params.get("time_step"),
                end_time=analysis_params.get("end_time"),
                StiffnessCoefficient=analysis_params.get("StiffnessCoefficient"),
                MassCoefficient=analysis_params.get("MassCoefficient")
            )
            self.logger.info("Analysis settings configured successfully with parameters: %s", analysis_params)
        except Exception as e:
            self.logger.error("Error configuring analysis settings: %s", str(e), exc_info=True)
    
        self.logger.info("run_analysis_setting completed.")
        pass

    def _parse_monitor_points_data(self, data):

        target_points = []
        monitor_points_info = []

        try:

            monitor_points = data.get("monitor_points", [])

            if monitor_points and isinstance(monitor_points[0], dict):
                self.logger.info("Detected new monitor points format with detailed information.")

                for point in monitor_points:
                    if isinstance(point, dict):
                        if "coordinates" in point:
                            coords = point["coordinates"]
                            if isinstance(coords, list) and len(coords) == 3:
                                try:
                                    x, y, z = float(coords[0]), float(coords[1]), float(coords[2])
                                    target_points.append((x, y, z))
                                    monitor_points_info.append(point)

                                    self.logger.debug("Parsed monitor point: %s at (%.3f, %.3f, %.3f)",
                                                    point.get("name", "Unknown"), x, y, z)
                                except (ValueError, TypeError) as e:
                                    self.logger.warning("Invalid coordinates in monitor point %s: %s",
                                                      point.get("name", "Unknown"), str(e))
                            else:
                                self.logger.warning("Invalid coordinates format in monitor point: %s", point)
                        else:
                            self.logger.warning("Monitor point missing coordinates field: %s", point)
                    else:
                        self.logger.warning("Invalid monitor point format (not a dict): %s", point)

            elif monitor_points and isinstance(monitor_points[0], list):
                self.logger.info("Detected old monitor points format (coordinate arrays).")

                for i, coords in enumerate(monitor_points):
                    if isinstance(coords, list) and len(coords) == 3:
                        try:
                            x, y, z = float(coords[0]), float(coords[1]), float(coords[2])
                            target_points.append((x, y, z))

                            point_info = {
                                "name": "Point_{}".format(i + 1),
                                "coordinates": [x, y, z],
                                "id": i + 1,
                                "source": "legacy_format"
                            }
                            monitor_points_info.append(point_info)

                            self.logger.debug("Parsed legacy monitor point %d at (%.3f, %.3f, %.3f)",
                                            i + 1, x, y, z)
                        except (ValueError, TypeError) as e:
                            self.logger.warning("Invalid coordinates in legacy monitor point %d: %s", i, str(e))
                    else:
                        self.logger.warning("Invalid coordinate format in legacy monitor point %d: %s", i, coords)

            elif "monitor_points_coordinates" in data:
                self.logger.info("Detected monitor_points_coordinates format (simplified).")

                coords_list = data["monitor_points_coordinates"]
                for i, coords in enumerate(coords_list):
                    if isinstance(coords, list) and len(coords) == 3:
                        try:
                            x, y, z = float(coords[0]), float(coords[1]), float(coords[2])
                            target_points.append((x, y, z))

                            point_info = {
                                "name": "Coord_Point_{}".format(i + 1),
                                "coordinates": [x, y, z],
                                "id": i + 1,
                                "source": "coordinates_format"
                            }
                            monitor_points_info.append(point_info)

                            self.logger.debug("Parsed coordinate point %d at (%.3f, %.3f, %.3f)",
                                            i + 1, x, y, z)
                        except (ValueError, TypeError) as e:
                            self.logger.warning("Invalid coordinates in coordinate point %d: %s", i, str(e))
                    else:
                        self.logger.warning("Invalid coordinate format in coordinate point %d: %s", i, coords)

            else:
                self.logger.warning("No valid monitor points data found in JSON.")

            if target_points:
                self.logger.info("Successfully parsed %d monitor points.", len(target_points))
                if data.get("monitor_points_count"):
                    expected_count = data["monitor_points_count"]
                    if len(target_points) != expected_count:
                        self.logger.warning("Parsed point count (%d) differs from expected count (%d).",
                                          len(target_points), expected_count)
            else:
                self.logger.warning("No valid monitor points were parsed from the data.")

        except Exception as e:
            self.logger.error("Error parsing monitor points data: %s", str(e), exc_info=True)

        return target_points, monitor_points_info

    def run_constrain_setting(self):
        """
        Configure displacement constraints based on a JSON file.
        """
        try:
            # Get named selections
            named_selections_dict = self.selection_mgr.get_named_selections_dict()

            # File path for displacement constraint settings
            constrain_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\constrain_config_latest.json"

            # Load the JSON file
            with open(constrain_result_path, "r") as f:
                data = json.load(f)

            # Process each displacement constraint
            for constraint_name, value_dict in data.items():

                if isinstance(value_dict, dict):
                    # Determine if it is a remote displacement or a regular displacement
                    if value_dict.get("type") == "remote":
                        self.analysis_manager.create_remote_displacement(
                            self.analysis, named_selections_dict, constraint_name, value_dict.get("values", {})
                        )
                    elif value_dict.get("type") == "displacement":
                        self.analysis_manager.create_displacement_constraint(
                            self.analysis, named_selections_dict, constraint_name, value_dict.get("values", {})
                        )
                else:

                    self.logger.info("Skipping non-dictionary item: {} = {}".format(constraint_name, value_dict))
            self.logger.info("All constraints configured successfully.")
        except IOError as e:
            self.logger.error(str(e), exc_info=True)
        except ValueError as e:
            self.logger.error("Error parsing JSON file %s: %s", constrain_result_path, str(e), exc_info=True)
        except Exception as e:
            self.logger.error("Error in run_constrain_setting: %s", str(e), exc_info=True)
        
        forces_file_path = data.get("force_file_path")

        force_files_map = {
            "yl-wall": ["yl-fx.out", "yl-fy.out", "yl-fz.out"],
            "wk-wall": ["wk-fx.out", "wk-fy.out", "wk-fz.out"],
            # "qbq-qbg": ["qbq-qbg-fx.out", "qbq-qbg-fy.out", "qbq-qbg-fz.out"],
            # "hbq-hbg": ["hbq-hbg-fx.out", "hbq-hbg-fy.out", "hbq-hbg-fz.out"], 
            # "QBQ-WALL": ["QBQ-WALL-fx.out", "QBQ-WALL-fy.out", "QBQ-WALL-fz.out"],
            # "HBQ-WALL": ["HBQ-WALL-fx.out", "HBQ-WALL-fy.out", "HBQ-WALL-fz.out"]
        }

        # Applying force
        self.analysis_manager.apply_forces(self.analysis, forces_file_path, named_selections_dict, force_files_map)
        
        # rotation_speed
        rotation_speed = data.get("rotation_speed")
        rotation_axis = data.get("rotation_axis", "z")  
        self.analysis_manager.add_Rotationalvlocity(self.analysis, named_selections_dict, rotation_speed, rotation_axis)
        pass
    
    def run_connection_setting(self):
        """
        Example of how to read bearing/bushing data from external JSON(s),
        then create connections in a loop.
        """
        try:
            named_selections_dict = self.selection_mgr.get_named_selections_dict()

            connection_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\connection_config_latest.json"
            bearing_list = self.connection_manager.load_bearing_config(connection_result_path)
            self.connection_manager.create_bearing_in_batch(named_selections_dict, bearing_list)

            bushing_list = self.connection_manager.load_bushing_config(connection_result_path)
            self.connection_manager.create_bushing_joints_in_batch(named_selections_dict, bushing_list)

            self.logger.info("All bearing and bushing connections created successfully.")
        except Exception as e:
            self.logger.error("Error in run_connection_setting: %s", str(e), exc_info=True)
        pass
    
    # 给出监测点
    def run_result_setting(self):
        
        try:
            self.logger.info("Starting run_result_setting...")

            monitor_config_path = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/result_output__20250802_224509/monitor_points_20250802_224509.json"
            target_points = []
            monitor_points_info = []

            if os.path.exists(monitor_config_path):
                try:
                    import codecs
                    with codecs.open(monitor_config_path, "r", "utf-8-sig") as f:
                        data = json.load(f)

                    target_points, monitor_points_info = self._parse_monitor_points_data(data)

                    self.logger.info("Loaded %d monitoring points from %s.", len(target_points), monitor_config_path)
                    if monitor_points_info:
                        self.logger.info("Monitor points source: %s", data.get("monitor_points_source", "unknown"))

                except Exception as e:
                    self.logger.error("Error reading monitor config file %s: %s", monitor_config_path, str(e), exc_info=True)
            else:
                self.logger.warning("Monitor config file not found: %s. Using default target_points.", monitor_config_path)


            if not target_points:
                self.logger.warning("No target_points available. Skipping run_result_setting.")
                return
    
            node_coords = self.geometry_helper.store_node_coordinates(self.analysis)
            if not node_coords:
                self.logger.error("No node coordinates available. Exiting run_result_setting.")
                return
    
            closest_nodes = self.geometry_helper.find_closest_nodes(node_coords, target_points)
    
            solution = self.analysis.Solution
            for i, ((tx, ty, tz), node_id) in enumerate(closest_nodes.iteritems()):
                if node_id is None:
                    self.logger.warning("Skipping point (%s, %s, %s), no closest node found.", tx, ty, tz)
                    continue

                point_info = monitor_points_info[i] if i < len(monitor_points_info) else {}
                point_name = point_info.get("name", "Point_{}".format(i + 1))

                self.logger.info("Processing monitor point '%s' at (%.3f, %.3f, %.3f), closest node: %s.",
                               point_name, tx, ty, tz, node_id)

                selection_p = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.MeshNodes)
                selection_p.Ids = [node_id]
                coord_system = self.Model.CoordinateSystems.AddCoordinateSystem()
                coord_system.OriginLocation = selection_p

                if point_name:
                    coord_system.Name = "CS_{}".format(point_name)

                try:
                    acceleration_probe = solution.AddAccelerationProbe()
                    acceleration_probe.LocationMethod = LocationDefinitionMethod.CoordinateSystem
                    acceleration_probe.Orientation = coord_system
                    acceleration_probe.CoordinateSystemSelection = coord_system
                    velocity_probe = solution.AddVelocityProbe()
                    velocity_probe.LocationMethod = LocationDefinitionMethod.CoordinateSystem
                    velocity_probe.Orientation = coord_system
                    velocity_probe.CoordinateSystemSelection = coord_system
                    if point_name:
                        acceleration_probe.Name = "AccelProbe_{}".format(point_name)
                        velocity_probe.Name = "VelocityProbe_{}".format(point_name)

                    self.logger.info("Acceleration probe and Velocity probe '%s' added for monitor point '%s' at (%.3f, %.3f, %.3f), node=%s.",
                                   acceleration_probe.Name if hasattr(acceleration_probe, 'Name') else "AccelProbe",
                                   point_name, tx, ty, tz, node_id)
                except Exception as e:
                    self.logger.error("Error adding acceleration probe for monitor point '%s' at node %s: %s",
                                    point_name, node_id, str(e), exc_info=True)
    
            self.logger.info("run_result_setting completed successfully.")
        except Exception as e:
            self.logger.error("Error in run_result_setting: %s", str(e), exc_info=True)
        pass
    
    def run(self):
        app.clear_previous_setup()
        app.run_mesh_setting()
        app.run_analysis_setting()
        app.run_constrain_setting()
        app.run_connection_setting()
        app.run_result_setting()
        
        self.logger.info("MainApp run completed.")


app = MainApp(ExtAPI)
ExtAPI.Application.ActiveUnitSystem = MechanicalUnitSystem.StandardNMM
ExtAPI.Application.ActiveAngularVelocityUnit=AngularVelocityUnitType.RPM
app.run()