[{"name": "Reference Model A", "size": 1.5, "frequencies": [38.2, 71.5, 102.8, 139.4, 178.9, 225.6, 278.3, 334.7], "node_count": 12000, "element_count": 9500, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.json", "description": "Reference model from previous study"}, {"name": "Reference Model B", "size": 3.0, "frequencies": [37.8, 70.2, 101.5, 137.8, 176.4, 223.1, 275.8, 331.2], "node_count": 6000, "element_count": 4800, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.json", "description": "Coarser reference model"}, {"name": "Literature Model C", "size": 2.0, "frequencies": [38.7, 71.9, 103.4, 140.2, 179.1, 226.8, 279.5, 335.8], "node_count": 8000, "element_count": 6400, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.json", "description": "Model from literature comparison"}, {"name": "Benchmark Model 1", "size": 1.0, "frequencies": [41.5, 74.8, 107.2, 144.6, 188.3, 237.9, 293.5, 351.2], "node_count": 15000, "element_count": 12000, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.csv", "description": "High-fidelity benchmark model"}, {"name": "Benchmark Model 2", "size": 2.5, "frequencies": [40.8, 73.4, 105.9, 142.7, 185.6, 234.8, 289.7, 347.1], "node_count": 7500, "element_count": 6000, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.csv", "description": "Medium-fidelity benchmark model"}, {"name": "Validation Case A", "size": 0.8, "frequencies": [42.1, 75.6, 108.8, 146.2, 190.7, 240.3, 296.8, 354.9], "node_count": 18000, "element_count": 14500, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.csv", "description": "Validation case from test suite"}, {"name": "Validation Case B", "size": 4.0, "frequencies": [39.9, 72.1, 104.3, 140.8, 183.2, 231.7, 286.4, 343.6], "node_count": 4500, "element_count": 3600, "source": "imported", "import_time": "2025-07-29 10:24:50", "file_path": "sample_modal_data.csv", "description": "Coarse validation case"}]