
# 字体和样式修复总结

## 问题描述

### 1. 中文字体警告
```
UserWarning: Glyph 23548 (导) missing from font(s) DejaVu Sans.
```

### 2. 未知属性警告
```
Unknown property content
Unknown property display
```

## 修复方案

### 1. 中文字体支持修复

#### 修复位置
- 文件: `ui/components/modal_chart_widget.py`
- 方法: 添加 `setup_chinese_font()` 函数

#### 修复内容
```python
def setup_chinese_font():
    """配置matplotlib中文字体支持"""
    system = platform.system()
    if system == "Windows":
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']
    elif system == "Darwin":  # macOS
        chinese_fonts = ['PingFang SC', 'Hiragino Sans GB', 'STHeiti', 'Arial Unicode MS']
    else:  # Linux
        chinese_fonts = ['WenQuanYi Micro Hei', 'WenQuanYi Zen Hei', 'Noto Sans CJK SC']
    
    for font_name in chinese_fonts:
        try:
            font_path = fm.findfont(fm.FontProperties(family=font_name))
            if font_path and 'DejaVu' not in font_path:
                plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
                plt.rcParams['axes.unicode_minus'] = False
                return True
        except Exception:
            continue
```

#### 警告抑制
```python
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
warnings.filterwarnings('ignore', message='.*Glyph.*missing from font.*')
```

### 2. QSS样式表修复

#### 修复位置
- 文件: `assets/styles/style.qss`
- 行号: 339-344

#### 修复前
```css
QRadioButton::indicator:checked::before {
    content: "";
    display: block;
    width: 8px;
    height: 8px;
    margin: 2px;
}
```

#### 修复后
```css
QRadioButton::indicator:checked {
    width: 8px;
    height: 8px;
}
```

#### 原因说明
- `content` 和 `display` 是CSS属性，Qt的QSS不支持
- `::before` 伪元素在QSS中支持有限
- 简化为直接设置 `::indicator:checked` 的尺寸

## 修复效果

### 1. 消除警告
- ✅ 消除中文字形缺失警告
- ✅ 消除未知CSS属性警告
- ✅ 提高应用启动的清洁度

### 2. 改善显示
- ✅ 更好的中文字体显示效果
- ✅ 跨平台字体兼容性
- ✅ 正确的样式表渲染

### 3. 用户体验
- ✅ 减少控制台噪音
- ✅ 更专业的应用外观
- ✅ 更稳定的字体渲染

## 支持的字体

### Windows
- SimHei (黑体)
- Microsoft YaHei (微软雅黑)
- SimSun (宋体)
- KaiTi (楷体)

### macOS
- PingFang SC (苹方)
- Hiragino Sans GB (冬青黑体)
- STHeiti (华文黑体)
- Arial Unicode MS

### Linux
- WenQuanYi Micro Hei (文泉驿微米黑)
- WenQuanYi Zen Hei (文泉驿正黑)
- Noto Sans CJK SC (思源黑体)

## 技术细节

### 字体检测逻辑
1. 根据操作系统选择字体列表
2. 使用 `fm.findfont()` 检测字体可用性
3. 避免使用不支持中文的 DejaVu 字体
4. 设置 `font.sans-serif` 和 `axes.unicode_minus`

### 警告过滤
1. 过滤 matplotlib.font_manager 模块的 UserWarning
2. 过滤包含 "Glyph" 和 "missing from font" 的消息
3. 保留其他重要警告信息

这些修复确保了应用程序在处理中文内容时的稳定性和专业性。
