<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分析设置界面 - 高级分析配置系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-red-600 to-pink-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-red-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    📊 分析设置界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-red-100">
                    高级分析配置系统 | 多物理场耦合与智能求解
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-red-500 bg-opacity-20 text-red-100 text-sm font-semibold px-4 py-2 rounded-full border border-red-400">
                        📊 分析类型 | ⚙️ 求解设置 | 🎯 收敛控制 | 📈 结果输出
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                分析设置界面用于配置有限元分析的各种参数，包括分析类型、求解器设置、收敛控制等。正确的分析设置是获得准确结果的关键。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">分析类型选择</h3>
                    </div>
                    <p class="text-sm text-blue-600">选择静力学、动力学、热分析等分析类型</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">求解器设置</h3>
                    </div>
                    <p class="text-sm text-green-600">配置求解器类型、算法和性能参数</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">收敛控制</h3>
                    </div>
                    <p class="text-sm text-purple-600">设置收敛准则、容差和迭代控制</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">结果输出</h3>
                    </div>
                    <p class="text-sm text-orange-600">控制结果文件格式和输出内容</p>
                </div>
            </div>
        </section>

        <!-- Analysis Types -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">📊 分析类型</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">支持多种分析类型以满足不同的工程需求</p>
            </div>
            
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚖️</span>静力学分析 (Static Analysis)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 分析结构在静态载荷下的响应</li>
                                    <li>• 计算位移、应力和应变</li>
                                    <li>• 支持线性和非线性分析</li>
                                    <li>• 适用于强度和刚度评估</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">应用场景</h4>
                                <p class="text-sm text-blue-600">结构强度分析、变形计算、安全系数评估</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌊</span>模态分析 (Modal Analysis)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 计算结构的固有频率和振型</li>
                                    <li>• 识别结构的动态特性</li>
                                    <li>• 支持预应力模态分析</li>
                                    <li>• 可提取多阶模态</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">应用场景</h4>
                                <p class="text-sm text-green-600">振动分析、共振避免、动态设计优化</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📈</span>谐响应分析 (Harmonic Response)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 分析结构在谐波激励下的响应</li>
                                    <li>• 计算频率响应函数</li>
                                    <li>• 支持复数结果输出</li>
                                    <li>• 可考虑阻尼效应</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">应用场景</h4>
                                <p class="text-sm text-purple-600">振动传递分析、频响特性评估、噪声预测</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⏱️</span>瞬态分析 (Transient Analysis)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 分析结构的时域动态响应</li>
                                    <li>• 支持任意时变载荷</li>
                                    <li>• 可处理非线性问题</li>
                                    <li>• 考虑惯性和阻尼效应</li>
                                </ul>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">应用场景</h4>
                                <p class="text-sm text-orange-600">冲击分析、地震响应、启动过程分析</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌡️</span>热分析 (Thermal Analysis)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 计算温度分布和热流</li>
                                    <li>• 支持稳态和瞬态热分析</li>
                                    <li>• 考虑各种传热方式</li>
                                    <li>• 可与结构分析耦合</li>
                                </ul>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-red-800 mb-2">应用场景</h4>
                                <p class="text-sm text-red-600">热应力分析、散热设计、温度场计算</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Solver Settings -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">⚙️ 求解器设置</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔧 求解器类型</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                            <h4 class="font-semibold text-blue-800 mb-2">直接求解器</h4>
                            <p class="text-sm text-blue-600">适用于中小型问题，精度高但内存需求大</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                            <h4 class="font-semibold text-green-800 mb-2">迭代求解器</h4>
                            <p class="text-sm text-green-600">适用于大型问题，内存效率高</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                            <h4 class="font-semibold text-purple-800 mb-2">多重网格求解器</h4>
                            <p class="text-sm text-purple-600">适用于超大型问题，收敛速度快</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🎯 算法选择</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-green-800 mb-2">线性分析算法</h4>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>• Sparse直接求解器</li>
                                    <li>• PCG迭代求解器</li>
                                    <li>• ICCG预条件共轭梯度</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-blue-800 mb-2">非线性分析算法</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• Newton-Raphson方法</li>
                                    <li>• 修正Newton方法</li>
                                    <li>• 弧长控制方法</li>
                                </ul>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-purple-800 mb-2">特征值求解算法</h4>
                                <ul class="text-sm text-purple-600 space-y-1">
                                    <li>• Block Lanczos方法</li>
                                    <li>• Subspace迭代法</li>
                                    <li>• Unsymmetric方法</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="font-semibold text-orange-800 mb-2">动态分析算法</h4>
                                <ul class="text-sm text-orange-600 space-y-1">
                                    <li>• Newmark积分方法</li>
                                    <li>• HHT-α方法</li>
                                    <li>• 模态叠加法</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Convergence Control -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 收敛控制</h2>

            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📊 收敛准则设置</h3>
                <p class="text-gray-600 mb-4">收敛控制是确保分析结果准确性的关键因素：</p>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                            <h4 class="font-semibold text-purple-800 mb-2">力收敛准则</h4>
                            <p class="text-sm text-purple-600 mb-2">控制残余力的收敛容差</p>
                            <div class="text-xs text-gray-500">
                                <p>• 默认值：1e-3</p>
                                <p>• 范围：1e-6 到 1e-1</p>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                            <h4 class="font-semibold text-blue-800 mb-2">位移收敛准则</h4>
                            <p class="text-sm text-blue-600 mb-2">控制位移增量的收敛容差</p>
                            <div class="text-xs text-gray-500">
                                <p>• 默认值：5e-3</p>
                                <p>• 范围：1e-6 到 1e-1</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                            <h4 class="font-semibold text-green-800 mb-2">最大迭代次数</h4>
                            <p class="text-sm text-green-600 mb-2">限制每个载荷步的最大迭代次数</p>
                            <div class="text-xs text-gray-500">
                                <p>• 默认值：25</p>
                                <p>• 范围：5 到 1000</p>
                            </div>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                            <h4 class="font-semibold text-orange-800 mb-2">线搜索控制</h4>
                            <p class="text-sm text-orange-600 mb-2">优化Newton-Raphson迭代的步长</p>
                            <div class="text-xs text-gray-500">
                                <p>• 自适应线搜索</p>
                                <p>• 提高收敛稳定性</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Operation Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📋 操作指南</h2>

            <div class="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🔄 分析设置流程</h3>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📊 选择分析类型</h4>
                            <p class="text-gray-600 text-sm">根据工程需求选择合适的分析类型：静力学、模态、谐响应、瞬态或热分析。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">⚙️ 配置求解器</h4>
                            <p class="text-gray-600 text-sm">选择合适的求解器类型和算法，考虑问题规模和计算资源。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🎯 设置收敛控制</h4>
                            <p class="text-gray-600 text-sm">配置收敛准则、容差和迭代控制参数，确保分析的准确性和稳定性。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📈 配置输出选项</h4>
                            <p class="text-gray-600 text-sm">设置结果文件格式、输出内容和后处理选项。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">5</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🚀 启动分析</h4>
                            <p class="text-gray-600 text-sm">检查所有设置后启动分析计算，监控求解进度和收敛状态。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Settings -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔬 高级设置</h2>

            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">⏱️ 时间步长控制</h3>
                    <ul class="space-y-2 text-sm text-blue-600">
                        <li>• 自适应时间步长</li>
                        <li>• 最小/最大步长限制</li>
                        <li>• 步长缩放因子</li>
                        <li>• 时间积分精度控制</li>
                    </ul>
                </div>

                <div class="bg-green-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">🔄 载荷步控制</h3>
                    <ul class="space-y-2 text-sm text-green-600">
                        <li>• 载荷步数设置</li>
                        <li>• 载荷增量控制</li>
                        <li>• 自动载荷步细分</li>
                        <li>• 载荷步重启功能</li>
                    </ul>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 mb-4">💾 内存管理</h3>
                    <ul class="space-y-2 text-sm text-purple-600">
                        <li>• 内存使用优化</li>
                        <li>• 磁盘缓存设置</li>
                        <li>• 并行计算配置</li>
                        <li>• 内存监控和报告</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Output Configuration -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📈 结果输出配置</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📁 文件格式选择</h3>
                    <div class="grid md:grid-cols-4 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-orange-800 mb-1">RST格式</h4>
                            <p class="text-xs text-orange-600">ANSYS标准结果文件</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-green-800 mb-1">VTK格式</h4>
                            <p class="text-xs text-green-600">通用可视化格式</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-blue-800 mb-1">CSV格式</h4>
                            <p class="text-xs text-blue-600">表格数据导出</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-purple-800 mb-1">HDF5格式</h4>
                            <p class="text-xs text-purple-600">大数据存储格式</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📊 输出内容控制</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-indigo-400">
                                <h4 class="font-semibold text-indigo-800 mb-2">节点结果</h4>
                                <ul class="text-sm text-indigo-600 space-y-1">
                                    <li>• 位移和旋转</li>
                                    <li>• 速度和加速度</li>
                                    <li>• 反力和反力矩</li>
                                    <li>• 温度和热流</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                                <h4 class="font-semibold text-blue-800 mb-2">单元结果</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 应力和应变</li>
                                    <li>• 单元力和力矩</li>
                                    <li>• 能量密度</li>
                                    <li>• 失效指标</li>
                                </ul>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">模态结果</h4>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>• 固有频率</li>
                                    <li>• 振型向量</li>
                                    <li>• 模态参与因子</li>
                                    <li>• 有效质量</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                                <h4 class="font-semibold text-purple-800 mb-2">频响结果</h4>
                                <ul class="text-sm text-purple-600 space-y-1">
                                    <li>• 幅值和相位</li>
                                    <li>• 传递函数</li>
                                    <li>• 功率谱密度</li>
                                    <li>• 相干函数</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📊</span>如何选择合适的分析类型？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">分析类型的选择应基于工程问题的性质和分析目标。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">选择指南：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• <strong>静力学分析</strong> - 用于强度和刚度评估</li>
                                <li>• <strong>模态分析</strong> - 用于振动特性分析</li>
                                <li>• <strong>谐响应分析</strong> - 用于频域振动分析</li>
                                <li>• <strong>瞬态分析</strong> - 用于时域动态分析</li>
                                <li>• <strong>热分析</strong> - 用于温度场和热应力分析</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚙️</span>求解器选择有什么影响？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">求解器的选择直接影响计算效率和内存使用。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">选择建议：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 小型问题（<10万自由度）：使用直接求解器</li>
                                <li>• 中型问题（10-100万自由度）：使用迭代求解器</li>
                                <li>• 大型问题（>100万自由度）：使用多重网格求解器</li>
                                <li>• 考虑计算机内存和处理器核数</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🎯</span>收敛问题如何解决？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">收敛问题通常由模型设置或参数选择不当引起。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">解决方法：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 检查模型的约束和载荷设置</li>
                                <li>• 调整收敛容差和迭代次数</li>
                                <li>• 使用载荷步细分或弧长控制</li>
                                <li>• 改善网格质量和密度</li>
                                <li>• 检查材料属性的合理性</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⏱️</span>如何优化计算效率？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">计算效率优化可以显著减少分析时间。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">优化策略：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 选择合适的求解器和算法</li>
                                <li>• 优化网格质量，避免过度细化</li>
                                <li>• 使用对称性和周期性简化模型</li>
                                <li>• 启用并行计算功能</li>
                                <li>• 合理设置内存和磁盘缓存</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 收敛容差设置过松可能影响结果精度</li>
                    <li>• 收敛容差设置过严可能导致不收敛</li>
                    <li>• 大型问题需要充足的内存和计算时间</li>
                    <li>• 非线性分析需要合理的载荷步设置</li>
                    <li>• 动态分析的时间步长影响结果精度</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 从简单的线性分析开始验证模型</li>
                    <li>• 使用默认设置作为初始配置</li>
                    <li>• 监控求解过程中的收敛历史</li>
                    <li>• 保存成功的分析配置作为模板</li>
                    <li>• 定期备份重要的分析结果</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">高级分析配置系统 - 多物理场耦合与智能求解</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
