"""
解耦窗口管理器实现

此模块实现了基于事件总线的解耦窗口管理器，减少模块间的直接依赖。
通过事件驱动的架构，实现了高内聚低耦合的设计。

主要特性：
1. 事件驱动的窗口切换
2. 懒加载和缓存机制
3. 性能优化的窗口状态管理
4. 可插拔的过渡效果系统

作者: [作者名]
日期: [日期]
"""

import logging
import time
from typing import Dict, Optional, Callable, Any
from collections import OrderedDict
from PySide6.QtWidgets import QMainWindow
from PySide6.QtCore import QTimer, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QCloseEvent

from core.event_bus import (
    get_event_bus, Event, EventPriority, 
    WindowSwitchEvent, WindowRegistrationEvent
)
from core.window_manager_interface import (
    WindowManagerInterface, WindowType, WindowFactory, 
    WindowStateManager, WindowTransitionEffect, WindowValidator,
    WindowManagerConfig, SimpleTransitionEffect
)

logger = logging.getLogger(__name__)


class MemoryWindowStateManager(WindowStateManager):
    """内存窗口状态管理器"""
    
    def __init__(self, max_states: int = 50):
        self._states: Dict[WindowType, Dict[str, Any]] = {}
        self._max_states = max_states
    
    def save_state(self, window_type: WindowType, window: QMainWindow) -> None:
        """保存窗口状态"""
        try:
            state = {
                'geometry': window.geometry(),
                'window_state': window.windowState(),
                'opacity': window.windowOpacity(),
                'timestamp': time.time()
            }
            self._states[window_type] = state
            logger.debug(f"Saved state for window: {window_type}")
        except Exception as e:
            logger.error(f"Failed to save state for {window_type}: {e}")
    
    def restore_state(self, window_type: WindowType, window: QMainWindow) -> None:
        """恢复窗口状态"""
        try:
            if window_type in self._states:
                state = self._states[window_type]
                window.setGeometry(state['geometry'])
                window.setWindowState(state['window_state'])
                window.setWindowOpacity(state['opacity'])
                logger.debug(f"Restored state for window: {window_type}")
        except Exception as e:
            logger.error(f"Failed to restore state for {window_type}: {e}")
    
    def clear_state(self, window_type: WindowType) -> None:
        """清除窗口状态"""
        if window_type in self._states:
            del self._states[window_type]
            logger.debug(f"Cleared state for window: {window_type}")


class WindowCache:
    """窗口缓存管理器"""
    
    def __init__(self, max_size: int = 10):
        self._cache: OrderedDict[WindowType, QMainWindow] = OrderedDict()
        self._factories: Dict[WindowType, WindowFactory] = {}
        self._max_size = max_size
        self._access_count: Dict[WindowType, int] = {}
    
    def register_factory(self, window_type: WindowType, factory: WindowFactory) -> None:
        """注册窗口工厂"""
        self._factories[window_type] = factory
        logger.debug(f"Registered factory for window: {window_type}")
    
    def get_window(self, window_type: WindowType, **kwargs) -> Optional[QMainWindow]:
        """获取窗口实例（懒加载）"""
        # 如果缓存中存在，直接返回
        if window_type in self._cache:
            self._access_count[window_type] = self._access_count.get(window_type, 0) + 1
            # 移到最后（LRU策略）
            self._cache.move_to_end(window_type)
            return self._cache[window_type]
        
        # 如果有工厂，创建新实例
        if window_type in self._factories:
            try:
                window = self._factories[window_type].create_window(window_type, **kwargs)
                self.cache_window(window_type, window)
                return window
            except Exception as e:
                logger.error(f"Failed to create window {window_type}: {e}")
                return None
        
        return None
    
    def cache_window(self, window_type: WindowType, window: QMainWindow) -> None:
        """缓存窗口实例"""
        # 如果缓存已满，移除最少使用的窗口
        if len(self._cache) >= self._max_size:
            # 找到访问次数最少的窗口
            lru_window_type = min(self._access_count.keys(), 
                                key=lambda k: self._access_count[k])
            self.remove_window(lru_window_type)
        
        self._cache[window_type] = window
        self._access_count[window_type] = 1
        logger.debug(f"Cached window: {window_type}")
    
    def remove_window(self, window_type: WindowType) -> None:
        """从缓存中移除窗口"""
        if window_type in self._cache:
            window = self._cache.pop(window_type)
            self._access_count.pop(window_type, None)
            # 清理窗口资源
            try:
                window.close()
                window.deleteLater()
            except Exception as e:
                logger.warning(f"Error cleaning up window {window_type}: {e}")
            logger.debug(f"Removed window from cache: {window_type}")
    
    def clear_cache(self) -> None:
        """清空缓存"""
        for window_type in list(self._cache.keys()):
            self.remove_window(window_type)
        logger.info("Window cache cleared")


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self._switch_times: Dict[str, float] = {}
        self._switch_count: Dict[str, int] = {}
        self._total_switches = 0
    
    def record_switch(self, from_window: Optional[WindowType], 
                     to_window: WindowType, duration: float) -> None:
        """记录窗口切换性能"""
        switch_key = f"{from_window}->{to_window}"
        self._switch_times[switch_key] = duration
        self._switch_count[switch_key] = self._switch_count.get(switch_key, 0) + 1
        self._total_switches += 1
    
    def get_average_switch_time(self) -> float:
        """获取平均切换时间"""
        if not self._switch_times:
            return 0.0
        return sum(self._switch_times.values()) / len(self._switch_times)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'total_switches': self._total_switches,
            'average_switch_time': self.get_average_switch_time(),
            'switch_times': self._switch_times.copy(),
            'switch_counts': self._switch_count.copy()
        }


class DecoupledWindowManager(WindowManagerInterface):
    """解耦窗口管理器实现"""
    
    def __init__(self, config: Optional[WindowManagerConfig] = None):
        self._config = config or WindowManagerConfig()
        self._current_window: Optional[WindowType] = None
        self._event_bus = get_event_bus()
        
        # 组件初始化
        self._window_cache = WindowCache(max_size=self._config.max_cached_windows)
        self._state_manager = MemoryWindowStateManager()
        self._transition_effect = SimpleTransitionEffect()
        self._performance_monitor = PerformanceMonitor()
        
        # 直接注册的窗口（向后兼容）
        self._registered_windows: Dict[WindowType, QMainWindow] = {}
        
        # 注册事件处理器
        self._event_bus.subscribe("window_switch_request", self._handle_switch_request)
        
        logger.info("DecoupledWindowManager initialized")
    
    def register_window_factory(self, window_type: WindowType, factory: WindowFactory) -> None:
        """注册窗口工厂"""
        if not WindowValidator.validate_window_type(window_type):
            raise ValueError(f"Invalid window type: {window_type}")
        
        self._window_cache.register_factory(window_type, factory)
        
        # 发布注册事件
        event = WindowRegistrationEvent(
            window_type=window_type.name,
            data={'factory_registered': True}
        )
        self._event_bus.publish(event)
    
    def register_window(self, window_type: WindowType, window: QMainWindow) -> None:
        """注册窗口实例"""
        if not WindowValidator.validate_window_type(window_type):
            raise ValueError(f"Invalid window type: {window_type}")
        
        if not WindowValidator.validate_window_instance(window):
            raise ValueError(f"Invalid window instance: {window}")
        
        # 缓存窗口
        self._window_cache.cache_window(window_type, window)
        # 同时保存到直接注册字典（向后兼容）
        self._registered_windows[window_type] = window
        
        # 发布注册事件
        event = WindowRegistrationEvent(
            window_type=window_type.name,
            window_instance=window,
            data={'direct_registered': True}
        )
        self._event_bus.publish(event)
        
        logger.info(f"Registered window: {window_type}")
    
    def get_window(self, window_type: WindowType) -> Optional[QMainWindow]:
        """获取窗口实例"""
        if not WindowValidator.validate_window_type(window_type):
            return None
        
        # 优先从缓存获取
        window = self._window_cache.get_window(window_type)
        if window:
            return window
        
        # 回退到直接注册的窗口
        return self._registered_windows.get(window_type)
    
    def switch_to(self, window_type: WindowType, **kwargs) -> None:
        """切换到指定窗口"""
        start_time = time.time()
        
        try:
            # 验证切换条件
            is_valid, error_msg = WindowValidator.validate_switch_conditions(
                self._current_window, window_type
            )
            if not is_valid:
                raise ValueError(error_msg)
            
            # 获取目标窗口
            target_window = self.get_window(window_type)
            if not target_window:
                raise ValueError(f"Window type {window_type} not registered")
            
            # 隐藏当前窗口
            current_window = None
            if self._current_window:
                current_window = self.get_window(self._current_window)
                if current_window:
                    # 保存当前窗口状态
                    if self._config.enable_state_caching:
                        self._state_manager.save_state(self._current_window, current_window)
                    
                    # 应用隐藏效果
                    if self._config.enable_transition_effects:
                        self._transition_effect.apply_hide_effect(current_window)
                    else:
                        current_window.hide()
            
            # 恢复目标窗口状态
            if self._config.enable_state_caching:
                self._state_manager.restore_state(window_type, target_window)
            
            # 显示目标窗口
            if self._config.enable_transition_effects:
                self._transition_effect.apply_show_effect(target_window)
            else:
                target_window.show()
            
            # 发布窗口切换事件
            event = WindowSwitchEvent(
                from_window=self._current_window.name if self._current_window else None,
                to_window=window_type.name,
                priority=EventPriority.HIGH
            )
            self._event_bus.publish(event)
            
            # 更新当前窗口
            self._current_window = window_type
            
            # 记录性能
            duration = time.time() - start_time
            self._performance_monitor.record_switch(
                self._current_window, window_type, duration
            )
            
            logger.info(f"Switched to window: {window_type} (took {duration:.3f}s)")
            
        except Exception as e:
            logger.error(f"Failed to switch to window {window_type}: {e}")
            raise
    
    def get_current_window_type(self) -> Optional[WindowType]:
        """获取当前窗口类型"""
        return self._current_window
    
    def is_window_registered(self, window_type: WindowType) -> bool:
        """检查窗口是否已注册"""
        return (window_type in self._registered_windows or 
                self._window_cache.get_window(window_type) is not None)
    
    def _handle_switch_request(self, event: Event) -> None:
        """处理窗口切换请求事件"""
        try:
            if hasattr(event, 'to_window'):
                window_type = WindowType[event.to_window.upper()]
                self.switch_to(window_type)
        except Exception as e:
            logger.error(f"Failed to handle switch request: {e}")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self._performance_monitor.get_stats()
    
    def cleanup(self) -> None:
        """清理资源"""
        self._window_cache.clear_cache()
        logger.info("DecoupledWindowManager cleanup completed")
