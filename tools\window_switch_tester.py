"""
窗口切换测试工具

此工具用于测试和验证窗口切换功能的正确性，特别是主界面隐藏问题。

功能：
1. 自动化测试所有窗口切换场景
2. 验证窗口显示/隐藏状态
3. 检测窗口切换问题
4. 生成测试报告

作者: [作者名]
日期: [日期]
"""

import sys
import os
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from window_manager import WindowManager, WindowType


@dataclass
class TestResult:
    """测试结果"""
    test_name: str
    success: bool
    error_message: str = ""
    window_states: Dict[str, bool] = None
    duration: float = 0.0


class WindowSwitchTester:
    """窗口切换测试器"""
    
    def __init__(self, window_manager: WindowManager):
        self.window_manager = window_manager
        self.test_results: List[TestResult] = []
        self.app = QApplication.instance()
    
    def run_all_tests(self) -> List[TestResult]:
        """运行所有测试"""
        print("🧪 开始窗口切换测试...")
        
        # 测试用例列表
        test_cases = [
            ("初始状态验证", self._test_initial_state),
            ("主窗口到网格窗口", lambda: self._test_window_switch(WindowType.MAIN, WindowType.MESH)),
            ("网格窗口到分析窗口", lambda: self._test_window_switch(WindowType.MESH, WindowType.ANALYSIS)),
            ("分析窗口到约束窗口", lambda: self._test_window_switch(WindowType.ANALYSIS, WindowType.CONSTRAIN)),
            ("约束窗口到结果窗口", lambda: self._test_window_switch(WindowType.CONSTRAIN, WindowType.RESULT)),
            ("结果窗口到主窗口", lambda: self._test_window_switch(WindowType.RESULT, WindowType.MAIN)),
            ("主窗口到所有窗口循环测试", self._test_main_to_all_windows),
            ("多次快速切换测试", self._test_rapid_switching),
            ("窗口状态一致性测试", self._test_window_state_consistency),
        ]
        
        for test_name, test_func in test_cases:
            print(f"\n📋 执行测试: {test_name}")
            try:
                start_time = time.time()
                result = test_func()
                duration = time.time() - start_time
                
                if result:
                    result.duration = duration
                    self.test_results.append(result)
                    status = "✅ 通过" if result.success else "❌ 失败"
                    print(f"   {status} ({duration:.3f}s)")
                    if not result.success:
                        print(f"   错误: {result.error_message}")
                else:
                    # 如果测试函数没有返回结果，创建一个失败的结果
                    self.test_results.append(TestResult(
                        test_name=test_name,
                        success=False,
                        error_message="测试函数没有返回结果",
                        duration=time.time() - start_time
                    ))
                    print("   ❌ 失败 - 测试函数没有返回结果")
                    
            except Exception as e:
                self.test_results.append(TestResult(
                    test_name=test_name,
                    success=False,
                    error_message=str(e),
                    duration=time.time() - start_time
                ))
                print(f"   ❌ 异常: {e}")
        
        self._print_test_summary()
        return self.test_results
    
    def _test_initial_state(self) -> TestResult:
        """测试初始状态"""
        try:
            # 检查是否只有主窗口显示
            states = self.window_manager.validate_window_states()
            
            # 应该只有主窗口显示
            visible_windows = [name for name, visible in states.items() if visible]
            
            if len(visible_windows) == 1 and "MAIN" in visible_windows:
                return TestResult(
                    test_name="初始状态验证",
                    success=True,
                    window_states=states
                )
            else:
                return TestResult(
                    test_name="初始状态验证",
                    success=False,
                    error_message=f"期望只有主窗口显示，实际显示: {visible_windows}",
                    window_states=states
                )
        except Exception as e:
            return TestResult(
                test_name="初始状态验证",
                success=False,
                error_message=str(e)
            )
    
    def _test_window_switch(self, from_window: WindowType, to_window: WindowType) -> TestResult:
        """测试窗口切换"""
        try:
            # 确保起始窗口是当前窗口
            self.window_manager.switch_to(from_window)
            time.sleep(0.1)  # 等待切换完成
            
            # 记录切换前状态
            states_before = self.window_manager.validate_window_states()
            
            # 执行切换
            self.window_manager.switch_to(to_window)
            time.sleep(0.1)  # 等待切换完成
            
            # 记录切换后状态
            states_after = self.window_manager.validate_window_states()
            
            # 验证结果
            # 1. 目标窗口应该显示
            if not states_after.get(to_window.name, False):
                return TestResult(
                    test_name=f"{from_window.name} -> {to_window.name}",
                    success=False,
                    error_message=f"目标窗口 {to_window.name} 没有显示",
                    window_states=states_after
                )
            
            # 2. 源窗口应该隐藏（如果不是同一个窗口）
            if from_window != to_window and states_after.get(from_window.name, False):
                return TestResult(
                    test_name=f"{from_window.name} -> {to_window.name}",
                    success=False,
                    error_message=f"源窗口 {from_window.name} 没有隐藏",
                    window_states=states_after
                )
            
            # 3. 只有目标窗口应该显示
            visible_windows = [name for name, visible in states_after.items() if visible]
            if len(visible_windows) != 1 or visible_windows[0] != to_window.name:
                return TestResult(
                    test_name=f"{from_window.name} -> {to_window.name}",
                    success=False,
                    error_message=f"期望只有 {to_window.name} 显示，实际显示: {visible_windows}",
                    window_states=states_after
                )
            
            return TestResult(
                test_name=f"{from_window.name} -> {to_window.name}",
                success=True,
                window_states=states_after
            )
            
        except Exception as e:
            return TestResult(
                test_name=f"{from_window.name} -> {to_window.name}",
                success=False,
                error_message=str(e)
            )
    
    def _test_main_to_all_windows(self) -> TestResult:
        """测试从主窗口到所有其他窗口的切换"""
        try:
            # 确保从主窗口开始
            self.window_manager.switch_to(WindowType.MAIN)
            time.sleep(0.1)
            
            # 测试到每个窗口的切换
            target_windows = [
                WindowType.MESH, WindowType.PRE, WindowType.CONNECTION,
                WindowType.ANALYSIS, WindowType.CONSTRAIN, WindowType.RESULT
            ]
            
            failed_switches = []
            
            for target_window in target_windows:
                try:
                    # 切换到目标窗口
                    self.window_manager.switch_to(target_window)
                    time.sleep(0.1)
                    
                    # 验证状态
                    states = self.window_manager.validate_window_states()
                    visible_windows = [name for name, visible in states.items() if visible]
                    
                    if len(visible_windows) != 1 or visible_windows[0] != target_window.name:
                        failed_switches.append(f"MAIN -> {target_window.name}: 显示状态错误 {visible_windows}")
                    
                    # 切换回主窗口
                    self.window_manager.switch_to(WindowType.MAIN)
                    time.sleep(0.1)
                    
                except Exception as e:
                    failed_switches.append(f"MAIN -> {target_window.name}: 异常 {str(e)}")
            
            if failed_switches:
                return TestResult(
                    test_name="主窗口到所有窗口循环测试",
                    success=False,
                    error_message="; ".join(failed_switches)
                )
            else:
                return TestResult(
                    test_name="主窗口到所有窗口循环测试",
                    success=True
                )
                
        except Exception as e:
            return TestResult(
                test_name="主窗口到所有窗口循环测试",
                success=False,
                error_message=str(e)
            )
    
    def _test_rapid_switching(self) -> TestResult:
        """测试快速切换"""
        try:
            windows = [WindowType.MAIN, WindowType.MESH, WindowType.ANALYSIS, WindowType.MAIN]
            
            for i in range(len(windows) - 1):
                self.window_manager.switch_to(windows[i + 1])
                time.sleep(0.05)  # 快速切换，只等待50ms
            
            # 验证最终状态
            states = self.window_manager.validate_window_states()
            visible_windows = [name for name, visible in states.items() if visible]
            
            if len(visible_windows) == 1 and visible_windows[0] == "MAIN":
                return TestResult(
                    test_name="多次快速切换测试",
                    success=True,
                    window_states=states
                )
            else:
                return TestResult(
                    test_name="多次快速切换测试",
                    success=False,
                    error_message=f"快速切换后状态错误: {visible_windows}",
                    window_states=states
                )
                
        except Exception as e:
            return TestResult(
                test_name="多次快速切换测试",
                success=False,
                error_message=str(e)
            )
    
    def _test_window_state_consistency(self) -> TestResult:
        """测试窗口状态一致性"""
        try:
            # 多次切换并检查状态一致性
            for _ in range(3):
                self.window_manager.switch_to(WindowType.MESH)
                time.sleep(0.1)
                states1 = self.window_manager.validate_window_states()
                
                self.window_manager.switch_to(WindowType.MAIN)
                time.sleep(0.1)
                states2 = self.window_manager.validate_window_states()
                
                # 检查状态是否符合预期
                if not states1.get("MESH", False) or states1.get("MAIN", False):
                    return TestResult(
                        test_name="窗口状态一致性测试",
                        success=False,
                        error_message="网格窗口切换后状态不一致"
                    )
                
                if not states2.get("MAIN", False) or states2.get("MESH", False):
                    return TestResult(
                        test_name="窗口状态一致性测试",
                        success=False,
                        error_message="主窗口切换后状态不一致"
                    )
            
            return TestResult(
                test_name="窗口状态一致性测试",
                success=True
            )
            
        except Exception as e:
            return TestResult(
                test_name="窗口状态一致性测试",
                success=False,
                error_message=str(e)
            )
    
    def _print_test_summary(self) -> None:
        """打印测试摘要"""
        print("\n" + "="*60)
        print("🎯 窗口切换测试摘要")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result.success)
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result.success:
                    print(f"   • {result.test_name}: {result.error_message}")
        else:
            print(f"\n✅ 所有测试都通过了！")


def main():
    """主函数 - 独立运行测试"""
    print("⚠️  注意: 这是一个独立的测试工具")
    print("请在主应用程序中集成此测试器来验证窗口切换功能")
    print("\n使用方法:")
    print("1. 在主应用程序中导入 WindowSwitchTester")
    print("2. 创建实例: tester = WindowSwitchTester(window_manager)")
    print("3. 运行测试: results = tester.run_all_tests()")


if __name__ == "__main__":
    main()
