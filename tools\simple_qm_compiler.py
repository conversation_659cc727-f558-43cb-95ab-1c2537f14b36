#!/usr/bin/env python3
"""
简单的QM文件编译器

当lrelease工具不可用时，使用此脚本将.ts文件转换为.qm文件。
这是一个简化版本，主要用于测试目的。

作者: [作者名]
日期: [日期]
"""

import os
import sys
import xml.etree.ElementTree as ET
import struct
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SimpleQMCompiler:
    """简单的QM文件编译器"""
    
    def __init__(self):
        self.translations = {}
    
    def parse_ts_file(self, ts_file_path: str) -> dict:
        """解析TS文件
        
        Args:
            ts_file_path: TS文件路径
            
        Returns:
            dict: 翻译字典
        """
        try:
            tree = ET.parse(ts_file_path)
            root = tree.getroot()
            
            translations = {}
            
            for context in root.findall('context'):
                context_name = context.find('name').text
                
                for message in context.findall('message'):
                    source_elem = message.find('source')
                    translation_elem = message.find('translation')
                    
                    if source_elem is not None and translation_elem is not None:
                        source_text = source_elem.text or ""
                        translation_text = translation_elem.text or ""
                        
                        # 创建键值对
                        key = f"{context_name}:{source_text}"
                        translations[key] = translation_text
            
            logger.info(f"从 {ts_file_path} 解析了 {len(translations)} 个翻译")
            return translations
            
        except Exception as e:
            logger.error(f"解析TS文件失败 {ts_file_path}: {e}")
            return {}
    
    def create_simple_qm(self, translations: dict, qm_file_path: str) -> bool:
        """创建简单的QM文件
        
        这是一个简化版本，创建一个包含翻译数据的二进制文件。
        虽然不是标准的Qt QM格式，但可以被我们的应用程序读取。
        
        Args:
            translations: 翻译字典
            qm_file_path: QM文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            with open(qm_file_path, 'wb') as f:
                # 写入文件头
                f.write(b'SIMPLE_QM')  # 8字节标识
                f.write(struct.pack('<I', len(translations)))  # 翻译数量
                
                # 写入翻译数据
                for key, value in translations.items():
                    key_bytes = key.encode('utf-8')
                    value_bytes = value.encode('utf-8')
                    
                    # 写入键长度和键
                    f.write(struct.pack('<I', len(key_bytes)))
                    f.write(key_bytes)
                    
                    # 写入值长度和值
                    f.write(struct.pack('<I', len(value_bytes)))
                    f.write(value_bytes)
            
            logger.info(f"成功创建QM文件: {qm_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建QM文件失败 {qm_file_path}: {e}")
            return False
    
    def compile_ts_to_qm(self, ts_file_path: str, qm_file_path: str) -> bool:
        """将TS文件编译为QM文件
        
        Args:
            ts_file_path: TS文件路径
            qm_file_path: QM文件路径
            
        Returns:
            bool: 是否成功
        """
        if not os.path.exists(ts_file_path):
            logger.error(f"TS文件不存在: {ts_file_path}")
            return False
        
        # 解析TS文件
        translations = self.parse_ts_file(ts_file_path)
        if not translations:
            logger.warning(f"没有找到翻译内容: {ts_file_path}")
            return False
        
        # 创建QM文件
        return self.create_simple_qm(translations, qm_file_path)


class SimpleQMReader:
    """简单的QM文件读取器"""
    
    @staticmethod
    def read_qm_file(qm_file_path: str) -> dict:
        """读取QM文件
        
        Args:
            qm_file_path: QM文件路径
            
        Returns:
            dict: 翻译字典
        """
        try:
            if not os.path.exists(qm_file_path):
                return {}
            
            with open(qm_file_path, 'rb') as f:
                # 读取文件头
                header = f.read(8)
                if header != b'SIMPLE_QM':
                    logger.error(f"无效的QM文件格式: {qm_file_path}")
                    return {}
                
                # 读取翻译数量
                count_data = f.read(4)
                if len(count_data) != 4:
                    logger.error(f"QM文件格式错误: {qm_file_path}")
                    return {}
                
                count = struct.unpack('<I', count_data)[0]
                translations = {}
                
                # 读取翻译数据
                for _ in range(count):
                    # 读取键长度和键
                    key_len_data = f.read(4)
                    if len(key_len_data) != 4:
                        break
                    key_len = struct.unpack('<I', key_len_data)[0]
                    key_bytes = f.read(key_len)
                    if len(key_bytes) != key_len:
                        break
                    key = key_bytes.decode('utf-8')
                    
                    # 读取值长度和值
                    value_len_data = f.read(4)
                    if len(value_len_data) != 4:
                        break
                    value_len = struct.unpack('<I', value_len_data)[0]
                    value_bytes = f.read(value_len)
                    if len(value_bytes) != value_len:
                        break
                    value = value_bytes.decode('utf-8')
                    
                    translations[key] = value
                
                logger.info(f"从 {qm_file_path} 读取了 {len(translations)} 个翻译")
                return translations
                
        except Exception as e:
            logger.error(f"读取QM文件失败 {qm_file_path}: {e}")
            return {}


def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("用法: python simple_qm_compiler.py <ts_file> <qm_file>")
        sys.exit(1)
    
    ts_file = sys.argv[1]
    qm_file = sys.argv[2]
    
    compiler = SimpleQMCompiler()
    if compiler.compile_ts_to_qm(ts_file, qm_file):
        print(f"成功编译: {ts_file} -> {qm_file}")
    else:
        print(f"编译失败: {ts_file}")
        sys.exit(1)


if __name__ == '__main__':
    main()
