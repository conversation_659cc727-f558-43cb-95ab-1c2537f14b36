# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'pre.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QDoubleSpinBox, QFormLayout,
    QGroupBox, QHBoxLayout, QHeaderView, QLabel,
    QLineEdit, QMainWindow, QPushButton, QSizePolicy,
    QSpacerItem, QStatusBar, QTabWidget, QTreeWidget,
    QTreeWidgetItem, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1200, 800)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_main = QVBoxLayout(self.centralwidget)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(10, 10, 10, 10)
        self.tabWidget_main = QTabWidget(self.centralwidget)
        self.tabWidget_main.setObjectName(u"tabWidget_main")
        self.tab_geometry = QWidget()
        self.tab_geometry.setObjectName(u"tab_geometry")
        self.verticalLayout_geometry = QVBoxLayout(self.tab_geometry)
        self.verticalLayout_geometry.setObjectName(u"verticalLayout_geometry")
        self.label_geometry_info = QLabel(self.tab_geometry)
        self.label_geometry_info.setObjectName(u"label_geometry_info")
        self.label_geometry_info.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(16)
        self.label_geometry_info.setFont(font)

        self.verticalLayout_geometry.addWidget(self.label_geometry_info)

        self.verticalSpacer_geometry = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_geometry.addItem(self.verticalSpacer_geometry)

        self.tabWidget_main.addTab(self.tab_geometry, "")
        self.tab_material = QWidget()
        self.tab_material.setObjectName(u"tab_material")
        self.horizontalLayout_material = QHBoxLayout(self.tab_material)
        self.horizontalLayout_material.setSpacing(15)
        self.horizontalLayout_material.setObjectName(u"horizontalLayout_material")
        self.widget_material_left = QWidget(self.tab_material)
        self.widget_material_left.setObjectName(u"widget_material_left")
        self.widget_material_left.setMaximumWidth(400)
        self.verticalLayout_material_left = QVBoxLayout(self.widget_material_left)
        self.verticalLayout_material_left.setSpacing(10)
        self.verticalLayout_material_left.setObjectName(u"verticalLayout_material_left")
        self.verticalLayout_material_left.setContentsMargins(0, 0, 0, 0)
        self.lineEdit_material_search = QLineEdit(self.widget_material_left)
        self.lineEdit_material_search.setObjectName(u"lineEdit_material_search")

        self.verticalLayout_material_left.addWidget(self.lineEdit_material_search)

        self.treeWidget_material_library = QTreeWidget(self.widget_material_left)
        self.treeWidget_material_library.setObjectName(u"treeWidget_material_library")
        self.treeWidget_material_library.setHeaderHidden(True)

        self.verticalLayout_material_left.addWidget(self.treeWidget_material_library)

        self.horizontalLayout_material_buttons = QHBoxLayout()
        self.horizontalLayout_material_buttons.setObjectName(u"horizontalLayout_material_buttons")
        self.pushButton_material_new = QPushButton(self.widget_material_left)
        self.pushButton_material_new.setObjectName(u"pushButton_material_new")

        self.horizontalLayout_material_buttons.addWidget(self.pushButton_material_new)

        self.pushButton_material_copy = QPushButton(self.widget_material_left)
        self.pushButton_material_copy.setObjectName(u"pushButton_material_copy")

        self.horizontalLayout_material_buttons.addWidget(self.pushButton_material_copy)

        self.pushButton_material_delete = QPushButton(self.widget_material_left)
        self.pushButton_material_delete.setObjectName(u"pushButton_material_delete")

        self.horizontalLayout_material_buttons.addWidget(self.pushButton_material_delete)


        self.verticalLayout_material_left.addLayout(self.horizontalLayout_material_buttons)


        self.horizontalLayout_material.addWidget(self.widget_material_left)

        self.widget_material_right = QWidget(self.tab_material)
        self.widget_material_right.setObjectName(u"widget_material_right")
        self.verticalLayout_material_right = QVBoxLayout(self.widget_material_right)
        self.verticalLayout_material_right.setSpacing(15)
        self.verticalLayout_material_right.setObjectName(u"verticalLayout_material_right")
        self.verticalLayout_material_right.setContentsMargins(0, 0, 0, 0)
        self.groupBox_material_properties = QGroupBox(self.widget_material_right)
        self.groupBox_material_properties.setObjectName(u"groupBox_material_properties")
        self.formLayout_material_properties = QFormLayout(self.groupBox_material_properties)
        self.formLayout_material_properties.setObjectName(u"formLayout_material_properties")
        self.formLayout_material_properties.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)
        self.label_material_name = QLabel(self.groupBox_material_properties)
        self.label_material_name.setObjectName(u"label_material_name")

        self.formLayout_material_properties.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_material_name)

        self.lineEdit_material_name = QLineEdit(self.groupBox_material_properties)
        self.lineEdit_material_name.setObjectName(u"lineEdit_material_name")

        self.formLayout_material_properties.setWidget(0, QFormLayout.ItemRole.FieldRole, self.lineEdit_material_name)

        self.label_young_modulus = QLabel(self.groupBox_material_properties)
        self.label_young_modulus.setObjectName(u"label_young_modulus")

        self.formLayout_material_properties.setWidget(1, QFormLayout.ItemRole.LabelRole, self.label_young_modulus)

        self.doubleSpinBox_young_modulus = QDoubleSpinBox(self.groupBox_material_properties)
        self.doubleSpinBox_young_modulus.setObjectName(u"doubleSpinBox_young_modulus")
        self.doubleSpinBox_young_modulus.setDecimals(2)
        self.doubleSpinBox_young_modulus.setMaximum(999999.000000000000000)
        self.doubleSpinBox_young_modulus.setValue(200.000000000000000)

        self.formLayout_material_properties.setWidget(1, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_young_modulus)

        self.label_density = QLabel(self.groupBox_material_properties)
        self.label_density.setObjectName(u"label_density")

        self.formLayout_material_properties.setWidget(2, QFormLayout.ItemRole.LabelRole, self.label_density)

        self.doubleSpinBox_density = QDoubleSpinBox(self.groupBox_material_properties)
        self.doubleSpinBox_density.setObjectName(u"doubleSpinBox_density")
        self.doubleSpinBox_density.setDecimals(2)
        self.doubleSpinBox_density.setMaximum(99999.000000000000000)
        self.doubleSpinBox_density.setValue(7850.000000000000000)

        self.formLayout_material_properties.setWidget(2, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_density)

        self.label_poisson_ratio = QLabel(self.groupBox_material_properties)
        self.label_poisson_ratio.setObjectName(u"label_poisson_ratio")

        self.formLayout_material_properties.setWidget(3, QFormLayout.ItemRole.LabelRole, self.label_poisson_ratio)

        self.doubleSpinBox_poisson_ratio = QDoubleSpinBox(self.groupBox_material_properties)
        self.doubleSpinBox_poisson_ratio.setObjectName(u"doubleSpinBox_poisson_ratio")
        self.doubleSpinBox_poisson_ratio.setDecimals(3)
        self.doubleSpinBox_poisson_ratio.setMaximum(0.500000000000000)
        self.doubleSpinBox_poisson_ratio.setSingleStep(0.010000000000000)
        self.doubleSpinBox_poisson_ratio.setValue(0.300000000000000)

        self.formLayout_material_properties.setWidget(3, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_poisson_ratio)


        self.verticalLayout_material_right.addWidget(self.groupBox_material_properties)

        self.groupBox_material_assignment = QGroupBox(self.widget_material_right)
        self.groupBox_material_assignment.setObjectName(u"groupBox_material_assignment")
        self.verticalLayout_assignment = QVBoxLayout(self.groupBox_material_assignment)
        self.verticalLayout_assignment.setObjectName(u"verticalLayout_assignment")
        self.formLayout_assignment = QFormLayout()
        self.formLayout_assignment.setObjectName(u"formLayout_assignment")
        self.label_structure_select = QLabel(self.groupBox_material_assignment)
        self.label_structure_select.setObjectName(u"label_structure_select")

        self.formLayout_assignment.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_structure_select)

        self.comboBox_structure_select = QComboBox(self.groupBox_material_assignment)
        self.comboBox_structure_select.setObjectName(u"comboBox_structure_select")

        self.formLayout_assignment.setWidget(0, QFormLayout.ItemRole.FieldRole, self.comboBox_structure_select)


        self.verticalLayout_assignment.addLayout(self.formLayout_assignment)

        self.horizontalLayout_assignment_buttons = QHBoxLayout()
        self.horizontalLayout_assignment_buttons.setObjectName(u"horizontalLayout_assignment_buttons")
        self.pushButton_assign_material = QPushButton(self.groupBox_material_assignment)
        self.pushButton_assign_material.setObjectName(u"pushButton_assign_material")

        self.horizontalLayout_assignment_buttons.addWidget(self.pushButton_assign_material)

        self.pushButton_apply_all = QPushButton(self.groupBox_material_assignment)
        self.pushButton_apply_all.setObjectName(u"pushButton_apply_all")

        self.horizontalLayout_assignment_buttons.addWidget(self.pushButton_apply_all)


        self.verticalLayout_assignment.addLayout(self.horizontalLayout_assignment_buttons)


        self.verticalLayout_material_right.addWidget(self.groupBox_material_assignment)

        self.verticalSpacer_material_right = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_material_right.addItem(self.verticalSpacer_material_right)


        self.horizontalLayout_material.addWidget(self.widget_material_right)

        self.tabWidget_main.addTab(self.tab_material, "")

        self.verticalLayout_main.addWidget(self.tabWidget_main)

        self.horizontalLayout_navigation = QHBoxLayout()
        self.horizontalLayout_navigation.setSpacing(20)
        self.horizontalLayout_navigation.setObjectName(u"horizontalLayout_navigation")
        self.horizontalSpacer_left = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_left)

        self.push_finish = QPushButton(self.centralwidget)
        self.push_finish.setObjectName(u"push_finish")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.push_finish.sizePolicy().hasHeightForWidth())
        self.push_finish.setSizePolicy(sizePolicy)
        self.push_finish.setMinimumSize(QSize(200, 65))
        self.push_finish.setFont(font)

        self.horizontalLayout_navigation.addWidget(self.push_finish)

        self.push_meshui = QPushButton(self.centralwidget)
        self.push_meshui.setObjectName(u"push_meshui")
        sizePolicy.setHeightForWidth(self.push_meshui.sizePolicy().hasHeightForWidth())
        self.push_meshui.setSizePolicy(sizePolicy)
        self.push_meshui.setMinimumSize(QSize(250, 65))
        self.push_meshui.setFont(font)

        self.horizontalLayout_navigation.addWidget(self.push_meshui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font)

        self.horizontalLayout_navigation.addWidget(self.push_mainui)

        self.horizontalSpacer_right = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_right)


        self.verticalLayout_main.addLayout(self.horizontalLayout_navigation)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        self.tabWidget_main.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u524d\u5904\u7406", None))
        self.label_geometry_info.setText(QCoreApplication.translate("MainWindow", u"\u51e0\u4f55\u5904\u7406\u529f\u80fd\u533a\u57df", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_geometry), QCoreApplication.translate("MainWindow", u"\u51e0\u4f55\u5904\u7406", None))
        self.lineEdit_material_search.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u641c\u7d22\u6750\u6599...", None))
        self.pushButton_material_new.setText(QCoreApplication.translate("MainWindow", u"\u65b0\u5efa", None))
        self.pushButton_material_copy.setText(QCoreApplication.translate("MainWindow", u"\u590d\u5236", None))
        self.pushButton_material_delete.setText(QCoreApplication.translate("MainWindow", u"\u5220\u9664", None))
        self.groupBox_material_properties.setTitle(QCoreApplication.translate("MainWindow", u"\u6750\u6599\u5c5e\u6027", None))
        self.label_material_name.setText(QCoreApplication.translate("MainWindow", u"\u6750\u6599\u540d\u79f0:", None))
        self.label_young_modulus.setText(QCoreApplication.translate("MainWindow", u"\u6768\u6c0f\u6a21\u91cf (GPa):", None))
        self.doubleSpinBox_young_modulus.setSuffix(QCoreApplication.translate("MainWindow", u" GPa", None))
        self.label_density.setText(QCoreApplication.translate("MainWindow", u"\u5bc6\u5ea6 (kg/m\u00b3):", None))
        self.doubleSpinBox_density.setSuffix(QCoreApplication.translate("MainWindow", u" kg/m\u00b3", None))
        self.label_poisson_ratio.setText(QCoreApplication.translate("MainWindow", u"\u6cca\u677e\u6bd4:", None))
        self.groupBox_material_assignment.setTitle(QCoreApplication.translate("MainWindow", u"\u7ed3\u6784\u4f53\u5206\u914d", None))
        self.label_structure_select.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u7ed3\u6784\u4f53:", None))
        self.pushButton_assign_material.setText(QCoreApplication.translate("MainWindow", u"\u5206\u914d\u6750\u6599", None))
        self.pushButton_apply_all.setText(QCoreApplication.translate("MainWindow", u"\u5e94\u7528\u5230\u5168\u90e8", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_material), QCoreApplication.translate("MainWindow", u"\u6750\u6599\u7ba1\u7406", None))
        self.push_finish.setText(QCoreApplication.translate("MainWindow", u"\u5b8c\u6210\u524d\u5904\u7406", None))
        self.push_meshui.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u8fde\u63a5\u8bbe\u7f6e)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

