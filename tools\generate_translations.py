#!/usr/bin/env python3
"""
翻译文件生成工具

此脚本用于：
1. 扫描源代码中的可翻译字符串
2. 生成 .ts 翻译文件
3. 编译 .qm 翻译文件
4. 管理翻译文件的更新

使用方法:
    python tools/generate_translations.py --scan     # 扫描并生成.ts文件
    python tools/generate_translations.py --compile  # 编译.qm文件
    python tools/generate_translations.py --all      # 执行所有操作

作者: [作者名]
日期: [日期]
"""

import os
import sys
import subprocess
import argparse
import logging
from pathlib import Path
from typing import List, Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TranslationGenerator:
    """翻译文件生成器"""
    
    def __init__(self):
        self.project_root = project_root
        self.translations_dir = self.project_root / 'translations'
        self.source_dirs = [
            self.project_root / 'views',
            self.project_root / 'ctrl',
            self.project_root / 'core',
            self.project_root / 'ui',
            self.project_root,  # 根目录的.py文件
        ]
        
        # 支持的语言
        self.languages = ['zh_CN', 'en_US', 'ja_JP']
        
        # 确保翻译目录存在
        self.translations_dir.mkdir(exist_ok=True)
        
        logger.info(f"翻译生成器初始化完成，项目根目录: {self.project_root}")
    
    def find_python_files(self) -> List[Path]:
        """查找所有Python源文件
        
        Returns:
            List[Path]: Python文件路径列表
        """
        python_files = []
        
        for source_dir in self.source_dirs:
            if source_dir.is_dir():
                # 递归查找.py文件
                python_files.extend(source_dir.rglob('*.py'))
            elif source_dir.is_file() and source_dir.suffix == '.py':
                python_files.append(source_dir)
        
        # 过滤掉一些不需要的文件
        excluded_patterns = ['__pycache__', '.pyc', 'test_', 'tests/', 'build/', 'dist/']
        filtered_files = []
        
        for file_path in python_files:
            if not any(pattern in str(file_path) for pattern in excluded_patterns):
                filtered_files.append(file_path)
        
        logger.info(f"找到 {len(filtered_files)} 个Python文件")
        return filtered_files
    
    def find_ui_files(self) -> List[Path]:
        """查找所有UI文件
        
        Returns:
            List[Path]: UI文件路径列表
        """
        ui_files = []
        ui_dir = self.project_root / 'ui'
        
        if ui_dir.is_dir():
            ui_files.extend(ui_dir.glob('*.ui'))
        
        logger.info(f"找到 {len(ui_files)} 个UI文件")
        return ui_files
    
    def generate_ts_files(self) -> bool:
        """生成.ts翻译文件
        
        Returns:
            bool: 是否成功
        """
        try:
            # 查找源文件
            python_files = self.find_python_files()
            ui_files = self.find_ui_files()
            
            if not python_files and not ui_files:
                logger.warning("没有找到源文件")
                return False
            
            # 为每种语言生成.ts文件
            for language in self.languages:
                ts_file = self.translations_dir / f'app_{language}.ts'
                
                # 构建pylupdate命令
                cmd = ['pylupdate6']
                
                # 添加Python文件
                for py_file in python_files:
                    cmd.append(str(py_file))
                
                # 添加UI文件
                for ui_file in ui_files:
                    cmd.append(str(ui_file))
                
                # 指定输出文件
                cmd.extend(['-ts', str(ts_file)])
                
                logger.info(f"生成 {language} 翻译文件: {ts_file}")
                logger.debug(f"执行命令: {' '.join(cmd)}")
                
                # 执行命令
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"成功生成 {ts_file}")
                else:
                    logger.error(f"生成 {ts_file} 失败:")
                    logger.error(f"stdout: {result.stdout}")
                    logger.error(f"stderr: {result.stderr}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"生成.ts文件时发生错误: {e}")
            return False
    
    def compile_qm_files(self) -> bool:
        """编译.qm翻译文件
        
        Returns:
            bool: 是否成功
        """
        try:
            success_count = 0
            
            for language in self.languages:
                ts_file = self.translations_dir / f'app_{language}.ts'
                qm_file = self.translations_dir / f'app_{language}.qm'
                
                if not ts_file.exists():
                    logger.warning(f"翻译源文件不存在: {ts_file}")
                    continue
                
                # 构建lrelease命令
                cmd = ['lrelease', str(ts_file), '-qm', str(qm_file)]
                
                logger.info(f"编译 {language} 翻译文件: {qm_file}")
                logger.debug(f"执行命令: {' '.join(cmd)}")
                
                # 执行命令
                result = subprocess.run(cmd, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"成功编译 {qm_file}")
                    success_count += 1
                else:
                    logger.error(f"编译 {qm_file} 失败:")
                    logger.error(f"stdout: {result.stdout}")
                    logger.error(f"stderr: {result.stderr}")
            
            return success_count > 0
            
        except Exception as e:
            logger.error(f"编译.qm文件时发生错误: {e}")
            return False
    
    def create_sample_translations(self) -> None:
        """创建示例翻译内容"""
        try:
            # 创建示例翻译文件
            sample_translations = {
                'zh_CN': {
                    'Application': '应用程序',
                    'File': '文件',
                    'Edit': '编辑',
                    'View': '视图',
                    'Help': '帮助',
                    'Settings': '设置',
                    'Language': '语言',
                    'Exit': '退出',
                    'About': '关于',
                    'Error': '错误',
                    'Warning': '警告',
                    'Information': '信息',
                    'OK': '确定',
                    'Cancel': '取消',
                    'Yes': '是',
                    'No': '否',
                    'Save': '保存',
                    'Open': '打开',
                    'Close': '关闭'
                },
                'en_US': {
                    'Application': 'Application',
                    'File': 'File',
                    'Edit': 'Edit',
                    'View': 'View',
                    'Help': 'Help',
                    'Settings': 'Settings',
                    'Language': 'Language',
                    'Exit': 'Exit',
                    'About': 'About',
                    'Error': 'Error',
                    'Warning': 'Warning',
                    'Information': 'Information',
                    'OK': 'OK',
                    'Cancel': 'Cancel',
                    'Yes': 'Yes',
                    'No': 'No',
                    'Save': 'Save',
                    'Open': 'Open',
                    'Close': 'Close'
                },
                'ja_JP': {
                    'Application': 'アプリケーション',
                    'File': 'ファイル',
                    'Edit': '編集',
                    'View': '表示',
                    'Help': 'ヘルプ',
                    'Settings': '設定',
                    'Language': '言語',
                    'Exit': '終了',
                    'About': 'について',
                    'Error': 'エラー',
                    'Warning': '警告',
                    'Information': '情報',
                    'OK': 'OK',
                    'Cancel': 'キャンセル',
                    'Yes': 'はい',
                    'No': 'いいえ',
                    'Save': '保存',
                    'Open': '開く',
                    'Close': '閉じる'
                }
            }
            
            # 创建README文件
            readme_content = """# 翻译文件说明

此目录包含应用程序的翻译文件。

## 文件类型

- `.ts` 文件：翻译源文件，包含原始文本和翻译文本
- `.qm` 文件：编译后的翻译文件，应用程序运行时使用

## 支持的语言

- `zh_CN`：简体中文
- `en_US`：英语（美国）
- `ja_JP`：日语

## 更新翻译

1. 运行 `python tools/generate_translations.py --scan` 扫描源代码并更新.ts文件
2. 使用Qt Linguist编辑.ts文件中的翻译
3. 运行 `python tools/generate_translations.py --compile` 编译.qm文件

## 注意事项

- 请不要直接编辑.qm文件
- 添加新的可翻译字符串后，需要重新扫描源代码
- 翻译完成后，需要重新编译.qm文件
"""
            
            readme_file = self.translations_dir / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            logger.info("示例翻译内容和说明文件已创建")
            
        except Exception as e:
            logger.error(f"创建示例翻译内容时发生错误: {e}")
    
    def check_tools(self) -> bool:
        """检查必要的工具是否可用
        
        Returns:
            bool: 工具是否可用
        """
        tools = ['pylupdate6', 'lrelease']
        missing_tools = []
        
        for tool in tools:
            try:
                result = subprocess.run([tool, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"{tool} 可用")
                else:
                    missing_tools.append(tool)
            except FileNotFoundError:
                missing_tools.append(tool)
        
        if missing_tools:
            logger.error(f"缺少必要工具: {', '.join(missing_tools)}")
            logger.error("请确保已安装Qt开发工具")
            return False
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='翻译文件生成工具')
    parser.add_argument('--scan', action='store_true', help='扫描源代码并生成.ts文件')
    parser.add_argument('--compile', action='store_true', help='编译.qm文件')
    parser.add_argument('--all', action='store_true', help='执行所有操作')
    parser.add_argument('--check', action='store_true', help='检查工具可用性')
    parser.add_argument('--sample', action='store_true', help='创建示例翻译内容')
    
    args = parser.parse_args()
    
    generator = TranslationGenerator()
    
    if args.check or args.all:
        if not generator.check_tools():
            sys.exit(1)
    
    if args.sample:
        generator.create_sample_translations()
    
    if args.scan or args.all:
        logger.info("开始扫描源代码...")
        if not generator.generate_ts_files():
            logger.error("生成.ts文件失败")
            sys.exit(1)
    
    if args.compile or args.all:
        logger.info("开始编译翻译文件...")
        if not generator.compile_qm_files():
            logger.error("编译.qm文件失败")
            sys.exit(1)
    
    if not any([args.scan, args.compile, args.all, args.check, args.sample]):
        parser.print_help()
    
    logger.info("翻译文件生成完成")


if __name__ == '__main__':
    main()
