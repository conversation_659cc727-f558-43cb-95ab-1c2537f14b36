"""
启动画面主题选择器

此工具允许用户预览和测试不同的美化主题，包括：
1. 加载预设主题配置
2. 实时预览主题效果
3. 导出自定义主题配置
4. 主题对比功能

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import json
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def load_themes():
    """加载主题配置"""
    try:
        with open('config/beautiful_themes.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ 主题配置文件不存在: config/beautiful_themes.json")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ 主题配置文件格式错误: {e}")
        return None

def display_theme_menu(themes_data):
    """显示主题菜单"""
    print("\n🎨 可用的美化主题：")
    print("=" * 50)
    
    themes = themes_data['themes']
    theme_list = list(themes.keys())
    
    for i, (theme_key, theme_info) in enumerate(themes.items(), 1):
        print(f"{i}. {theme_info['name']}")
        print(f"   {theme_info['description']}")
        print()
    
    print(f"{len(theme_list) + 1}. 退出")
    print("=" * 50)
    
    return theme_list

def preview_theme(theme_config, theme_name):
    """预览主题效果"""
    print(f"\n🎨 正在预览主题: {theme_name}")
    
    try:
        from PySide6.QtWidgets import QApplication
        from core.splash_screen_fixed import SplashScreenManager
        
        # 创建或获取应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建启动画面管理器
        splash_manager = SplashScreenManager(theme_config)
        splash = splash_manager.show_splash()
        
        print(f"✅ {theme_name} 主题启动画面显示成功")
        
        # 模拟进度更新
        steps = [
            (20, f"正在加载 {theme_name} 主题..."),
            (40, "正在应用视觉效果..."),
            (60, "正在优化渐变背景..."),
            (80, "正在完善动画效果..."),
            (100, f"{theme_name} 主题加载完成！")
        ]
        
        for progress, status in steps:
            splash_manager.update_progress_by_percentage(progress, status)
            print(f"  进度: {progress}% - {status}")
            time.sleep(1)
            app.processEvents()
        
        time.sleep(2)
        splash_manager.hide_splash()
        print(f"✅ {theme_name} 主题预览完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 主题预览失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_themes(themes_data):
    """对比多个主题"""
    print("\n🔍 主题对比模式")
    print("请选择要对比的主题（输入主题编号，用空格分隔）：")
    
    themes = themes_data['themes']
    theme_list = list(themes.keys())
    
    # 显示主题列表
    for i, (theme_key, theme_info) in enumerate(themes.items(), 1):
        print(f"{i}. {theme_info['name']}")
    
    try:
        selection = input("\n请输入要对比的主题编号（例如：1 3 5）: ").strip()
        if not selection:
            return
        
        selected_indices = [int(x) - 1 for x in selection.split()]
        selected_themes = [(theme_list[i], themes[theme_list[i]]) for i in selected_indices 
                          if 0 <= i < len(theme_list)]
        
        if not selected_themes:
            print("❌ 没有选择有效的主题")
            return
        
        print(f"\n🔍 开始对比 {len(selected_themes)} 个主题...")
        
        for theme_key, theme_info in selected_themes:
            print(f"\n--- {theme_info['name']} ---")
            preview_theme(theme_info['config'], theme_info['name'])
            
            input("按回车键继续下一个主题...")
        
        print("\n✅ 主题对比完成")
        
    except ValueError:
        print("❌ 输入格式错误，请输入数字")
    except Exception as e:
        print(f"❌ 对比过程中发生错误: {e}")

def export_theme_config(theme_config, theme_name):
    """导出主题配置"""
    try:
        filename = f"custom_theme_{theme_name.lower().replace(' ', '_')}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(theme_config, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 主题配置已导出到: {filename}")
        return True
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False

def create_custom_theme():
    """创建自定义主题"""
    print("\n🎨 创建自定义主题")
    print("基于现有主题进行自定义...")
    
    # 这里可以添加交互式主题创建功能
    # 为了简化，暂时提供一个示例
    custom_config = {
        "enabled": True,
        "show_fade_in": True,
        "fade_in_duration": 600,
        "fade_out_duration": 400,
        "minimum_display_time": 2000,
        "show_rotation_animation": True,
        "colors": {
            "primary": "#ff6b6b",    # 自定义红色
            "secondary": "#4ecdc4",  # 自定义青色
            "text": "#ffffff",
            "progress_background": "#34495e"
        },
        "fonts": {
            "title_size": 18,
            "version_size": 12,
            "status_size": 10,
            "font_family": "Microsoft YaHei"
        },
        "layout": {
            "width": 500,
            "height": 350
        }
    }
    
    print("✅ 自定义主题配置已创建")
    preview_theme(custom_config, "自定义主题")
    
    save = input("\n是否保存此自定义主题？(y/n): ").lower().strip()
    if save == 'y':
        export_theme_config(custom_config, "custom")

def main():
    """主函数"""
    print("=" * 60)
    print("🎨 启动画面主题选择器")
    print("=" * 60)
    print("欢迎使用美化启动画面主题选择器！")
    print("您可以预览、对比和自定义不同的主题效果。")
    
    # 加载主题配置
    themes_data = load_themes()
    if not themes_data:
        return
    
    while True:
        try:
            # 显示主菜单
            print("\n📋 主菜单：")
            print("1. 预览单个主题")
            print("2. 对比多个主题")
            print("3. 创建自定义主题")
            print("4. 查看主题特性说明")
            print("5. 退出")
            
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                # 预览单个主题
                theme_list = display_theme_menu(themes_data)
                
                try:
                    selection = int(input(f"\n请选择主题 (1-{len(theme_list) + 1}): "))
                    
                    if selection == len(theme_list) + 1:
                        continue
                    elif 1 <= selection <= len(theme_list):
                        theme_key = theme_list[selection - 1]
                        theme_info = themes_data['themes'][theme_key]
                        preview_theme(theme_info['config'], theme_info['name'])
                    else:
                        print("❌ 无效的选择")
                        
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            elif choice == '2':
                # 对比多个主题
                compare_themes(themes_data)
            
            elif choice == '3':
                # 创建自定义主题
                create_custom_theme()
            
            elif choice == '4':
                # 查看主题特性说明
                print("\n✨ 美化主题特性：")
                features = themes_data['visual_features']
                for feature, description in features.items():
                    print(f"• {description}")
                
                print(f"\n📚 使用说明：")
                instructions = themes_data['usage_instructions']
                for key, value in instructions.items():
                    print(f"• {key}: {value}")
            
            elif choice == '5':
                print("\n👋 感谢使用主题选择器！")
                break
            
            else:
                print("❌ 无效的选择，请输入 1-5")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
