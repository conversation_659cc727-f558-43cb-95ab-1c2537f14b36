"""
跨线程错误处理模块

此模块提供跨线程的错误处理机制，确保：
1. 异常能够正确地在线程间传播
2. UI线程能够接收并处理工作线程的异常
3. 提供统一的错误报告和日志记录

作者: [作者名]
日期: [日期]
"""

import sys
import traceback
import threading
import logging
from typing import Dict, Any, Optional, Callable
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QMessageBox, QApplication

logger = logging.getLogger(__name__)


class ThreadException:
    """线程异常信息封装类"""
    
    def __init__(self, thread_id: int, thread_name: str, exception: Exception, 
                 traceback_str: str, context: Optional[Dict[str, Any]] = None):
        self.thread_id = thread_id
        self.thread_name = thread_name
        self.exception = exception
        self.traceback_str = traceback_str
        self.context = context or {}
        self.timestamp = threading.current_thread().ident
    
    def __str__(self) -> str:
        return f"ThreadException(thread={self.thread_name}, exception={self.exception})"


class CrossThreadErrorHandler(QObject):
    """跨线程错误处理器
    
    负责收集、传播和处理来自不同线程的异常。
    """
    
    # 信号定义
    error_occurred = Signal(object)  # ThreadException
    critical_error_occurred = Signal(object)  # ThreadException
    
    def __init__(self):
        super().__init__()
        self._error_handlers: Dict[str, Callable] = {}
        self._error_count = 0
        self._max_errors_per_minute = 10
        self._error_timestamps = []
        
        # 连接信号到处理函数
        self.error_occurred.connect(self._handle_error)
        self.critical_error_occurred.connect(self._handle_critical_error)
        
        # 设置全局异常钩子
        self._setup_global_exception_hook()
        
        logger.info("CrossThreadErrorHandler 初始化完成")
    
    def _setup_global_exception_hook(self) -> None:
        """设置全局异常钩子"""
        def exception_hook(exc_type, exc_value, exc_traceback):
            """全局异常钩子函数"""
            if issubclass(exc_type, KeyboardInterrupt):
                # 允许 KeyboardInterrupt 正常处理
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            # 创建异常信息
            thread = threading.current_thread()
            tb_str = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            
            thread_exception = ThreadException(
                thread_id=thread.ident,
                thread_name=thread.name,
                exception=exc_value,
                traceback_str=tb_str,
                context={'exc_type': exc_type.__name__}
            )
            
            # 发射信号
            self.critical_error_occurred.emit(thread_exception)
        
        sys.excepthook = exception_hook
    
    def report_error(self, exception: Exception, context: Optional[Dict[str, Any]] = None,
                    is_critical: bool = False) -> None:
        """报告错误
        
        Args:
            exception: 异常对象
            context: 上下文信息
            is_critical: 是否为关键错误
        """
        try:
            thread = threading.current_thread()
            tb_str = traceback.format_exc()
            
            thread_exception = ThreadException(
                thread_id=thread.ident,
                thread_name=thread.name,
                exception=exception,
                traceback_str=tb_str,
                context=context or {}
            )
            
            if is_critical:
                self.critical_error_occurred.emit(thread_exception)
            else:
                self.error_occurred.emit(thread_exception)
                
        except Exception as e:
            # 如果错误报告本身失败，使用基本日志记录
            logger.error(f"错误报告失败: {e}")
            logger.error(f"原始异常: {exception}")
    
    def _handle_error(self, thread_exception: ThreadException) -> None:
        """处理一般错误
        
        Args:
            thread_exception: 线程异常对象
        """
        try:
            # 检查错误频率
            if self._is_error_rate_too_high():
                logger.warning("错误频率过高，跳过部分错误处理")
                return
            
            # 记录错误
            logger.error(f"线程错误 [{thread_exception.thread_name}]: {thread_exception.exception}")
            logger.debug(f"错误堆栈:\n{thread_exception.traceback_str}")
            
            # 调用注册的错误处理器
            handler_name = f"thread_{thread_exception.thread_name}"
            if handler_name in self._error_handlers:
                self._error_handlers[handler_name](thread_exception)
            
            self._error_count += 1
            
        except Exception as e:
            logger.error(f"处理线程错误时发生异常: {e}")
    
    def _handle_critical_error(self, thread_exception: ThreadException) -> None:
        """处理关键错误
        
        Args:
            thread_exception: 线程异常对象
        """
        try:
            # 记录关键错误
            logger.critical(f"关键线程错误 [{thread_exception.thread_name}]: {thread_exception.exception}")
            logger.critical(f"错误堆栈:\n{thread_exception.traceback_str}")
            
            # 在主线程中显示错误对话框
            if self._is_main_thread():
                self._show_critical_error_dialog(thread_exception)
            else:
                # 使用QTimer在主线程中显示对话框
                QTimer.singleShot(0, lambda: self._show_critical_error_dialog(thread_exception))
            
        except Exception as e:
            logger.error(f"处理关键线程错误时发生异常: {e}")
    
    def _show_critical_error_dialog(self, thread_exception: ThreadException) -> None:
        """显示关键错误对话框
        
        Args:
            thread_exception: 线程异常对象
        """
        try:
            app = QApplication.instance()
            if app is None:
                return
            
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("关键错误")
            msg_box.setText(f"线程 '{thread_exception.thread_name}' 发生关键错误")
            msg_box.setInformativeText(str(thread_exception.exception))
            msg_box.setDetailedText(thread_exception.traceback_str)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.exec()
            
        except Exception as e:
            logger.error(f"显示错误对话框失败: {e}")
    
    def register_error_handler(self, thread_name: str, handler: Callable[[ThreadException], None]) -> None:
        """注册特定线程的错误处理器
        
        Args:
            thread_name: 线程名称
            handler: 错误处理函数
        """
        handler_name = f"thread_{thread_name}"
        self._error_handlers[handler_name] = handler
        logger.info(f"已注册线程 '{thread_name}' 的错误处理器")
    
    def unregister_error_handler(self, thread_name: str) -> None:
        """注销特定线程的错误处理器
        
        Args:
            thread_name: 线程名称
        """
        handler_name = f"thread_{thread_name}"
        if handler_name in self._error_handlers:
            del self._error_handlers[handler_name]
            logger.info(f"已注销线程 '{thread_name}' 的错误处理器")
    
    def _is_error_rate_too_high(self) -> bool:
        """检查错误频率是否过高
        
        Returns:
            bool: 错误频率是否过高
        """
        import time
        current_time = time.time()
        
        # 清理超过1分钟的时间戳
        self._error_timestamps = [ts for ts in self._error_timestamps if current_time - ts < 60]
        
        # 添加当前时间戳
        self._error_timestamps.append(current_time)
        
        return len(self._error_timestamps) > self._max_errors_per_minute
    
    def _is_main_thread(self) -> bool:
        """检查当前是否在主线程中
        
        Returns:
            bool: 是否在主线程中
        """
        try:
            app = QApplication.instance()
            if app is None:
                return False
            return QThread.currentThread() == app.thread()
        except:
            return False
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息
        
        Returns:
            Dict[str, Any]: 错误统计信息
        """
        return {
            'total_errors': self._error_count,
            'recent_errors': len(self._error_timestamps),
            'registered_handlers': len(self._error_handlers),
            'handler_names': list(self._error_handlers.keys())
        }


# 全局错误处理器实例
_global_error_handler: Optional[CrossThreadErrorHandler] = None


def get_global_error_handler() -> CrossThreadErrorHandler:
    """获取全局错误处理器实例
    
    Returns:
        CrossThreadErrorHandler: 全局错误处理器实例
    """
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = CrossThreadErrorHandler()
    return _global_error_handler


def report_thread_error(exception: Exception, context: Optional[Dict[str, Any]] = None,
                       is_critical: bool = False) -> None:
    """报告线程错误的便捷函数
    
    Args:
        exception: 异常对象
        context: 上下文信息
        is_critical: 是否为关键错误
    """
    handler = get_global_error_handler()
    handler.report_error(exception, context, is_critical)


def validate_thread_operation(operation_name: str) -> bool:
    """验证线程操作的安全性
    
    Args:
        operation_name: 操作名称
        
    Returns:
        bool: 操作是否安全
    """
    try:
        thread = threading.current_thread()
        logger.debug(f"验证线程操作 '{operation_name}' 在线程 '{thread.name}' 中")
        
        # 检查是否在主线程中进行UI操作
        if 'ui' in operation_name.lower() or 'window' in operation_name.lower():
            app = QApplication.instance()
            if app and QThread.currentThread() != app.thread():
                logger.warning(f"UI操作 '{operation_name}' 不在主线程中执行")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"验证线程操作时发生错误: {e}")
        return False
