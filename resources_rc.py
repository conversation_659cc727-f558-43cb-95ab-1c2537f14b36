# Resource object code (Python 3)
# Created by: object code
# Created by: The Resource Compiler for Qt version 6.9.1
# WARNING! All changes made in this file will be lost!

from PySide6 import QtCore

qt_resource_data = b"\
\x00\x00\x01N\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg widt\
h=\x2224\x22 height=\x222\
4\x22 viewBox=\x220 0 \
24 24\x22\x0d\x0a     xml\
ns=\x22http://www.w\
3.org/2000/svg\x22 \
fill=\x22none\x22\x0d\x0a   \
  stroke=\x22curren\
tColor\x22 stroke-w\
idth=\x222\x22 stroke-\
linecap=\x22round\x22 \
stroke-linejoin=\
\x22round\x22>\x0d\x0a  <pat\
h d=\x22M3 6a2 2 0 \
0 1 2-2h4l2 2h8a\
2 2 0 0 1 2 2v8a\
2 2 0 0 1-2 2H5a\
2 2 0 0 1-2-2V6z\
\x22/>\x0d\x0a</svg>\x0d\x0a\
\x00\x00\x01\xaa\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg widt\
h=\x2224\x22 height=\x222\
4\x22 viewBox=\x220 0 \
24 24\x22\x0d\x0a     xml\
ns=\x22http://www.w\
3.org/2000/svg\x22 \
fill=\x22none\x22\x0d\x0a   \
  stroke=\x22curren\
tColor\x22 stroke-w\
idth=\x222\x22 stroke-\
linecap=\x22round\x22 \
stroke-linejoin=\
\x22round\x22>\x0d\x0a  <!--\
 folder back -->\
\x0d\x0a  <path d=\x22M3 \
8.5V6a2 2 0 0 1 \
2-2h4l2 2h8a2 2 \
0 0 1 2 2v1\x22/>\x0d\x0a\
  <!-- open flap\
 -->\x0d\x0a  <path d=\
\x22M3 8.5h18l-1.5 \
9a2 2 0 0 1-2 1.\
5H6.5a2 2 0 0 1-\
2-1.5L3 8.5z\x22/>\x0d\
\x0a</svg>\x0d\x0a\
\x00\x00\x01I\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg widt\
h=\x2224\x22 height=\x222\
4\x22 viewBox=\x220 0 \
24 24\x22\x0d\x0a     xml\
ns=\x22http://www.w\
3.org/2000/svg\x22 \
fill=\x22none\x22\x0d\x0a   \
  stroke=\x22curren\
tColor\x22 stroke-w\
idth=\x222\x22 stroke-\
linecap=\x22round\x22 \
stroke-linejoin=\
\x22round\x22>\x0d\x0a  <lin\
e x1=\x226\x22  y1=\x226\x22\
  x2=\x2218\x22 y2=\x2218\
\x22/>\x0d\x0a  <line x1=\
\x2218\x22 y1=\x226\x22  x2=\
\x226\x22  y2=\x2218\x22/>\x0d\x0a\
</svg>\x0d\x0a\
\x00\x00\x01\x1a\
<\
?xml version=\x221.\
0\x22 encoding=\x22UTF\
-8\x22?>\x0d\x0a<svg widt\
h=\x2224\x22 height=\x222\
4\x22 viewBox=\x220 0 \
24 24\x22\x0d\x0a     xml\
ns=\x22http://www.w\
3.org/2000/svg\x22 \
fill=\x22none\x22\x0d\x0a   \
  stroke=\x22curren\
tColor\x22 stroke-w\
idth=\x222\x22 stroke-\
linecap=\x22round\x22 \
stroke-linejoin=\
\x22round\x22>\x0d\x0a  <pol\
yline points=\x225 \
13 9 17 19 7\x22/>\x0d\
\x0a</svg>\x0d\x0a\
"

qt_resource_name = b"\
\x00\x06\
\x06\x8a\x9c\xb3\
\x00a\
\x00s\x00s\x00e\x00t\x00s\
\x00\x05\
\x00o\xa6S\
\x00i\
\x00c\x00o\x00n\x00s\
\x00\x0a\
\x0a\xc8\xf6\x87\
\x00f\
\x00o\x00l\x00d\x00e\x00r\x00.\x00s\x00v\x00g\
\x00\x0f\
\x04\x18O\x87\
\x00f\
\x00o\x00l\x00d\x00e\x00r\x00-\x00o\x00p\x00e\x00n\x00.\x00s\x00v\x00g\
\x00\x09\
\x06\x98\x8e\xa7\
\x00c\
\x00l\x00o\x00s\x00e\x00.\x00s\x00v\x00g\
\x00\x09\
\x0b\x9e\x89\x07\
\x00c\
\x00h\x00e\x00c\x00k\x00.\x00s\x00v\x00g\
"

qt_resource_struct = b"\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x01\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x00\x00\x02\x00\x00\x00\x01\x00\x00\x00\x02\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00\x12\x00\x02\x00\x00\x00\x04\x00\x00\x00\x03\
\x00\x00\x00\x00\x00\x00\x00\x00\
\x00\x00\x00<\x00\x00\x00\x00\x00\x01\x00\x00\x01R\
\x00\x00\x01\x97\x97\xea\xb3\xce\
\x00\x00\x00`\x00\x00\x00\x00\x00\x01\x00\x00\x03\x00\
\x00\x00\x01\x97\x97\xebG\x97\
\x00\x00\x00\x22\x00\x00\x00\x00\x00\x01\x00\x00\x00\x00\
\x00\x00\x01\x97\x97\xec)\xb4\
\x00\x00\x00x\x00\x00\x00\x00\x00\x01\x00\x00\x04M\
\x00\x00\x01\x97\x97\xeb\x86\xcd\
"

def qInitResources():
    QtCore.qRegisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

def qCleanupResources():
    QtCore.qUnregisterResourceData(0x03, qt_resource_struct, qt_resource_name, qt_resource_data)

qInitResources()
