# Qt集成数据访问层
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from PySide6.QtWidgets import QApplication
from typing import Optional, List, Any, Callable
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor

from core.database.services.project_service import ProjectService
from core.database.services.analysis_service import AnalysisService
from core.database.models import Project, Analysis

class DatabaseWorker(QThread):
    """数据库操作工作线程"""
    
    # 信号定义
    operation_completed = Signal(str, object)  # 操作类型, 结果
    operation_failed = Signal(str, str)        # 操作类型, 错误信息
    progress_updated = Signal(int)             # 进度百分比
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.operations_queue = []
        self.is_running = False
        
    def add_operation(self, operation_type: str, operation_func: Callable, *args, **kwargs):
        """添加数据库操作到队列"""
        self.operations_queue.append({
            'type': operation_type,
            'func': operation_func,
            'args': args,
            'kwargs': kwargs
        })
        
        if not self.is_running:
            self.start()
    
    def run(self):
        """执行队列中的数据库操作"""
        self.is_running = True
        
        while self.operations_queue:
            operation = self.operations_queue.pop(0)
            
            try:
                # 执行数据库操作
                result = operation['func'](*operation['args'], **operation['kwargs'])
                
                # 发送完成信号
                self.operation_completed.emit(operation['type'], result)
                
            except Exception as e:
                logging.error(f"数据库操作失败: {operation['type']}, 错误: {str(e)}")
                self.operation_failed.emit(operation['type'], str(e))
        
        self.is_running = False

class QtDatabaseManager(QObject):
    """Qt应用程序的数据库管理器"""
    
    # 信号定义
    projects_loaded = Signal(list)
    project_created = Signal(object)
    analysis_completed = Signal(object)
    data_updated = Signal(str, object)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化服务
        self.project_service = None
        self.analysis_service = None
        
        # 工作线程
        self.db_worker = DatabaseWorker(self)
        self.db_worker.operation_completed.connect(self._on_operation_completed)
        self.db_worker.operation_failed.connect(self._on_operation_failed)
        
        # 线程池用于CPU密集型操作
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # 定时器用于定期同步
        self.sync_timer = QTimer(self)
        self.sync_timer.timeout.connect(self._periodic_sync)
        self.sync_timer.start(30000)  # 30秒同步一次
    
    def initialize_services(self, project_service: ProjectService, analysis_service: AnalysisService):
        """初始化数据库服务"""
        self.project_service = project_service
        self.analysis_service = analysis_service
    
    # 项目管理方法
    def load_user_projects_async(self, user_id: int):
        """异步加载用户项目"""
        self.db_worker.add_operation(
            'load_projects',
            self.project_service.get_user_projects,
            user_id
        )
    
    def create_project_async(self, name: str, description: str, user_id: int):
        """异步创建项目"""
        self.db_worker.add_operation(
            'create_project',
            self.project_service.create_project,
            name, description, user_id
        )
    
    def update_project_async(self, project_id: int, updates: dict):
        """异步更新项目"""
        self.db_worker.add_operation(
            'update_project',
            self.project_service.update_project,
            project_id, updates
        )
    
    # 分析管理方法
    def create_analysis_async(self, project_id: int, analysis_config: dict):
        """异步创建分析"""
        self.db_worker.add_operation(
            'create_analysis',
            self.analysis_service.create_analysis,
            project_id, analysis_config
        )
    
    def save_analysis_results_async(self, analysis_id: int, results_data: dict):
        """异步保存分析结果"""
        self.db_worker.add_operation(
            'save_results',
            self.analysis_service.save_results,
            analysis_id, results_data
        )
    
    def load_analysis_results_async(self, analysis_id: int):
        """异步加载分析结果"""
        self.db_worker.add_operation(
            'load_results',
            self.analysis_service.get_analysis_results,
            analysis_id
        )
    
    # 信号处理方法
    def _on_operation_completed(self, operation_type: str, result: Any):
        """处理操作完成信号"""
        if operation_type == 'load_projects':
            self.projects_loaded.emit(result)
        elif operation_type == 'create_project':
            self.project_created.emit(result)
        elif operation_type == 'create_analysis':
            self.analysis_completed.emit(result)
        elif operation_type in ['save_results', 'load_results']:
            self.data_updated.emit(operation_type, result)
    
    def _on_operation_failed(self, operation_type: str, error_message: str):
        """处理操作失败信号"""
        logging.error(f"数据库操作失败: {operation_type}, 错误: {error_message}")
        
        # 可以发送错误信号给UI层处理
        # self.operation_error.emit(operation_type, error_message)
    
    def _periodic_sync(self):
        """定期数据同步"""
        # 这里可以实现定期的数据同步逻辑
        # 例如：同步缓存、检查数据一致性等
        pass

class QtModelAdapter:
    """Qt模型适配器，用于将数据库模型适配到Qt的MVC架构"""
    
    @staticmethod
    def project_to_qt_model(project: Project) -> dict:
        """将Project模型转换为Qt可用的字典"""
        return {
            'id': project.id,
            'name': project.name,
            'description': project.description,
            'status': project.status,
            'created_at': project.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': project.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
            'metadata': project.metadata or {}
        }
    
    @staticmethod
    def analysis_to_qt_model(analysis: Analysis) -> dict:
        """将Analysis模型转换为Qt可用的字典"""
        return {
            'id': analysis.id,
            'project_id': analysis.project_id,
            'name': analysis.name,
            'type': analysis.type,
            'status': analysis.status,
            'configuration': analysis.configuration,
            'created_at': analysis.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': analysis.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }
    
    @staticmethod
    def qt_model_to_project(qt_model: dict) -> Project:
        """将Qt模型转换为Project对象"""
        project = Project()
        project.id = qt_model.get('id')
        project.name = qt_model['name']
        project.description = qt_model.get('description', '')
        project.status = qt_model.get('status', 'active')
        project.metadata = qt_model.get('metadata', {})
        return project

# 在主窗口中的集成示例
class MainWindowDatabaseIntegration:
    """主窗口数据库集成示例"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.db_manager = QtDatabaseManager(main_window)
        
        # 连接信号
        self.db_manager.projects_loaded.connect(self._on_projects_loaded)
        self.db_manager.project_created.connect(self._on_project_created)
        self.db_manager.analysis_completed.connect(self._on_analysis_completed)
    
    def initialize_database_integration(self):
        """初始化数据库集成"""
        # 初始化数据库服务
        from core.database.database_factory import DatabaseFactory
        
        db_factory = DatabaseFactory()
        project_service = db_factory.create_project_service()
        analysis_service = db_factory.create_analysis_service()
        
        self.db_manager.initialize_services(project_service, analysis_service)
        
        # 加载初始数据
        self.load_initial_data()
    
    def load_initial_data(self):
        """加载初始数据"""
        # 假设当前用户ID为1
        current_user_id = 1
        self.db_manager.load_user_projects_async(current_user_id)
    
    def _on_projects_loaded(self, projects: List[Project]):
        """处理项目加载完成"""
        # 更新UI中的项目列表
        project_models = [QtModelAdapter.project_to_qt_model(p) for p in projects]
        
        # 更新项目下拉框或列表
        if hasattr(self.main_window, 'project_combo'):
            self.main_window.project_combo.clear()
            for project in project_models:
                self.main_window.project_combo.addItem(
                    project['name'], 
                    project['id']
                )
    
    def _on_project_created(self, project: Project):
        """处理项目创建完成"""
        project_model = QtModelAdapter.project_to_qt_model(project)
        
        # 添加到项目列表
        if hasattr(self.main_window, 'project_combo'):
            self.main_window.project_combo.addItem(
                project_model['name'],
                project_model['id']
            )
        
        # 显示成功消息
        self.main_window.show_status_message(f"项目 '{project.name}' 创建成功")
    
    def _on_analysis_completed(self, analysis: Analysis):
        """处理分析完成"""
        analysis_model = QtModelAdapter.analysis_to_qt_model(analysis)
        
        # 更新分析列表
        self.main_window.update_analysis_list(analysis_model)
        
        # 显示完成消息
        self.main_window.show_status_message(f"分析 '{analysis.name}' 创建完成")
    
    def create_new_project(self, name: str, description: str):
        """创建新项目"""
        current_user_id = 1  # 实际应用中应该从用户会话获取
        self.db_manager.create_project_async(name, description, current_user_id)
    
    def save_current_analysis(self, analysis_config: dict):
        """保存当前分析配置"""
        current_project_id = self.main_window.get_current_project_id()
        if current_project_id:
            self.db_manager.create_analysis_async(current_project_id, analysis_config)