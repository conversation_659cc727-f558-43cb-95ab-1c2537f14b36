"""
检查系统可用字体，特别是中文字体

此脚本用于检测系统中可用的中文字体，为matplotlib配置提供参考
"""

import matplotlib.font_manager as fm
import matplotlib.pyplot as plt
import os

def check_system_fonts():
    """检查系统中可用的字体"""
    print("🔍 检查系统字体...")
    
    # 获取所有系统字体
    font_list = fm.findSystemFonts()
    print(f"系统中共有 {len(font_list)} 个字体文件")
    
    # 检查中文字体
    chinese_fonts = []
    target_fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong', 'Microsoft JhengHei']
    
    for font_path in font_list:
        try:
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            
            # 检查是否是目标中文字体
            for target in target_fonts:
                if target.lower() in font_name.lower():
                    chinese_fonts.append({
                        'name': font_name,
                        'path': font_path,
                        'target': target
                    })
                    break
        except Exception:
            continue
    
    print(f"\n✅ 找到 {len(chinese_fonts)} 个中文字体:")
    for font in chinese_fonts:
        print(f"  - {font['name']} ({font['target']})")
        print(f"    路径: {font['path']}")
    
    return chinese_fonts

def test_chinese_display():
    """测试中文字体显示效果"""
    print("\n🧪 测试中文字体显示...")
    
    # 测试不同字体配置
    font_configs = [
        ['Microsoft YaHei', 'SimHei', 'DejaVu Sans'],
        ['SimHei', 'Microsoft YaHei', 'DejaVu Sans'],
        ['SimSun', 'Microsoft YaHei', 'DejaVu Sans'],
        ['DejaVu Sans']  # 默认字体作为对比
    ]
    
    test_text = "模态分析结果图表"
    
    for i, fonts in enumerate(font_configs):
        try:
            plt.rcParams['font.sans-serif'] = fonts
            plt.rcParams['axes.unicode_minus'] = False
            
            fig, ax = plt.subplots(figsize=(8, 2))
            ax.text(0.5, 0.5, test_text, fontsize=16, ha='center', va='center')
            ax.set_title(f"字体配置 {i+1}: {fonts[0]}")
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            
            # 保存测试图片
            filename = f"font_test_{i+1}.png"
            plt.savefig(filename, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"✅ 字体测试 {i+1} 完成: {filename}")
            
        except Exception as e:
            print(f"❌ 字体测试 {i+1} 失败: {str(e)}")

def get_best_chinese_font():
    """获取最佳中文字体配置"""
    chinese_fonts = check_system_fonts()
    
    if not chinese_fonts:
        print("⚠️ 未找到中文字体，将使用英文替代方案")
        return ['Arial', 'DejaVu Sans'], False
    
    # 按优先级排序
    priority_order = ['Microsoft YaHei', 'SimHei', 'Microsoft JhengHei', 'SimSun', 'KaiTi', 'FangSong']
    
    best_font = None
    for priority in priority_order:
        for font in chinese_fonts:
            if priority.lower() in font['name'].lower():
                best_font = font['name']
                break
        if best_font:
            break
    
    if best_font:
        font_config = [best_font, 'DejaVu Sans', 'Arial']
        print(f"✅ 推荐字体配置: {font_config}")
        return font_config, True
    else:
        print("⚠️ 未找到优先字体，使用第一个可用中文字体")
        font_config = [chinese_fonts[0]['name'], 'DejaVu Sans', 'Arial']
        return font_config, True

if __name__ == "__main__":
    print("=" * 60)
    print("🔤 matplotlib中文字体检查工具")
    print("=" * 60)
    
    # 检查字体
    font_config, has_chinese = get_best_chinese_font()
    
    # 测试字体显示
    test_chinese_display()
    
    print("\n" + "=" * 60)
    print("📋 字体配置建议:")
    print(f"推荐配置: {font_config}")
    print(f"中文支持: {'是' if has_chinese else '否'}")
    
    if has_chinese:
        print("✅ 系统支持中文字体显示")
    else:
        print("⚠️ 系统不支持中文字体，建议:")
        print("  1. 安装Microsoft YaHei字体")
        print("  2. 或使用英文标签替代")
        print("  3. 或下载字体文件到项目目录")
    
    print("=" * 60)
