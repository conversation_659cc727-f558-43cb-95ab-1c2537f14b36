# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh_parameter_dialog.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QB<PERSON>, QColor, QC<PERSON>alGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QComboBox, QDialog,
    QDoubleSpinBox, QFormLayout, QGroupBox, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QSizePolicy,
    QSpacerItem, QSpinBox, QVBoxLayout, QWidget)

class Ui_MeshParameterDialog(object):
    def setupUi(self, MeshParameterDialog):
        if not MeshParameterDialog.objectName():
            MeshParameterDialog.setObjectName(u"MeshParameterDialog")
        MeshParameterDialog.resize(450, 300)
        MeshParameterDialog.setMinimumSize(QSize(450, 300))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(10)
        MeshParameterDialog.setFont(font)
        MeshParameterDialog.setModal(True)
        self.verticalLayout_main = QVBoxLayout(MeshParameterDialog)
        self.verticalLayout_main.setSpacing(15)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(20, 20, 20, 20)
        self.label_title = QLabel(MeshParameterDialog)
        self.label_title.setObjectName(u"label_title")
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei UI"])
        font1.setPointSize(16)
        font1.setBold(True)
        self.label_title.setFont(font1)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_main.addWidget(self.label_title)

        self.groupBox_basic_params = QGroupBox(MeshParameterDialog)
        self.groupBox_basic_params.setObjectName(u"groupBox_basic_params")
        self.formLayout_basic = QFormLayout(self.groupBox_basic_params)
        self.formLayout_basic.setObjectName(u"formLayout_basic")
        self.formLayout_basic.setHorizontalSpacing(15)
        self.formLayout_basic.setVerticalSpacing(15)
        self.label_mesh_name = QLabel(self.groupBox_basic_params)
        self.label_mesh_name.setObjectName(u"label_mesh_name")

        self.formLayout_basic.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_mesh_name)

        self.lineEdit_mesh_name = QLineEdit(self.groupBox_basic_params)
        self.lineEdit_mesh_name.setObjectName(u"lineEdit_mesh_name")
        self.lineEdit_mesh_name.setMinimumSize(QSize(200, 30))

        self.formLayout_basic.setWidget(0, QFormLayout.ItemRole.FieldRole, self.lineEdit_mesh_name)

        self.label_mesh_size = QLabel(self.groupBox_basic_params)
        self.label_mesh_size.setObjectName(u"label_mesh_size")

        self.formLayout_basic.setWidget(1, QFormLayout.ItemRole.LabelRole, self.label_mesh_size)

        self.doubleSpinBox_mesh_size = QDoubleSpinBox(self.groupBox_basic_params)
        self.doubleSpinBox_mesh_size.setObjectName(u"doubleSpinBox_mesh_size")
        self.doubleSpinBox_mesh_size.setMinimumSize(QSize(200, 30))
        self.doubleSpinBox_mesh_size.setDecimals(2)
        self.doubleSpinBox_mesh_size.setMinimum(0.100000000000000)
        self.doubleSpinBox_mesh_size.setMaximum(1000.000000000000000)
        self.doubleSpinBox_mesh_size.setValue(10.000000000000000)

        self.formLayout_basic.setWidget(1, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_mesh_size)

        self.label_element_type = QLabel(self.groupBox_basic_params)
        self.label_element_type.setObjectName(u"label_element_type")

        self.formLayout_basic.setWidget(2, QFormLayout.ItemRole.LabelRole, self.label_element_type)

        self.comboBox_element_type = QComboBox(self.groupBox_basic_params)
        self.comboBox_element_type.addItem("")
        self.comboBox_element_type.addItem("")
        self.comboBox_element_type.addItem("")
        self.comboBox_element_type.setObjectName(u"comboBox_element_type")
        self.comboBox_element_type.setMinimumSize(QSize(200, 30))

        self.formLayout_basic.setWidget(2, QFormLayout.ItemRole.FieldRole, self.comboBox_element_type)


        self.verticalLayout_main.addWidget(self.groupBox_basic_params)

        self.groupBox_quality_settings = QGroupBox(MeshParameterDialog)
        self.groupBox_quality_settings.setObjectName(u"groupBox_quality_settings")
        self.formLayout_quality = QFormLayout(self.groupBox_quality_settings)
        self.formLayout_quality.setObjectName(u"formLayout_quality")
        self.formLayout_quality.setHorizontalSpacing(15)
        self.formLayout_quality.setVerticalSpacing(15)
        self.label_skewness = QLabel(self.groupBox_quality_settings)
        self.label_skewness.setObjectName(u"label_skewness")

        self.formLayout_quality.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_skewness)

        self.doubleSpinBox_skewness = QDoubleSpinBox(self.groupBox_quality_settings)
        self.doubleSpinBox_skewness.setObjectName(u"doubleSpinBox_skewness")
        self.doubleSpinBox_skewness.setMinimumSize(QSize(200, 30))
        self.doubleSpinBox_skewness.setDecimals(3)
        self.doubleSpinBox_skewness.setMaximum(1.000000000000000)
        self.doubleSpinBox_skewness.setSingleStep(0.010000000000000)
        self.doubleSpinBox_skewness.setValue(0.900000000000000)

        self.formLayout_quality.setWidget(0, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_skewness)

        self.label_aspect_ratio = QLabel(self.groupBox_quality_settings)
        self.label_aspect_ratio.setObjectName(u"label_aspect_ratio")

        self.formLayout_quality.setWidget(1, QFormLayout.ItemRole.LabelRole, self.label_aspect_ratio)

        self.doubleSpinBox_aspect_ratio = QDoubleSpinBox(self.groupBox_quality_settings)
        self.doubleSpinBox_aspect_ratio.setObjectName(u"doubleSpinBox_aspect_ratio")
        self.doubleSpinBox_aspect_ratio.setMinimumSize(QSize(200, 30))
        self.doubleSpinBox_aspect_ratio.setDecimals(1)
        self.doubleSpinBox_aspect_ratio.setMinimum(1.000000000000000)
        self.doubleSpinBox_aspect_ratio.setMaximum(100.000000000000000)
        self.doubleSpinBox_aspect_ratio.setValue(20.000000000000000)

        self.formLayout_quality.setWidget(1, QFormLayout.ItemRole.FieldRole, self.doubleSpinBox_aspect_ratio)

        self.label_smoothing = QLabel(self.groupBox_quality_settings)
        self.label_smoothing.setObjectName(u"label_smoothing")

        self.formLayout_quality.setWidget(2, QFormLayout.ItemRole.LabelRole, self.label_smoothing)

        self.spinBox_smoothing = QSpinBox(self.groupBox_quality_settings)
        self.spinBox_smoothing.setObjectName(u"spinBox_smoothing")
        self.spinBox_smoothing.setMinimumSize(QSize(200, 30))
        self.spinBox_smoothing.setMaximum(10)
        self.spinBox_smoothing.setValue(3)

        self.formLayout_quality.setWidget(2, QFormLayout.ItemRole.FieldRole, self.spinBox_smoothing)


        self.verticalLayout_main.addWidget(self.groupBox_quality_settings)

        self.groupBox_advanced_settings = QGroupBox(MeshParameterDialog)
        self.groupBox_advanced_settings.setObjectName(u"groupBox_advanced_settings")
        self.verticalLayout_advanced = QVBoxLayout(self.groupBox_advanced_settings)
        self.verticalLayout_advanced.setObjectName(u"verticalLayout_advanced")
        self.checkBox_auto_sizing = QCheckBox(self.groupBox_advanced_settings)
        self.checkBox_auto_sizing.setObjectName(u"checkBox_auto_sizing")
        self.checkBox_auto_sizing.setChecked(True)

        self.verticalLayout_advanced.addWidget(self.checkBox_auto_sizing)

        self.checkBox_capture_curvature = QCheckBox(self.groupBox_advanced_settings)
        self.checkBox_capture_curvature.setObjectName(u"checkBox_capture_curvature")
        self.checkBox_capture_curvature.setChecked(True)

        self.verticalLayout_advanced.addWidget(self.checkBox_capture_curvature)

        self.checkBox_capture_proximity = QCheckBox(self.groupBox_advanced_settings)
        self.checkBox_capture_proximity.setObjectName(u"checkBox_capture_proximity")

        self.verticalLayout_advanced.addWidget(self.checkBox_capture_proximity)


        self.verticalLayout_main.addWidget(self.groupBox_advanced_settings)

        self.verticalSpacer = QSpacerItem(20, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)

        self.verticalLayout_main.addItem(self.verticalSpacer)

        self.horizontalLayout_buttons = QHBoxLayout()
        self.horizontalLayout_buttons.setObjectName(u"horizontalLayout_buttons")
        self.horizontalSpacer_buttons = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_buttons.addItem(self.horizontalSpacer_buttons)

        self.btn_reset = QPushButton(MeshParameterDialog)
        self.btn_reset.setObjectName(u"btn_reset")
        self.btn_reset.setMinimumSize(QSize(100, 40))

        self.horizontalLayout_buttons.addWidget(self.btn_reset)

        self.btn_cancel = QPushButton(MeshParameterDialog)
        self.btn_cancel.setObjectName(u"btn_cancel")
        self.btn_cancel.setMinimumSize(QSize(100, 40))

        self.horizontalLayout_buttons.addWidget(self.btn_cancel)

        self.btn_ok = QPushButton(MeshParameterDialog)
        self.btn_ok.setObjectName(u"btn_ok")
        self.btn_ok.setMinimumSize(QSize(100, 40))

        self.horizontalLayout_buttons.addWidget(self.btn_ok)


        self.verticalLayout_main.addLayout(self.horizontalLayout_buttons)


        self.retranslateUi(MeshParameterDialog)
        self.btn_ok.clicked.connect(MeshParameterDialog.accept)
        self.btn_cancel.clicked.connect(MeshParameterDialog.reject)

        self.btn_ok.setDefault(True)


        QMetaObject.connectSlotsByName(MeshParameterDialog)
    # setupUi

    def retranslateUi(self, MeshParameterDialog):
        MeshParameterDialog.setWindowTitle(QCoreApplication.translate("MeshParameterDialog", u"\u7f51\u683c\u53c2\u6570\u8bbe\u7f6e", None))
        self.label_title.setText(QCoreApplication.translate("MeshParameterDialog", u"\u7f51\u683c\u53c2\u6570\u914d\u7f6e", None))
        self.label_title.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QLabel {\n"
"    color: #34495e;\n"
"    padding: 10px;\n"
"    border-bottom: 2px solid #3498db;\n"
"}", None))
        self.groupBox_basic_params.setTitle(QCoreApplication.translate("MeshParameterDialog", u"\u57fa\u672c\u53c2\u6570", None))
        self.groupBox_basic_params.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.label_mesh_name.setText(QCoreApplication.translate("MeshParameterDialog", u"\u7f51\u683c\u540d\u79f0:", None))
        self.lineEdit_mesh_name.setPlaceholderText(QCoreApplication.translate("MeshParameterDialog", u"\u8bf7\u8f93\u5165\u7f51\u683c\u540d\u79f0\uff0c\u5982\"\u7c97\u7f51\u683c\"\u3001\"\u7ec6\u7f51\u683c\"", None))
        self.label_mesh_size.setText(QCoreApplication.translate("MeshParameterDialog", u"\u7f51\u683c\u5c3a\u5bf8(mm):", None))
        self.label_element_type.setText(QCoreApplication.translate("MeshParameterDialog", u"\u5355\u5143\u7c7b\u578b:", None))
        self.comboBox_element_type.setItemText(0, QCoreApplication.translate("MeshParameterDialog", u"\u56db\u9762\u4f53", None))
        self.comboBox_element_type.setItemText(1, QCoreApplication.translate("MeshParameterDialog", u"\u516d\u9762\u4f53", None))
        self.comboBox_element_type.setItemText(2, QCoreApplication.translate("MeshParameterDialog", u"\u6df7\u5408", None))

        self.groupBox_quality_settings.setTitle(QCoreApplication.translate("MeshParameterDialog", u"\u8d28\u91cf\u8bbe\u7f6e", None))
        self.groupBox_quality_settings.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.label_skewness.setText(QCoreApplication.translate("MeshParameterDialog", u"\u504f\u659c\u5ea6:", None))
        self.label_aspect_ratio.setText(QCoreApplication.translate("MeshParameterDialog", u"\u957f\u5bbd\u6bd4:", None))
        self.label_smoothing.setText(QCoreApplication.translate("MeshParameterDialog", u"\u5e73\u6ed1\u8fed\u4ee3:", None))
        self.groupBox_advanced_settings.setTitle(QCoreApplication.translate("MeshParameterDialog", u"\u9ad8\u7ea7\u8bbe\u7f6e", None))
        self.groupBox_advanced_settings.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.checkBox_auto_sizing.setText(QCoreApplication.translate("MeshParameterDialog", u"\u81ea\u52a8\u5c3a\u5bf8\u8c03\u6574", None))
        self.checkBox_capture_curvature.setText(QCoreApplication.translate("MeshParameterDialog", u"\u6355\u83b7\u66f2\u7387", None))
        self.checkBox_capture_proximity.setText(QCoreApplication.translate("MeshParameterDialog", u"\u6355\u83b7\u90bb\u8fd1\u6027", None))
        self.btn_reset.setText(QCoreApplication.translate("MeshParameterDialog", u"\u91cd\u7f6e", None))
        self.btn_reset.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QPushButton {\n"
"    background-color: #f39c12;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #e67e22;\n"
"}", None))
        self.btn_cancel.setText(QCoreApplication.translate("MeshParameterDialog", u"\u53d6\u6d88", None))
        self.btn_cancel.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QPushButton {\n"
"    background-color: #7f8c8d;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6c7b7d;\n"
"}", None))
        self.btn_ok.setText(QCoreApplication.translate("MeshParameterDialog", u"\u786e\u5b9a", None))
        self.btn_ok.setStyleSheet(QCoreApplication.translate("MeshParameterDialog", u"QPushButton {\n"
"    background-color: #3498db;\n"
"    font-size: 12px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #2980b9;\n"
"}", None))
    # retranslateUi

