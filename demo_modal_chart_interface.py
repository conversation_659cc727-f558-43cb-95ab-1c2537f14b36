"""
模态分析图表界面完整演示

展示三种图表类型的完整功能，包括：
1. 频率对比图 - 柱状图显示不同网格的模态频率对比
2. 模态分布图 - 堆叠图显示频率分布
3. 网格收敛性分析 - 折线图显示网格尺寸对频率的影响

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """创建演示用的真实测试数据"""
    return [
        {
            'name': 'Ultra Fine Mesh',
            'size': 0.5,
            'frequencies': [41.23, 73.45, 105.67, 142.89, 186.12, 234.56, 289.34, 345.78],
            'node_count': 25000,
            'element_count': 20000
        },
        {
            'name': 'Fine Mesh',
            'size': 1.0,
            'frequencies': [42.15, 74.82, 107.23, 144.56, 188.91, 237.45, 292.67, 349.12],
            'node_count': 15000,
            'element_count': 12000
        },
        {
            'name': 'Medium Mesh',
            'size': 2.0,
            'frequencies': [43.67, 76.89, 109.45, 147.23, 192.78, 241.56, 297.89, 354.23],
            'node_count': 8000,
            'element_count': 6500
        },
        {
            'name': 'Coarse Mesh',
            'size': 4.0,
            'frequencies': [45.89, 79.34, 112.67, 151.23, 197.45, 246.78, 304.56, 361.89],
            'node_count': 3000,
            'element_count': 2400
        },
        {
            'name': 'Very Coarse Mesh',
            'size': 8.0,
            'frequencies': [48.56, 82.45, 116.78, 156.34, 203.67, 253.45, 312.89, 371.23],
            'node_count': 1200,
            'element_count': 950
        }
    ]

def generate_chart_demo(chart_type, description, data):
    """生成单个图表演示"""
    print(f"\n📊 生成 {description}...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用无GUI后端
        
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 更新图表
        chart_widget.update_chart(chart_type, data)
        
        # 保存高质量图表
        filename = f"demo_{chart_type}.png"
        chart_widget.save_chart(filename, dpi=300)  # 高DPI用于演示
        
        if os.path.exists(filename):
            file_size = os.path.getsize(filename)
            print(f"  ✅ {description} 生成成功")
            print(f"     文件: {filename}")
            print(f"     大小: {file_size:,} 字节")
            print(f"     分辨率: 300 DPI (高质量)")
            return True
        else:
            print(f"  ❌ {description} 生成失败")
            return False
            
    except Exception as e:
        print(f"  ❌ {description} 生成失败: {str(e)}")
        return False

def create_chart_comparison():
    """创建图表对比演示"""
    print("\n📊 创建图表功能对比演示...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Modal Analysis Chart Types Comparison', fontsize=20, fontweight='bold')
        
        # 演示数据
        demo_data = create_demo_data()
        mesh_names = [item['name'] for item in demo_data]
        mesh_sizes = [item['size'] for item in demo_data]
        frequencies = [item['frequencies'] for item in demo_data]
        
        # 1. 频率对比图 (左上)
        ax1 = axes[0, 0]
        x = range(len(mesh_names))
        for mode_idx in range(min(5, len(frequencies[0]))):
            mode_freqs = [freq[mode_idx] if mode_idx < len(freq) else 0 for freq in frequencies]
            ax1.bar([i + mode_idx*0.15 for i in x], mode_freqs, 
                   width=0.15, label=f'Mode {mode_idx+1}', alpha=0.8)
        ax1.set_title('Frequency Comparison Chart', fontweight='bold')
        ax1.set_xlabel('Mesh Scheme')
        ax1.set_ylabel('Frequency (Hz)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 模态分布图 (右上)
        ax2 = axes[0, 1]
        bottom = [0] * len(mesh_names)
        for mode_idx in range(min(5, len(frequencies[0]))):
            mode_freqs = [freq[mode_idx] if mode_idx < len(freq) else 0 for freq in frequencies]
            ax2.bar(mesh_names, mode_freqs, bottom=bottom, 
                   label=f'Mode {mode_idx+1}', alpha=0.8)
            bottom = [b + f for b, f in zip(bottom, mode_freqs)]
        ax2.set_title('Modal Distribution Chart', fontweight='bold')
        ax2.set_xlabel('Mesh Scheme')
        ax2.set_ylabel('Cumulative Frequency (Hz)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 网格收敛性分析 (左下)
        ax3 = axes[1, 0]
        for mode_idx in range(min(3, len(frequencies[0]))):
            mode_freqs = [freq[mode_idx] if mode_idx < len(freq) else 0 for freq in frequencies]
            ax3.plot(mesh_sizes, mode_freqs, 'o-', 
                    linewidth=2, markersize=8, label=f'Mode {mode_idx+1}')
        ax3.set_title('Mesh Convergence Analysis', fontweight='bold')
        ax3.set_xlabel('Mesh Size (mm)')
        ax3.set_ylabel('Frequency (Hz)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        ax3.set_xscale('log')
        
        # 4. 特性说明 (右下)
        ax4 = axes[1, 1]
        ax4.axis('off')
        
        features_text = """
Chart Features:

• Frequency Comparison Chart
  - Bar chart showing modal frequencies
  - Compare up to 8 modes across meshes
  - Color-coded mode identification
  - Frequency value annotations

• Modal Distribution Chart  
  - Stacked bar chart showing distribution
  - Cumulative frequency visualization
  - Easy identification of dominant modes
  - Mesh scheme comparison

• Mesh Convergence Analysis
  - Line plot showing convergence trends
  - Logarithmic mesh size scale
  - Multiple mode tracking
  - Convergence assessment

• Professional Features
  - High-quality output (300 DPI)
  - Customizable display options
  - Export functionality
  - Error handling
        """
        
        ax4.text(0.05, 0.95, features_text, transform=ax4.transAxes, 
                fontsize=11, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        plt.savefig('demo_chart_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✅ 图表对比演示生成成功")
        print("     文件: demo_chart_comparison.png")
        return True
        
    except Exception as e:
        print(f"  ❌ 图表对比演示生成失败: {str(e)}")
        return False

def main():
    """主演示函数"""
    print("=" * 70)
    print("🎯 模态分析图表界面完整演示")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 创建演示数据
    demo_data = create_demo_data()
    print(f"📋 演示数据准备完成，包含 {len(demo_data)} 个网格方案")
    print(f"   网格尺寸范围: {demo_data[-1]['size']} - {demo_data[0]['size']} mm")
    print(f"   模态数量: {len(demo_data[0]['frequencies'])} 阶")
    print(f"   节点数范围: {demo_data[-1]['node_count']:,} - {demo_data[0]['node_count']:,}")
    
    # 生成三种图表类型
    chart_configs = [
        ("frequency_comparison", "频率对比图 (Frequency Comparison Chart)"),
        ("mode_distribution", "模态分布图 (Modal Distribution Chart)"),
        ("mesh_convergence", "网格收敛性分析 (Mesh Convergence Analysis)")
    ]
    
    success_count = 0
    for chart_type, description in chart_configs:
        if generate_chart_demo(chart_type, description, demo_data):
            success_count += 1
    
    # 创建对比演示
    comparison_ok = create_chart_comparison()
    
    print("\n" + "=" * 70)
    print("📋 演示生成结果:")
    print(f"图表生成: {success_count}/{len(chart_configs)} 成功")
    print(f"对比演示: {'✅ 成功' if comparison_ok else '❌ 失败'}")
    
    if success_count == len(chart_configs) and comparison_ok:
        print("\n🎉 完整界面演示生成成功！")
        print("\n📁 生成的演示文件:")
        print("  • demo_frequency_comparison.png - 频率对比图")
        print("  • demo_mode_distribution.png - 模态分布图") 
        print("  • demo_mesh_convergence.png - 网格收敛性分析")
        print("  • demo_chart_comparison.png - 功能对比演示")
        
        print("\n✨ 界面特点:")
        print("  ✅ 专业的英文界面，无字体问题")
        print("  ✅ 高质量输出 (300 DPI)")
        print("  ✅ 丰富的数据可视化")
        print("  ✅ 清晰的图表标识和标签")
        print("  ✅ 完善的错误处理机制")
        
        print("\n🎯 可用于:")
        print("  • 产品演示和展示")
        print("  • 用户界面设计参考")
        print("  • 功能测试和验证")
        print("  • 文档和培训材料")
        
    else:
        print("\n⚠️ 部分演示生成失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
