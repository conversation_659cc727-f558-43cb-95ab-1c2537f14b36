<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果显示 - 帮助文档</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>结果显示界面</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>界面概述</h2>
            <p>结果显示界面用于查看和分析计算完成后的结果数据。该界面提供了丰富的可视化工具和数据处理功能，帮助用户深入理解分析结果。</p>
            
            <div class="ui-description">
                <h3>界面组成</h3>
                <ul>
                    <li><strong>结果树</strong> - 位于界面左侧，以树形结构显示所有可用的结果类型</li>
                    <li><strong>可视化区域</strong> - 位于界面中央，显示选定结果的图形表示</li>
                    <li><strong>属性面板</strong> - 位于界面右侧，显示和控制当前结果的显示属性</li>
                    <li><strong>时间轴</strong> - 位于界面底部，用于控制动态结果的时间步</li>
                    <li><strong>工具栏</strong> - 提供常用的结果处理工具和视图控制</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>主要功能</h2>
            
            <div class="feature-card">
                <h3>结果可视化</h3>
                <ul>
                    <li>等值线/等值面显示</li>
                    <li>矢量图显示</li>
                    <li>变形动画</li>
                    <li>截面视图</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>数据分析</h3>
                <ul>
                    <li>最大/最小值查询</li>
                    <li>特定点数据提取</li>
                    <li>路径结果分析</li>
                    <li>频率响应分析</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>结果导出</h3>
                <ul>
                    <li>图像导出(PNG, JPG)</li>
                    <li>数据导出(CSV, Excel)</li>
                    <li>报告生成(HTML, PDF)</li>
                    <li>动画导出(AVI, GIF)</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>高级处理</h3>
                <ul>
                    <li>数据过滤</li>
                    <li>结果对比</li>
                    <li>参数化研究</li>
                    <li><strong>振动分析</strong></li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>操作指南</h2>
            
            <h3>查看结果</h3>
            <ol>
                <li>在左侧结果树中选择要查看的结果类型</li>
                <li>结果将在中央可视化区域显示</li>
                <li>使用右侧属性面板调整显示参数</li>
                <li>对于时变结果，使用底部时间轴控制显示的时间步</li>
            </ol>
            
            <h3>调整视图</h3>
            <ul>
                <li><strong>旋转视图</strong> - 按住鼠标左键并拖动</li>
                <li><strong>平移视图</strong> - 按住鼠标中键并拖动</li>
                <li><strong>缩放视图</strong> - 滚动鼠标滚轮或按住鼠标右键上下拖动</li>
                <li><strong>重置视图</strong> - 点击工具栏中的"重置视图"按钮</li>
            </ul>
            
            <h3>导出结果</h3>
            <ol>
                <li>设置所需的视图和显示参数</li>
                <li>点击工具栏中的"导出"按钮</li>
                <li>选择导出格式和选项</li>
                <li>指定保存位置和文件名</li>
                <li>点击"保存"完成导出</li>
            </ol>
        </div>

        <div class="section">
            <h2>振动分析功能</h2>
            <div class="feature-highlight">
                <p>结果界面现已集成振动分析功能，可以对结构的振动特性进行深入分析：</p>
                <ul>
                    <li>点击工具栏中的"后处理"按钮</li>
                    <li>在下拉菜单中选择"振动分析"</li>
                    <li>系统将打开振动分析模块，加载当前结果数据</li>
                </ul>
                <p>振动分析模块提供以下功能：</p>
                <ul>
                    <li>时域振动数据处理</li>
                    <li>FFT频谱分析</li>
                    <li>1/3倍频程分析</li>
                    <li>多方向振动对比</li>
                    <li>振动级别评估</li>
                </ul>
                <div class="buttons">
                    <a href="vibration.html" class="button">了解更多振动分析功能</a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>常见问题</h2>
            
            <div class="faq">
                <h3>Q: 如何放大查看特定区域的结果？</h3>
                <p>A: 使用工具栏中的"区域放大"工具，然后在可视化区域拖动鼠标选择要放大的区域。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 如何显示特定点的数值？</h3>
                <p>A: 使用工具栏中的"查询"工具，然后点击模型上的任意点，系统将显示该点的详细数据。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 如何创建高质量的结果图像？</h3>
                <p>A: 在导出图像前，使用属性面板中的"高级渲染"选项，设置高分辨率和抗锯齿等参数，然后导出为PNG格式。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 振动分析模块支持哪些数据格式？</h3>
                <p>A: 振动分析模块支持从计算结果直接提取数据，也支持导入外部的CSV、TXT等格式的振动测量数据。</p>
            </div>
        </div>

        <a href="index.html" class="back-link">返回主页</a>
    </div>

    <div class="footer">
        <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
    </div>
</body>
</html> 