<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主界面 - 帮助文档</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>主界面</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>界面概述</h2>
            <p>主界面是软件的核心操作中心，提供了访问所有功能模块的入口。界面采用现代化设计，布局清晰，操作直观。</p>
            
            <div class="ui-description">
                <h3>界面组成</h3>
                <ul>
                    <li><strong>菜单栏</strong> - 位于界面顶部，包含文件、编辑、视图、工具和帮助等菜单</li>
                    <li><strong>工具栏</strong> - 位于菜单栏下方，提供常用功能的快捷按钮</li>
                    <li><strong>导航面板</strong> - 位于界面左侧，用于在不同功能模块之间切换</li>
                    <li><strong>主工作区</strong> - 界面中央的主要内容显示区域</li>
                    <li><strong>状态栏</strong> - 位于界面底部，显示当前状态和提示信息</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>主要功能</h2>
            
            <div class="feature-card">
                <h3>项目管理</h3>
                <ul>
                    <li>创建新项目</li>
                    <li>打开现有项目</li>
                    <li>保存项目</li>
                    <li>项目设置管理</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>模型处理</h3>
                <ul>
                    <li>导入几何模型</li>
                    <li>模型查看和编辑</li>
                    <li>模型属性设置</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>分析流程控制</h3>
                <ul>
                    <li>启动分析</li>
                    <li>暂停/继续分析</li>
                    <li>终止分析</li>
                    <li>分析进度监控</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>结果处理</h3>
                <ul>
                    <li>查看分析结果</li>
                    <li>结果可视化</li>
                    <li>导出报告</li>
                    <li>振动分析</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>操作指南</h2>
            
            <h3>创建新项目</h3>
            <ol>
                <li>点击菜单栏中的"文件" → "新建项目"</li>
                <li>在弹出的对话框中设置项目名称和保存路径</li>
                <li>点击"确定"按钮完成创建</li>
            </ol>
            
            <h3>打开现有项目</h3>
            <ol>
                <li>点击菜单栏中的"文件" → "打开项目"</li>
                <li>在文件浏览器中选择要打开的项目文件</li>
                <li>点击"打开"按钮</li>
            </ol>
            
            <h3>访问功能模块</h3>
            <p>通过左侧导航面板可以快速切换到不同的功能模块：</p>
            <ul>
                <li>点击"网格划分"进入网格设置界面</li>
                <li>点击"前处理"进入模型处理界面</li>
                <li>点击"连接设置"进入连接参数配置界面</li>
                <li>点击"分析设置"进入分析参数配置界面</li>
                <li>点击"约束设置"进入边界条件设置界面</li>
                <li>点击"结果显示"进入结果查看界面</li>
                <li>点击"振动分析"进入振动数据分析界面</li>
            </ul>
        </div>

        <div class="section">
            <h2>快捷键</h2>
            <table>
                <tr>
                    <th>快捷键</th>
                    <th>功能</th>
                </tr>
                <tr>
                    <td>Ctrl+N</td>
                    <td>新建项目</td>
                </tr>
                <tr>
                    <td>Ctrl+O</td>
                    <td>打开项目</td>
                </tr>
                <tr>
                    <td>Ctrl+S</td>
                    <td>保存项目</td>
                </tr>
                <tr>
                    <td>F5</td>
                    <td>开始分析</td>
                </tr>
                <tr>
                    <td>F8</td>
                    <td>查看结果</td>
                </tr>
                <tr>
                    <td>F1</td>
                    <td>打开帮助</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <h2>新增功能：振动分析</h2>
            <div class="feature-highlight">
                <p>现在您可以直接从主界面访问振动分析模块，进行高级振动数据处理和可视化：</p>
                <ol>
                    <li>在导航面板中点击"振动分析"</li>
                    <li>或在结果显示界面点击"后处理"按钮，然后选择"振动分析"</li>
                </ol>
                <div class="buttons">
                    <a href="vibration.html" class="button">了解振动分析模块</a>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>常见问题</h2>
            
            <div class="faq">
                <h3>Q: 如何更改软件界面语言？</h3>
                <p>A: 点击菜单栏中的"设置" → "语言"，然后选择所需的语言。更改将在软件重启后生效。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 如何配置ANSYS路径？</h3>
                <p>A: 点击菜单栏中的"设置" → "ANSYS路径"，在弹出的对话框中选择ANSYS Workbench的安装路径。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 界面显示异常怎么办？</h3>
                <p>A: 尝试点击菜单栏中的"视图" → "重置布局"，恢复默认界面设置。如果问题仍然存在，请尝试重启软件。</p>
            </div>
        </div>

        <a href="index.html" class="back-link">返回主页</a>
    </div>

    <div class="footer">
        <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
    </div>
</body>
</html> 