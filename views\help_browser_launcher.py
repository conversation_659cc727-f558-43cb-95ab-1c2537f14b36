#!/usr/bin/env python3
"""
帮助文档浏览器启动器

使用系统默认浏览器打开帮助文档，替代Qt对话框界面
"""

import os
import sys
import logging
import tempfile
from typing import Optional, Dict, List
from urllib.parse import quote
from pathlib import Path

from PySide6.QtCore import QObject, QUrl, QTimer, Signal
from PySide6.QtGui import QDesktopServices
from PySide6.QtWidgets import QMessageBox, QApplication

from core.i18n_manager import tr

logger = logging.getLogger(__name__)


class HelpBrowserLauncher(QObject):
    """帮助文档浏览器启动器
    
    负责在系统默认浏览器中打开帮助文档，替代传统的Qt对话框界面
    """
    
    # 信号定义
    help_opened = Signal(str)  # 帮助文档已打开
    help_failed = Signal(str)  # 帮助文档打开失败
    
    def __init__(self, help_base_dir: str, parent=None):
        """初始化帮助文档启动器
        
        Args:
            help_base_dir: 帮助文档基础目录路径
            parent: 父对象
        """
        super().__init__(parent)
        
        self.help_base_dir = Path(help_base_dir).resolve()
        self.parent_widget = parent
        
        # 验证帮助文档目录
        if not self.help_base_dir.exists():
            logger.error(f"帮助文档目录不存在: {self.help_base_dir}")
            raise FileNotFoundError(f"帮助文档目录不存在: {self.help_base_dir}")
        
        # 诊断现代化文件
        self._diagnose_modern_files()
        
        logger.info(f"HelpBrowserLauncher 初始化完成，帮助目录: {self.help_base_dir}")
    
    def open_help(self, file_name: str = "index.html") -> bool:
        """在系统默认浏览器中打开帮助文档
        
        Args:
            file_name: 要打开的帮助文件名，默认为index.html
            
        Returns:
            bool: 是否成功启动浏览器
        """
        try:
            # 显示状态消息
            self._show_status_message(tr("正在打开帮助文档...", "HelpBrowserLauncher"))
            
            # 获取目标文件路径
            target_file = self._get_target_file(file_name)
            if not target_file:
                return False
            
            # 构建文件URL
            file_url = self._build_file_url(target_file)
            
            # 在浏览器中打开
            success = self._open_in_browser(file_url, target_file.name)
            
            if success:
                self.help_opened.emit(str(target_file))
                logger.info(f"帮助文档已在浏览器中打开: {target_file.name}")
            else:
                self.help_failed.emit(f"无法打开帮助文档: {file_name}")
                
            return success
            
        except Exception as e:
            logger.error(f"打开帮助文档失败: {e}", exc_info=True)
            self._show_error_message(
                tr("帮助文档错误", "HelpBrowserLauncher"),
                tr("打开帮助文档时发生错误：{}", "HelpBrowserLauncher").format(str(e))
            )
            self.help_failed.emit(str(e))
            return False
    
    def open_help_section(self, section: str) -> bool:
        """打开特定的帮助文档章节
        
        Args:
            section: 章节名称，如 'main', 'mesh', 'api' 等
            
        Returns:
            bool: 是否成功打开
        """
        # 章节到文件名的映射（优先使用现代化版本）
        section_mapping = {
            'main': 'main_modern.html',
            'mesh': 'mesh_modern.html',
            'pre': 'pre_modern.html',
            'connection': 'connection_modern.html',
            'analysis': 'analysis_modern.html',
            'constrain': 'constrain_modern.html',
            'result': 'result_modern.html',
            'vibration': 'vibration_modern.html',
            'api': 'api_modern.html',
            'thread_safety': 'thread_safety_modern.html',
            'i18n': 'i18n_modern.html',
            'troubleshooting': 'troubleshooting_modern.html',
            'encoding': 'encoding_guide.html',
            'overview': 'index.html'
        }
        
        file_name = section_mapping.get(section, f"{section}.html")
        return self.open_help(file_name)
    
    def _get_target_file(self, file_name: str) -> Optional[Path]:
        """获取目标文件路径，支持备用文件机制
        
        Args:
            file_name: 文件名
            
        Returns:
            Path: 目标文件路径，如果文件不存在则返回None
        """
        target_file = self.help_base_dir / file_name
        
        # 检查文件是否存在
        if target_file.exists():
            logger.debug(f"找到目标文件: {target_file}")
            return target_file
        
        # 尝试备用文件
        fallback_file = self._get_fallback_file(file_name)
        if fallback_file:
            fallback_path = self.help_base_dir / fallback_file
            if fallback_path.exists():
                logger.info(f"使用备用文件: {fallback_file}")
                return fallback_path
        
        # 文件不存在
        logger.error(f"帮助文件不存在: {target_file}")
        self._show_error_message(
            tr("文件不存在", "HelpBrowserLauncher"),
            tr("帮助文件不存在：{}\n\n请检查文件是否已正确安装。", "HelpBrowserLauncher").format(target_file)
        )
        return None
    
    def _get_fallback_file(self, file_name: str) -> Optional[str]:
        """获取备用文件名（现代化版本 → 经典版本）
        
        Args:
            file_name: 原始文件名
            
        Returns:
            str: 备用文件名，如果没有备用则返回None
        """
        fallback_mapping = {
            "main_modern.html": "main.html",
            "mesh_modern.html": "mesh.html", 
            "pre_modern.html": "pre.html",
            "connection_modern.html": "connection.html",
            "analysis_modern.html": "analysis.html",
            "constrain_modern.html": "constrain.html",
            "result_modern.html": "result.html",
            "vibration_modern.html": "vibration.html",
            "api_modern.html": "api.html",
            "thread_safety_modern.html": "thread_safety.html",
            "i18n_modern.html": "i18n.html",
            "troubleshooting_modern.html": "troubleshooting.html"
        }
        
        return fallback_mapping.get(file_name)
    
    def _build_file_url(self, file_path: Path) -> QUrl:
        """构建文件URL
        
        Args:
            file_path: 文件路径
            
        Returns:
            QUrl: 文件URL对象
        """
        # 确保路径是绝对路径
        abs_path = file_path.resolve()
        
        # 构建file://协议的URL
        if sys.platform.startswith('win'):
            # Windows: file:///C:/path/to/file.html
            url_string = f"file:///{abs_path.as_posix()}"
        else:
            # Unix-like: file:///path/to/file.html
            url_string = f"file://{abs_path.as_posix()}"
        
        logger.debug(f"构建文件URL: {url_string}")
        return QUrl(url_string)
    
    def _open_in_browser(self, file_url: QUrl, file_name: str) -> bool:
        """在系统默认浏览器中打开URL
        
        Args:
            file_url: 文件URL
            file_name: 文件名（用于日志）
            
        Returns:
            bool: 是否成功打开
        """
        try:
            # 使用QDesktopServices打开URL
            success = QDesktopServices.openUrl(file_url)
            
            if success:
                logger.info(f"成功在浏览器中打开: {file_name}")
                self._show_status_message(
                    tr("帮助文档已在浏览器中打开", "HelpBrowserLauncher"),
                    timeout=3000
                )
            else:
                logger.warning(f"浏览器打开失败: {file_name}")
                self._show_error_message(
                    tr("浏览器启动失败", "HelpBrowserLauncher"),
                    tr("无法启动系统默认浏览器打开帮助文档。\n\n请检查系统是否安装了浏览器，或手动打开文件：\n{}", "HelpBrowserLauncher").format(file_url.toString())
                )
            
            return success
            
        except Exception as e:
            logger.error(f"打开浏览器时发生异常: {e}", exc_info=True)
            self._show_error_message(
                tr("浏览器启动错误", "HelpBrowserLauncher"),
                tr("启动浏览器时发生错误：{}", "HelpBrowserLauncher").format(str(e))
            )
            return False

    def _diagnose_modern_files(self) -> None:
        """诊断现代化HTML文件的存在性"""
        logger.info("开始诊断现代化HTML文件...")

        # 需要检查的现代化文件列表
        modern_files = [
            "main_modern.html",
            "mesh_modern.html",
            "pre_modern.html",
            "connection_modern.html",
            "analysis_modern.html",
            "constrain_modern.html",
            "result_modern.html",
            "vibration_modern.html",
            "api_modern.html",
            "thread_safety_modern.html",
            "i18n_modern.html",
            "troubleshooting_modern.html"
        ]

        missing_files = []
        existing_files = []

        for file_name in modern_files:
            file_path = self.help_base_dir / file_name
            if file_path.exists():
                existing_files.append(file_name)
                logger.debug(f"✅ 现代化文件存在: {file_name}")
            else:
                missing_files.append(file_name)
                logger.warning(f"❌ 现代化文件缺失: {file_name}")

        # 记录诊断结果
        logger.info(f"现代化文件诊断完成:")
        logger.info(f"  - 存在的文件: {len(existing_files)}/{len(modern_files)}")
        logger.info(f"  - 缺失的文件: {len(missing_files)}")

        if missing_files:
            logger.warning(f"缺失的现代化文件: {', '.join(missing_files)}")
            logger.info("系统将自动使用经典版本作为备用")
        else:
            logger.info("所有现代化文件都已就绪！")

    def _show_status_message(self, message: str, timeout: int = 2000) -> None:
        """显示状态消息

        Args:
            message: 状态消息
            timeout: 显示时长（毫秒）
        """
        try:
            # 如果有父窗口且有状态栏，显示在状态栏
            if self.parent_widget and hasattr(self.parent_widget, 'statusBar'):
                self.parent_widget.statusBar().showMessage(message, timeout)
            else:
                # 否则记录到日志
                logger.info(f"状态: {message}")

        except Exception as e:
            logger.debug(f"显示状态消息失败: {e}")

    def _show_error_message(self, title: str, message: str) -> None:
        """显示错误消息对话框

        Args:
            title: 对话框标题
            message: 错误消息
        """
        try:
            # 使用简单的消息框显示错误
            msg_box = QMessageBox(self.parent_widget)
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()

        except Exception as e:
            logger.error(f"显示错误消息失败: {e}")

    def get_available_help_sections(self) -> Dict[str, str]:
        """获取可用的帮助文档章节

        Returns:
            Dict[str, str]: 章节名称到显示名称的映射
        """
        sections = {
            'overview': tr('概述', 'HelpBrowserLauncher'),
            'main': tr('主界面', 'HelpBrowserLauncher'),
            'mesh': tr('网格划分', 'HelpBrowserLauncher'),
            'pre': tr('前处理', 'HelpBrowserLauncher'),
            'connection': tr('连接设置', 'HelpBrowserLauncher'),
            'analysis': tr('分析设置', 'HelpBrowserLauncher'),
            'constrain': tr('约束设置', 'HelpBrowserLauncher'),
            'result': tr('结果显示', 'HelpBrowserLauncher'),
            'vibration': tr('振动分析', 'HelpBrowserLauncher'),
            'api': tr('API接口', 'HelpBrowserLauncher'),
            'thread_safety': tr('线程安全', 'HelpBrowserLauncher'),
            'i18n': tr('国际化', 'HelpBrowserLauncher'),
            'troubleshooting': tr('故障排除', 'HelpBrowserLauncher'),
            'encoding': tr('编码处理', 'HelpBrowserLauncher')
        }

        # 过滤掉不存在的章节
        available_sections = {}
        for section_key, section_name in sections.items():
            if section_key == 'overview':
                file_name = 'index.html'
            elif section_key == 'encoding':
                file_name = 'encoding_guide.html'
            else:
                file_name = f"{section_key}_modern.html"

            file_path = self.help_base_dir / file_name
            fallback_path = None

            # 检查备用文件
            if not file_path.exists():
                fallback_file = self._get_fallback_file(file_name)
                if fallback_file:
                    fallback_path = self.help_base_dir / fallback_file

            if file_path.exists() or (fallback_path and fallback_path.exists()):
                available_sections[section_key] = section_name

        return available_sections

    @staticmethod
    def create_help_launcher(help_base_dir: str, parent=None) -> 'HelpBrowserLauncher':
        """创建帮助文档启动器的工厂方法

        Args:
            help_base_dir: 帮助文档基础目录
            parent: 父对象

        Returns:
            HelpBrowserLauncher: 帮助文档启动器实例
        """
        try:
            return HelpBrowserLauncher(help_base_dir, parent)
        except Exception as e:
            logger.error(f"创建帮助文档启动器失败: {e}")
            raise
