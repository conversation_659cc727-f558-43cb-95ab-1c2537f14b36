"""
网格参数编辑对话框模块

此模块定义了网格参数编辑对话框类，负责：
1. 显示网格参数编辑界面
2. 处理用户输入验证
3. 提供网格参数的创建和编辑功能

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Optional, Dict, Any
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QDoubleValidator, QIntValidator
from PySide6.QtWidgets import QDialog, QMessageBox

from core.mesh_manager import MeshParameter, ElementType, MeshQualitySettings
from ui.ui_mesh_parameter_dialog import Ui_MeshParameterDialog

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MeshParameterDialog(QDialog):
    """网格参数编辑对话框类"""
    
    # 自定义信号
    parameter_accepted = Signal(object)  # 参数确认信号，传递MeshParameter对象
    
    def __init__(self, parent=None, mesh_parameter: Optional[MeshParameter] = None):
        """初始化对话框
        
        Args:
            parent: 父窗口
            mesh_parameter: 要编辑的网格参数，如果为None则创建新参数
        """
        super().__init__(parent)
        
        # 设置UI
        self.ui = Ui_MeshParameterDialog()
        self.ui.setupUi(self)
        
        # 设置对话框属性
        self.setModal(True)
        self.setWindowTitle("网格参数设置")
        
        # 存储原始参数（用于编辑模式）
        self._original_parameter = mesh_parameter
        self._is_edit_mode = mesh_parameter is not None
        
        # 设置输入验证器
        self._setup_validators()
        
        # 连接信号槽
        self._connect_signals()
        
        # 初始化界面数据
        self._initialize_ui()
        
        # 如果是编辑模式，加载现有参数
        if self._is_edit_mode:
            self._load_parameter_data(mesh_parameter)
        else:
            self._load_default_values()
        
        logger.debug(f"网格参数对话框初始化完成，模式: {'编辑' if self._is_edit_mode else '新建'}")
    
    def _setup_validators(self):
        """设置输入验证器"""
        # 网格尺寸范围设置 (0.1 - 1000.0)
        self.ui.doubleSpinBox_mesh_size.setRange(0.1, 1000.0)
        self.ui.doubleSpinBox_mesh_size.setDecimals(2)
        self.ui.doubleSpinBox_mesh_size.setSingleStep(0.1)

        # 偏斜度范围设置 (0.0 - 1.0)
        self.ui.doubleSpinBox_skewness.setRange(0.0, 1.0)
        self.ui.doubleSpinBox_skewness.setDecimals(3)
        self.ui.doubleSpinBox_skewness.setSingleStep(0.01)

        # 长宽比范围设置 (1.0 - 100.0)
        self.ui.doubleSpinBox_aspect_ratio.setRange(1.0, 100.0)
        self.ui.doubleSpinBox_aspect_ratio.setDecimals(1)
        self.ui.doubleSpinBox_aspect_ratio.setSingleStep(1.0)

        # 平滑迭代范围设置 (0 - 10)
        self.ui.spinBox_smoothing.setRange(6, 10000)
        self.ui.spinBox_smoothing.setSingleStep(1)
        
        logger.debug("输入验证器设置完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        # 按钮信号
        self.ui.btn_ok.clicked.connect(self._on_ok_clicked)
        self.ui.btn_cancel.clicked.connect(self.reject)
        self.ui.btn_reset.clicked.connect(self._on_reset_clicked)
        
        # 输入变化信号（用于实时验证）
        self.ui.lineEdit_mesh_name.textChanged.connect(self._validate_input)
        self.ui.doubleSpinBox_mesh_size.valueChanged.connect(self._validate_input)
        self.ui.doubleSpinBox_skewness.valueChanged.connect(self._validate_input)
        self.ui.doubleSpinBox_aspect_ratio.valueChanged.connect(self._validate_input)
        
        logger.debug("信号槽连接完成")
    
    def _initialize_ui(self):
        """初始化UI界面"""
        # 设置单元类型下拉框
        self.ui.comboBox_element_type.clear()
        for element_type in ElementType:
            self.ui.comboBox_element_type.addItem(element_type.value, element_type)
        
        # 设置默认选中项
        self.ui.comboBox_element_type.setCurrentIndex(0)
        
        logger.debug("UI界面初始化完成")
    
    def _load_default_values(self):
        """加载默认值"""
        # 从配置管理器获取默认设置
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_settings = config_manager.get_mesh_default_settings()
            
            # 设置默认值
            self.ui.lineEdit_mesh_name.setText("新网格")
            self.ui.doubleSpinBox_mesh_size.setValue(10.0)
            
            # 设置默认单元类型
            default_element_type = default_settings.get("element_type", "四面体")
            for i in range(self.ui.comboBox_element_type.count()):
                if self.ui.comboBox_element_type.itemText(i) == default_element_type:
                    self.ui.comboBox_element_type.setCurrentIndex(i)
                    break
            
            # 设置默认质量参数
            quality_settings = default_settings.get("quality_settings", {})
            self.ui.doubleSpinBox_skewness.setValue(quality_settings.get("skewness", 0.9))
            self.ui.doubleSpinBox_aspect_ratio.setValue(quality_settings.get("aspect_ratio", 20.0))
            self.ui.spinBox_smoothing.setValue(quality_settings.get("smoothing_iterations", 3))
            
            # 设置默认高级选项
            self.ui.checkBox_auto_sizing.setChecked(quality_settings.get("auto_sizing", True))
            self.ui.checkBox_capture_curvature.setChecked(quality_settings.get("capture_curvature", True))
            self.ui.checkBox_capture_proximity.setChecked(quality_settings.get("capture_proximity", False))
            
        except Exception as e:
            logger.warning(f"加载默认配置失败，使用硬编码默认值: {str(e)}")
            # 使用硬编码默认值
            self.ui.lineEdit_mesh_name.setText("新网格")
            self.ui.doubleSpinBox_mesh_size.setValue(10.0)
            self.ui.doubleSpinBox_skewness.setValue(0.9)
            self.ui.doubleSpinBox_aspect_ratio.setValue(20.0)
            self.ui.spinBox_smoothing.setValue(3)
            self.ui.checkBox_auto_sizing.setChecked(True)
            self.ui.checkBox_capture_curvature.setChecked(True)
            self.ui.checkBox_capture_proximity.setChecked(False)
        
        logger.debug("默认值加载完成")
    
    def _load_parameter_data(self, mesh_parameter: MeshParameter):
        """加载网格参数数据到界面
        
        Args:
            mesh_parameter: 网格参数对象
        """
        try:
            # 基本参数
            self.ui.lineEdit_mesh_name.setText(mesh_parameter.name)
            self.ui.doubleSpinBox_mesh_size.setValue(mesh_parameter.size)
            
            # 设置单元类型
            for i in range(self.ui.comboBox_element_type.count()):
                if self.ui.comboBox_element_type.itemData(i) == mesh_parameter.element_type:
                    self.ui.comboBox_element_type.setCurrentIndex(i)
                    break
            
            # 质量设置
            quality = mesh_parameter.quality_settings
            self.ui.doubleSpinBox_skewness.setValue(quality.skewness)
            self.ui.doubleSpinBox_aspect_ratio.setValue(quality.aspect_ratio)
            self.ui.spinBox_smoothing.setValue(quality.smoothing_iterations)
            
            # 高级设置
            self.ui.checkBox_auto_sizing.setChecked(quality.auto_sizing)
            self.ui.checkBox_capture_curvature.setChecked(quality.capture_curvature)
            self.ui.checkBox_capture_proximity.setChecked(quality.capture_proximity)
            
            logger.debug(f"网格参数数据加载完成: {mesh_parameter.name}")
            
        except Exception as e:
            logger.error(f"加载网格参数数据失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "警告", f"加载网格参数数据失败: {str(e)}")
    
    def _validate_input(self):
        """验证输入数据"""
        try:
            # 创建临时参数对象进行验证
            temp_parameter = self._create_parameter_from_ui()
            errors = temp_parameter.validate()
            
            # 更新确定按钮状态
            self.ui.btn_ok.setEnabled(len(errors) == 0)
            
            # 如果有错误，可以在这里显示提示（可选）
            if errors:
                logger.debug(f"输入验证失败: {'; '.join(errors)}")
            
        except Exception as e:
            logger.debug(f"输入验证异常: {str(e)}")
            self.ui.btn_ok.setEnabled(False)
    
    def _create_parameter_from_ui(self) -> MeshParameter:
        """从UI界面创建网格参数对象
        
        Returns:
            MeshParameter: 网格参数对象
        """
        # 创建网格参数对象
        if self._is_edit_mode:
            mesh_param = MeshParameter()
            mesh_param.id = self._original_parameter.id  # 保持原有ID
        else:
            mesh_param = MeshParameter()
        
        # 基本参数
        mesh_param.name = self.ui.lineEdit_mesh_name.text().strip()
        mesh_param.size = self.ui.doubleSpinBox_mesh_size.value()
        mesh_param.element_type = self.ui.comboBox_element_type.currentData()
        
        # 质量设置
        mesh_param.quality_settings = MeshQualitySettings(
            skewness=self.ui.doubleSpinBox_skewness.value(),
            aspect_ratio=self.ui.doubleSpinBox_aspect_ratio.value(),
            smoothing_iterations=self.ui.spinBox_smoothing.value(),
            auto_sizing=self.ui.checkBox_auto_sizing.isChecked(),
            capture_curvature=self.ui.checkBox_capture_curvature.isChecked(),
            capture_proximity=self.ui.checkBox_capture_proximity.isChecked()
        )
        
        return mesh_param
    
    def _on_ok_clicked(self):
        """确定按钮点击处理"""
        try:
            # 创建网格参数对象
            mesh_parameter = self._create_parameter_from_ui()
            
            # 验证参数
            errors = mesh_parameter.validate()
            if errors:
                error_msg = "参数验证失败:\n" + "\n".join(errors)
                QMessageBox.warning(self, "参数错误", error_msg)
                return
            
            # 发出信号并关闭对话框
            self.parameter_accepted.emit(mesh_parameter)
            self.accept()
            
            logger.info(f"网格参数确认: {mesh_parameter.name}")
            
        except Exception as e:
            logger.error(f"确认网格参数失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"确认网格参数失败: {str(e)}")
    
    def _on_reset_clicked(self):
        """重置按钮点击处理"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有参数吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self._is_edit_mode:
                self._load_parameter_data(self._original_parameter)
            else:
                self._load_default_values()
            
            logger.debug("参数已重置")
    
    def get_mesh_parameter(self) -> Optional[MeshParameter]:
        """获取网格参数对象（用于外部调用）
        
        Returns:
            Optional[MeshParameter]: 网格参数对象，如果验证失败则返回None
        """
        try:
            mesh_parameter = self._create_parameter_from_ui()
            errors = mesh_parameter.validate()
            if errors:
                return None
            return mesh_parameter
        except Exception:
            return None
