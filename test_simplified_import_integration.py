"""
测试简化的导入功能集成

验证简化后的导入功能是否正确工作：
1. 简化的文件选择对话框
2. 导入数据集成到现有列表
3. 混合数据源的对比显示

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simplified_data_manager():
    """测试简化的数据管理器"""
    print("🧪 测试简化的数据管理器...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建数据管理器
        data_manager = ModalDataManager("test_simplified.pkl")
        
        # 测试导入功能
        test_files = [
            "test_data/reference_models.json",
            "test_data/benchmark_models.csv"
        ]
        
        import_count = 0
        for file_path in test_files:
            if os.path.exists(file_path):
                success = data_manager.import_from_file(file_path)
                if success:
                    print(f"  ✅ 导入成功: {os.path.basename(file_path)}")
                    import_count += 1
                else:
                    print(f"  ❌ 导入失败: {os.path.basename(file_path)}")
            else:
                print(f"  ⚠️ 文件不存在: {file_path}")
        
        # 显示导入结果
        imported_results = data_manager.get_imported_results()
        print(f"  📊 总导入记录: {len(imported_results)}")
        
        for i, result in enumerate(imported_results[:3]):
            print(f"    {i+1}. {result.name} - 尺寸: {result.size}mm - 模态: {len(result.frequencies)}")
        
        return import_count > 0
        
    except Exception as e:
        print(f"  ❌ 数据管理器测试失败: {str(e)}")
        return False

def test_mixed_data_chart():
    """测试混合数据源的图表显示"""
    print("\n📊 测试混合数据源图表...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # 无GUI后端
        
        from ui.components.modal_data_manager import ModalDataManager
        from ui.components.modal_chart_widget import ModalChartWidget
        
        # 创建数据管理器并导入数据
        data_manager = ModalDataManager("test_mixed_chart.pkl")
        
        if os.path.exists("test_data/reference_models.json"):
            data_manager.import_from_file("test_data/reference_models.json")
        
        # 创建图表组件
        chart_widget = ModalChartWidget(data_manager=data_manager)
        
        # 模拟混合数据（当前计算结果 + 导入结果）
        mixed_data = [
            # 当前计算结果
            {
                'name': '[当前] Fine Mesh',
                'size': 1.0,
                'frequencies': [42.0, 75.0, 107.5, 144.0, 187.5],
                'node_count': 15000,
                'element_count': 12000,
                'source': 'current'
            },
            {
                'name': '[当前] Medium Mesh',
                'size': 2.0,
                'frequencies': [41.5, 74.2, 106.8, 142.9, 185.3],
                'node_count': 8000,
                'element_count': 6400,
                'source': 'current'
            },
            # 导入结果
            {
                'name': '[导入] Reference Model',
                'size': 1.5,
                'frequencies': [41.8, 74.6, 107.1, 143.5, 186.4],
                'node_count': 12000,
                'element_count': 9500,
                'source': 'imported'
            }
        ]
        
        # 测试三种图表类型
        chart_types = ["frequency_comparison", "mode_distribution", "mesh_convergence"]
        
        success_count = 0
        for chart_type in chart_types:
            try:
                chart_widget.update_chart(chart_type, mixed_data)
                
                filename = f"test_simplified_{chart_type}.png"
                chart_widget.save_chart(filename, dpi=150)
                
                if os.path.exists(filename):
                    file_size = os.path.getsize(filename)
                    print(f"  ✅ {chart_type}: {file_size:,} 字节")
                    success_count += 1
                else:
                    print(f"  ❌ {chart_type}: 文件未生成")
                    
            except Exception as e:
                print(f"  ❌ {chart_type}: {str(e)}")
        
        return success_count == len(chart_types)
        
    except Exception as e:
        print(f"  ❌ 混合数据图表测试失败: {str(e)}")
        return False

def test_list_integration():
    """测试列表集成功能"""
    print("\n📋 测试列表集成功能...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建数据管理器
        data_manager = ModalDataManager("test_list_integration.pkl")
        
        # 导入测试数据
        if os.path.exists("test_data/convergence_study.json"):
            success = data_manager.import_from_file("test_data/convergence_study.json")
            print(f"  导入收敛性研究数据: {'✅ 成功' if success else '❌ 失败'}")
        
        # 模拟当前计算结果
        current_results = [
            {
                'id': 'mesh_001',
                'name': 'Current Fine',
                'size': 1.0,
                'frequencies': [42.0, 75.0, 107.5],
                'node_count': 15000,
                'element_count': 12000
            },
            {
                'id': 'mesh_002', 
                'name': 'Current Medium',
                'size': 2.0,
                'frequencies': [41.5, 74.2, 106.8],
                'node_count': 8000,
                'element_count': 6400
            }
        ]
        
        # 模拟列表项创建
        list_items = []
        
        # 添加当前结果
        for result in current_results:
            item_data = {
                'display_text': f"[当前] {result['name']}\n尺寸: {result['size']:.2f}mm | 模态数: {len(result['frequencies'])}",
                'item_id': f"current_{result['id']}",
                'data_type': 'current',
                'background_color': 'light_blue'
            }
            list_items.append(item_data)
        
        # 添加导入结果
        imported_results = data_manager.get_imported_results()
        for i, result in enumerate(imported_results):
            item_data = {
                'display_text': f"[导入] {result.name}\n尺寸: {result.size:.2f}mm | 模态数: {len(result.frequencies)}",
                'item_id': f"imported_{i}",
                'data_type': 'imported',
                'background_color': 'light_green'
            }
            list_items.append(item_data)
        
        print(f"  📊 列表项统计:")
        print(f"    当前结果: {len(current_results)} 项")
        print(f"    导入结果: {len(imported_results)} 项")
        print(f"    总计: {len(list_items)} 项")
        
        # 显示前几项
        for i, item in enumerate(list_items[:5]):
            print(f"    {i+1}. [{item['data_type']}] {item['display_text'].split('\\n')[0]}")
        
        if len(list_items) > 5:
            print(f"    ... 还有 {len(list_items) - 5} 项")
        
        return len(list_items) > 0
        
    except Exception as e:
        print(f"  ❌ 列表集成测试失败: {str(e)}")
        return False

def create_simplified_usage_guide():
    """创建简化使用指南"""
    print("\n📋 创建简化使用指南...")
    
    guide = """
# 简化的模态结果导入对比功能使用指南

## 功能概述

经过简化设计，模态结果导入对比功能现在更加直观和易用：

### 🎯 主要改进

1. **简化导入流程**
   - 移除了复杂的导入管理界面
   - 点击"导入结果"直接打开文件选择对话框
   - 选择文件后自动导入，无需额外操作

2. **统一的对比列表**
   - 导入的结果直接添加到"选择对比网格"列表中
   - 当前计算结果和导入结果在同一个列表中显示
   - 用颜色和标签清晰区分数据来源

3. **无缝的对比工作流程**
   - 在统一列表中选择要对比的项目
   - 支持同时选择当前结果和导入结果
   - 一键生成包含所有选中数据的对比图表

## 🚀 使用步骤

### 第一步：导入外部结果
```
1. 在网格管理界面切换到"模态结果对比"标签页
2. 点击紫色的"导入结果"按钮
3. 在文件选择对话框中选择要导入的文件
   - 支持多选：可同时选择多个文件
   - 支持格式：JSON、CSV、TXT
4. 点击"打开"，系统自动导入所有选中文件
5. 导入完成后会显示成功消息和统计信息
```

### 第二步：选择对比数据
```
1. 在"选择对比网格"列表中查看所有可用数据：
   - [当前] 标签：当前计算的网格结果（浅蓝色背景）
   - [导入] 标签：导入的外部结果（浅绿色背景）
2. 使用Ctrl+点击选择多个要对比的项目
3. 可以同时选择当前结果和导入结果
```

### 第三步：生成对比图表
```
1. 选择图表类型：
   - 频率对比图：柱状图显示各模态频率对比
   - 模态分布图：堆叠图显示频率分布
   - 网格收敛性分析：折线图显示收敛趋势
2. 点击"更新图表"按钮
3. 在右侧查看生成的对比图表
```

### 第四步：保存和导出
```
1. 点击"保存图表"导出高质量图表文件
2. 点击"导出结果"保存数据文件
```

## 📊 数据格式要求

导入的数据文件必须包含以下字段：

### JSON格式示例
```json
{
  "name": "Reference Model",
  "size": 1.5,
  "frequencies": [42.1, 75.3, 107.8, 144.2, 188.6],
  "node_count": 12000,
  "element_count": 9500,
  "description": "参考模型描述"
}
```

### CSV格式示例
```csv
name,size,frequencies,node_count,element_count,description
"Model A",1.0,"[41.5, 74.8, 107.2]",15000,12000,"模型A描述"
```

### 必需字段
- **name**: 网格名称
- **size**: 网格尺寸 (mm)
- **frequencies**: 模态频率数组 (Hz)
- **node_count**: 节点数量
- **element_count**: 单元数量

## 🎨 界面标识

### 列表项标识
- **[当前]** + 浅蓝色背景：当前计算的网格结果
- **[导入]** + 浅绿色背景：导入的外部结果

### 图表标识
- **实心柱状图/线条**：当前计算结果
- **带边框的柱状图/虚线**：导入的外部结果
- **图例说明**：自动显示数据来源标识

## ✨ 使用技巧

### 1. 批量导入
- 在文件选择对话框中使用Ctrl+点击选择多个文件
- 系统会依次导入所有选中的文件
- 导入完成后显示成功/失败统计

### 2. 数据持久化
- 导入的数据自动保存到本地
- 程序重启后导入的结果仍然可用
- 无需重复导入相同的数据

### 3. 对比分析
- 建议同时选择2-5个结果进行对比
- 网格收敛性分析适合选择不同尺寸的网格
- 频率对比图适合比较相同尺寸的不同模型

### 4. 结果验证
- 与实验数据对比验证仿真精度
- 与参考模型对比验证方法正确性
- 与不同软件结果对比验证一致性

## 🎉 预期效果

使用简化的导入对比功能，您将获得：

1. **更简单的操作流程**：3步完成导入对比
2. **更直观的数据管理**：统一列表显示所有数据
3. **更清晰的对比效果**：自动区分数据来源
4. **更高效的分析工作**：无缝集成到现有工作流程

这个简化设计让模态结果对比分析变得更加高效和用户友好！
"""
    
    try:
        with open("simplified_import_usage_guide.md", "w", encoding="utf-8") as f:
            f.write(guide)
        print("  ✅ 简化使用指南已保存: simplified_import_usage_guide.md")
        return True
    except Exception as e:
        print(f"  ❌ 使用指南创建失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🎯 简化的导入功能集成测试")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 测试各个组件
    data_manager_ok = test_simplified_data_manager()
    chart_ok = test_mixed_data_chart()
    list_ok = test_list_integration()
    guide_ok = create_simplified_usage_guide()
    
    print("\n" + "=" * 70)
    print("📋 简化功能测试结果:")
    print(f"数据管理器: {'✅ 通过' if data_manager_ok else '❌ 失败'}")
    print(f"混合数据图表: {'✅ 通过' if chart_ok else '❌ 失败'}")
    print(f"列表集成: {'✅ 通过' if list_ok else '❌ 失败'}")
    print(f"使用指南: {'✅ 生成' if guide_ok else '❌ 失败'}")
    
    if all([data_manager_ok, chart_ok, list_ok]):
        print("\n🎉 简化的导入功能集成成功！")
        print("\n✨ 主要改进:")
        print("  ✅ 简化导入流程 - 直接文件选择对话框")
        print("  ✅ 统一对比列表 - 当前结果和导入结果在同一列表")
        print("  ✅ 清晰数据标识 - 颜色和标签区分数据来源")
        print("  ✅ 无缝工作流程 - 选择数据 → 选择图表 → 生成对比")
        print("  ✅ 数据持久化 - 程序重启后仍可使用")
        
        print("\n🎯 用户体验:")
        print("  • 3步完成导入对比：导入 → 选择 → 对比")
        print("  • 直观的数据管理：统一列表显示")
        print("  • 清晰的视觉区分：当前vs导入结果")
        print("  • 高效的分析流程：无需复杂操作")
        
        print("\n📁 生成的文件:")
        print("  • test_simplified_*.png - 简化版对比图表")
        print("  • simplified_import_usage_guide.md - 详细使用指南")
        
    else:
        print("\n⚠️ 部分功能测试失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
