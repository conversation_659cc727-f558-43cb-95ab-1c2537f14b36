#!/usr/bin/env python3
"""
帮助文档章节选择对话框

提供一个简单的对话框让用户选择要打开的帮助文档章节
"""

import logging
from typing import Optional, Dict

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QMessageBox, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from core.i18n_manager import tr

logger = logging.getLogger(__name__)


class HelpSectionDialog(QDialog):
    """帮助文档章节选择对话框"""
    
    # 信号定义
    section_selected = Signal(str)  # 章节被选择
    
    def __init__(self, available_sections: Dict[str, str], parent=None):
        """初始化章节选择对话框
        
        Args:
            available_sections: 可用章节字典 {section_key: section_name}
            parent: 父窗口
        """
        super().__init__(parent)
        
        self.available_sections = available_sections
        self.selected_section = None
        
        self._setup_ui()
        self._populate_sections()
        self._connect_signals()
        
        logger.debug("HelpSectionDialog 初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setWindowTitle(tr("选择帮助文档章节", "HelpSectionDialog"))
        self.setModal(True)
        self.resize(400, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(12)
        
        # 标题标签
        title_label = QLabel(tr("请选择要查看的帮助文档章节：", "HelpSectionDialog"))
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; margin-bottom: 8px;")
        main_layout.addWidget(title_label)
        
        # 章节列表
        self.section_list = QListWidget()
        self.section_list.setAlternatingRowColors(True)
        self.section_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #0078d4;
                selection-color: white;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:hover {
                background-color: #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
        """)
        main_layout.addWidget(self.section_list)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        main_layout.addWidget(separator)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        # 打开按钮
        self.open_button = QPushButton(tr("打开", "HelpSectionDialog"))
        self.open_button.setDefault(True)
        self.open_button.setEnabled(False)
        self.open_button.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        
        # 取消按钮
        self.cancel_button = QPushButton(tr("取消", "HelpSectionDialog"))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                color: #333;
                border: 1px solid #ccc;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.open_button)
        
        main_layout.addLayout(button_layout)
        
        # 设置窗口图标
        self.setWindowIcon(self.style().standardIcon(self.style().StandardPixmap.SP_FileDialogInfoView))
    
    def _populate_sections(self) -> None:
        """填充章节列表"""
        try:
            # 按优先级排序章节
            section_order = [
                'overview',
                'main',
                'mesh', 
                'pre',
                'connection',
                'analysis',
                'constrain',
                'result',
                'vibration',
                'api',
                'thread_safety',
                'i18n',
                'encoding',
                'troubleshooting'
            ]
            
            # 添加有序的章节
            for section_key in section_order:
                if section_key in self.available_sections:
                    section_name = self.available_sections[section_key]
                    item = QListWidgetItem(section_name)
                    item.setData(Qt.ItemDataRole.UserRole, section_key)
                    
                    # 为不同类型的章节设置图标
                    icon = self._get_section_icon(section_key)
                    if icon:
                        item.setIcon(icon)
                    
                    self.section_list.addItem(item)
            
            # 添加剩余的章节（如果有）
            for section_key, section_name in self.available_sections.items():
                if section_key not in section_order:
                    item = QListWidgetItem(section_name)
                    item.setData(Qt.ItemDataRole.UserRole, section_key)
                    self.section_list.addItem(item)
            
            # 默认选择第一项
            if self.section_list.count() > 0:
                self.section_list.setCurrentRow(0)
                
        except Exception as e:
            logger.error(f"填充章节列表失败: {e}")
    
    def _get_section_icon(self, section_key: str) -> Optional[QIcon]:
        """获取章节图标
        
        Args:
            section_key: 章节键
            
        Returns:
            QIcon: 章节图标，如果没有则返回None
        """
        icon_mapping = {
            'overview': self.style().StandardPixmap.SP_ComputerIcon,
            'main': self.style().StandardPixmap.SP_ComputerIcon,
            'mesh': self.style().StandardPixmap.SP_FileDialogDetailedView,
            'pre': self.style().StandardPixmap.SP_FileDialogListView,
            'connection': self.style().StandardPixmap.SP_DriveNetIcon,
            'analysis': self.style().StandardPixmap.SP_FileDialogInfoView,
            'constrain': self.style().StandardPixmap.SP_DialogApplyButton,
            'result': self.style().StandardPixmap.SP_FileDialogDetailedView,
            'vibration': self.style().StandardPixmap.SP_MediaPlay,
            'api': self.style().StandardPixmap.SP_CommandLink,
            'thread_safety': self.style().StandardPixmap.SP_DialogOkButton,
            'i18n': self.style().StandardPixmap.SP_BrowserReload,
            'encoding': self.style().StandardPixmap.SP_FileDialogContentsView,
            'troubleshooting': self.style().StandardPixmap.SP_MessageBoxWarning
        }
        
        standard_pixmap = icon_mapping.get(section_key)
        if standard_pixmap:
            return self.style().standardIcon(standard_pixmap)
        
        return None
    
    def _connect_signals(self) -> None:
        """连接信号"""
        try:
            # 列表选择变化
            self.section_list.itemSelectionChanged.connect(self._on_selection_changed)
            
            # 双击打开
            self.section_list.itemDoubleClicked.connect(self._on_item_double_clicked)
            
            # 按钮点击
            self.open_button.clicked.connect(self._on_open_clicked)
            self.cancel_button.clicked.connect(self.reject)
            
        except Exception as e:
            logger.error(f"连接信号失败: {e}")
    
    def _on_selection_changed(self) -> None:
        """处理选择变化"""
        try:
            has_selection = len(self.section_list.selectedItems()) > 0
            self.open_button.setEnabled(has_selection)
            
        except Exception as e:
            logger.error(f"处理选择变化失败: {e}")
    
    def _on_item_double_clicked(self, item: QListWidgetItem) -> None:
        """处理项目双击"""
        try:
            if item:
                self._open_selected_section()
                
        except Exception as e:
            logger.error(f"处理项目双击失败: {e}")
    
    def _on_open_clicked(self) -> None:
        """处理打开按钮点击"""
        try:
            self._open_selected_section()
            
        except Exception as e:
            logger.error(f"处理打开按钮点击失败: {e}")
    
    def _open_selected_section(self) -> None:
        """打开选中的章节"""
        try:
            current_item = self.section_list.currentItem()
            if current_item:
                section_key = current_item.data(Qt.ItemDataRole.UserRole)
                self.selected_section = section_key
                self.section_selected.emit(section_key)
                self.accept()
            else:
                QMessageBox.warning(
                    self,
                    tr("未选择章节", "HelpSectionDialog"),
                    tr("请先选择一个帮助文档章节。", "HelpSectionDialog")
                )
                
        except Exception as e:
            logger.error(f"打开选中章节失败: {e}")
    
    def get_selected_section(self) -> Optional[str]:
        """获取选中的章节
        
        Returns:
            str: 选中的章节键，如果没有选择则返回None
        """
        return self.selected_section
    
    @staticmethod
    def select_help_section(available_sections: Dict[str, str], parent=None) -> Optional[str]:
        """静态方法：显示章节选择对话框
        
        Args:
            available_sections: 可用章节字典
            parent: 父窗口
            
        Returns:
            str: 选中的章节键，如果取消则返回None
        """
        try:
            dialog = HelpSectionDialog(available_sections, parent)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                return dialog.get_selected_section()
            return None
            
        except Exception as e:
            logger.error(f"显示章节选择对话框失败: {e}")
            return None
