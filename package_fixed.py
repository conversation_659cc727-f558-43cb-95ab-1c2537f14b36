#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复日志路径问题后的打包脚本

此脚本解决了打包后程序无法找到logs目录的问题。

使用方法:
    python package_fixed.py
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def ensure_logs_directory():
    """确保logs目录存在并包含必要的文件"""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 创建必要的日志文件（空文件）
    log_files = [
        "app.log",
        "error.log", 
        "performance.log",
        "resource_manager.log",
        "startup_config.log",
        "exceptions.log"
    ]
    
    for log_file in log_files:
        log_path = logs_dir / log_file
        if not log_path.exists():
            log_path.touch()
            print(f"✅ 创建日志文件: {log_path}")
    
    print(f"✅ 日志目录准备完成: {logs_dir}")

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['dist', 'build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)

def run_pyinstaller():
    """运行PyInstaller"""
    print("📦 开始PyInstaller打包...")
    
    # 使用简化的spec文件
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "qt_new_simple.spec"]
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    print("⏳ 打包中，请稍候...")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 打包成功!")
        
        # 检查输出文件
        exe_path = Path("dist/vibration_transfer/振动传递计算软件.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📊 可执行文件大小: {size_mb:.2f} MB")
            print(f"📁 输出路径: {exe_path.parent}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败!")
        print("标准输出:")
        print(e.stdout)
        print("标准错误:")
        print(e.stderr)
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def verify_package():
    """验证打包结果"""
    print("🔍 验证打包结果...")
    
    dist_dir = Path("dist/vibration_transfer")
    if not dist_dir.exists():
        print(f"❌ 输出目录不存在: {dist_dir}")
        return False
    
    exe_file = dist_dir / "振动传递计算软件.exe"
    if not exe_file.exists():
        print(f"❌ 可执行文件不存在: {exe_file}")
        return False
    
    # 检查关键目录
    required_dirs = ["assets", "help", "config", "translations", "logs", "originscript", "ui"]
    missing_dirs = []
    
    for dir_name in required_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists():
            print(f"✅ 目录存在: {dir_name}")
        else:
            missing_dirs.append(dir_name)
            print(f"⚠️ 目录缺失: {dir_name}")
    
    if missing_dirs:
        print(f"⚠️ 缺失目录: {', '.join(missing_dirs)}")
    
    print("✅ 打包结果验证完成")
    return True

def main():
    """主函数"""
    print("🚀 振动传递计算软件 - 修复版打包工具")
    print("=" * 50)
    
    try:
        # 1. 确保logs目录存在
        ensure_logs_directory()
        
        # 2. 清理旧文件
        clean_build_dirs()
        
        # 3. 执行打包
        if not run_pyinstaller():
            print("\n❌ 打包失败!")
            sys.exit(1)
        
        # 4. 验证结果
        if not verify_package():
            print("\n❌ 打包验证失败!")
            sys.exit(1)
        
        print("\n🎉 打包完成!")
        print("💡 提示: 可执行文件位于 dist/vibration_transfer/ 目录")
        print("💡 建议: 在目标机器上测试程序运行")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程发生异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
