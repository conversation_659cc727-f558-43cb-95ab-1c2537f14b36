# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh_simple.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QComboBox,
    QGroupBox, QHBoxLayout, QHeaderView, QLabel, 
    QMainWindow, QPushButton, QSizePolicy, QSpacerItem, 
    QTableWidget, QTableWidgetItem, QTextEdit, QVBoxLayout,
    QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1200, 800)
        MainWindow.setMinimumSize(QSize(1200, 800))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(10)
        MainWindow.setFont(font)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_main = QVBoxLayout(self.centralwidget)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        self.label_title = QLabel(self.centralwidget)
        self.label_title.setObjectName(u"label_title")
        self.label_title.setMinimumSize(QSize(0, 60))
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei UI"])
        font1.setPointSize(24)
        font1.setBold(True)
        self.label_title.setFont(font1)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.verticalLayout_main.addWidget(self.label_title)

        # 主要内容区域
        self.horizontalLayout_main = QHBoxLayout()
        self.horizontalLayout_main.setSpacing(15)
        self.horizontalLayout_main.setObjectName(u"horizontalLayout_main")

        # 左侧面板 - 网格参数管理
        self.widget_left_panel = QWidget(self.centralwidget)
        self.widget_left_panel.setObjectName(u"widget_left_panel")
        self.widget_left_panel.setMinimumSize(QSize(600, 0))
        self.widget_left_panel.setMaximumSize(QSize(650, 16777215))
        self.verticalLayout_left = QVBoxLayout(self.widget_left_panel)
        self.verticalLayout_left.setObjectName(u"verticalLayout_left")
        
        # 网格参数组
        self.groupBox_mesh_params = QGroupBox(self.widget_left_panel)
        self.groupBox_mesh_params.setObjectName(u"groupBox_mesh_params")
        self.verticalLayout_params = QVBoxLayout(self.groupBox_mesh_params)
        self.verticalLayout_params.setObjectName(u"verticalLayout_params")
        
        # 工具栏
        self.horizontalLayout_toolbar = QHBoxLayout()
        self.horizontalLayout_toolbar.setObjectName(u"horizontalLayout_toolbar")
        
        self.btn_add_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_add_mesh.setObjectName(u"btn_add_mesh")
        self.btn_add_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_add_mesh)

        self.btn_import_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_import_mesh.setObjectName(u"btn_import_mesh")
        self.btn_import_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_import_mesh)

        self.btn_export_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_export_mesh.setObjectName(u"btn_export_mesh")
        self.btn_export_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_export_mesh)

        self.horizontalSpacer_toolbar = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_toolbar.addItem(self.horizontalSpacer_toolbar)
        self.verticalLayout_params.addLayout(self.horizontalLayout_toolbar)

        # 网格参数表格
        self.tableWidget_mesh_params = QTableWidget(self.groupBox_mesh_params)
        if (self.tableWidget_mesh_params.columnCount() < 6):
            self.tableWidget_mesh_params.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.tableWidget_mesh_params.setObjectName(u"tableWidget_mesh_params")
        self.tableWidget_mesh_params.setMinimumSize(QSize(0, 300))
        self.tableWidget_mesh_params.setAlternatingRowColors(True)
        self.tableWidget_mesh_params.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.tableWidget_mesh_params.setSortingEnabled(True)
        self.verticalLayout_params.addWidget(self.tableWidget_mesh_params)

        self.verticalLayout_left.addWidget(self.groupBox_mesh_params)
        self.horizontalLayout_main.addWidget(self.widget_left_panel)

        # 右侧面板 - 网格预览和操作
        self.widget_right_panel = QWidget(self.centralwidget)
        self.widget_right_panel.setObjectName(u"widget_right_panel")
        self.verticalLayout_right = QVBoxLayout(self.widget_right_panel)
        self.verticalLayout_right.setObjectName(u"verticalLayout_right")

        # 网格预览组
        self.groupBox_mesh_preview = QGroupBox(self.widget_right_panel)
        self.groupBox_mesh_preview.setObjectName(u"groupBox_mesh_preview")
        self.verticalLayout_preview = QVBoxLayout(self.groupBox_mesh_preview)
        self.verticalLayout_preview.setObjectName(u"verticalLayout_preview")

        # 网格选择下拉框
        self.comboBox_mesh_select = QComboBox(self.groupBox_mesh_preview)
        self.comboBox_mesh_select.setObjectName(u"comboBox_mesh_select")
        self.comboBox_mesh_select.setMinimumSize(QSize(0, 35))
        self.verticalLayout_preview.addWidget(self.comboBox_mesh_select)

        # 网格信息显示
        self.textEdit_mesh_info = QTextEdit(self.groupBox_mesh_preview)
        self.textEdit_mesh_info.setObjectName(u"textEdit_mesh_info")
        self.textEdit_mesh_info.setMaximumSize(QSize(16777215, 200))
        self.textEdit_mesh_info.setReadOnly(True)
        self.verticalLayout_preview.addWidget(self.textEdit_mesh_info)

        self.verticalLayout_right.addWidget(self.groupBox_mesh_preview)

        # 操作按钮组
        self.groupBox_operations = QGroupBox(self.widget_right_panel)
        self.groupBox_operations.setObjectName(u"groupBox_operations")
        self.verticalLayout_operations = QVBoxLayout(self.groupBox_operations)
        self.verticalLayout_operations.setObjectName(u"verticalLayout_operations")

        # 网格生成按钮
        self.btn_generate_mesh = QPushButton(self.groupBox_operations)
        self.btn_generate_mesh.setObjectName(u"btn_generate_mesh")
        self.btn_generate_mesh.setMinimumSize(QSize(0, 45))
        self.verticalLayout_operations.addWidget(self.btn_generate_mesh)

        # 模态计算按钮
        self.btn_modal_analysis = QPushButton(self.groupBox_operations)
        self.btn_modal_analysis.setObjectName(u"btn_modal_analysis")
        self.btn_modal_analysis.setMinimumSize(QSize(0, 45))
        self.verticalLayout_operations.addWidget(self.btn_modal_analysis)

        # 导出结果按钮
        self.btn_export_results = QPushButton(self.groupBox_operations)
        self.btn_export_results.setObjectName(u"btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(0, 45))
        self.verticalLayout_operations.addWidget(self.btn_export_results)

        self.verticalLayout_right.addWidget(self.groupBox_operations)

        # 添加垂直空间
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.verticalLayout_right.addItem(self.verticalSpacer)

        self.horizontalLayout_main.addWidget(self.widget_right_panel)
        self.verticalLayout_main.addLayout(self.horizontalLayout_main)

        # 导航按钮
        self.horizontalLayout_navigation = QHBoxLayout()
        self.horizontalLayout_navigation.setObjectName(u"horizontalLayout_navigation")

        self.btn_main_menu = QPushButton(self.centralwidget)
        self.btn_main_menu.setObjectName(u"btn_main_menu")
        self.btn_main_menu.setMinimumSize(QSize(100, 40))
        self.horizontalLayout_navigation.addWidget(self.btn_main_menu)

        self.push_constrainui = QPushButton(self.centralwidget)
        self.push_constrainui.setObjectName(u"push_constrainui")
        self.push_constrainui.setMinimumSize(QSize(100, 40))
        self.horizontalLayout_navigation.addWidget(self.push_constrainui)

        self.horizontalSpacer_nav = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_nav)

        self.push_resultui = QPushButton(self.centralwidget)
        self.push_resultui.setObjectName(u"push_resultui")
        self.push_resultui.setMinimumSize(QSize(100, 40))
        self.horizontalLayout_navigation.addWidget(self.push_resultui)

        self.verticalLayout_main.addLayout(self.horizontalLayout_navigation)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"网格无关性验证", None))
        self.label_title.setText(QCoreApplication.translate("MainWindow", u"网格无关性验证系统", None))
        self.groupBox_mesh_params.setTitle(QCoreApplication.translate("MainWindow", u"网格参数管理", None))
        self.btn_add_mesh.setText(QCoreApplication.translate("MainWindow", u"添加网格", None))
        self.btn_import_mesh.setText(QCoreApplication.translate("MainWindow", u"导入配置", None))
        self.btn_export_mesh.setText(QCoreApplication.translate("MainWindow", u"导出配置", None))
        ___qtablewidgetitem = self.tableWidget_mesh_params.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"名称", None))
        ___qtablewidgetitem1 = self.tableWidget_mesh_params.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"尺寸(mm)", None))
        ___qtablewidgetitem2 = self.tableWidget_mesh_params.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"单元类型", None))
        ___qtablewidgetitem3 = self.tableWidget_mesh_params.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"状态", None))
        ___qtablewidgetitem4 = self.tableWidget_mesh_params.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"创建时间", None))
        ___qtablewidgetitem5 = self.tableWidget_mesh_params.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"操作", None))
        self.groupBox_mesh_preview.setTitle(QCoreApplication.translate("MainWindow", u"网格预览", None))
        self.groupBox_operations.setTitle(QCoreApplication.translate("MainWindow", u"操作", None))
        self.btn_generate_mesh.setText(QCoreApplication.translate("MainWindow", u"生成网格", None))
        self.btn_modal_analysis.setText(QCoreApplication.translate("MainWindow", u"模态分析", None))
        self.btn_export_results.setText(QCoreApplication.translate("MainWindow", u"导出结果", None))
        self.btn_main_menu.setText(QCoreApplication.translate("MainWindow", u"主菜单", None))
        self.push_constrainui.setText(QCoreApplication.translate("MainWindow", u"上一步", None))
        self.push_resultui.setText(QCoreApplication.translate("MainWindow", u"下一步", None))
    # retranslateUi
