<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1042</width>
    <height>491</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>4</number>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>Tab 1</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_10">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_24">
          <item>
           <widget class="QPushButton" name="pushButton_force">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>75</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>15</pointsize>
             </font>
            </property>
            <property name="text">
             <string>选择力文件夹</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="lineEdit_force">
            <property name="minimumSize">
             <size>
              <width>750</width>
              <height>60</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>15</pointsize>
             </font>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Orientation::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>488</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="plainTextEdit_force">
          <property name="minimumSize">
           <size>
            <width>1000</width>
            <height>250</height>
           </size>
          </property>
          <property name="undoRedoEnabled">
           <bool>true</bool>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_2">
       <attribute name="title">
        <string>Tab 2</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_8">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <widget class="QLabel" name="label_7">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>100</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>20</pointsize>
             </font>
            </property>
            <property name="text">
             <string>进口法兰</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <item>
             <widget class="QLabel" name="label">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>X Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="xcomponent_flange1">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="QLabel" name="label_2">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Y Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="ycomponent_flange1">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <item>
             <widget class="QLabel" name="label_3">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Z Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="zcomponent_flange1">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="QLabel" name="label_8">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>100</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>20</pointsize>
             </font>
            </property>
            <property name="text">
             <string>出口法兰</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <item>
             <widget class="QLabel" name="label_4">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>X Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="xcomponent_flange2">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <item>
             <widget class="QLabel" name="label_6">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Y Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="ycomponent_flange2">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_7">
            <item>
             <widget class="QLabel" name="label_5">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <property name="text">
               <string>Z Component</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignmentFlag::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="zcomponent_flange2">
              <property name="minimumSize">
               <size>
                <width>100</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Times New Roman</family>
                <pointsize>15</pointsize>
               </font>
              </property>
              <item>
               <property name="text">
                <string>Free</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Constant</string>
               </property>
              </item>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_3">
       <attribute name="title">
        <string>页</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_23">
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_8">
          <item>
           <widget class="QLabel" name="label_11">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>100</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>20</pointsize>
             </font>
            </property>
            <property name="text">
             <string>轴承1</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_21">
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_4">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <item>
                 <widget class="QLabel" name="label_12">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>X Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="xcomponent_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_10">
                <item>
                 <widget class="QLabel" name="label_10">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Y Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="ycomponent_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_11">
                <item>
                 <widget class="QLabel" name="label_9">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Z Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="zcomponent_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_5">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_12">
                <item>
                 <widget class="QLabel" name="label_13">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation X</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="xrotation_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_13">
                <item>
                 <widget class="QLabel" name="label_14">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation Y</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="yrotation_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_14">
                <item>
                 <widget class="QLabel" name="label_15">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation Z</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="zrotation_bearing1">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_9">
          <item>
           <widget class="QLabel" name="label_16">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>100</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>20</pointsize>
             </font>
            </property>
            <property name="text">
             <string>轴承2</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_22">
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_6">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_15">
                <item>
                 <widget class="QLabel" name="label_19">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>X Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="xcomponent_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_18">
                <item>
                 <widget class="QLabel" name="label_17">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Y Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="ycomponent_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_19">
                <item>
                 <widget class="QLabel" name="label_22">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Z Component</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="zcomponent_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
            <item>
             <layout class="QVBoxLayout" name="verticalLayout_7">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_16">
                <item>
                 <widget class="QLabel" name="label_20">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation X</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="xrotation_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_17">
                <item>
                 <widget class="QLabel" name="label_21">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation Y</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="yrotation_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_20">
                <item>
                 <widget class="QLabel" name="label_18">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>Rotation Z</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignmentFlag::AlignCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="zrotation_bearing2">
                  <property name="minimumSize">
                   <size>
                    <width>125</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Times New Roman</family>
                    <pointsize>15</pointsize>
                   </font>
                  </property>
                  <item>
                   <property name="text">
                    <string>Constant</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>Free</string>
                   </property>
                  </item>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_4">
       <attribute name="title">
        <string>页</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_11">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_25">
          <item>
           <widget class="QLabel" name="label_24">
            <property name="minimumSize">
             <size>
              <width>500</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>25</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Rotation speed</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="lineEdit_rotation_speed">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>25</pointsize>
             </font>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_26">
          <item>
           <widget class="QLabel" name="label_23">
            <property name="minimumSize">
             <size>
              <width>100</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>25</pointsize>
             </font>
            </property>
            <property name="text">
             <string>Rotation axis</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignmentFlag::AlignCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QComboBox" name="rotation_axis">
            <property name="minimumSize">
             <size>
              <width>400</width>
              <height>50</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>25</pointsize>
             </font>
            </property>
            <property name="layoutDirection">
             <enum>Qt::LayoutDirection::LeftToRight</enum>
            </property>
            <item>
             <property name="text">
              <string>z</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>y</string>
             </property>
            </item>
            <item>
             <property name="text">
              <string>x</string>
             </property>
            </item>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_5">
       <attribute name="title">
        <string>页</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_12">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_27">
          <item>
           <widget class="QPushButton" name="pushButton_point">
            <property name="minimumSize">
             <size>
              <width>200</width>
              <height>75</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>宋体</family>
              <pointsize>15</pointsize>
             </font>
            </property>
            <property name="text">
             <string>选择监测点</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLineEdit" name="lineEdit_point">
            <property name="minimumSize">
             <size>
              <width>750</width>
              <height>60</height>
             </size>
            </property>
            <property name="font">
             <font>
              <family>Times New Roman</family>
              <pointsize>15</pointsize>
             </font>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QPlainTextEdit" name="plainTextEdit_point">
          <property name="minimumSize">
           <size>
            <width>1000</width>
            <height>250</height>
           </size>
          </property>
          <property name="undoRedoEnabled">
           <bool>true</bool>
          </property>
          <property name="readOnly">
           <bool>true</bool>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="push_finish">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>完成设置</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_analysisui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>上一步(分析设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_resultui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>下一步(网格无关性验证)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_mainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
