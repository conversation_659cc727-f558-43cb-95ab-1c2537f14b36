<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编码处理技术指南 - 振动传递计算软件</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .code-block {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-green-600 to-teal-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-green-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🌐 编码处理技术指南
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-green-100">
                    多编码处理系统 | 中文字符完美显示解决方案
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-green-500 bg-opacity-20 text-green-100 text-sm font-semibold px-4 py-2 rounded-full border border-green-400">
                        🔤 UTF-8 | 🔄 智能转换 | 🛡️ 错误恢复 | 📊 实时监控
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">

        <!-- System Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🌐 多编码处理系统概述</h2>

            <div class="bg-gradient-to-r from-green-50 to-teal-50 p-6 rounded-lg mb-6">
                <p class="text-gray-700 text-lg leading-relaxed">
                    本软件采用先进的多编码处理系统，确保中文字符在各种环境下都能正确显示和处理。系统支持UTF-8、GBK、CP936等多种编码格式，并提供智能编码检测和转换机制。
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <span class="font-bold text-sm">UTF</span>
                    </div>
                    <h3 class="font-semibold text-blue-800 mb-2">统一编码标准</h3>
                    <p class="text-sm text-blue-600">系统内部统一使用UTF-8编码</p>
                </div>

                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                    </div>
                    <h3 class="font-semibold text-green-800 mb-2">多编码兼容</h3>
                    <p class="text-sm text-green-600">自动检测和处理多种编码格式</p>
                </div>

                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="font-semibold text-purple-800 mb-2">智能转换</h3>
                    <p class="text-sm text-purple-600">编码错误自动恢复和转换</p>
                </div>

                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                    <h3 class="font-semibold text-orange-800 mb-2">实时监控</h3>
                    <p class="text-sm text-orange-600">支持多编码日志文件的实时读取</p>
                </div>
            </div>
        </section>

        <!-- Technical Implementation -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🔧 技术实现原理</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">深入了解多编码处理系统的核心技术和实现机制</p>
            </div>

            <div class="space-y-6">
                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">📋</span>编码检测机制
                    </h3>
                    <p class="text-gray-600 mb-4">系统采用多层编码检测策略，确保准确识别文件编码格式：</p>
                    <div class="code-block">
                        <pre class="text-sm"><code>def detect_encoding(file_path):
    """智能编码检测"""
    encodings = ['utf-8', 'gbk', 'cp936', 'utf-16']

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
                return encoding, content
        except UnicodeDecodeError:
            continue

    # 使用容错模式
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        return 'utf-8', f.read()</code></pre>
                    </div>
                    <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                        <p class="text-sm text-blue-700"><strong>技术特点：</strong>按优先级尝试不同编码，失败时自动降级到容错模式，确保文件总能被正确读取。</p>
                    </div>
                </div>

                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🔄</span>实时日志监控
                    </h3>
                    <p class="text-gray-600 mb-4">LogMonitorThread实现多编码日志监控，确保实时性和准确性：</p>
                    <div class="code-block">
                        <pre class="text-sm"><code>class LogMonitorThread(QThread):
    def run(self):
        while self.running:
            try:
                # 尝试多种编码读取
                for encoding in ['utf-8', 'gbk', 'cp936']:
                    try:
                        with open(self.log_file, 'r',
                                encoding=encoding, errors='ignore') as f:
                            f.seek(self.last_position)
                            new_content = f.read()
                            if new_content:
                                self.log_updated.emit(new_content)
                            break
                    except UnicodeDecodeError:
                        continue
            except Exception as e:
                self.error_occurred.emit(str(e))</code></pre>
                    </div>
                    <div class="mt-4 p-4 bg-green-50 rounded-lg">
                        <p class="text-sm text-green-700"><strong>核心优势：</strong>多编码尝试机制确保日志文件总能被正确读取，实时监控保证信息的及时性。</p>
                    </div>
                </div>

                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">⚡</span>进程输出捕获
                    </h3>
                    <p class="text-gray-600 mb-4">增强的进程输出捕获机制，确保所有输出都能被正确处理：</p>
                    <div class="code-block">
                        <pre class="text-sm"><code>def capture_process_output(process, log_writer):
    """捕获进程输出并处理编码"""
    try:
        for line in iter(process.stdout.readline, ''):
            if not line:
                break

            # 处理编码问题
            try:
                clean_line = line.strip()
                if clean_line:
                    log_writer.write_line(clean_line)
            except UnicodeError:
                # 编码错误恢复
                safe_line = line.encode('utf-8', errors='ignore')
                safe_line = safe_line.decode('utf-8')
                log_writer.write_line(safe_line.strip())

    except Exception as e:
        log_writer.write_line(f"捕获输出时出错: {e}")</code></pre>
                    </div>
                    <div class="mt-4 p-4 bg-orange-50 rounded-lg">
                        <p class="text-sm text-orange-700"><strong>容错设计：</strong>即使遇到编码错误，系统也能通过错误恢复机制继续正常工作。</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Configuration and Optimization -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🛠️ 配置和优化</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">系统配置优化和性能调优指南</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8">
                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🔧</span>系统配置优化
                    </h3>

                    <div class="space-y-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-3">Windows系统设置</h4>
                            <ol class="space-y-2 text-sm text-blue-700">
                                <li class="flex items-start">
                                    <span class="bg-blue-200 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                                    <div>
                                        <strong>区域设置</strong>
                                        <ul class="mt-1 ml-4 space-y-1 text-xs">
                                            <li>• 控制面板 → 区域 → 管理</li>
                                            <li>• 更改系统区域设置为"中文(简体，中国)"</li>
                                            <li>• 勾选"Beta版: 使用Unicode UTF-8提供全球语言支持"</li>
                                        </ul>
                                    </div>
                                </li>
                                <li class="flex items-start">
                                    <span class="bg-blue-200 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                                    <div>
                                        <strong>命令行编码</strong>
                                        <ul class="mt-1 ml-4 space-y-1 text-xs">
                                            <li>• 运行 <code class="bg-blue-100 px-1 rounded">chcp 65001</code> 设置UTF-8编码</li>
                                            <li>• 在批处理文件中添加编码设置</li>
                                        </ul>
                                    </div>
                                </li>
                            </ol>
                        </div>

                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-3">应用程序配置</h4>
                            <ul class="space-y-2 text-sm text-green-700">
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    确保所有配置文件使用UTF-8编码保存
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    日志文件统一使用UTF-8格式
                                </li>
                                <li class="flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    数据库连接使用UTF-8字符集
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">📊</span>性能优化策略
                    </h3>

                    <div class="space-y-4">
                        <div class="flex items-start p-3 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-purple-800 text-sm">缓存机制</h4>
                                <p class="text-xs text-purple-600 mt-1">缓存编码检测结果，避免重复检测</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-indigo-50 rounded-lg">
                            <div class="w-8 h-8 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-indigo-800 text-sm">异步处理</h4>
                                <p class="text-xs text-indigo-600 mt-1">使用异步I/O处理大文件编码转换</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-pink-50 rounded-lg">
                            <div class="w-8 h-8 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-pink-800 text-sm">内存管理</h4>
                                <p class="text-xs text-pink-600 mt-1">及时释放编码转换过程中的临时对象</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-yellow-50 rounded-lg">
                            <div class="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-yellow-800 text-sm">错误恢复</h4>
                                <p class="text-xs text-yellow-600 mt-1">快速错误恢复机制，避免阻塞主线程</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Troubleshooting -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🔍 故障诊断和解决</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">常见编码问题的诊断方法和解决方案</p>
            </div>

            <div class="space-y-6">
                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-red-600 mb-6 flex items-center">
                        <span class="text-2xl mr-3">❌</span>常见编码问题
                    </h3>

                    <div class="space-y-6">
                        <!-- Problem 1 -->
                        <div class="border-l-4 border-red-400 bg-red-50 p-4 rounded-r-lg">
                            <h4 class="font-semibold text-red-800 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                中文显示乱码
                            </h4>

                            <div class="grid md:grid-cols-3 gap-4 text-sm">
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-red-700 mb-2">症状</h5>
                                    <p class="text-red-600">界面或日志中中文显示为问号或方块</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-red-700 mb-2">原因</h5>
                                    <p class="text-red-600">编码不匹配或字体不支持中文</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-red-700 mb-2">解决方案</h5>
                                    <ol class="text-red-600 space-y-1 text-xs">
                                        <li>1. 检查系统区域设置</li>
                                        <li>2. 确认应用程序编码配置</li>
                                        <li>3. 验证字体支持中文字符</li>
                                        <li>4. 使用编码检测工具诊断</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Problem 2 -->
                        <div class="border-l-4 border-orange-400 bg-orange-50 p-4 rounded-r-lg">
                            <h4 class="font-semibold text-orange-800 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                日志文件读取失败
                            </h4>

                            <div class="grid md:grid-cols-3 gap-4 text-sm">
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-orange-700 mb-2">症状</h5>
                                    <p class="text-orange-600">无法读取或解析日志文件内容</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-orange-700 mb-2">原因</h5>
                                    <p class="text-orange-600">编码格式不匹配或文件损坏</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-orange-700 mb-2">解决方案</h5>
                                    <ol class="text-orange-600 space-y-1 text-xs">
                                        <li>1. 使用多编码尝试读取</li>
                                        <li>2. 启用容错模式处理</li>
                                        <li>3. 检查文件完整性</li>
                                        <li>4. 重新生成日志文件</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Problem 3 -->
                        <div class="border-l-4 border-yellow-400 bg-yellow-50 p-4 rounded-r-lg">
                            <h4 class="font-semibold text-yellow-800 mb-3 flex items-center">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 6a3 3 0 013-3h10a1 1 0 01.8 1.6L14.25 8l2.55 3.4A1 1 0 0116 13H6a1 1 0 00-1 1v3a1 1 0 11-2 0V6z" clip-rule="evenodd"></path>
                                </svg>
                                进程输出编码错误
                            </h4>

                            <div class="grid md:grid-cols-3 gap-4 text-sm">
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-yellow-700 mb-2">症状</h5>
                                    <p class="text-yellow-600">ANSYS进程输出包含乱码字符</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-yellow-700 mb-2">原因</h5>
                                    <p class="text-yellow-600">进程输出编码与系统编码不匹配</p>
                                </div>
                                <div class="bg-white p-3 rounded-lg">
                                    <h5 class="font-semibold text-yellow-700 mb-2">解决方案</h5>
                                    <ol class="text-yellow-600 space-y-1 text-xs">
                                        <li>1. 设置进程环境变量</li>
                                        <li>2. 使用编码转换管道</li>
                                        <li>3. 启用容错输出捕获</li>
                                        <li>4. 配置系统代码页</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Best Practices -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">📚 最佳实践</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">编码处理的开发和运维最佳实践指南</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8">
                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">💡</span>开发建议
                    </h3>

                    <div class="space-y-4">
                        <div class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-blue-800 text-sm">统一编码</h4>
                                <p class="text-xs text-blue-600 mt-1">项目中统一使用UTF-8编码</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-green-50 rounded-lg">
                            <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-green-800 text-sm">显式声明</h4>
                                <p class="text-xs text-green-600 mt-1">在文件操作中明确指定编码格式</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-purple-50 rounded-lg">
                            <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-purple-800 text-sm">错误处理</h4>
                                <p class="text-xs text-purple-600 mt-1">为编码操作添加适当的异常处理</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                            <div class="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-orange-800 text-sm">测试验证</h4>
                                <p class="text-xs text-orange-600 mt-1">在不同编码环境下测试应用程序</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🔧</span>运维建议
                    </h3>

                    <div class="space-y-4">
                        <div class="flex items-start p-3 bg-indigo-50 rounded-lg">
                            <div class="w-8 h-8 bg-indigo-100 text-indigo-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-indigo-800 text-sm">环境检查</h4>
                                <p class="text-xs text-indigo-600 mt-1">定期检查系统编码设置</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-pink-50 rounded-lg">
                            <div class="w-8 h-8 bg-pink-100 text-pink-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-pink-800 text-sm">日志监控</h4>
                                <p class="text-xs text-pink-600 mt-1">监控编码相关的错误日志</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-teal-50 rounded-lg">
                            <div class="w-8 h-8 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
                                    <path fill-rule="evenodd" d="M3 8a2 2 0 012-2v9a2 2 0 01-2-2V8zM15 6a2 2 0 012 2v7a2 2 0 01-2 2V6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-teal-800 text-sm">备份策略</h4>
                                <p class="text-xs text-teal-600 mt-1">重要数据使用多种编码格式备份</p>
                            </div>
                        </div>

                        <div class="flex items-start p-3 bg-gray-50 rounded-lg">
                            <div class="w-8 h-8 bg-gray-100 text-gray-600 rounded-full flex items-center justify-center mr-3 mt-1">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 text-sm">文档维护</h4>
                                <p class="text-xs text-gray-600 mt-1">维护编码配置和故障处理文档</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Debug Tools -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🛠️ 调试工具</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">内置和外部编码调试工具推荐</p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8">
                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🔧</span>内置调试工具
                    </h3>

                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                            <div class="w-10 h-10 bg-blue-100 text-blue-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-blue-800">编码检测器</h4>
                                <p class="text-sm text-blue-600">检测文件的实际编码格式</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-green-50 rounded-lg">
                            <div class="w-10 h-10 bg-green-100 text-green-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                                    <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3a2 2 0 012-2h2a2 2 0 012 2v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-green-800">日志分析器</h4>
                                <p class="text-sm text-green-600">分析日志文件的编码问题</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                            <div class="w-10 h-10 bg-purple-100 text-purple-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-purple-800">转换工具</h4>
                                <p class="text-sm text-purple-600">批量转换文件编码格式</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                            <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-orange-800">验证工具</h4>
                                <p class="text-sm text-orange-600">验证编码转换的正确性</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🌐</span>外部工具推荐
                    </h3>

                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-indigo-50 rounded-lg">
                            <div class="w-10 h-10 bg-indigo-100 text-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                <span class="font-bold text-xs">N++</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-indigo-800">Notepad++</h4>
                                <p class="text-sm text-indigo-600">查看和转换文件编码</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-pink-50 rounded-lg">
                            <div class="w-10 h-10 bg-pink-100 text-pink-600 rounded-lg flex items-center justify-center mr-3">
                                <span class="font-bold text-xs">HxD</span>
                            </div>
                            <div>
                                <h4 class="font-semibold text-pink-800">HxD</h4>
                                <p class="text-sm text-pink-600">十六进制编辑器，查看文件字节</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-teal-50 rounded-lg">
                            <div class="w-10 h-10 bg-teal-100 text-teal-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-teal-800">chardet</h4>
                                <p class="text-sm text-teal-600">Python编码检测库</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <div class="w-10 h-10 bg-gray-100 text-gray-600 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2 5a2 2 0 012-2h8a2 2 0 012 2v10a2 2 0 002 2H4a2 2 0 01-2-2V5zm3 1h6v4H5V6zm6 6H5v2h6v-2z" clip-rule="evenodd"></path>
                                    <path d="M15 7h1a2 2 0 012 2v5.5a1.5 1.5 0 01-3 0V9a1 1 0 00-1-1h-1v1z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">iconv</h4>
                                <p class="text-sm text-gray-600">命令行编码转换工具</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2024 振动传递计算软件 - 编码处理技术指南</p>
            <p class="text-gray-400 text-sm mt-2">多编码处理系统 | 中文字符完美显示解决方案</p>
        </div>
    </footer>

    <!-- JavaScript for scroll animations -->
    <script>
        // Scroll reveal animation
        function revealOnScroll() {
            const reveals = document.querySelectorAll('.scroll-reveal');

            for (let i = 0; i < reveals.length; i++) {
                const windowHeight = window.innerHeight;
                const elementTop = reveals[i].getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < windowHeight - elementVisible) {
                    reveals[i].classList.add('visible');
                }
            }
        }

        window.addEventListener('scroll', revealOnScroll);

        // Initial check
        revealOnScroll();
    </script>
</body>
</html>
