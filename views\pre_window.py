"""
前处理窗口模块

此模块定义了应用程序的前处理窗口类，负责：
1. 显示前处理界面
2. 处理前处理相关参数
3. 材料库管理功能

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Optional, List
from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import QTreeWidgetItem, QMessageBox

from ui import ui_pre
from .base_window import BaseWindow
from core.material_manager import (
    get_material_library, get_material_assignment,
    Material, MaterialCategory, MaterialLibrary, MaterialAssignment
)

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class PreWindow(BaseWindow):
    """前处理窗口类"""
    def __init__(self, window_manager):
        super().__init__(window_manager)
        self.ui = ui_pre.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("前处理")

        # 材料管理相关属性
        self.material_library: MaterialLibrary = get_material_library()
        self.material_assignment: MaterialAssignment = get_material_assignment()
        self.current_selected_material: Optional[Material] = None

        # 应用材料管理样式
        self.apply_material_management_styles()

        # 初始化材料管理界面
        self.setup_material_management()

        # 应用按钮动画效果
        self.setup_animated_buttons()

    def apply_material_management_styles(self):
        """应用材料管理样式表"""
        try:
            # 读取样式文件
            import os
            style_path = os.path.join(os.path.dirname(__file__), '..', 'styles', 'material_management_styles.qss')

            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as f:
                    style_content = f.read()
                self.setStyleSheet(style_content)
                logger.debug("材料管理样式表应用成功")
            else:
                # 如果样式文件不存在，使用内联样式
                self.apply_fallback_styles()
                logger.warning("样式文件不存在，使用备用样式")

        except Exception as e:
            logger.error(f"应用样式表失败: {e}")
            self.apply_fallback_styles()

    def apply_fallback_styles(self):
        """应用备用样式"""
        fallback_style = """
        QMainWindow {
            background-color: #fafafa;
        }

        QTabWidget::pane {
            border: 1px solid #d0d0d0;
            border-radius: 8px;
            background-color: #ffffff;
        }

        QTabBar::tab {
            background: #f0f0f0;
            border: 1px solid #c0c0c0;
            border-radius: 6px;
            padding: 8px 16px;
            margin-right: 2px;
        }

        QTabBar::tab:selected {
            background: #ffffff;
            border-color: #0078d4;
            color: #0078d4;
        }

        QTreeWidget {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #ffffff;
        }

        QGroupBox {
            font-weight: 600;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            margin-top: 12px;
            padding-top: 8px;
        }

        QPushButton {
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }
        """
        self.setStyleSheet(fallback_style)

    def setup_material_management(self):
        """初始化材料管理界面"""
        try:
            # 初始化材料库树形视图
            self.setup_material_tree()

            # 初始化材料属性编辑区域
            self.setup_material_properties()

            # 初始化结构体选择下拉框
            self.setup_structure_selection()

            # 连接材料管理信号槽
            self.connect_material_signals()

            # 加载默认选择
            self.load_default_material_selection()

            logger.info("材料管理界面初始化完成")

        except Exception as e:
            logger.error(f"材料管理界面初始化失败: {e}")

    def setup_material_tree(self):
        """设置材料库树形视图"""
        tree = self.ui.treeWidget_material_library
        tree.clear()

        # 获取图标管理器
        try:
            from utils.icon_manager import get_material_category_icon, get_operation_icon
            icon_manager_available = True
        except ImportError:
            icon_manager_available = False
            logger.warning("图标管理器不可用，将使用无图标模式")

        # 创建分类节点
        categories = {
            MaterialCategory.GENERAL: "一般材料库",
            MaterialCategory.THERMAL: "热材料库",
            MaterialCategory.COMPOSITE: "复合材料库",
            MaterialCategory.CUSTOM: "自定义材料库"
        }

        category_items = {}

        for category, display_name in categories.items():
            category_item = QTreeWidgetItem([display_name])
            category_item.setData(0, Qt.UserRole, category)

            # 设置分类图标
            if icon_manager_available:
                category_icon = get_material_category_icon(category)
                if not category_icon.isNull():
                    category_item.setIcon(0, category_icon)

            tree.addTopLevelItem(category_item)
            category_items[category] = category_item

            # 设置预置库为只读样式
            if category != MaterialCategory.CUSTOM:
                category_item.setForeground(0, tree.palette().color(tree.palette().ColorRole.Mid))
                # 添加只读提示
                category_item.setToolTip(0, f"{display_name}（预置材料，只读）")
            else:
                category_item.setToolTip(0, f"{display_name}（可编辑）")

        # 添加材料到对应分类
        for material in self.material_library.materials.values():
            if material.category in category_items:
                material_item = QTreeWidgetItem([material.name])
                material_item.setData(0, Qt.UserRole, material)

                # 设置材料图标
                if icon_manager_available:
                    if material.is_readonly:
                        material_icon = get_operation_icon('material')
                    else:
                        material_icon = get_operation_icon('material')

                    if not material_icon.isNull():
                        material_item.setIcon(0, material_icon)

                # 设置工具提示
                tooltip = f"材料名称: {material.name}\n"
                tooltip += f"杨氏模量: {material.young_modulus} GPa\n"
                tooltip += f"密度: {material.density} kg/m³\n"
                tooltip += f"泊松比: {material.poisson_ratio}\n"
                tooltip += f"类型: {'只读' if material.is_readonly else '可编辑'}"
                material_item.setToolTip(0, tooltip)

                category_items[material.category].addChild(material_item)

                # 设置只读材料样式
                if material.is_readonly:
                    material_item.setForeground(0, tree.palette().color(tree.palette().ColorRole.Mid))

        # 展开所有分类
        tree.expandAll()

        # 设置树形视图的其他属性
        tree.setAlternatingRowColors(True)
        tree.setRootIsDecorated(True)
        tree.setIndentation(20)

        logger.debug("材料库树形视图设置完成")

    def setup_material_properties(self):
        """设置材料属性编辑区域"""
        # 设置默认值
        self.ui.lineEdit_material_name.setText("")
        self.ui.doubleSpinBox_young_modulus.setValue(200.0)
        self.ui.doubleSpinBox_density.setValue(7850.0)
        self.ui.doubleSpinBox_poisson_ratio.setValue(0.3)

        # 初始状态禁用编辑
        self.set_material_properties_enabled(False)

        logger.debug("材料属性编辑区域设置完成")

    def setup_structure_selection(self):
        """设置结构体选择下拉框"""
        # 添加示例结构体（占位符实现）
        structures = ["结构体1", "结构体2", "结构体3", "ROTOR", "STATOR"]
        self.ui.comboBox_structure_select.addItems(structures)

        logger.debug("结构体选择下拉框设置完成")

    def connect_material_signals(self):
        """连接材料管理相关信号槽"""
        # 材料库树形视图选择变化
        self.ui.treeWidget_material_library.itemSelectionChanged.connect(
            self.on_material_selection_changed)

        # 材料搜索
        self.ui.lineEdit_material_search.textChanged.connect(
            self.on_material_search)

        # 材料属性编辑
        self.ui.lineEdit_material_name.textChanged.connect(
            self.on_material_property_changed)
        self.ui.doubleSpinBox_young_modulus.valueChanged.connect(
            self.on_material_property_changed)
        self.ui.doubleSpinBox_density.valueChanged.connect(
            self.on_material_property_changed)
        self.ui.doubleSpinBox_poisson_ratio.valueChanged.connect(
            self.on_material_property_changed)

        # 设置按钮图标和工具提示
        self.setup_material_buttons()

        logger.debug("材料管理信号槽连接完成")

    def setup_material_buttons(self):
        """设置材料操作按钮的图标和工具提示"""
        try:
            from utils.icon_manager import get_operation_icon

            # 新建按钮
            new_icon = get_operation_icon('new')
            if not new_icon.isNull():
                self.ui.pushButton_material_new.setIcon(new_icon)
            self.ui.pushButton_material_new.setToolTip("创建新的自定义材料")

            # 复制按钮
            copy_icon = get_operation_icon('copy')
            if not copy_icon.isNull():
                self.ui.pushButton_material_copy.setIcon(copy_icon)
            self.ui.pushButton_material_copy.setToolTip("复制选中的材料")

            # 删除按钮
            delete_icon = get_operation_icon('delete')
            if not delete_icon.isNull():
                self.ui.pushButton_material_delete.setIcon(delete_icon)
            self.ui.pushButton_material_delete.setToolTip("删除选中的自定义材料")

            # 分配按钮
            assign_icon = get_operation_icon('assign')
            if not assign_icon.isNull():
                self.ui.pushButton_assign_material.setIcon(assign_icon)
            self.ui.pushButton_assign_material.setToolTip("将材料分配到选中的结构体")

            # 应用全部按钮
            apply_all_icon = get_operation_icon('apply_all')
            if not apply_all_icon.isNull():
                self.ui.pushButton_apply_all.setIcon(apply_all_icon)
            self.ui.pushButton_apply_all.setToolTip("将材料应用到所有结构体")

            # 搜索框图标（使用更简单的方式）
            search_icon = get_operation_icon('search')
            if not search_icon.isNull():
                # 直接设置搜索框的占位符文本，包含图标提示
                current_placeholder = self.ui.lineEdit_material_search.placeholderText()
                self.ui.lineEdit_material_search.setPlaceholderText(f"🔍 {current_placeholder}")

                # 设置简单的样式，不使用SVG
                search_style = """
                QLineEdit#lineEdit_material_search {
                    padding-left: 8px;
                    font-size: 10pt;
                }
                """
                self.ui.lineEdit_material_search.setStyleSheet(search_style)

            logger.debug("材料操作按钮设置完成")

        except ImportError:
            logger.warning("图标管理器不可用，跳过按钮图标设置")
        except Exception as e:
            logger.error(f"设置材料操作按钮失败: {e}")

    def load_default_material_selection(self):
        """加载默认材料选择"""
        # 选择第一个材料（Structural Steel）
        tree = self.ui.treeWidget_material_library
        for i in range(tree.topLevelItemCount()):
            category_item = tree.topLevelItem(i)
            if category_item.childCount() > 0:
                first_material_item = category_item.child(0)
                tree.setCurrentItem(first_material_item)
                break

    def on_material_selection_changed(self):
        """材料选择变化处理"""
        tree = self.ui.treeWidget_material_library
        current_item = tree.currentItem()

        if not current_item:
            self.current_selected_material = None
            self.set_material_properties_enabled(False)
            return

        # 获取材料对象
        material = current_item.data(0, Qt.UserRole)
        if isinstance(material, Material):
            self.current_selected_material = material
            self.load_material_properties(material)
            self.set_material_properties_enabled(not material.is_readonly)
        else:
            self.current_selected_material = None
            self.set_material_properties_enabled(False)

    def load_material_properties(self, material: Material):
        """加载材料属性到编辑区域"""
        self.ui.lineEdit_material_name.setText(material.name)
        self.ui.doubleSpinBox_young_modulus.setValue(material.young_modulus)
        self.ui.doubleSpinBox_density.setValue(material.density)
        self.ui.doubleSpinBox_poisson_ratio.setValue(material.poisson_ratio)

    def set_material_properties_enabled(self, enabled: bool):
        """设置材料属性编辑区域的启用状态"""
        self.ui.lineEdit_material_name.setEnabled(enabled)
        self.ui.doubleSpinBox_young_modulus.setEnabled(enabled)
        self.ui.doubleSpinBox_density.setEnabled(enabled)
        self.ui.doubleSpinBox_poisson_ratio.setEnabled(enabled)

    def on_material_search(self, keyword: str):
        """材料搜索处理"""
        # 占位符实现：简单的文本过滤
        tree = self.ui.treeWidget_material_library

        for i in range(tree.topLevelItemCount()):
            category_item = tree.topLevelItem(i)
            category_visible = False

            for j in range(category_item.childCount()):
                material_item = category_item.child(j)
                material_name = material_item.text(0).lower()
                visible = keyword.lower() in material_name if keyword else True
                material_item.setHidden(not visible)

                if visible:
                    category_visible = True

            category_item.setHidden(not category_visible)

    def on_material_property_changed(self):
        """材料属性变化处理（占位符实现）"""
        if not self.current_selected_material or self.current_selected_material.is_readonly:
            return

        # 占位符：这里可以实现实时保存或标记为已修改
        logger.debug("材料属性已修改")

    def refresh_material_tree(self):
        """刷新材料库树形视图"""
        try:
            # 保存当前选中的材料ID
            current_material_id = None
            if self.current_selected_material:
                current_material_id = self.current_selected_material.id

            # 重新设置树形视图
            self.setup_material_tree()

            # 尝试恢复选中状态
            if current_material_id:
                self.select_material_by_id(current_material_id)

            logger.debug("材料库树形视图已刷新")

        except Exception as e:
            logger.error(f"刷新材料库树形视图失败: {e}")

    def select_material_by_id(self, material_id: str) -> bool:
        """根据材料ID选中材料

        Args:
            material_id: 材料ID

        Returns:
            bool: 是否成功选中
        """
        try:
            tree = self.ui.treeWidget_material_library

            # 遍历所有分类和材料项
            for i in range(tree.topLevelItemCount()):
                category_item = tree.topLevelItem(i)
                for j in range(category_item.childCount()):
                    material_item = category_item.child(j)
                    material = material_item.data(0, Qt.UserRole)

                    if isinstance(material, Material) and material.id == material_id:
                        tree.setCurrentItem(material_item)
                        tree.scrollToItem(material_item)
                        return True

            return False

        except Exception as e:
            logger.error(f"选中材料失败: {e}")
            return False

    def get_selected_material(self) -> Optional[Material]:
        """获取当前选中的材料

        Returns:
            Material: 选中的材料对象，如果没有选中返回None
        """
        tree = self.ui.treeWidget_material_library
        current_item = tree.currentItem()

        if not current_item:
            return None

        material = current_item.data(0, Qt.UserRole)
        if isinstance(material, Material):
            return material

        return None

    def select_next_material_after_deletion(self, deleted_material_id: str):
        """删除材料后选中下一个材料

        Args:
            deleted_material_id: 被删除的材料ID
        """
        try:
            tree = self.ui.treeWidget_material_library

            # 查找第一个可用的材料
            for i in range(tree.topLevelItemCount()):
                category_item = tree.topLevelItem(i)
                if category_item.childCount() > 0:
                    first_material_item = category_item.child(0)
                    tree.setCurrentItem(first_material_item)
                    tree.scrollToItem(first_material_item)
                    return

            # 如果没有材料，清空选择
            tree.clearSelection()
            self.current_selected_material = None
            self.set_material_properties_enabled(False)

        except Exception as e:
            logger.error(f"选中下一个材料失败: {e}")

    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.push_finish,
            self.ui.push_meshui,
            self.ui.push_mainui
        ]

        # 保存按钮的点击处理函数
        from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step
        from window_manager import WindowType
        # 注意：不在这里连接finish按钮，避免重复连接
        # finish按钮的连接在ctrl/pre_slot.py中统一处理
        # 使用统一的导航管理器
        meshui_handler = lambda: navigate_to_next_step(self.window_manager, WindowType.PRE)
        mainui_handler = lambda: navigate_to_main_menu(self.window_manager)

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        # 重新连接信号（不包括finish按钮）
        # self.ui.push_finish.clicked.connect(finish_handler)  # 移除重复连接
        self.ui.push_meshui.clicked.connect(meshui_handler)
        self.ui.push_mainui.clicked.connect(mainui_handler)
