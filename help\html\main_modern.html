<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主界面 - 软件核心操作中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-slate-600 to-gray-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-slate-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🏠 主界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-slate-100">
                    软件核心操作中心 | 统一的功能模块访问入口
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-slate-500 bg-opacity-20 text-slate-100 text-sm font-semibold px-4 py-2 rounded-full border border-slate-400">
                        🎛️ 项目管理 | 🔧 模型处理 | 📊 分析控制 | 📈 结果查看
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                主界面是软件的核心操作中心，提供了访问所有功能模块的入口。界面采用现代化设计，布局清晰，操作直观。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">菜单栏</h3>
                    </div>
                    <p class="text-sm text-blue-600">位于界面顶部，包含文件、编辑、视图、工具和帮助等菜单</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">工具栏</h3>
                    </div>
                    <p class="text-sm text-green-600">位于菜单栏下方，提供常用功能的快捷按钮</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">导航面板</h3>
                    </div>
                    <p class="text-sm text-purple-600">位于界面左侧，用于在不同功能模块之间切换</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">主工作区</h3>
                    </div>
                    <p class="text-sm text-orange-600">界面中央的主要内容显示区域</p>
                </div>
                
                <div class="bg-teal-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-teal-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-teal-800">状态栏</h3>
                    </div>
                    <p class="text-sm text-teal-600">位于界面底部，显示当前状态和提示信息</p>
                </div>
            </div>
        </section>

        <!-- Main Features -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🚀 主要功能</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">全面的项目管理和分析流程控制功能</p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">📁 项目管理</h3>
                        <ul class="space-y-2 text-sm text-blue-600">
                            <li>• 创建新项目</li>
                            <li>• 打开现有项目</li>
                            <li>• 保存项目</li>
                            <li>• 项目设置管理</li>
                        </ul>
                    </div>
                    
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-green-800 mb-4">🔧 模型处理</h3>
                        <ul class="space-y-2 text-sm text-green-600">
                            <li>• 导入几何模型</li>
                            <li>• 模型查看和编辑</li>
                            <li>• 模型属性设置</li>
                        </ul>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-purple-800 mb-4">📊 分析流程控制</h3>
                        <ul class="space-y-2 text-sm text-purple-600">
                            <li>• 启动分析</li>
                            <li>• 暂停/继续分析</li>
                            <li>• 终止分析</li>
                            <li>• 分析进度监控</li>
                        </ul>
                    </div>
                    
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-orange-800 mb-4">📈 结果处理</h3>
                        <ul class="space-y-2 text-sm text-orange-600">
                            <li>• 查看分析结果</li>
                            <li>• 结果可视化</li>
                            <li>• 导出报告</li>
                            <li>• 振动分析</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Panel Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🧭 导航面板详解</h2>

            <div class="bg-gradient-to-r from-slate-50 to-gray-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📋 模块导航</h3>
                <p class="text-gray-600 mb-4">导航面板提供了快速访问各功能模块的入口：</p>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                            <div>
                                <strong class="text-gray-800">前处理模块</strong>
                                <p class="text-sm text-gray-600">几何导入、材料设置、面处理配置</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                            <div>
                                <strong class="text-gray-800">网格划分模块</strong>
                                <p class="text-sm text-gray-600">网格参数设置、网格生成和质量控制</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                            <div>
                                <strong class="text-gray-800">连接设置模块</strong>
                                <p class="text-sm text-gray-600">定义部件间的连接关系和参数</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">4</span>
                            <div>
                                <strong class="text-gray-800">约束设置模块</strong>
                                <p class="text-sm text-gray-600">边界条件定义和监控点管理</p>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">5</span>
                            <div>
                                <strong class="text-gray-800">分析设置模块</strong>
                                <p class="text-sm text-gray-600">分析类型选择、求解器配置</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-teal-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">6</span>
                            <div>
                                <strong class="text-gray-800">结果显示模块</strong>
                                <p class="text-sm text-gray-600">结果可视化、数据分析和导出</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">7</span>
                            <div>
                                <strong class="text-gray-800">振动分析模块</strong>
                                <p class="text-sm text-gray-600">专业的振动数据处理和分析</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">8</span>
                            <div>
                                <strong class="text-gray-800">帮助系统</strong>
                                <p class="text-sm text-gray-600">在线帮助文档和技术支持</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Project Management -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📁 项目管理功能</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🆕 创建新项目</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-blue-800 mb-2">项目模板</h4>
                            <p class="text-sm text-blue-600">选择预定义的项目模板快速开始</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-green-800 mb-2">项目设置</h4>
                            <p class="text-sm text-green-600">配置项目名称、路径和基本参数</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-purple-800 mb-2">初始化</h4>
                            <p class="text-sm text-purple-600">自动创建项目文件夹结构</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📂 打开现有项目</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">最近项目</h4>
                                <p class="text-sm text-green-600">快速访问最近使用的项目</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                                <h4 class="font-semibold text-blue-800 mb-2">浏览项目</h4>
                                <p class="text-sm text-blue-600">通过文件浏览器选择项目文件</p>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                                <h4 class="font-semibold text-purple-800 mb-2">项目验证</h4>
                                <p class="text-sm text-purple-600">自动检查项目文件完整性</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                                <h4 class="font-semibold text-orange-800 mb-2">版本兼容</h4>
                                <p class="text-sm text-orange-600">处理不同版本项目的兼容性</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Workflow Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔄 工作流程指南</h2>

            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📋 标准分析流程</h3>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📁 项目准备</h4>
                            <p class="text-gray-600 text-sm">创建新项目或打开现有项目，设置项目基本参数和工作目录。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔧 前处理设置</h4>
                            <p class="text-gray-600 text-sm">导入几何模型，设置材料属性，配置面处理参数。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔲 网格生成</h4>
                            <p class="text-gray-600 text-sm">设置网格参数，生成有限元网格，检查网格质量。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔗 连接定义</h4>
                            <p class="text-gray-600 text-sm">定义部件间的连接关系，设置连接参数。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">5</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔒 边界条件</h4>
                            <p class="text-gray-600 text-sm">设置约束条件，定义载荷，配置监控点。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">6</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📊 分析设置</h4>
                            <p class="text-gray-600 text-sm">选择分析类型，配置求解器，设置收敛参数。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">7</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🚀 执行分析</h4>
                            <p class="text-gray-600 text-sm">启动分析计算，监控求解进度，处理可能的错误。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">8</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📈 结果处理</h4>
                            <p class="text-gray-600 text-sm">查看分析结果，进行后处理，生成报告。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📁</span>如何创建新项目？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">创建新项目是开始分析工作的第一步。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">操作步骤：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 点击菜单栏"文件" → "新建项目"</li>
                                <li>• 选择合适的项目模板</li>
                                <li>• 设置项目名称和保存路径</li>
                                <li>• 配置项目基本参数</li>
                                <li>• 点击"创建"完成项目初始化</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🧭</span>如何在不同模块间切换？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">软件提供多种方式在功能模块间快速切换。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">切换方法：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 使用左侧导航面板的模块按钮</li>
                                <li>• 通过菜单栏的"视图"菜单选择</li>
                                <li>• 使用工具栏的快捷按钮</li>
                                <li>• 使用键盘快捷键（Ctrl+1-8）</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">💾</span>项目文件保存在哪里？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">项目文件的组织结构和保存位置说明。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">文件结构：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 项目主文件：.proj格式</li>
                                <li>• 几何文件：geometry文件夹</li>
                                <li>• 网格文件：mesh文件夹</li>
                                <li>• 结果文件：results文件夹</li>
                                <li>• 备份文件：backup文件夹</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>软件界面可以自定义吗？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">软件提供了丰富的界面自定义选项。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">自定义选项：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 调整面板布局和大小</li>
                                <li>• 自定义工具栏按钮</li>
                                <li>• 设置主题和颜色方案</li>
                                <li>• 配置快捷键</li>
                                <li>• 保存和加载布局配置</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 项目路径不支持中文字符，请使用英文路径</li>
                    <li>• 定期保存项目，避免数据丢失</li>
                    <li>• 大型项目建议定期清理临时文件</li>
                    <li>• 确保有足够的磁盘空间用于结果文件</li>
                    <li>• 不要在分析过程中关闭软件</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 熟悉各模块的功能和操作流程</li>
                    <li>• 使用项目模板提高工作效率</li>
                    <li>• 定期备份重要项目文件</li>
                    <li>• 充分利用帮助系统和文档</li>
                    <li>• 保持软件版本更新</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">软件核心操作中心 - 统一的功能模块访问入口</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
