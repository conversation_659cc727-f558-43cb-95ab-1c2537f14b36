"""
网格模块控制器

此模块负责处理网格界面的所有操作，包括：
1. 界面跳转控制
2. 网格参数配置
3. 网格生成流程控制
4. 结果展示更新

工作流程：
1. 用户输入网格参数
2. 生成JSON配置文件
3. 调用ANSYS Workbench进行网格生成
4. 更新界面显示结果

作者: [作者名]
日期: [日期]
"""

import json
import os
import shutil
import subprocess
from typing import Any, Optional
from dataclasses import dataclass
from datetime import datetime

from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QMessageBox, QWidget
from window_manager import WindowManager, WindowType
from resource_manager import ResourceManager
from error_handler import (
    ErrorHandler, AppError, AnsysError, ValidationError,
    FileOperationError, ConfigurationError, ErrorSeverity
)
from core.workflow_state import get_workflow_state, WorkflowStep

# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口


@dataclass
class MeshResult:
    """网格生成结果信息类"""
    output_dir: str
    timestamp: datetime
    element_size: float
    elements_count: Optional[int] = None
    nodes_count: Optional[int] = None


class MeshResultTracker:
    """网格结果跟踪器类
    
    用于跟踪和管理最新的网格生成结果信息。
    实现了单例模式，确保全局只有一个实例。
    """
    
    _instance = None
    _latest_result: Optional[MeshResult] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MeshResultTracker, cls).__new__(cls)
        return cls._instance
    
    @property
    def latest_result(self) -> Optional[MeshResult]:
        """获取最新的网格结果信息"""
        return self._latest_result
    
    def update_result(self, result: MeshResult) -> None:
        """更新最新的网格结果信息
        
        Args:
            result: 新的网格结果信息
        """
        self._latest_result = result
    
    def clear_result(self) -> None:
        """清除结果信息"""
        self._latest_result = None


def to_main_slot(window_manager: WindowManager) -> None:
    """跳转到主界面的槽函数
    
    Args:
        window_manager: 窗口管理器实例
    """
    window_manager.switch_to(WindowType.MAIN)

def to_pre_slot(window_manager: WindowManager) -> None:
    """跳转到前处理界面的槽函数
    
    Args:
        window_manager: 窗口管理器实例
    """
    window_manager.switch_to(WindowType.PRE)

def to_connection_slot(window_manager: WindowManager) -> None:
    """跳转到连接设置界面的槽函数
    
    Args:
        window_manager: 窗口管理器实例
    """
    window_manager.switch_to(WindowType.CONNECTION)

def cleanup_temp_files(resource_manager: ResourceManager, 
                      error_handler: ErrorHandler,
                      mesh_window: Optional[QWidget] = None,
                      exclude_paths: Optional[list[str]] = None) -> None:
    """清理临时文件
    
    Args:
        resource_manager: 资源管理器实例
        error_handler: 错误处理器实例
        mesh_window: 网格窗口实例，用于显示错误消息
        exclude_paths: 不清理的文件路径列表
    """
    try:
        # 1. 清理临时配置文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="mesh_config_",
            exclude_paths=exclude_paths
        )
        
        # 2. 清理临时脚本文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="mesh_wb_",
            exclude_paths=exclude_paths
        )
        
        # 3. 清理临时批处理文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="mesh_",
            suffix=".bat",
            exclude_paths=exclude_paths
        )
        
        # 4. 清理临时输出目录
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="mesh_output_",
            exclude_paths=exclude_paths
        )
        
    except Exception as e:
        if mesh_window:
            error_handler.handle_exception(
                e, mesh_window, show_message=False
            )

def generate_mesh(window_manager: WindowManager) -> None:
    """执行网格生成的完整流程
    
    此函数按顺序执行以下步骤：
    1. 生成配置文件
    2. 准备网格脚本
    3. 运行Workbench
    4. 清理临时文件
    
    Args:
        window_manager: 窗口管理器实例
    """
    pass
#     # 获取窗口实例和错误处理器
#     mesh_window = window_manager.get_window(WindowType.MESH)
#     main_window = window_manager.get_window(WindowType.MAIN)
#     error_handler = ErrorHandler()
#     result_tracker = MeshResultTracker()
    
#     if not mesh_window or not main_window:
#         error_handler.handle_error(
#             AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
#         )
#         return
    
#     # 用于存储需要保护的文件路径
#     active_files = []
        
#     try:
#         # 2. 初始化资源管理器
#         resource_manager = ResourceManager()
#         try:
#             resource_manager.initialize(main_window.ANSYS_Work_Dir)
            
#             # 首先清理旧的临时文件
#             cleanup_temp_files(resource_manager, error_handler, mesh_window)
            
#         except Exception as e:
#             raise ConfigurationError(
#                 "初始化资源管理器失败",
#                 details={'work_dir': main_window.ANSYS_Work_Dir}
#             ) from e

#         # 1. 验证并获取网格参数
#         try:
#             element_size = float(mesh_window.ui.allbodymeshsize.text()) * 0.001
#             if element_size <= 0 or element_size > 1.0:
#                 raise ValidationError(
#                     "网格尺寸必须在0-1000mm范围内",
#                     details={'input_value': element_size * 1000}
#                 )
#         except ValueError as e:
#             raise ValidationError("网格尺寸必须是有效的数字") from e

#         # 3. 创建临时输出目录
#         try:
#             temp_dir_name = resource_manager.create_temp_file(prefix="mesh_output_")
#             active_files.append(temp_dir_name)
#             os.remove(temp_dir_name)  # 删除生成的文件
#             temp_output_dir = temp_dir_name  # 使用这个名字作为目录名
#             os.makedirs(temp_output_dir, exist_ok=True)
            
#             # 创建新的结果跟踪记录
#             result_tracker.update_result(MeshResult(
#                 output_dir=temp_output_dir,
#                 timestamp=datetime.now(),
#                 element_size=element_size
#             ))
            
#         except Exception as e:
#             raise FileOperationError(
#                 "创建临时输出目录失败",
#                 details={'error': str(e)}
#             ) from e

#         # 4. 生成配置文件
#         config = {
#             "element_size": element_size,
#             "output_dir": temp_output_dir.replace("\\", "/")
#         }
#         try:
#             config_file = resource_manager.create_temp_file(prefix="mesh_config", suffix=".json")
#             active_files.append(config_file)
#             with open(config_file, "w") as f:
#                 json.dump(config, f, indent=4)
#         except Exception as e:
#             raise FileOperationError(
#                 "保存配置文件失败",
#                 details={'config_file': config_file}
#             ) from e

#         # 5. 准备网格脚本
#         try:
#             # 使用ResourceManager获取正确的源脚本路径，支持打包环境
#             source_file = resource_manager.get_resource_path("originscript", "meshpy.py")
#             with open(source_file, "r", encoding="utf-8") as f:
#                 script_content = f.read()
#         except Exception as e:
#             raise FileOperationError(
#                 "读取源脚本文件失败",
#                 details={'source_file': source_file}
#             ) from e

#         # 6. 修改脚本内容
#         target_dir = main_window.ANSYS_Work_Dir.replace("\\", "/")
#         old_target_dir = r'target_directory = r"D:/data/all-XM/autoworkbench/csdaima"'
#         new_target_dir = f'target_directory = r"{target_dir}"'
#         script_content = script_content.replace(old_target_dir, new_target_dir)

#         # 7. 添加网格生成代码
#         run_content = f'''
#         target_directory=r"{target_dir}"
#         output_directory=r"{temp_output_dir.replace('\\', '/')}"
#         current_directory = os.getcwd()
#         self.logger.info("Current working directory: %s", current_directory)
        
#         try:
#             os.chdir(target_directory)
#             self.logger.info("Successfully changed to target folder: %s", os.getcwd())
#             if not os.path.exists(output_directory):
#                     os.makedirs(output_directory)
#             self.logger.info("Created output directory: %s", output_directory)
#         except OSError as e:
#             self.logger.error("Failed to setup directories: %s", str(e))
#             raise
        
#         # Store bodies and generate mesh
#         all_bodies = self.mesh_manager.store_all_bodies()
#         self.mesh_manager.generate_allbody_mesh({element_size})

#         # Get named selections
#         named_selections_dict = self.selection_mgr.get_named_selections_dict()
        
#         # Save mesh preview image
#         mesh_png = os.path.join(output_directory, "mesh.png")
#         Elements, Nodes = self.mesh_manager.get_mesh_informationandpng(mesh_png)
        
#         # Save mesh information
#         outjson_file = os.path.join(output_directory, "mesh_outjson.json")
#         outjson={{
#             "Elements": Elements,
#             "Nodes": Nodes
#         }}
#         with open(outjson_file, "w") as f:
#             json.dump(outjson, f, indent=4)
#         self.logger.info("MainApp run completed. Results saved to: %s", output_directory)
# '''

#         # 8. 更新脚本内容
#         script_content = script_content.replace(
#             "def run(self):",
#             f"def run(self):{run_content}"
#         )

#         # 9. 创建新版本的脚本
#         try:
#             script_file = resource_manager.create_script_version("meshpy_copy.py", script_content)
#             print(f"✅ 脚本文件创建成功：{script_file}")
#         except Exception as e:
#             raise FileOperationError(
#                 "创建脚本文件失败",
#                 details={'script_name': "meshpy_copy.py"}
#             ) from e

#         # 10. 清理旧版本
#         resource_manager.clean_old_versions("meshpy_copy.py")

#         # 11. 创建Workbench控制脚本
#         wb_script_content = f'''
# # encoding: utf-8
# Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
# system1 = GetSystem(Name="SYS")
# model1 = system1.GetContainer(ComponentName="Model")
# model1.Edit()
# '''

#         # 12. 使用create_script_version返回的实际脚本路径
#         # 直接使用返回的路径，确保路径一致性
#         mesh_script = script_file.replace("\\", "/")
#         print(f"🔧 Workbench将引用脚本路径：{mesh_script}")
        
#         # 13. 添加执行命令
#         wb_script_content += f'''
# model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{mesh_script}")')
# model1.Exit()
# Save(Overwrite=True)
# '''

#         # 14. 创建Workbench脚本文件
#         try:
#             wb_script_file = resource_manager.create_temp_file(prefix="mesh_wb", suffix=".py")
#             active_files.append(wb_script_file)
#             with open(wb_script_file, "w", encoding="utf-8") as f:
#                 f.write(wb_script_content)
#         except Exception as e:
#             raise FileOperationError(
#                 "创建Workbench脚本文件失败",
#                 details={'script_file': wb_script_file}
#             ) from e

#         # 15. 创建批处理文件
#         try:
#             ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
#             wb_script_file = wb_script_file.replace("\\", "/")
#             bat_content = (
#                 f'"{ansys_path}" '
#                 f'-B -R "{wb_script_file}"'
#             )
#             bat_file = resource_manager.create_temp_file(prefix="mesh", suffix=".bat")
#             active_files.append(bat_file)
#             with open(bat_file, "w", encoding="utf-8") as f:
#                 f.write(bat_content)
#         except Exception as e:
#             raise FileOperationError(
#                 "创建批处理文件失败",
#                 details={'bat_file': bat_file}
#             ) from e

#         # 16. 执行批处理文件
#         try:
#             result = subprocess.run(
#                 f'"{bat_file}"',
#                 shell=True,
#                 check=True,
#                 text=True,
#                 capture_output=True
#             )
            
#             # 检查ANSYS输出中的错误信息
#             if "error" in result.stdout.lower() or "error" in result.stderr.lower():
#                 raise AnsysError(
#                     "ANSYS执行过程中出现错误",
#                     details={
#                         'stdout': result.stdout,
#                         'stderr': result.stderr
#                     }
#                 )
                
#             error_handler.handle_error(
#                 AppError(
#                     "网格无关性验证完成，可以查看结果",
#                     ErrorSeverity.INFO
#                 ),
#                 mesh_window
#             )

#             # 设置完成状态
#             window_manager.process_status.set_completed(WindowType.MESH)

#             # 标记工作流程步骤完成
#             workflow_state = get_workflow_state()
#             workflow_state.mark_step_completed(WorkflowStep.MESH_VALIDATION, {
#                 'timestamp': datetime.now().isoformat(),
#                 'element_size': element_size,
#                 'mesh_validation_completed': True
#             })
            
#             # 17. 自动更新显示
#             change_info(window_manager)
            
#         except subprocess.CalledProcessError as e:
#             result_tracker.clear_result()  # 清除失败的结果记录
#             raise AnsysError(
#                 "ANSYS执行失败",
#                 details={
#                     'return_code': e.returncode,
#                     'stdout': e.stdout,
#                     'stderr': e.stderr
#                 }
#             ) from e
            
#     except AppError as e:
#         # 处理应用程序异常
#         result_tracker.clear_result()  # 清除失败的结果记录
#         error_handler.handle_error(e, mesh_window)
#     except Exception as e:
#         # 处理其他未预期的异常
#         result_tracker.clear_result()  # 清除失败的结果记录
#         error_handler.handle_exception(e, mesh_window)
#     finally:
#         # 清理临时文件，但排除当前正在使用的文件
#         cleanup_temp_files(
#             resource_manager, 
#             error_handler,
#             mesh_window,
#             exclude_paths=active_files
#         )

def change_info(window_manager: WindowManager) -> None:
    """更新界面显示的网格信息
    
    此函数完成以下任务：
    1. 读取网格结果文件
    2. 更新节点和单元数量显示
    3. 显示网格预览图片
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 获取窗口实例和错误处理器
    mesh_window = window_manager.get_window(WindowType.MESH)
    main_window = window_manager.get_window(WindowType.MAIN)
    error_handler = ErrorHandler()
    result_tracker = MeshResultTracker()
    
    if not mesh_window or not main_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    try:
        # 1. 初始化资源管理器
        resource_manager = ResourceManager()
        resource_manager.initialize(main_window.ANSYS_Work_Dir)
        
        # 2. 确定结果文件路径
        latest_result = result_tracker.latest_result
        if latest_result and os.path.exists(latest_result.output_dir):
            result_dir = latest_result.output_dir
        else:
            # 如果没有最新结果或目录不存在，使用默认输出目录
            result_dir = os.path.join(main_window.ANSYS_Work_Dir, "out")
            error_handler.handle_error(
                AppError(
                    "未找到最新的网格结果，将使用默认输出目录",
                    ErrorSeverity.WARNING
                ),
                mesh_window
            )
            
        # 3. 读取结果文件
        result_file = os.path.join(result_dir, "mesh_outjson.json")
        if not os.path.exists(result_file):
            raise FileOperationError(
                "结果文件不存在",
                details={'result_file': result_file}
            )

        try:
            with open(result_file, "r", encoding="utf-8") as f:
                data = json.load(f)
                
            # 更新结果信息
            if latest_result:
                latest_result.elements_count = data.get("Elements")
                latest_result.nodes_count = data.get("Nodes")
                result_tracker.update_result(latest_result)
                
        except json.JSONDecodeError as e:
            raise FileOperationError(
                "结果文件格式错误",
                details={'result_file': result_file}
            ) from e
        except Exception as e:
            raise FileOperationError(
                "读取结果文件失败",
                details={'result_file': result_file}
            ) from e

        # 4. 更新界面显示
        mesh_window.ui.nodes_number.setText("Nodes: " + str(data.get("Nodes")))
        mesh_window.ui.elenments_number.setText("Elements: " + str(data.get("Elements")))

        # 5. 显示网格预览图片
        png_file = os.path.join(result_dir, "mesh.png")
        pixmap = QPixmap(png_file)
        if pixmap.isNull():
            raise FileOperationError(
                "无法加载预览图像",
                details={'image_file': png_file}
            )
        
        mesh_window.ui.meshfig.setPixmap(pixmap)
        mesh_window.ui.meshfig.setScaledContents(True)

        # 6. 整理输出文件
        resource_manager.organize_output_files()
        
        # 7. 显示成功消息
        error_handler.handle_error(
            AppError(
                "结果更新完成",
                ErrorSeverity.INFO
            ),
            mesh_window
        )
        
    except AppError as e:
        # 处理应用程序异常
        error_handler.handle_error(e, mesh_window)
    except Exception as e:
        # 处理其他未预期的异常
        error_handler.handle_exception(e, mesh_window)

def mesh_slot(window_manager: WindowManager) -> None:
    """初始化网格界面的所有槽函数连接
    
    此函数负责将网格界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮和网格生成功能按钮。
    
    Args:
        window_manager: 窗口管理器实例
    """
    mesh_window = window_manager.get_window(WindowType.MESH)
    if not mesh_window:
        ErrorHandler().handle_error(
            AppError("无法获取网格窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step

    mesh_window.ui.btn_main_menu.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 网格无关性验证的上一步应该是约束设置
    if hasattr(mesh_window.ui, 'push_constrainui'):
        mesh_window.ui.push_constrainui.clicked.connect(
            lambda: navigate_to_previous_step(window_manager, WindowType.MESH))

    # 网格无关性验证的下一步应该是计算结果
    if hasattr(mesh_window.ui, 'push_resultui'):
        mesh_window.ui.push_resultui.clicked.connect(
            lambda: navigate_to_next_step(window_manager, WindowType.MESH))
    
    # 网格生成按钮连接（使用实际的按钮名称）
    if hasattr(mesh_window.ui, 'btn_generate_mesh'):
        mesh_window.ui.btn_generate_mesh.clicked.connect(
            lambda: generate_mesh(window_manager))
    elif hasattr(mesh_window.ui, 'push_generatemesh'):
        mesh_window.ui.push_generatemesh.clicked.connect(
            lambda: generate_mesh(window_manager))

    # 结果显示按钮连接
    if hasattr(mesh_window.ui, 'push_result'):
        mesh_window.ui.push_result.clicked.connect(
            lambda: change_info(window_manager))
