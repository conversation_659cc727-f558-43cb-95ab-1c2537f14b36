"""
模态分析结果导入对话框

此模块提供用户界面来导入和管理外部模态分析结果：
1. 文件选择和批量导入
2. 导入结果预览和验证
3. 导入结果管理（查看、删除、重命名）
4. 数据格式转换和导出

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import logging
from typing import List, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QTableWidget, 
    QTableWidgetItem, QFileDialog, QMessageBox, QLabel, QLineEdit,
    QTextEdit, QSplitter, QGroupBox, QHeaderView, QMenu, QInputDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QAction

from .modal_data_manager import ModalDataManager, ModalResult

logger = logging.getLogger(__name__)


class ModalImportDialog(QDialog):
    """模态分析结果导入对话框"""
    
    # 信号定义
    results_imported = Signal()  # 导入完成信号
    results_updated = Signal()   # 结果更新信号
    
    def __init__(self, data_manager: ModalDataManager, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        self.load_imported_results()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("Modal Analysis Results Import & Management")
        self.setGeometry(200, 200, 1000, 700)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：导入功能
        import_group = self.create_import_group()
        splitter.addWidget(import_group)
        
        # 右侧：结果管理
        management_group = self.create_management_group()
        splitter.addWidget(management_group)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.export_button = QPushButton("Export Results")
        self.clear_button = QPushButton("Clear All")
        self.close_button = QPushButton("Close")
        
        button_layout.addWidget(self.export_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addStretch()
        button_layout.addWidget(self.close_button)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        self.export_button.clicked.connect(self.export_results)
        self.clear_button.clicked.connect(self.clear_all_results)
        self.close_button.clicked.connect(self.accept)
    
    def create_import_group(self) -> QGroupBox:
        """创建导入功能组"""
        group = QGroupBox("Import Modal Results")
        layout = QVBoxLayout(group)
        
        # 文件选择
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Select files to import...")
        self.browse_button = QPushButton("Browse Files")
        
        file_layout.addWidget(QLabel("Files:"))
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_button)
        
        layout.addLayout(file_layout)
        
        # 导入按钮
        import_layout = QHBoxLayout()
        self.import_button = QPushButton("Import Selected Files")
        self.import_button.setEnabled(False)
        
        import_layout.addWidget(self.import_button)
        import_layout.addStretch()
        
        layout.addLayout(import_layout)
        
        # 支持的格式说明
        format_label = QLabel(
            "Supported formats: JSON (.json), CSV (.csv), TXT (.txt)\n"
            "Required fields: name, size, frequencies, node_count, element_count"
        )
        format_label.setStyleSheet("color: #666; font-size: 11px;")
        layout.addWidget(format_label)
        
        # 导入日志
        layout.addWidget(QLabel("Import Log:"))
        self.import_log = QTextEdit()
        self.import_log.setMaximumHeight(150)
        self.import_log.setReadOnly(True)
        layout.addWidget(self.import_log)
        
        # 连接信号
        self.browse_button.clicked.connect(self.browse_files)
        self.import_button.clicked.connect(self.import_files)
        
        return group
    
    def create_management_group(self) -> QGroupBox:
        """创建结果管理组"""
        group = QGroupBox("Imported Results Management")
        layout = QVBoxLayout(group)
        
        # 结果表格
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(7)
        self.results_table.setHorizontalHeaderLabels([
            "Name", "Size (mm)", "Modes", "Nodes", "Elements", "Source", "Import Time"
        ])
        
        # 设置表格属性
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.results_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.results_table)
        
        # 统计信息
        self.stats_label = QLabel()
        self.update_stats_label()
        layout.addWidget(self.stats_label)
        
        return group
    
    def browse_files(self):
        """浏览文件"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setNameFilter(
            "Modal Results Files (*.json *.csv *.txt);;JSON Files (*.json);;CSV Files (*.csv);;Text Files (*.txt)"
        )
        
        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            if selected_files:
                self.file_path_edit.setText("; ".join(selected_files))
                self.import_button.setEnabled(True)
    
    def import_files(self):
        """导入文件"""
        file_paths = self.file_path_edit.text().split(";")
        file_paths = [path.strip() for path in file_paths if path.strip()]
        
        if not file_paths:
            QMessageBox.warning(self, "Warning", "Please select files to import.")
            return
        
        self.import_log.clear()
        success_count = 0
        total_count = len(file_paths)
        
        for file_path in file_paths:
            if os.path.exists(file_path):
                self.import_log.append(f"Importing: {os.path.basename(file_path)}")
                
                if self.data_manager.import_from_file(file_path):
                    success_count += 1
                    self.import_log.append(f"✅ Success: {os.path.basename(file_path)}")
                else:
                    self.import_log.append(f"❌ Failed: {os.path.basename(file_path)}")
            else:
                self.import_log.append(f"❌ File not found: {file_path}")
        
        # 更新界面
        self.load_imported_results()
        self.import_log.append(f"\nImport completed: {success_count}/{total_count} files successful")
        
        if success_count > 0:
            self.results_imported.emit()
            QMessageBox.information(
                self, "Import Complete", 
                f"Successfully imported {success_count} out of {total_count} files."
            )
        
        # 清空文件选择
        self.file_path_edit.clear()
        self.import_button.setEnabled(False)
    
    def load_imported_results(self):
        """加载导入的结果到表格"""
        results = self.data_manager.get_imported_results()
        
        self.results_table.setRowCount(len(results))
        
        for row, result in enumerate(results):
            self.results_table.setItem(row, 0, QTableWidgetItem(result.name))
            self.results_table.setItem(row, 1, QTableWidgetItem(f"{result.size:.2f}"))
            self.results_table.setItem(row, 2, QTableWidgetItem(str(len(result.frequencies))))
            self.results_table.setItem(row, 3, QTableWidgetItem(str(result.node_count)))
            self.results_table.setItem(row, 4, QTableWidgetItem(str(result.element_count)))
            self.results_table.setItem(row, 5, QTableWidgetItem(result.source))
            self.results_table.setItem(row, 6, QTableWidgetItem(result.import_time))
        
        self.update_stats_label()
    
    def update_stats_label(self):
        """更新统计信息标签"""
        imported_count = len(self.data_manager.get_imported_results())
        current_count = len(self.data_manager.get_current_results())
        total_count = imported_count + current_count
        
        self.stats_label.setText(
            f"Statistics: {imported_count} imported, {current_count} current, {total_count} total results"
        )
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        if self.results_table.itemAt(position) is None:
            return
        
        menu = QMenu(self)
        
        rename_action = QAction("Rename", self)
        rename_action.triggered.connect(self.rename_result)
        menu.addAction(rename_action)
        
        delete_action = QAction("Delete", self)
        delete_action.triggered.connect(self.delete_result)
        menu.addAction(delete_action)
        
        menu.addSeparator()
        
        view_details_action = QAction("View Details", self)
        view_details_action.triggered.connect(self.view_result_details)
        menu.addAction(view_details_action)
        
        menu.exec(self.results_table.mapToGlobal(position))
    
    def rename_result(self):
        """重命名结果"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            return
        
        results = self.data_manager.get_imported_results()
        if current_row >= len(results):
            return
        
        current_name = results[current_row].name
        new_name, ok = QInputDialog.getText(
            self, "Rename Result", "Enter new name:", text=current_name
        )
        
        if ok and new_name.strip() and new_name != current_name:
            results[current_row].name = new_name.strip()
            self.data_manager.save_data()
            self.load_imported_results()
            self.results_updated.emit()
    
    def delete_result(self):
        """删除结果"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            return
        
        results = self.data_manager.get_imported_results()
        if current_row >= len(results):
            return
        
        result_name = results[current_row].name
        reply = QMessageBox.question(
            self, "Delete Result", 
            f"Are you sure you want to delete '{result_name}'?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self.data_manager.remove_imported_result(current_row):
                self.load_imported_results()
                self.results_updated.emit()
    
    def view_result_details(self):
        """查看结果详情"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            return
        
        results = self.data_manager.get_imported_results()
        if current_row >= len(results):
            return
        
        result = results[current_row]
        
        details = f"""
Result Details:

Name: {result.name}
Mesh Size: {result.size} mm
Modal Frequencies: {result.frequencies}
Node Count: {result.node_count:,}
Element Count: {result.element_count:,}
Source: {result.source}
Import Time: {result.import_time}
File Path: {result.file_path}
Description: {result.description}
        """
        
        QMessageBox.information(self, f"Details - {result.name}", details)
    
    def export_results(self):
        """导出结果"""
        if not self.data_manager.get_all_results():
            QMessageBox.warning(self, "Warning", "No results to export.")
            return
        
        file_dialog = QFileDialog(self)
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setNameFilter("JSON Files (*.json);;CSV Files (*.csv)")
        
        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            if self.data_manager.export_to_file(file_path):
                QMessageBox.information(self, "Export Complete", f"Results exported to {file_path}")
            else:
                QMessageBox.critical(self, "Export Failed", "Failed to export results.")
    
    def clear_all_results(self):
        """清空所有导入的结果"""
        if not self.data_manager.get_imported_results():
            QMessageBox.information(self, "Info", "No imported results to clear.")
            return
        
        reply = QMessageBox.question(
            self, "Clear All Results", 
            "Are you sure you want to clear all imported results?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.data_manager.clear_imported_results()
            self.load_imported_results()
            self.results_updated.emit()
            QMessageBox.information(self, "Cleared", "All imported results have been cleared.")
