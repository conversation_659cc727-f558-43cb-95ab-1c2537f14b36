"""
工作流程状态管理器

管理振动传递计算软件的工作流程状态，确保按正确顺序完成各个步骤

作者: [作者名]
日期: [日期]
"""

import json
import os
from typing import Dict, List, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class WorkflowStep(Enum):
    """工作流程步骤枚举"""
    PREPROCESSING = "preprocessing"
    CONNECTION = "connection"
    ANALYSIS = "analysis"
    CONSTRAIN = "constrain"
    MESH_VALIDATION = "mesh_validation"
    RESULT = "result"
    POST_PROCESSING = "post_processing"

class WorkflowState:
    """工作流程状态管理器"""
    
    def __init__(self, state_file: str = "config/workflow_state.json"):
        """
        初始化工作流程状态管理器
        
        Args:
            state_file: 状态文件路径
        """
        self.state_file = state_file
        self.completed_steps: Dict[str, bool] = {}
        self.step_data: Dict[str, Dict] = {}
        self._load_state()
    
    def _load_state(self) -> None:
        """从文件加载状态"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.completed_steps = data.get('completed_steps', {})
                    self.step_data = data.get('step_data', {})
                logger.info(f"工作流程状态已从 {self.state_file} 加载")
            else:
                self._initialize_default_state()
                logger.info("初始化默认工作流程状态")
        except Exception as e:
            logger.error(f"加载工作流程状态失败: {e}")
            self._initialize_default_state()
    
    def _initialize_default_state(self) -> None:
        """初始化默认状态"""
        self.completed_steps = {step.value: False for step in WorkflowStep}
        self.step_data = {step.value: {} for step in WorkflowStep}
    
    def _save_state(self) -> None:
        """保存状态到文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.state_file), exist_ok=True)
            
            data = {
                'completed_steps': self.completed_steps,
                'step_data': self.step_data
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"工作流程状态已保存到 {self.state_file}")
        except Exception as e:
            logger.error(f"保存工作流程状态失败: {e}")
    
    def mark_step_completed(self, step: WorkflowStep, data: Optional[Dict] = None) -> None:
        """
        标记步骤为已完成
        
        Args:
            step: 工作流程步骤
            data: 步骤相关数据
        """
        self.completed_steps[step.value] = True
        if data:
            self.step_data[step.value] = data
        
        self._save_state()
        logger.info(f"步骤 {step.value} 已标记为完成")
    
    def mark_step_incomplete(self, step: WorkflowStep) -> None:
        """
        标记步骤为未完成
        
        Args:
            step: 工作流程步骤
        """
        self.completed_steps[step.value] = False
        self.step_data[step.value] = {}
        
        self._save_state()
        logger.info(f"步骤 {step.value} 已标记为未完成")
    
    def is_step_completed(self, step: WorkflowStep) -> bool:
        """
        检查步骤是否已完成
        
        Args:
            step: 工作流程步骤
            
        Returns:
            bool: 步骤是否已完成
        """
        return self.completed_steps.get(step.value, False)
    
    def get_step_data(self, step: WorkflowStep) -> Dict:
        """
        获取步骤数据
        
        Args:
            step: 工作流程步骤
            
        Returns:
            Dict: 步骤数据
        """
        return self.step_data.get(step.value, {})
    
    def can_access_step(self, step: WorkflowStep) -> bool:
        """
        检查是否可以访问指定步骤
        
        Args:
            step: 工作流程步骤
            
        Returns:
            bool: 是否可以访问
        """
        # 定义步骤依赖关系
        dependencies = {
            WorkflowStep.PREPROCESSING: [],  # 前处理无依赖
            WorkflowStep.CONNECTION: [],     # 连接设置无依赖
            WorkflowStep.ANALYSIS: [],       # 分析设置无依赖
            WorkflowStep.CONSTRAIN: [],      # 约束设置无依赖
            WorkflowStep.MESH_VALIDATION: [  # 网格无关性验证需要前三个步骤完成
                WorkflowStep.CONNECTION,
                WorkflowStep.ANALYSIS,
                WorkflowStep.CONSTRAIN
            ],
            WorkflowStep.RESULT: [           # 计算结果需要网格验证完成
                WorkflowStep.MESH_VALIDATION
            ],
            WorkflowStep.POST_PROCESSING: [  # 后处理需要计算结果完成
                WorkflowStep.RESULT
            ]
        }
        
        required_steps = dependencies.get(step, [])
        
        # 检查所有依赖步骤是否已完成
        for required_step in required_steps:
            if not self.is_step_completed(required_step):
                return False
        
        return True
    
    def get_missing_dependencies(self, step: WorkflowStep) -> List[WorkflowStep]:
        """
        获取指定步骤缺失的依赖
        
        Args:
            step: 工作流程步骤
            
        Returns:
            List[WorkflowStep]: 缺失的依赖步骤列表
        """
        dependencies = {
            WorkflowStep.MESH_VALIDATION: [
                WorkflowStep.CONNECTION,
                WorkflowStep.ANALYSIS,
                WorkflowStep.CONSTRAIN
            ],
            WorkflowStep.RESULT: [
                WorkflowStep.MESH_VALIDATION
            ],
            WorkflowStep.POST_PROCESSING: [
                WorkflowStep.RESULT
            ]
        }
        
        required_steps = dependencies.get(step, [])
        missing = []
        
        for required_step in required_steps:
            if not self.is_step_completed(required_step):
                missing.append(required_step)
        
        return missing
    
    def reset_workflow(self) -> None:
        """重置整个工作流程"""
        self._initialize_default_state()
        self._save_state()
        logger.info("工作流程已重置")
    
    def get_completion_status(self) -> Dict[str, bool]:
        """
        获取所有步骤的完成状态
        
        Returns:
            Dict[str, bool]: 步骤完成状态字典
        """
        return self.completed_steps.copy()
    
    def get_next_available_step(self) -> Optional[WorkflowStep]:
        """
        获取下一个可用的步骤
        
        Returns:
            Optional[WorkflowStep]: 下一个可用的步骤，如果没有则返回None
        """
        for step in WorkflowStep:
            if not self.is_step_completed(step) and self.can_access_step(step):
                return step
        return None

# 全局工作流程状态管理器实例
_workflow_state = None

def get_workflow_state() -> WorkflowState:
    """获取全局工作流程状态管理器实例"""
    global _workflow_state
    if _workflow_state is None:
        _workflow_state = WorkflowState()
    return _workflow_state

def reset_global_workflow_state() -> None:
    """重置全局工作流程状态"""
    global _workflow_state
    _workflow_state = None

# 导航相关的辅助功能

def get_workflow_step_dependencies() -> Dict[WorkflowStep, List[WorkflowStep]]:
    """获取工作流程步骤依赖关系映射

    Returns:
        Dict[WorkflowStep, List[WorkflowStep]]: 步骤依赖关系映射
    """
    return {
        WorkflowStep.PREPROCESSING: [],  # 前处理无依赖
        WorkflowStep.CONNECTION: [],     # 连接设置无依赖
        WorkflowStep.ANALYSIS: [],       # 分析设置无依赖
        WorkflowStep.CONSTRAIN: [],      # 约束设置无依赖
        WorkflowStep.MESH_VALIDATION: [  # 网格无关性验证需要前三个步骤完成
            WorkflowStep.CONNECTION,
            WorkflowStep.ANALYSIS,
            WorkflowStep.CONSTRAIN
        ],
        WorkflowStep.RESULT: [           # 计算结果需要网格验证完成
            WorkflowStep.MESH_VALIDATION
        ],
        WorkflowStep.POST_PROCESSING: [  # 后处理需要计算结果完成
            WorkflowStep.RESULT
        ]
    }

def get_workflow_step_display_names() -> Dict[WorkflowStep, str]:
    """获取工作流程步骤的中文显示名称

    Returns:
        Dict[WorkflowStep, str]: 步骤显示名称映射
    """
    return {
        WorkflowStep.PREPROCESSING: "前处理",
        WorkflowStep.CONNECTION: "连接设置",
        WorkflowStep.ANALYSIS: "分析设置",
        WorkflowStep.CONSTRAIN: "设置约束",
        WorkflowStep.MESH_VALIDATION: "网格无关性验证",
        WorkflowStep.RESULT: "计算结果",
        WorkflowStep.POST_PROCESSING: "后处理"
    }
