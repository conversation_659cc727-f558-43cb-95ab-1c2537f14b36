#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量模态计算功能测试脚本

此脚本用于测试新实现的BatchModalCalculationManager类的基本功能。

作者: AI Assistant
日期: 2025-07-31
"""

import sys
import os
import logging
import tempfile
import json
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_batch_modal_calculation_manager():
    """测试BatchModalCalculationManager的基本功能"""
    try:
        # 导入必要的模块
        from PySide6.QtCore import QCoreApplication
        from PySide6.QtWidgets import QApplication
        from window_manager import WindowManager
        from core.mesh_manager import MeshParameter, ElementType
        from views.mesh_window_merged import BatchModalCalculationManager
        
        logger.info("开始测试BatchModalCalculationManager")
        
        # 创建Qt应用程序（测试需要）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        # 创建窗口管理器
        window_manager = WindowManager()
        
        # 创建测试网格参数
        test_meshes = [
            MeshParameter("test_mesh_12mm", 12.0, ElementType.TETRAHEDRON),
            MeshParameter("test_mesh_8mm", 8.0, ElementType.TETRAHEDRON),
            MeshParameter("test_mesh_15mm", 15.0, ElementType.TETRAHEDRON)
        ]
        
        # 创建测试计算参数
        calc_params = {
            'modal_count': 5,
            'limit_freq': True,
            'freq_min': 0.0,
            'freq_max': 1000.0
        }
        
        logger.info(f"创建测试网格: {len(test_meshes)} 个")
        for mesh in test_meshes:
            logger.info(f"  - {mesh.name}: {mesh.size}mm, {mesh.element_type}")
        
        # 创建BatchModalCalculationManager实例
        batch_manager = BatchModalCalculationManager(window_manager)
        logger.info("BatchModalCalculationManager 创建成功")
        
        # 测试基本属性
        assert batch_manager.window_manager == window_manager
        assert len(batch_manager.batch_meshes) == 0
        assert len(batch_manager.completed_meshes) == 0
        assert len(batch_manager.failed_meshes) == 0
        logger.info("✅ 基本属性测试通过")
        
        # 测试文件名解析功能
        test_filename = "modal_freq_0.012.json"
        mesh_size = batch_manager._extract_mesh_size_from_filename(test_filename)
        expected_size = 12.0  # 0.012m = 12mm
        assert abs(mesh_size - expected_size) < 0.001, f"期望 {expected_size}, 得到 {mesh_size}"
        logger.info(f"✅ 文件名解析测试通过: {test_filename} -> {mesh_size}mm")
        
        # 测试网格匹配功能
        batch_manager.batch_meshes = test_meshes
        found_mesh = batch_manager._find_mesh_by_size(12.0)
        assert found_mesh is not None
        assert found_mesh.name == "test_mesh_12mm"
        logger.info(f"✅ 网格匹配测试通过: 找到网格 {found_mesh.name}")
        
        # 测试输出目录创建
        output_dir = batch_manager._create_output_directory()
        assert os.path.exists(output_dir)
        logger.info(f"✅ 输出目录创建测试通过: {output_dir}")
        
        # 测试期望文件列表准备
        batch_manager.output_directory = output_dir
        batch_manager._prepare_expected_files()
        assert len(batch_manager.expected_files) == len(test_meshes) * 2  # 每个网格2个文件
        logger.info(f"✅ 期望文件列表准备测试通过: {len(batch_manager.expected_files)} 个文件")
        
        # 测试配置文件生成
        batch_manager.calc_params = calc_params
        config_path = batch_manager._create_batch_mesh_config()
        assert os.path.exists(config_path)
        
        # 验证配置文件内容
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        assert config_data['batch_mode'] == True
        assert len(config_data['element_size']) == len(test_meshes)
        assert len(config_data['mesh_names']) == len(test_meshes)
        logger.info(f"✅ 配置文件生成测试通过: {config_path}")
        
        # 测试资源清理
        batch_manager.cleanup()
        logger.info("✅ 资源清理测试通过")
        
        logger.info("🎉 所有测试通过！BatchModalCalculationManager 基本功能正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}", exc_info=True)
        return False

def test_file_parsing():
    """测试文件解析功能"""
    try:
        from views.mesh_window_merged import BatchModalCalculationManager
        from window_manager import WindowManager
        
        logger.info("开始测试文件解析功能")
        
        window_manager = WindowManager()
        batch_manager = BatchModalCalculationManager(window_manager)
        
        # 测试用例
        test_cases = [
            ("modal_freq_0.012.json", 12.0),
            ("modal_freq_0.008.json", 8.0),
            ("modal_freq_0.015.json", 15.0),
            ("modal_freq_0.0065.json", 6.5),
            ("invalid_file.json", None),
            ("modal_freq_.json", None),
            ("not_modal_file.json", None)
        ]
        
        for filename, expected in test_cases:
            result = batch_manager._extract_mesh_size_from_filename(filename)
            if expected is None:
                assert result is None, f"文件 {filename} 应该返回 None，但得到 {result}"
            else:
                assert result is not None, f"文件 {filename} 不应该返回 None"
                assert abs(result - expected) < 0.001, f"文件 {filename} 期望 {expected}，得到 {result}"
            
            logger.info(f"✅ {filename} -> {result}")
        
        logger.info("🎉 文件解析功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 文件解析测试失败: {str(e)}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始批量模态计算功能测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 运行测试
    tests = [
        ("文件解析功能测试", test_file_parsing),
        ("BatchModalCalculationManager基本功能测试", test_batch_modal_calculation_manager)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}", exc_info=True)
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！批量模态计算功能基本实现正确")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
