"""
新建项目对话框模块 (优化版)

此模块定义了新建项目的对话框类，负责：
1. 模型文件选择
2. 项目名称输入和验证
3. 保存路径选择和验证
4. 输入验证（仅英文字符）

界面经过现代化美化，提升了用户体验，但保持原有功能和代码架构不变。

作者: [作者名]
日期: [日期]
"""

import os
import re
import logging
from typing import Tuple
from PySide6.QtCore import Qt, QCoreApplication, QSize
from PySide6.QtGui import QFont, QIcon # [优化] 导入QIcon
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog,
    QMessageBox, QGroupBox, QTextEdit, QFrame
)

logger = logging.getLogger(__name__)

def tr(text: str) -> str:
    """翻译函数的便捷包装"""
    return QCoreApplication.translate("NewProjectDialog", text)

class NewProjectDialog(QDialog):
    """新建项目对话框类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.model_file_path = ""
        self.project_name = ""
        self.save_directory = ""
        
        self.setup_ui()
        self.setup_connections()
        self.apply_styles()
        
        logger.info("新建项目对话框初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(tr("新建工作台项目"))
        self.setModal(True)
        self.setMinimumSize(720, 600)
        self.resize(800, 650)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20) # [优化] 调整主布局间距
        main_layout.setContentsMargins(28, 28, 28, 28) # [优化] 调整边距
        
        # 标题
        title_label = QLabel(tr("创建新的ANSYS Workbench项目"))
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("titleLabel")
        main_layout.addWidget(title_label)
        
        # [优化] 将QTextEdit替换为QLabel以实现更好的UI集成
        info_label = QLabel()
        info_label.setObjectName("infoLabel")
        info_label.setText(tr(
            "请选择CAD模型文件，为您的项目命名，并指定一个保存位置。<br>"
            "<b>注意:</b> 项目名称和保存路径必须使用<b>纯英文</b>字符。"
        ))
        info_label.setWordWrap(True)
        info_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(info_label)
        
        # 模型文件选择组
        model_group = QGroupBox(tr("1. 模型文件选择"))
        model_layout = QFormLayout(model_group) # [优化] 直接使用QFormLayout更简洁
        model_layout.setRowWrapPolicy(QFormLayout.WrapAllRows)
        model_layout.setLabelAlignment(Qt.AlignLeft)
        
        self.model_file_edit = QLineEdit()
        self.model_file_edit.setReadOnly(True)
        self.model_file_edit.setPlaceholderText(tr("请选择CAD模型文件..."))
        
        self.model_browse_btn = QPushButton(tr("浏览"))
        # [优化] 添加图标
        self.model_browse_btn.setIcon(QIcon.fromTheme("document-open", QIcon(":/icons/folder-open.svg")))
        
        model_file_layout = QHBoxLayout()
        model_file_layout.addWidget(self.model_file_edit)
        model_file_layout.addWidget(self.model_browse_btn)
        
        model_layout.addRow(tr("模型文件:"), model_file_layout)
        main_layout.addWidget(model_group)
        
        # 项目配置组
        config_group = QGroupBox(tr("2. 项目配置"))
        config_layout = QFormLayout(config_group)
        config_layout.setRowWrapPolicy(QFormLayout.WrapAllRows)
        config_layout.setLabelAlignment(Qt.AlignLeft)

        # 项目名称
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText(tr("例如：my_simulation_project"))
        config_layout.addRow(tr("项目名称:"), self.project_name_edit)
        
        # 保存路径
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setReadOnly(True)
        self.save_path_edit.setPlaceholderText(tr("选择项目保存目录..."))
        
        self.save_browse_btn = QPushButton(tr("浏览"))
        # [优化] 添加图标
        self.save_browse_btn.setIcon(QIcon.fromTheme("folder-open", QIcon(":/icons/folder.svg")))
        
        save_path_layout = QHBoxLayout()
        save_path_layout.addWidget(self.save_path_edit)
        save_path_layout.addWidget(self.save_browse_btn)
        
        config_layout.addRow(tr("保存路径:"), save_path_layout)
        main_layout.addWidget(config_group)
        
        main_layout.addStretch(1) # [优化] 添加一个弹性空间，使按钮组能固定在底部
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setObjectName("separatorLine") # [优化] 添加对象名以便样式化
        main_layout.addWidget(line)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton(tr("取消"))
        self.cancel_btn.setObjectName("cancelButton")
        # [优化] 添加图标
        self.cancel_btn.setIcon(QIcon.fromTheme("window-close", QIcon(":/icons/close.svg")))

        self.create_btn = QPushButton(tr("创建项目"))
        self.create_btn.setDefault(True)
        self.create_btn.setObjectName("createButton")
        # [优化] 添加图标
        self.create_btn.setIcon(QIcon.fromTheme("document-new", QIcon(":/icons/check.svg")))
        
        # [优化] 为所有按钮设置统一的图标大小
        for btn in [self.model_browse_btn, self.save_browse_btn, self.cancel_btn, self.create_btn]:
            btn.setIconSize(QSize(16, 16))

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.create_btn)
        
        main_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.model_browse_btn.clicked.connect(self.browse_model_file)
        self.save_browse_btn.clicked.connect(self.browse_save_directory)
        self.project_name_edit.textChanged.connect(self.validate_inputs)
        # [优化] 路径和模型文件选择后也触发一次校验
        self.model_file_edit.textChanged.connect(self.validate_inputs)
        self.save_path_edit.textChanged.connect(self.validate_inputs)
        self.cancel_btn.clicked.connect(self.reject)
        self.create_btn.clicked.connect(self.create_project)
    
    def apply_styles(self):
        """[优化] 应用全新、现代化的样式表"""
        self.setStyleSheet("""
            /* --- 全局与基础 --- */
            NewProjectDialog {
                background-color: #f7f8fc;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            /* --- 标题与说明 --- */
            #titleLabel {
                color: #1f2937;
                margin-bottom: 8px;
            }
            #infoLabel {
                color: #4b5563;
                font-size: 14px;
                padding: 12px;
                background-color: #eef2ff;
                border-radius: 8px;
                border: 1px solid #e0e7ff;
                margin-bottom: 12px;
            }

            /* --- 分组框 --- */
            QGroupBox {
                font-weight: 600;
                font-size: 15px;
                color: #374151;
                border: 1px solid #d1d5db;
                border-radius: 12px;
                margin-top: 1em;
                padding: 20px 18px 18px 18px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 16px;
                padding: 0 8px;
                background-color: #f7f8fc;
            }
            
            /* --- 表单标签 --- */
            QFormLayout QLabel {
                font-weight: 500;
                font-size: 14px;
                color: #374151;
            }

            /* --- 输入框 --- */
            QLineEdit {
                padding: 10px 12px;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                font-size: 14px;
                background-color: #ffffff;
                color: #1f2937;
            }
            QLineEdit:focus {
                border: 1px solid #4f46e5;
                background-color: #ffffff;
            }
            QLineEdit:read-only {
                background-color: #f3f4f6;
                color: #6b7280;
                border-color: #e5e7eb;
            }

            /* --- 按钮通用样式 --- */
            QPushButton {
                padding: 10px 20px;
                border: 1px solid #d1d5db;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                background-color: #ffffff;
                color: #374151;
                /* [优化] 让图标和文字有一定间距 */
                spacing: 8px; 
            }
            QPushButton:hover {
                background-color: #f9fafb;
                border-color: #9ca3af;
            }
            QPushButton:pressed {
                background-color: #f3f4f6;
            }
            QPushButton:disabled {
                background-color: #f3f4f6;
                color: #9ca3af;
                border-color: #e5e7eb;
            }
            
            /* --- 浏览按钮 (更低调) --- */
            QPushButton[text="浏览"] {
                padding: 10px 14px;
            }

            /* --- 主要操作按钮: 创建 --- */
            #createButton {
                background-color: #4f46e5;
                color: white;
                border-color: #4f46e5;
            }
            #createButton:hover {
                background-color: #4338ca;
                border-color: #4338ca;
            }
            #createButton:pressed {
                background-color: #3730a3;
            }
            #createButton:disabled {
                background-color: #a5b4fc;
                border-color: #a5b4fc;
                color: #e0e7ff;
            }

            /* --- 次要操作按钮: 取消 --- */
            #cancelButton {
                background-color: transparent;
                border-color: transparent;
                color: #4b5563;
            }
            #cancelButton:hover {
                background-color: #e5e7eb;
                color: #1f2937;
                border-color: transparent;
            }
            #cancelButton:pressed {
                background-color: #d1d5db;
            }

            /* --- 分隔线 --- */
            #separatorLine {
                border: none;
                height: 1px;
                background-color: #e5e7eb;
                margin: 8px 0;
            }
        """)
    
    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            tr("选择CAD模型文件"),
            "",
            tr("CAD文件 (*.stp *.step *.iges *.igs *.sat *.x_t *.x_b);;所有文件 (*)")
        )
        
        if file_path:
            self.model_file_path = file_path
            self.model_file_edit.setText(file_path)
            logger.info(f"选择模型文件: {file_path}")
            # self.validate_inputs() # [优化] 此处不再需要，已连接到textChanged信号
    
    def browse_save_directory(self):
        """浏览保存目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            tr("选择项目保存目录"),
            ""
        )
        
        if dir_path:
            self.save_directory = dir_path
            self.save_path_edit.setText(dir_path)
            logger.info(f"选择保存目录: {dir_path}")
            # self.validate_inputs() # [优化] 此处不再需要，已连接到textChanged信号
    
    def validate_inputs(self):
        """验证所有输入并更新“创建”按钮状态"""
        # [优化] 将校验逻辑集中于此，当任何相关输入改变时触发
        model_valid = bool(self.model_file_path and os.path.exists(self.model_file_path))
        name_valid = self.validate_project_name()
        path_valid = bool(self.save_directory and os.path.exists(self.save_directory))
        
        # 启用/禁用创建按钮
        is_valid = model_valid and name_valid and path_valid
        if self.create_btn.isEnabled() != is_valid:
            self.create_btn.setEnabled(is_valid)
        
        return is_valid
    
    def validate_project_name(self) -> bool:
        """验证项目名称（仅英文字符、数字、下划线、连字符）"""
        name = self.project_name_edit.text().strip()
        if not name:
            self.project_name_edit.setStyleSheet("") # 恢复默认样式
            return False
        
        pattern = r'^[a-zA-Z0-9_-]+$'
        is_valid = bool(re.match(pattern, name))
        
        # [优化] 提供即时的视觉反馈
        if not is_valid:
            self.project_name_edit.setStyleSheet("border: 1px solid #ef4444;")
        else:
            self.project_name_edit.setStyleSheet("") # 验证通过，恢复
            
        return is_valid
    
    def validate_path_english_only(self, path: str) -> bool:
        """验证路径是否只包含ASCII字符"""
        if not path:
            return False
        try:
            path.encode('ascii')
            return True
        except UnicodeEncodeError:
            return False
    
    def create_project(self):
        """创建项目前的最终校验与确认"""
        # [优化] validate_inputs已经保证了基础有效性，这里做最终确认和特定错误提示
        if not self.validate_inputs():
            QMessageBox.warning(
                self,
                tr("输入不完整"),
                tr("请确保已选择模型文件、输入有效的项目名称并选择了保存目录。")
            )
            return
        
        project_name = self.project_name_edit.text().strip()
        
        # 检查保存路径是否包含非英文字符
        if not self.validate_path_english_only(self.save_directory):
            QMessageBox.warning(
                self,
                tr("路径错误"),
                tr("保存路径包含了非英文字符。请选择一个纯英文路径。")
            )
            return
        
        # 检查项目文件是否已存在
        project_file_path = os.path.join(self.save_directory, f"{project_name}.wbpj")
        if os.path.exists(project_file_path):
            reply = QMessageBox.question(
                self,
                tr("文件已存在"),
                tr(f"项目文件 <b>{project_name}.wbpj</b> 已存在于目标目录。<br><br>您想覆盖它吗？"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return
        
        # 保存配置并关闭对话框
        self.project_name = project_name
        
        logger.info(f"创建项目配置完成: 名称={project_name}, 模型={self.model_file_path}, 路径={self.save_directory}")
        self.accept()
    
    def get_project_config(self) -> Tuple[str, str, str]:
        """获取项目配置
        
        Returns:
            Tuple[str, str, str]: (模型文件路径, 项目名称, 保存目录)
        """
        return self.model_file_path, self.project_name, self.save_directory