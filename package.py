#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包脚本

简化的PyInstaller打包命令，用于快速打包振动传递计算软件。

使用方法:
    python package.py          # 标准打包
    python package.py --debug  # 调试模式打包
    python package.py --clean  # 清理后打包
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path

def clean_build_dirs():
    """清理构建目录"""
    dirs_to_clean = ['dist', 'build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
        else:
            print(f"📁 目录不存在: {dir_name}")

def run_pyinstaller(debug=False, clean=False):
    """运行PyInstaller"""
    if clean:
        clean_build_dirs()
    
    # 构建命令
    cmd = [sys.executable, "-m", "PyInstaller"]
    
    if clean:
        cmd.append("--clean")
    
    cmd.extend(["--noconfirm", "qt_new.spec"])
    
    if debug:
        print(f"🔧 调试模式: 启用控制台窗口")
        # 在调试模式下，可以修改spec文件中的console参数
    
    print(f"📦 执行命令: {' '.join(cmd)}")
    print("⏳ 打包中，请稍候...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ 打包成功!")
        
        # 检查输出文件
        exe_path = Path("dist/vibration_transfer/振动传递计算软件.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📊 可执行文件大小: {size_mb:.2f} MB")
            print(f"📁 输出路径: {exe_path.parent}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="振动传递计算软件快速打包工具")
    parser.add_argument("--debug", action="store_true", help="调试模式打包")
    parser.add_argument("--clean", action="store_true", help="清理后打包")
    
    args = parser.parse_args()
    
    print("🚀 振动传递计算软件 - 快速打包工具")
    print("=" * 40)
    
    success = run_pyinstaller(debug=args.debug, clean=args.clean)
    
    if success:
        print("\n🎉 打包完成!")
        print("💡 提示: 可执行文件位于 dist/vibration_transfer/ 目录")
    else:
        print("\n❌ 打包失败!")
        print("💡 提示: 请检查错误信息或使用 python build_package.py 获取详细日志")
        sys.exit(1)

if __name__ == "__main__":
    main()
