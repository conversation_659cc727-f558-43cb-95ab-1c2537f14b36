[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "vibration_transfer"
version = "0.2.0"
description = "ANSYS Workbench 振动传递计算软件"
readme = "README.md"
authors = [
    {name = "项目团队", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
]
requires-python = ">=3.12"
dependencies = [
    "PySide6>=6.5.0",
    "numpy>=1.22.0",
    "scipy>=1.8.0",
    "pandas>=1.4.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "pyyaml>=6.0",
    "jsonschema>=4.0.0",
    "librosa>=0.9.0",
    "fastapi==0.111.0",
    "uvicorn[standard]==0.29.0",
    "pydantic==2.7.1",
]

[project.urls]
Homepage = "https://github.com/yourusername/vibration_transfer"
Documentation = "https://github.com/yourusername/vibration_transfer#documentation"
Source = "https://github.com/yourusername/vibration_transfer"
Issues = "https://github.com/yourusername/vibration_transfer/issues"

[project.scripts]
vibration_transfer = "qt_new:initialize_application"

[tool.setuptools]
py-modules = ["qt_new", "window_manager", "resource_manager"]
packages = ["core", "views"]

[tool.setuptools.package-data]
"" = ["assets/icons/*.ico", "assets/styles/*.qss", "help/html/*"]

# 开发工具配置
[tool.black]
line-length = 88
target-version = ["py312"]

[tool.isort]
profile = "black"
line_length = 88 