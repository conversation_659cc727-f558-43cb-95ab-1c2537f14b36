<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>520</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>700</width>
    <height>520</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>宋体</family>
   </font>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label">
      <property name="minimumSize">
       <size>
        <width>350</width>
        <height>450</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>黑体</family>
        <pointsize>30</pointsize>
        <bold>true</bold>
        <hintingpreference>PreferDefaultHinting</hintingpreference>
       </font>
      </property>
      <property name="text">
       <string>振动传递计算软件</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
     </widget>
    </item>
    <item>
     <spacer name="horizontalSpacer">
      <property name="orientation">
       <enum>Qt::Orientation::Horizontal</enum>
      </property>
      <property name="sizeType">
       <enum>QSizePolicy::Policy::Minimum</enum>
      </property>
      <property name="sizeHint" stdset="0">
       <size>
        <width>115</width>
        <height>20</height>
       </size>
      </property>
     </spacer>
    </item>
    <item>
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="QPushButton" name="preprocessing">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>前处理</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="mesh">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
          <bold>false</bold>
         </font>
        </property>
        <property name="text">
         <string>网格无关性验证</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="connection">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
         </font>
        </property>
        <property name="text">
         <string>连接设置</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="analysis">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
         </font>
        </property>
        <property name="text">
         <string>分析设置</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="constrain">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
         </font>
        </property>
        <property name="text">
         <string>设置约束</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="result">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
         </font>
        </property>
        <property name="text">
         <string>计算结果</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="post">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>15</pointsize>
         </font>
        </property>
        <property name="text">
         <string>后处理</string>
        </property>
        <property name="autoDefault">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>700</width>
     <height>33</height>
    </rect>
   </property>
   <widget class="QMenu" name="menu">
    <property name="font">
     <font>
      <family>Times New Roman</family>
      <pointsize>12</pointsize>
     </font>
    </property>
    <property name="title">
     <string>File</string>
    </property>
    <addaction name="actionnew"/>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuSeting">
    <property name="title">
     <string>Seting</string>
    </property>
    <addaction name="actionwb_root_file"/>
    <addaction name="actionworkfile"/>
   </widget>
   <widget class="QMenu" name="menuReport">
    <property name="title">
     <string>Report</string>
    </property>
    <addaction name="actionExport"/>
   </widget>

   <addaction name="menu"/>
   <addaction name="menuSeting"/>
   <addaction name="menuReport"/>

  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <action name="actionwb_root_file">
   <property name="text">
    <string>wb root file</string>
   </property>
  </action>
  <action name="actionworkfile">
   <property name="text">
    <string>workfile</string>
   </property>
  </action>
  <action name="actionnew">
   <property name="text">
    <string>New</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="text">
    <string>Save</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>Exit</string>
   </property>
  </action>
  <action name="actionExport">
   <property name="text">
    <string>Export</string>
   </property>
  </action>

  <action name="actionAbout">
   <property name="text">
    <string>About</string>
   </property>
  </action>
  <action name="actionOpen">
   <property name="text">
    <string>Open</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
