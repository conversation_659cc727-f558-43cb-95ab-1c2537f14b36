altgraph==0.17.4
annotated-types==0.7.0
anyio==4.9.0
attrs==25.3.0
audioread==3.0.1
build==1.2.2.post1
certifi==2025.6.15
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
contourpy==1.3.2
cycler==0.12.1
decorator==5.2.1
dnspython==2.7.0
email_validator==2.2.0
fastapi==0.111.0
fastapi-cli==0.0.7
fonttools==4.58.4
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
idna==3.10
Jinja2==3.1.6
joblib==1.5.1
jsonschema==4.24.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
lazy_loader==0.4
librosa==0.11.0
llvmlite==0.44.0
markdown-it-py==3.0.0
MarkupSafe==3.0.2
matplotlib==3.10.3
mdurl==0.1.2
msgpack==1.1.1
numba==0.61.2
numpy==2.2.6
openpyxl==3.1.5
orjson==3.10.18
packaging==25.0
pandas==2.3.0
pefile==2023.2.7
pillow==11.2.1
platformdirs==4.3.8
pooch==1.8.2
pycparser==2.22
pydantic==2.7.1
pydantic_core==2.18.2
Pygments==2.19.1
pyinstaller==6.14.1
pyinstaller-hooks-contrib==2025.5
pyparsing==3.2.3
pyproject_hooks==1.2.0
PySide6==6.9.1
PySide6_Addons==6.9.1
PySide6_Essentials==6.9.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-multipart==0.0.20
pytz==2025.2
psutil==6.1.0
pywin32-ctypes==0.2.3
PyYAML==6.0.2
referencing==0.36.2
requests==2.32.4
rich==14.0.0
rich-toolkit==0.14.7
rpds-py==0.25.1
scikit-learn==1.7.0
scipy==1.15.3
seaborn==0.13.2
setuptools==80.9.0
shellingham==1.5.4
shiboken6==6.9.1
six==1.17.0
sniffio==1.3.1
soundfile==0.13.1
soxr==0.5.0.post1
starlette==0.37.2
threadpoolctl==3.6.0
typer==0.16.0
typing_extensions==4.14.0
tzdata==2025.2
ujson==5.10.0
urllib3==2.5.0
uvicorn==0.29.0
-e git+https://github.com/jacksu666/qtproject.git@9b1b61ccfdee5fec667071c947e7081a38a99f00#egg=vibration_transfer
watchfiles==1.1.0
websockets==15.0.1
wheel==0.45.1
