#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件对话框修复验证脚本
用于测试main_window.py中的QFileDialog修复效果

使用方法:
1. 在应用程序运行时，在Python控制台中导入此模块
2. 调用相应的测试函数

示例:
    import test_file_dialog_fix
    test_file_dialog_fix.test_file_dialogs(main_window_instance)
"""

def test_file_dialogs(main_window):
    """测试所有文件对话框功能"""
    print("\n" + "="*60)
    print("🧪 文件对话框修复验证测试")
    print("="*60)
    
    # 测试1: 检查主窗口对象状态
    print("\n1️⃣ 检查主窗口对象状态:")
    try:
        print(f"  主窗口对象: {main_window}")
        print(f"  主窗口类型: {type(main_window)}")
        print(f"  主窗口可见: {main_window.isVisible()}")
        print(f"  主窗口有效: {not main_window.isHidden()}")
        print("  ✅ 主窗口对象状态正常")
    except Exception as e:
        print(f"  ❌ 主窗口对象状态异常: {e}")
        return False
    
    # 测试2: 检查UI对象状态
    print("\n2️⃣ 检查UI对象状态:")
    try:
        ui = main_window.ui
        print(f"  UI对象: {ui}")
        print(f"  UI对象类型: {type(ui)}")
        
        if hasattr(ui, 'centralwidget'):
            centralwidget = ui.centralwidget
            print(f"  centralwidget对象: {centralwidget}")
            print(f"  centralwidget可见: {centralwidget.isVisible()}")
            print("  ✅ UI对象状态正常")
        else:
            print("  ⚠️ UI对象没有centralwidget属性")
    except Exception as e:
        print(f"  ❌ UI对象状态异常: {e}")
    
    # 测试3: 手动调用文件对话框方法
    print("\n3️⃣ 测试文件对话框方法:")
    
    test_methods = [
        ("ANSYS启动文件选择", "ANSYS_Start"),
        ("工作目录选择", "ANSYS_Work"),
        ("Workbench项目文件选择", "open_workbench_project")
    ]
    
    for name, method_name in test_methods:
        print(f"\n  测试 {name}:")
        try:
            if hasattr(main_window, method_name):
                method = getattr(main_window, method_name)
                print(f"    方法存在: ✅")
                print(f"    方法对象: {method}")
                print(f"    可以调用: {'✅' if callable(method) else '❌'}")
                
                # 注意：这里不实际调用方法，因为会弹出对话框
                print(f"    状态: 准备就绪（未实际调用以避免弹窗）")
            else:
                print(f"    ❌ 方法不存在")
        except Exception as e:
            print(f"    ❌ 检查方法时出错: {e}")

def check_object_lifecycle(main_window):
    """检查对象生命周期"""
    print("\n🔍 对象生命周期检查:")
    
    try:
        # 检查主窗口
        print(f"  主窗口引用计数: {main_window.__class__.__name__}")
        print(f"  主窗口内存地址: {hex(id(main_window))}")
        
        # 检查UI对象
        if hasattr(main_window, 'ui'):
            ui = main_window.ui
            print(f"  UI对象内存地址: {hex(id(ui))}")
            
            if hasattr(ui, 'centralwidget'):
                centralwidget = ui.centralwidget
                print(f"  centralwidget内存地址: {hex(id(centralwidget))}")
                
                # 尝试访问centralwidget的属性
                try:
                    geometry = centralwidget.geometry()
                    print(f"  centralwidget几何信息: {geometry}")
                    print("  ✅ centralwidget对象有效")
                except RuntimeError as e:
                    print(f"  ❌ centralwidget对象已被删除: {e}")
                except Exception as e:
                    print(f"  ⚠️ centralwidget访问异常: {e}")
        
    except Exception as e:
        print(f"  ❌ 对象生命周期检查失败: {e}")

def simulate_dialog_error(main_window):
    """模拟对话框错误场景"""
    print("\n⚠️ 模拟错误场景测试:")
    
    # 模拟使用已删除的centralwidget
    print("  模拟使用centralwidget作为父对象的情况...")
    
    try:
        from PySide6.QtWidgets import QFileDialog
        
        # 这是修复前的调用方式（可能出错）
        if hasattr(main_window.ui, 'centralwidget'):
            centralwidget = main_window.ui.centralwidget
            print(f"    centralwidget状态: 存在")
            
            # 检查是否可以安全使用
            try:
                geometry = centralwidget.geometry()
                print(f"    centralwidget几何: {geometry}")
                print("    ✅ centralwidget可以安全使用")
            except RuntimeError:
                print("    ❌ centralwidget已被C++删除，会导致错误")
            except Exception as e:
                print(f"    ⚠️ centralwidget状态未知: {e}")
        else:
            print("    ❌ centralwidget不存在")
            
    except Exception as e:
        print(f"    ❌ 模拟测试失败: {e}")

def full_test(main_window):
    """完整测试"""
    print("\n" + "="*80)
    print("🔍 文件对话框修复完整验证开始")
    print("="*80)
    
    test_file_dialogs(main_window)
    check_object_lifecycle(main_window)
    simulate_dialog_error(main_window)
    
    print("\n" + "="*80)
    print("🔍 文件对话框修复完整验证结束")
    print("="*80)
    
    print("\n📋 修复要点总结:")
    print("  1. 使用主窗口(self)作为QFileDialog的父对象")
    print("  2. 避免使用centralwidget作为父对象")
    print("  3. 添加RuntimeError异常处理")
    print("  4. 提供用户友好的错误提示")

if __name__ == "__main__":
    print("这是一个测试模块，请在应用程序中导入使用")
    print("使用方法:")
    print("  import test_file_dialog_fix")
    print("  test_file_dialog_fix.full_test(main_window_instance)")
