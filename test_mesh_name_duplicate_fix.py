"""
测试网格名称重复问题修复

此脚本验证修复后的网格名称重复处理是否正常工作：
1. 错误信号参数修复
2. 名称重复检查
3. 唯一名称生成
4. 用户体验改善

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_error_signal_fix():
    """测试错误信号修复"""
    print("🧪 测试错误信号修复...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow
        from core.mesh_manager import MeshManager, MeshParameter
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格窗口
        window = MeshWindow()
        
        # 检查错误处理函数的参数
        import inspect
        sig = inspect.signature(window._on_mesh_error)
        params = list(sig.parameters.keys())
        
        if len(params) == 2 and 'mesh_id' in params and 'error_message' in params:
            print("✅ 错误信号处理函数参数修复正确")
            print(f"  - 参数: {params}")
        else:
            print(f"❌ 错误信号处理函数参数不正确: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 错误信号修复测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_unique_name_generation():
    """测试唯一名称生成"""
    print("\n🧪 测试唯一名称生成...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_parameter_dialog_simplified import MeshParameterDialogSimplified
        from core.mesh_manager import MeshManager, MeshParameter
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格管理器并添加一些测试网格
        mesh_manager = MeshManager()
        
        # 添加一个名为"新网格"的网格
        test_mesh = MeshParameter()
        test_mesh.name = "新网格"
        test_mesh.size = 5.0
        mesh_manager.add_mesh(test_mesh)
        
        print(f"  - 已添加测试网格: {test_mesh.name}")
        
        # 创建对话框并测试唯一名称生成
        dialog = MeshParameterDialogSimplified()
        
        # 测试生成唯一名称
        unique_name = dialog._generate_unique_mesh_name("新网格")
        
        if unique_name != "新网格" and "新网格" in unique_name:
            print(f"✅ 唯一名称生成成功: '{unique_name}'")
        else:
            print(f"❌ 唯一名称生成失败: '{unique_name}'")
            return False
        
        # 测试默认值加载是否使用唯一名称
        default_name = dialog.ui.lineEdit_mesh_name.text()
        
        if default_name != "新网格" and "新网格" in default_name:
            print(f"✅ 默认值使用唯一名称: '{default_name}'")
        else:
            print(f"⚠️ 默认值可能不是唯一名称: '{default_name}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 唯一名称生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_duplicate_validation():
    """测试重复名称验证"""
    print("\n🧪 测试重复名称验证...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_parameter_dialog_simplified import MeshParameterDialogSimplified
        from core.mesh_manager import MeshManager, MeshParameter
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格管理器并添加测试网格
        mesh_manager = MeshManager()
        
        test_mesh = MeshParameter()
        test_mesh.name = "测试网格"
        test_mesh.size = 5.0
        mesh_manager.add_mesh(test_mesh)
        
        print(f"  - 已添加测试网格: {test_mesh.name}")
        
        # 创建对话框
        dialog = MeshParameterDialogSimplified()
        
        # 设置重复的名称
        dialog.ui.lineEdit_mesh_name.setText("测试网格")
        dialog.ui.doubleSpinBox_mesh_size.setValue(10.0)
        
        # 触发验证
        dialog._validate_input()
        
        # 检查确定按钮是否被禁用
        if not dialog.ui.btn_ok.isEnabled():
            print("✅ 重复名称验证正确 - 确定按钮已禁用")
        else:
            print("❌ 重复名称验证失败 - 确定按钮仍然可用")
            return False
        
        # 测试创建参数时的验证
        mesh_param = dialog._create_parameter_from_ui()
        errors = mesh_param.validate()
        
        # 手动检查重复名称
        if mesh_manager.get_mesh_by_name(mesh_param.name):
            print("✅ 重复名称检测正确")
        else:
            print("❌ 重复名称检测失败")
            return False
        
        # 设置唯一名称
        dialog.ui.lineEdit_mesh_name.setText("唯一测试网格")
        dialog._validate_input()
        
        # 检查确定按钮是否被启用
        if dialog.ui.btn_ok.isEnabled():
            print("✅ 唯一名称验证正确 - 确定按钮已启用")
        else:
            print("❌ 唯一名称验证失败 - 确定按钮仍然禁用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 重复名称验证测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_user_experience():
    """测试用户体验改善"""
    print("\n🧪 测试用户体验改善...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_parameter_dialog_simplified import MeshParameterDialogSimplified
        from core.mesh_manager import MeshManager, MeshParameter
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格管理器并添加测试网格
        mesh_manager = MeshManager()
        
        test_mesh = MeshParameter()
        test_mesh.name = "用户测试网格"
        test_mesh.size = 5.0
        mesh_manager.add_mesh(test_mesh)
        
        # 创建对话框
        dialog = MeshParameterDialogSimplified()
        
        # 模拟用户尝试使用重复名称
        dialog.ui.lineEdit_mesh_name.setText("用户测试网格")
        dialog.ui.doubleSpinBox_mesh_size.setValue(8.0)
        
        # 模拟点击确定按钮
        try:
            # 这应该显示错误消息而不是崩溃
            mesh_param = dialog._create_parameter_from_ui()
            errors = mesh_param.validate()
            
            # 检查重复名称
            if mesh_manager.get_mesh_by_name(mesh_param.name):
                errors.append(f"网格名称 '{mesh_param.name}' 已存在，请使用其他名称")
            
            if errors:
                print("✅ 用户体验改善 - 提供清晰的错误信息")
                print(f"  - 错误信息: {errors}")
            else:
                print("❌ 用户体验测试失败 - 没有检测到重复名称")
                return False
                
        except Exception as e:
            print(f"❌ 用户体验测试异常: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 用户体验测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 网格名称重复问题修复验证")
    print("=" * 70)
    print("\n修复内容：")
    print("🔧 修复错误信号参数不匹配问题")
    print("🔧 添加重复名称检查和验证")
    print("🔧 实现唯一名称自动生成")
    print("🔧 改善用户体验和错误提示")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    success_count = 0
    total_tests = 4
    
    # 测试错误信号修复
    if test_error_signal_fix():
        success_count += 1
    
    # 测试唯一名称生成
    if test_unique_name_generation():
        success_count += 1
    
    # 测试重复名称验证
    if test_duplicate_validation():
        success_count += 1
    
    # 测试用户体验改善
    if test_user_experience():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有修复验证通过！")
        print("✅ 错误信号参数修复正确")
        print("✅ 唯一名称生成功能正常")
        print("✅ 重复名称验证有效")
        print("✅ 用户体验显著改善")
        print("\n🎯 修复效果：")
        print("• 不再出现奇怪的UUID错误信息")
        print("• 自动生成唯一的网格名称")
        print("• 实时检查和提示重复名称")
        print("• 提供清晰的错误信息和解决建议")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
        print("请检查修复的实现")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
