"""
API服务器模块

此模块实现了与上游Electron应用程序的API接口，主要功能包括：
1. 定义API数据模型
2. 创建FastAPI应用实例
3. 实现API端点
4. 提供API服务器运行函数

作者: [作者名]
日期: [日期]
"""

from fastapi import FastAPI
from pydantic import BaseModel, Field, validator
import uvicorn
import logging
from typing import Optional, Dict, Any, Callable

# 设置日志
logger = logging.getLogger("api_server")
logger.setLevel(logging.INFO)
handler = logging.FileHandler('api_server.log')
handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# 定义数据模型
class SimulationParameters(BaseModel):
    time_step: float = Field(..., alias='timeStep', description="时间步长")
    end_time: float = Field(..., alias='endTime', description="结束时间")
    force_output_folder: str = Field(..., alias='forceOutputFolder', description="力的输出文件夹路径")
    stiffness_coefficient: Optional[float] = Field(None, alias='stiffnessCoefficient', description="刚度系数")
    mass_coefficient: Optional[float] = Field(None, alias='massCoefficient', description="质量系数")
    rotation_speed: Optional[float] = Field(None, alias='rotationSpeed', description="旋转速度")
    rotation_axis: Optional[str] = Field("z", alias='rotationAxis', description="旋转轴")

# 创建FastAPI应用实例
app = FastAPI(title="振动传递计算软件API", 
              description="提供与上游Electron应用程序通信的API接口",
              version="1.0.0")

# 定义全局回调函数
update_callback: Optional[Callable[[Dict[str, Any]], None]] = None

def set_update_callback(callback_func: Callable[[Dict[str, Any]], None]) -> None:
    """设置一个回调函数，当接收到API请求时调用"""
    global update_callback
    update_callback = callback_func
    logger.info("已设置API更新回调函数")

# 创建API端点
@app.post("/api/v1/simulation-params")
async def update_simulation_parameters(params: SimulationParameters):
    """接收来自上游应用的仿真参数"""
    try:
        logger.info(f"接收到API请求，数据: {params.model_dump(by_alias=True)}")
        
        # 执行额外的业务逻辑验证
        if params.time_step and params.end_time:
            if params.end_time / params.time_step > 10000:
                return {
                    "status": "error", 
                    "message": "结束时间与时间步长比值过大，可能导致计算量过大", 
                    "data_received": params.model_dump(by_alias=True)
                }
        
        if update_callback:
            # 调用回调函数更新界面或内部状态
            update_callback(params.model_dump(by_alias=True))
            return {"status": "success", "message": "参数已更新", "data_received": params.model_dump(by_alias=True)}
        else:
            logger.warning("接收到API请求，但未设置回调函数")
            return {"status": "warning", "message": "服务器已接收参数，但未设置处理回调", "data_received": params.model_dump(by_alias=True)}
    except ValueError as e:
        logger.error(f"参数验证失败: {str(e)}")
        return {"status": "error", "message": f"参数验证失败: {str(e)}", "data_received": {}}
    except Exception as e:
        logger.error(f"处理API请求时出错: {str(e)}")
        return {"status": "error", "message": f"服务器错误: {str(e)}", "data_received": {}}

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查端点，用于验证API服务是否正常运行"""
    return {"status": "ok", "service": "振动传递计算软件API"}

# 运行服务器的函数
def run_api_server(host: str = "127.0.0.1", port: int = 8000) -> None:
    """在指定的主机和端口上运行FastAPI服务"""
    logger.info(f"启动API服务器，地址: {host}:{port}")
    try:
        # 将 reload 设置为 False，因为这是在生产线程中运行
        uvicorn.run(app, host=host, port=port, log_level="info")
    except Exception as e:
        logger.error(f"API服务器启动失败: {str(e)}") 