"""
测试matplotlib警告修复

验证matplotlib图表组件中颜色属性警告的修复效果

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_matplotlib_color_warning():
    """测试matplotlib颜色属性警告修复"""
    print("🧪 测试matplotlib颜色属性警告修复...")
    
    try:
        # 捕获所有警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 导入图表组件
            from ui.components.modal_chart_widget import ModalChartWidget
            
            # 创建图表组件
            chart_widget = ModalChartWidget()
            
            # 准备测试数据（包含当前结果和导入结果）
            test_data = [
                {
                    'name': '[当前] Test Mesh 1',
                    'size': 1.0,
                    'frequencies': [42.0, 75.0, 107.5, 144.0, 187.5],
                    'node_count': 15000,
                    'element_count': 12000,
                    'source': 'current'
                },
                {
                    'name': '[导入] Reference Model',
                    'size': 1.5,
                    'frequencies': [41.8, 74.6, 107.1, 143.5, 186.4],
                    'node_count': 12000,
                    'element_count': 9500,
                    'source': 'imported'
                }
            ]
            
            # 测试频率对比图（最可能触发颜色警告的图表类型）
            chart_widget.update_chart("frequency_comparison", test_data)
            
            # 检查颜色相关警告
            color_warnings = [warning for warning in w 
                            if 'color' in str(warning.message).lower() and 'override' in str(warning.message)]
            
            print(f"  颜色属性警告数量: {len(color_warnings)}")
            
            if color_warnings:
                print("  ⚠️ 仍有颜色属性警告:")
                for warning in color_warnings:
                    print(f"    {warning.message}")
                return False
            else:
                print("  ✅ 无颜色属性警告")
                return True
                
    except Exception as e:
        print(f"  ❌ matplotlib颜色警告测试失败: {str(e)}")
        return False

def test_patch_creation():
    """测试Patch对象创建"""
    print("\n🧪 测试Patch对象创建...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用无GUI后端
        from matplotlib.patches import Patch
        
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 测试修复前的方式（会产生警告）
            print("  测试修复前的方式:")
            try:
                patch_old = Patch(color='gray', alpha=0.6, edgecolor='black', linewidth=1.5, label='Test')
                old_warnings = [warning for warning in w if 'color' in str(warning.message)]
                print(f"    警告数量: {len(old_warnings)}")
                if old_warnings:
                    print(f"    警告内容: {old_warnings[0].message}")
            except Exception as e:
                print(f"    创建失败: {str(e)}")
            
            # 清空警告列表
            w.clear()
            
            # 测试修复后的方式（不应产生警告）
            print("  测试修复后的方式:")
            try:
                patch_new = Patch(facecolor='gray', alpha=0.6, edgecolor='black', linewidth=1.5, label='Test')
                new_warnings = [warning for warning in w if 'color' in str(warning.message)]
                print(f"    警告数量: {len(new_warnings)}")
                if new_warnings:
                    print(f"    警告内容: {new_warnings[0].message}")
                else:
                    print("    ✅ 无警告")
                
                return len(new_warnings) == 0
                
            except Exception as e:
                print(f"    创建失败: {str(e)}")
                return False
                
    except Exception as e:
        print(f"  ❌ Patch创建测试失败: {str(e)}")
        return False

def test_chart_legend_creation():
    """测试图表图例创建"""
    print("\n🧪 测试图表图例创建...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        import matplotlib.pyplot as plt
        from matplotlib.patches import Patch
        
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 创建测试图表
            fig, ax = plt.subplots(figsize=(8, 6))
            
            # 模拟图表组件中的图例创建逻辑
            handles = []
            
            # 添加数据源图例（使用修复后的方式）
            source_handles = []
            source_handles.append(Patch(facecolor='gray', alpha=0.8, label='Current Results'))
            source_handles.append(Patch(facecolor='gray', alpha=0.6,
                                      edgecolor='black', linewidth=1.5, label='Imported Results'))
            handles.extend(source_handles)
            
            # 创建图例
            ax.legend(handles=handles, bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # 保存图表
            fig.savefig("test_legend_fix.png", dpi=100, bbox_inches='tight')
            plt.close(fig)
            
            # 检查警告
            color_warnings = [warning for warning in w 
                            if 'color' in str(warning.message).lower() and 'override' in str(warning.message)]
            
            print(f"  图例创建警告数量: {len(color_warnings)}")
            
            if color_warnings:
                print("  ⚠️ 仍有图例创建警告:")
                for warning in color_warnings:
                    print(f"    {warning.message}")
                return False
            else:
                print("  ✅ 无图例创建警告")
                
                # 检查文件是否生成
                if os.path.exists("test_legend_fix.png"):
                    file_size = os.path.getsize("test_legend_fix.png")
                    print(f"  ✅ 测试图例已生成: {file_size:,} 字节")
                    return True
                else:
                    print("  ❌ 测试图例生成失败")
                    return False
                
    except Exception as e:
        print(f"  ❌ 图表图例测试失败: {str(e)}")
        return False

def test_comprehensive_chart_warnings():
    """测试综合图表警告"""
    print("\n🧪 测试综合图表警告...")
    
    try:
        # 捕获所有警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 导入并创建图表组件
            from ui.components.modal_chart_widget import ModalChartWidget
            chart_widget = ModalChartWidget()
            
            # 准备复杂的测试数据
            complex_data = [
                {
                    'name': '[当前] Fine Mesh',
                    'size': 0.5,
                    'frequencies': [45.2, 78.1, 112.3, 149.7, 192.4, 240.8],
                    'node_count': 25000,
                    'element_count': 20000,
                    'source': 'current'
                },
                {
                    'name': '[当前] Medium Mesh',
                    'size': 1.0,
                    'frequencies': [44.8, 77.5, 111.2, 148.1, 190.3, 238.2],
                    'node_count': 15000,
                    'element_count': 12000,
                    'source': 'current'
                },
                {
                    'name': '[导入] Reference A',
                    'size': 0.8,
                    'frequencies': [45.0, 77.8, 111.8, 148.9, 191.2, 239.5],
                    'node_count': 18000,
                    'element_count': 14500,
                    'source': 'imported'
                },
                {
                    'name': '[导入] Reference B',
                    'size': 1.2,
                    'frequencies': [44.5, 77.2, 110.9, 147.8, 189.8, 237.1],
                    'node_count': 12000,
                    'element_count': 9600,
                    'source': 'imported'
                }
            ]
            
            # 测试所有图表类型
            chart_types = ["frequency_comparison", "mode_distribution", "mesh_convergence"]
            
            warning_counts = {}
            for chart_type in chart_types:
                print(f"    测试图表类型: {chart_type}")
                
                # 清空之前的警告
                w.clear()
                
                # 更新图表
                chart_widget.update_chart(chart_type, complex_data)
                
                # 统计警告
                all_warnings = len(w)
                color_warnings = len([warning for warning in w 
                                    if 'color' in str(warning.message).lower()])
                font_warnings = len([warning for warning in w 
                                   if 'font' in str(warning.message).lower() or 'glyph' in str(warning.message).lower()])
                
                warning_counts[chart_type] = {
                    'total': all_warnings,
                    'color': color_warnings,
                    'font': font_warnings
                }
                
                print(f"      总警告: {all_warnings}, 颜色: {color_warnings}, 字体: {font_warnings}")
            
            # 汇总结果
            total_color_warnings = sum(counts['color'] for counts in warning_counts.values())
            total_font_warnings = sum(counts['font'] for counts in warning_counts.values())
            
            print(f"  综合测试结果:")
            print(f"    总颜色警告: {total_color_warnings}")
            print(f"    总字体警告: {total_font_warnings}")
            
            return total_color_warnings == 0 and total_font_warnings == 0
            
    except Exception as e:
        print(f"  ❌ 综合图表警告测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🎯 matplotlib警告修复验证测试")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 测试各个修复
    color_warning_ok = test_matplotlib_color_warning()
    patch_ok = test_patch_creation()
    legend_ok = test_chart_legend_creation()
    comprehensive_ok = test_comprehensive_chart_warnings()
    
    print("\n" + "=" * 70)
    print("📋 matplotlib警告修复结果:")
    print(f"颜色属性警告: {'✅ 已修复' if color_warning_ok else '❌ 仍存在'}")
    print(f"Patch对象创建: {'✅ 正常' if patch_ok else '❌ 异常'}")
    print(f"图表图例创建: {'✅ 正常' if legend_ok else '❌ 异常'}")
    print(f"综合图表测试: {'✅ 通过' if comprehensive_ok else '❌ 失败'}")
    
    if all([color_warning_ok, patch_ok, legend_ok, comprehensive_ok]):
        print("\n🎉 matplotlib警告修复成功！")
        print("\n✨ 修复成果:")
        print("  ✅ 消除颜色属性覆盖警告")
        print("  ✅ 正确使用facecolor替代color")
        print("  ✅ 保持图例显示效果不变")
        print("  ✅ 所有图表类型都无警告")
        
        print("\n🛠️ 技术改进:")
        print("  • 使用facecolor而非color属性")
        print("  • 避免属性冲突和覆盖")
        print("  • 添加警告过滤机制")
        print("  • 保持视觉效果一致")
        
        print("\n🎯 修复效果:")
        print("  • 消除matplotlib UserWarning")
        print("  • 更清洁的控制台输出")
        print("  • 更专业的代码质量")
        print("  • 更稳定的图表渲染")
        
        print("\n📁 生成的文件:")
        print("  • test_legend_fix.png - 修复后的图例测试")
        
    else:
        print("\n⚠️ 部分修复验证失败，需要进一步检查")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
