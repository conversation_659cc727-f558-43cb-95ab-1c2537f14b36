<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1500</width>
    <height>800</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1500</width>
    <height>800</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <property name="iconSize">
   <size>
    <width>30</width>
    <height>30</height>
   </size>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="minimumSize">
       <size>
        <width>1500</width>
        <height>530</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>宋体</family>
        <pointsize>12</pointsize>
       </font>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab">
       <attribute name="title">
        <string>Tab 1</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QGroupBox" name="groupBox">
            <property name="minimumSize">
             <size>
              <width>600</width>
              <height>500</height>
             </size>
            </property>
            <property name="title">
             <string>轴承1</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_6">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_3">
               <item>
                <widget class="QLabel" name="label_19">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Connection Type</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="connection_zhou1">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <item>
                  <property name="text">
                   <string>Body-Body</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_4">
               <item>
                <widget class="QLabel" name="label_20">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Rotation Plane</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="rotation_zhou1">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <item>
                  <property name="text">
                   <string>X-Y Plane</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>Y-Z Plane</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>Z-X Plane</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <item>
                <widget class="QLabel" name="label_16">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>刚度系数(N/m)(K11 K22 K12 K21)</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="stiffness_zhou1">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>2 2 2 2</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_6">
               <item>
                <widget class="QLabel" name="label_18">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>阻尼系数(N·s/m)(C11 C22 C12 C21)</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="damping_zhou1">
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>2 2 2 2</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_11">
               <item>
                <widget class="QGroupBox" name="groupBox_3">
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="title">
                  <string>Reference</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_5">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_12">
                    <item>
                     <widget class="QLabel" name="label_24">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Reference Component</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="reference_zhou1">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>q</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>qq</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_13">
                    <item>
                     <widget class="QLabel" name="label_25">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Behavior</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="referencebehavior_zhou1">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>Rigid</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Deformable</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Beam</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_4">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>300</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="title">
                  <string>Mobile</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_3">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_14">
                    <item>
                     <widget class="QLabel" name="label_27">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Mobile Component</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="mobile_zhou1">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>q</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>qq</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_15">
                    <item>
                     <widget class="QLabel" name="label_26">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Behavior</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="mobilebehavior_zhou1">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>Rigid</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Deformable</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Beam</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QGroupBox" name="groupBox_2">
            <property name="minimumSize">
             <size>
              <width>600</width>
              <height>500</height>
             </size>
            </property>
            <property name="title">
             <string>轴承2</string>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_7">
               <item>
                <widget class="QLabel" name="label_22">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Connection Type</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="connection_zhou2">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <item>
                  <property name="text">
                   <string>Body-Body</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <item>
                <widget class="QLabel" name="label_21">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>Rotation Plane</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="rotation_zhou2">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <item>
                  <property name="text">
                   <string>X-Y Plane</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>Y-Z Plane</string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>Z-X Plane</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_9">
               <item>
                <widget class="QLabel" name="label_17">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>刚度系数(N/m)(K11 K22 K12 K21)</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignmentFlag::AlignLeading|Qt::AlignmentFlag::AlignLeft|Qt::AlignmentFlag::AlignVCenter</set>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="stiffness_zhou2">
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>2 2 2 2</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_10">
               <item>
                <widget class="QLabel" name="label_23">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>180</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>阻尼系数(N·s/m)(C11 C22 C12 C21)</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLineEdit" name="damping_zhou2">
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>16777215</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Times New Roman</family>
                   <pointsize>12</pointsize>
                  </font>
                 </property>
                 <property name="text">
                  <string>2 2 2 2</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_20">
               <item>
                <widget class="QGroupBox" name="groupBox_5">
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="title">
                  <string>Reference</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_7">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_16">
                    <item>
                     <widget class="QLabel" name="label_28">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Reference Component</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="reference_zhou2">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>q</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>qq</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_17">
                    <item>
                     <widget class="QLabel" name="label_29">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Behavior</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="referencebehavior_zhou2">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>Rigid</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Deformable</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Beam</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
               <item>
                <widget class="QGroupBox" name="groupBox_6">
                 <property name="minimumSize">
                  <size>
                   <width>250</width>
                   <height>100</height>
                  </size>
                 </property>
                 <property name="title">
                  <string>Mobile</string>
                 </property>
                 <layout class="QVBoxLayout" name="verticalLayout_8">
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_18">
                    <item>
                     <widget class="QLabel" name="label_30">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Mobile Component</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="mobile_zhou2">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>q</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>qq</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                  <item>
                   <layout class="QHBoxLayout" name="horizontalLayout_19">
                    <item>
                     <widget class="QLabel" name="label_31">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>50</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <property name="text">
                       <string>Behavior</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="QComboBox" name="mobilebehavior_zhou2">
                      <property name="minimumSize">
                       <size>
                        <width>150</width>
                        <height>40</height>
                       </size>
                      </property>
                      <property name="font">
                       <font>
                        <family>Times New Roman</family>
                        <pointsize>12</pointsize>
                       </font>
                      </property>
                      <item>
                       <property name="text">
                        <string>Rigid</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Deformable</string>
                       </property>
                      </item>
                      <item>
                       <property name="text">
                        <string>Beam</string>
                       </property>
                      </item>
                     </widget>
                    </item>
                   </layout>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_2">
       <attribute name="title">
        <string>页</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout">
        <item row="0" column="1">
         <widget class="QTabWidget" name="tabWidget_2">
          <property name="currentIndex">
           <number>1</number>
          </property>
          <widget class="QWidget" name="tab_3">
           <attribute name="title">
            <string>Tab 1</string>
           </attribute>
           <layout class="QHBoxLayout" name="horizontalLayout_48">
            <item>
             <widget class="QGroupBox" name="groupBox_7">
              <property name="minimumSize">
               <size>
                <width>600</width>
                <height>500</height>
               </size>
              </property>
              <property name="title">
               <string>bushing1</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_9">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_21">
                 <item>
                  <widget class="QLabel" name="label_32">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>180</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Connection Type</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="connection_bushing1">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <item>
                    <property name="text">
                     <string>Body-Ground</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_22"/>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_25">
                 <item>
                  <widget class="QGroupBox" name="groupBox_8">
                   <property name="minimumSize">
                    <size>
                     <width>300</width>
                     <height>100</height>
                    </size>
                   </property>
                   <property name="title">
                    <string>Mobile</string>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_10">
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_26">
                      <item>
                       <widget class="QLabel" name="label_36">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Mobile Component</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobile_bushing1">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>w</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>ww</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_27">
                      <item>
                       <widget class="QLabel" name="label_37">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Behavior</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobilebehavior_bushing1">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>Rigid</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Deformable</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Beam</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_23">
                 <item>
                  <widget class="QLabel" name="label">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Stiffness</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_stiffness_bushing1">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force X (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Y (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Z (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>1</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>2</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>4</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>9</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_24">
                 <item>
                  <widget class="QLabel" name="label_2">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Damping</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_damping_bushing1">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force*Time X (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Y (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Z (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>55</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_10">
              <property name="minimumSize">
               <size>
                <width>600</width>
                <height>500</height>
               </size>
              </property>
              <property name="title">
               <string>bushing2</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_15">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_39">
                 <item>
                  <widget class="QLabel" name="label_44">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>180</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Connection Type</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="connection_bushing2">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <item>
                    <property name="text">
                     <string>Body-Ground</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_40"/>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_41">
                 <item>
                  <widget class="QGroupBox" name="groupBox_13">
                   <property name="minimumSize">
                    <size>
                     <width>300</width>
                     <height>100</height>
                    </size>
                   </property>
                   <property name="title">
                    <string>Mobile</string>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_16">
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_42">
                      <item>
                       <widget class="QLabel" name="label_46">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Mobile Component</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobile_bushing2">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>w</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>ww</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_43">
                      <item>
                       <widget class="QLabel" name="label_47">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Behavior</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobilebehavior_bushing2">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>Rigid</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Deformable</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Beam</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_46">
                 <item>
                  <widget class="QLabel" name="label_5">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Stiffness</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_stiffness_bushing2">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force X (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Y (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Z (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>6</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string>5</string>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>1</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>2</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>4</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_47">
                 <item>
                  <widget class="QLabel" name="label_6">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Damping</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_damping_bushing2">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force*Time X (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Y (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Z (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="tab_4">
           <attribute name="title">
            <string>Tab 2</string>
           </attribute>
           <layout class="QHBoxLayout" name="horizontalLayout_67">
            <item>
             <widget class="QGroupBox" name="groupBox_18">
              <property name="minimumSize">
               <size>
                <width>600</width>
                <height>500</height>
               </size>
              </property>
              <property name="title">
               <string>bushing3</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_21">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_58">
                 <item>
                  <widget class="QLabel" name="label_56">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>180</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Connection Type</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="connection_bushing3">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <item>
                    <property name="text">
                     <string>Body-Ground</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_59"/>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_60">
                 <item>
                  <widget class="QGroupBox" name="groupBox_19">
                   <property name="minimumSize">
                    <size>
                     <width>300</width>
                     <height>100</height>
                    </size>
                   </property>
                   <property name="title">
                    <string>Mobile</string>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_22">
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_61">
                      <item>
                       <widget class="QLabel" name="label_58">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Mobile Component</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobile_bushing3">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>w</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>ww</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_62">
                      <item>
                       <widget class="QLabel" name="label_59">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Behavior</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobilebehavior_bushing3">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>Rigid</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Deformable</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Beam</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_65">
                 <item>
                  <widget class="QLabel" name="label_9">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Stiffness</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_stiffness_bushing3">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force X (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Y (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Z (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>57</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_66">
                 <item>
                  <widget class="QLabel" name="label_10">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Damping</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_damping_bushing3">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force*Time X (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Y (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Z (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>2</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>1</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>3</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
            <item>
             <widget class="QGroupBox" name="groupBox_15">
              <property name="minimumSize">
               <size>
                <width>600</width>
                <height>500</height>
               </size>
              </property>
              <property name="title">
               <string>bushing4</string>
              </property>
              <layout class="QVBoxLayout" name="verticalLayout_18">
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_49">
                 <item>
                  <widget class="QLabel" name="label_50">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="minimumSize">
                    <size>
                     <width>250</width>
                     <height>50</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>180</width>
                     <height>16777215</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Connection Type</string>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QComboBox" name="connection_bushing4">
                   <property name="minimumSize">
                    <size>
                     <width>0</width>
                     <height>40</height>
                    </size>
                   </property>
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <item>
                    <property name="text">
                     <string>Body-Ground</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_50"/>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_51">
                 <item>
                  <widget class="QGroupBox" name="groupBox_16">
                   <property name="minimumSize">
                    <size>
                     <width>300</width>
                     <height>100</height>
                    </size>
                   </property>
                   <property name="title">
                    <string>Mobile</string>
                   </property>
                   <layout class="QVBoxLayout" name="verticalLayout_19">
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_52">
                      <item>
                       <widget class="QLabel" name="label_52">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Mobile Component</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobile_bushing4">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>w</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>ww</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                    <item>
                     <layout class="QHBoxLayout" name="horizontalLayout_53">
                      <item>
                       <widget class="QLabel" name="label_53">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>16777215</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <property name="text">
                         <string>Behavior</string>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QComboBox" name="mobilebehavior_bushing4">
                        <property name="minimumSize">
                         <size>
                          <width>150</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="font">
                         <font>
                          <family>Times New Roman</family>
                          <pointsize>12</pointsize>
                         </font>
                        </property>
                        <item>
                         <property name="text">
                          <string>Rigid</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Deformable</string>
                         </property>
                        </item>
                        <item>
                         <property name="text">
                          <string>Beam</string>
                         </property>
                        </item>
                       </widget>
                      </item>
                     </layout>
                    </item>
                   </layout>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_56">
                 <item>
                  <widget class="QLabel" name="label_7">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Stiffness</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_stiffness_bushing4">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>150</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force X (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Y (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force Z (N)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>57</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>75</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>5</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>3</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
               <item>
                <layout class="QHBoxLayout" name="horizontalLayout_57">
                 <item>
                  <widget class="QLabel" name="label_8">
                   <property name="font">
                    <font>
                     <family>Times New Roman</family>
                     <pointsize>15</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>Damping</string>
                   </property>
                   <property name="alignment">
                    <set>Qt::AlignmentFlag::AlignCenter</set>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="QTableWidget" name="tableWidget_damping_bushing4">
                   <property name="minimumSize">
                    <size>
                     <width>561</width>
                     <height>140</height>
                    </size>
                   </property>
                   <property name="maximumSize">
                    <size>
                     <width>561</width>
                     <height>141</height>
                    </size>
                   </property>
                   <property name="gridStyle">
                    <enum>Qt::PenStyle::SolidLine</enum>
                   </property>
                   <property name="rowCount">
                    <number>3</number>
                   </property>
                   <attribute name="horizontalHeaderMinimumSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="horizontalHeaderDefaultSectionSize">
                    <number>125</number>
                   </attribute>
                   <attribute name="verticalHeaderMinimumSectionSize">
                    <number>30</number>
                   </attribute>
                   <attribute name="verticalHeaderDefaultSectionSize">
                    <number>35</number>
                   </attribute>
                   <row>
                    <property name="text">
                     <string>Force*Time X (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Y (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <row>
                    <property name="text">
                     <string>Force*Time Z (N·s)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </row>
                   <column>
                    <property name="text">
                     <string>Per Unit X (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Y (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                   </column>
                   <column>
                    <property name="text">
                     <string>Per Unit Z (m)</string>
                    </property>
                    <property name="font">
                     <font>
                      <family>Times New Roman</family>
                      <pointsize>15</pointsize>
                      <underline>false</underline>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                   </column>
                   <item row="0" column="0">
                    <property name="text">
                     <string>1</string>
                    </property>
                   </item>
                   <item row="0" column="1">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <strikeout>false</strikeout>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="0" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="font">
                     <font>
                      <pointsize>15</pointsize>
                     </font>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="1" column="0">
                    <property name="text">
                     <string>2</string>
                    </property>
                   </item>
                   <item row="1" column="1">
                    <property name="text">
                     <string>6</string>
                    </property>
                   </item>
                   <item row="1" column="2">
                    <property name="text">
                     <string/>
                    </property>
                    <property name="flags">
                     <set>ItemIsDragEnabled|ItemIsDropEnabled|ItemIsUserCheckable</set>
                    </property>
                   </item>
                   <item row="2" column="0">
                    <property name="text">
                     <string>4</string>
                    </property>
                   </item>
                   <item row="2" column="1">
                    <property name="text">
                     <string>7</string>
                    </property>
                   </item>
                   <item row="2" column="2">
                    <property name="text">
                     <string>8</string>
                    </property>
                   </item>
                  </widget>
                 </item>
                </layout>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="push_generateconnection">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>设置连接</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_meshui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>上一步(前处理)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_analysisui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>下一步(分析设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_mainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
