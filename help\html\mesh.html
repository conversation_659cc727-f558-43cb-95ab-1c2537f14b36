<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格划分 - 帮助文档</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>网格划分界面</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>界面概述</h2>
            <p>网格划分界面用于设置和生成有限元分析所需的网格。合适的网格质量对于获得准确的分析结果至关重要。</p>
            
            <div class="ui-description">
                <h3>界面组成</h3>
                <ul>
                    <li><strong>参数设置区</strong> - 位于界面左侧，用于设置网格划分参数</li>
                    <li><strong>预览区域</strong> - 位于界面中央，显示模型和生成的网格</li>
                    <li><strong>网格信息区</strong> - 显示网格统计信息，如节点数量和单元数量</li>
                    <li><strong>操作按钮</strong> - 包括生成网格、重置参数等功能按钮</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>主要功能</h2>
            
            <div class="feature-card">
                <h3>网格参数设置</h3>
                <ul>
                    <li>全局网格尺寸设置</li>
                    <li>局部网格细化</li>
                    <li>网格类型选择</li>
                    <li>高级网格控制选项</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>网格生成</h3>
                <ul>
                    <li>自动网格生成</li>
                    <li>网格优化</li>
                    <li>网格生成进度监控</li>
                    <li>错误处理和提示</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>网格质量评估</h3>
                <ul>
                    <li>网格质量检查</li>
                    <li>网格统计信息</li>
                    <li>质量问题标识</li>
                    <li>质量改进建议</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>网格可视化</h3>
                <ul>
                    <li>网格显示控制</li>
                    <li>截面查看</li>
                    <li>网格细节放大</li>
                    <li>视图操作</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>参数设置说明</h2>
            
            <div class="ui-description">
                <h3>全局网格尺寸</h3>
                <ul>
                    <li><strong>设置范围</strong>：0-1000mm</li>
                    <li><strong>推荐值</strong>：根据模型尺寸的1/20到1/50</li>
                    <li><strong>影响</strong>：数值越小，网格越密，计算越精确但耗时更长</li>
                </ul>
            </div>
            
            <div class="ui-description">
                <h3>局部网格控制</h3>
                <ul>
                    <li><strong>选择区域</strong>：点击模型上需要细化的区域</li>
                    <li><strong>细化比例</strong>：设置相对于全局尺寸的细化程度</li>
                    <li><strong>过渡区域</strong>：控制细化区域到普通区域的过渡平滑度</li>
                </ul>
            </div>
            
            <div class="ui-description">
                <h3>网格类型</h3>
                <ul>
                    <li><strong>四面体网格</strong>：适用于复杂几何形状</li>
                    <li><strong>六面体网格</strong>：适用于规则几何形状，计算效率更高</li>
                    <li><strong>混合网格</strong>：结合两种网格类型的优势</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>操作指南</h2>
            
            <div class="step-guide">
                <h3>基本操作流程</h3>
                <ol>
                    <li>
                        <strong>设置网格参数</strong>
                        <p>在参数设置区输入合适的全局网格尺寸和其他参数。确保输入值在有效范围内。</p>
                    </li>
                    <li>
                        <strong>生成网格</strong>
                        <p>点击"生成网格"按钮，等待网格生成完成。生成过程中会显示进度信息。</p>
                    </li>
                    <li>
                        <strong>检查网格质量</strong>
                        <p>查看网格信息区的统计数据，确认节点数量和单元数量是否合理。观察预览区域中的网格质量。</p>
                    </li>
                    <li>
                        <strong>调整参数（如需）</strong>
                        <p>如果网格质量不满意，可以调整参数并重新生成网格。</p>
                    </li>
                    <li>
                        <strong>确认并继续</strong>
                        <p>确认网格质量满足要求后，点击"前往连接界面"按钮进入下一步。</p>
                    </li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>网格质量评估</h2>
            <p>网格质量对计算结果的准确性有重要影响。软件提供以下质量评估指标：</p>
            
            <div class="interpretation">
                <h3>节点和单元数量</h3>
                <p>显示网格的总节点数和单元数，这反映了网格的密度。</p>
                <ul>
                    <li>节点数过少可能导致结果不准确</li>
                    <li>节点数过多会增加计算时间和资源消耗</li>
                </ul>
            </div>
          
        </div>

        <div class="section">
            <h2>常见问题</h2>
            
            <div class="faq">
                <h3>Q: 如何选择合适的网格尺寸？</h3>
                <p>A: 网格尺寸通常建议设置为模型最大尺寸的1/20到1/50。可以从较大的尺寸开始，根据需要逐步减小，直到获得满意的网格质量。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 网格生成失败怎么办？</h3>
                <p>A: 首先检查网格尺寸是否合适，然后确认几何模型是否有问题。如果问题仍然存在，可以尝试增大网格尺寸或修复几何模型。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 为什么生成网格需要很长时间？</h3>
                <p>A: 网格生成时间与模型复杂度和网格密度直接相关。复杂模型或高密度网格需要更长的处理时间。可以考虑简化模型或增大网格尺寸来减少处理时间。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 如何处理网格质量问题？</h3>
                <p>A: 对于质量较低的区域，可以尝试局部细化网格、调整网格参数或修改几何模型。有时，简化复杂几何特征也能改善网格质量。</p>
            </div>
        </div>

        <div class="warning">
            <h3>注意事项</h3>
            <ul>
                <li>网格尺寸过小会显著增加计算时间</li>
                <li>网格尺寸过大可能影响计算精度</li>
                <li>生成网格过程中请勿关闭软件</li>
                <li>确保有足够的系统内存用于网格生成</li>
                <li>对于大型模型，建议先使用较粗的网格进行初步分析</li>
            </ul>
        </div>

        <div class="section">
            <h2>网格参数详解</h2>
            
            <div class="interpretation">
                <h3>网格尺寸参数</h3>
                <p>网格尺寸是控制网格密度的关键参数，直接影响计算精度和效率：</p>
                <ul>
                    <li><strong>全局尺寸</strong> - 控制整个模型的网格密度</li>
                    <li><strong>局部尺寸</strong> - 可在关键区域设置更小的网格尺寸</li>
                    <li><strong>增长率</strong> - 控制网格尺寸从小到大的过渡速率</li>
                    <li><strong>曲率因子</strong> - 控制曲面区域的网格细化程度</li>
                </ul>
            </div>
            
            <div class="interpretation">
                <h3>网格类型选择指南</h3>
                <p>不同的网格类型适用于不同的分析场景：</p>
                <ul>
                    <li><strong>四面体网格</strong> - 自动化程度高，适合复杂几何形状，但计算效率较低</li>
                    <li><strong>六面体网格</strong> - 计算效率高，结果精度好，但需要更多的人工干预</li>
                    <li><strong>混合网格</strong> - 在不同区域使用不同类型的网格，平衡自动化和计算效率</li>
                    <li><strong>壳单元网格</strong> - 适用于薄壁结构，可显著减少计算资源需求</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>高级网格控制</h2>
            
            <div class="feature-card">
                <h3>边界层网格</h3>
                <ul>
                    <li>在表面附近生成高质量的有序网格</li>
                    <li>适用于流体分析和热传导分析</li>
                    <li>可控制层数、增长率和初始层厚度</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>接触区域网格控制</h3>
                <ul>
                    <li>在接触区域自动细化网格</li>
                    <li>确保接触面网格兼容性</li>
                    <li>提高接触分析的精度</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h3>网格映射</h3>
                <ul>
                    <li>在相似几何区域创建一致的网格</li>
                    <li>确保结果的可比性</li>
                    <li>简化后处理分析</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>网格质量改进技巧</h2>
            
            <div class="tip">
                <h3>几何简化</h3>
                <p>复杂几何特征往往导致网格质量问题，可通过以下方式简化：</p>
                <ul>
                    <li>移除小特征（如小孔、小圆角）</li>
                    <li>合并相近的面</li>
                    <li>修复几何缺陷（如小缝隙、尖角）</li>
                </ul>
            </div>
            
            <div class="tip">
                <h3>网格优化</h3>
                <p>生成网格后，可以使用以下技术改善网格质量：</p>
                <ul>
                    <li>网格平滑处理 - 改善单元形状</li>
                    <li>节点调整 - 手动或自动调整关键区域的节点位置</li>
                    <li>网格重构 - 重新生成问题区域的网格</li>
                </ul>
            </div>
        </div>

        <a href="index.html" class="back-link">返回主页</a>
    </div>

    <div class="footer">
        <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
    </div>
</body>
</html> 