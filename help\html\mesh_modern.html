<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网格划分界面 - 有限元网格生成系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-emerald-600 to-teal-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-emerald-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🔲 网格划分界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-emerald-100">
                    有限元网格生成系统 | 智能化网格质量控制与优化
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-emerald-500 bg-opacity-20 text-emerald-100 text-sm font-semibold px-4 py-2 rounded-full border border-emerald-400">
                        ⚙️ 参数设置 | 🔲 网格生成 | 📊 质量评估 | 👁️ 可视化
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                网格划分界面用于设置和生成有限元分析所需的网格。合适的网格质量对于获得准确的分析结果至关重要。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">参数设置区</h3>
                    </div>
                    <p class="text-sm text-blue-600">位于界面左侧，用于设置网格划分参数</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">预览区域</h3>
                    </div>
                    <p class="text-sm text-green-600">位于界面中央，显示模型和生成的网格</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">网格信息区</h3>
                    </div>
                    <p class="text-sm text-purple-600">显示网格统计信息，如节点数量和单元数量</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">操作按钮</h3>
                    </div>
                    <p class="text-sm text-orange-600">包括生成网格、重置参数等功能按钮</p>
                </div>
            </div>
        </section>

        <!-- Main Features -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🚀 主要功能</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">全面的网格生成和质量控制功能</p>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">⚙️ 网格参数设置</h3>
                        <ul class="space-y-2 text-sm text-blue-600">
                            <li>• 全局网格尺寸设置</li>
                            <li>• 局部网格细化</li>
                            <li>• 网格类型选择</li>
                            <li>• 高级网格控制选项</li>
                        </ul>
                    </div>
                    
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-green-800 mb-4">🔲 网格生成</h3>
                        <ul class="space-y-2 text-sm text-green-600">
                            <li>• 自动网格生成</li>
                            <li>• 网格优化</li>
                            <li>• 网格生成进度监控</li>
                            <li>• 错误处理和提示</li>
                        </ul>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="bg-purple-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-purple-800 mb-4">📊 网格质量评估</h3>
                        <ul class="space-y-2 text-sm text-purple-600">
                            <li>• 网格质量检查</li>
                            <li>• 网格统计信息</li>
                            <li>• 质量问题标识</li>
                            <li>• 质量改进建议</li>
                        </ul>
                    </div>
                    
                    <div class="bg-orange-50 p-6 rounded-lg">
                        <h3 class="text-lg font-semibold text-orange-800 mb-4">👁️ 网格可视化</h3>
                        <ul class="space-y-2 text-sm text-orange-600">
                            <li>• 网格显示控制</li>
                            <li>• 截面查看</li>
                            <li>• 网格细节放大</li>
                            <li>• 视图操作</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Parameter Settings -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">⚙️ 参数设置说明</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🌐 全局网格尺寸</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-blue-800 mb-2">设置范围</h4>
                            <p class="text-sm text-blue-600">0-1000mm</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-green-800 mb-2">推荐值</h4>
                            <p class="text-sm text-green-600">根据模型尺寸的1/20到1/50</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-purple-800 mb-2">影响</h4>
                            <p class="text-sm text-purple-600">数值越小，网格越密，计算越精确但耗时更长</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🎯 局部网格控制</h3>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                            <div>
                                <strong class="text-gray-800">选择区域</strong>
                                <p class="text-sm text-gray-600">点击模型上需要细化的区域</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                            <div>
                                <strong class="text-gray-800">细化比例</strong>
                                <p class="text-sm text-gray-600">设置相对于全局尺寸的细化程度</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                            <div>
                                <strong class="text-gray-800">过渡区域</strong>
                                <p class="text-sm text-gray-600">控制细化区域到普通区域的过渡平滑度</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔲 网格类型</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                            <h4 class="font-semibold text-blue-800 mb-2">四面体网格</h4>
                            <p class="text-sm text-blue-600">适用于复杂几何形状</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                            <h4 class="font-semibold text-green-800 mb-2">六面体网格</h4>
                            <p class="text-sm text-green-600">适用于规则几何形状，计算效率更高</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                            <h4 class="font-semibold text-purple-800 mb-2">混合网格</h4>
                            <p class="text-sm text-purple-600">结合两种网格类型的优势</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Operation Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📋 操作指南</h2>

            <div class="bg-gradient-to-r from-emerald-50 to-teal-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🔄 基本操作流程</h3>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">⚙️ 设置网格参数</h4>
                            <p class="text-gray-600 text-sm">在参数设置区输入合适的全局网格尺寸和其他参数。确保输入值在有效范围内。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-emerald-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔲 生成网格</h4>
                            <p class="text-gray-600 text-sm">点击"生成网格"按钮，等待网格生成完成。生成过程中会显示进度信息。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-teal-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">📊 检查网格质量</h4>
                            <p class="text-gray-600 text-sm">查看网格信息区的统计数据，确认节点数量和单元数量是否合理。观察预览区域中的网格质量。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-teal-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔧 调整参数（如需）</h4>
                            <p class="text-gray-600 text-sm">如果网格质量不满意，可以调整参数并重新生成网格。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-cyan-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">5</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">✅ 确认并继续</h4>
                            <p class="text-gray-600 text-sm">确认网格质量满足要求后，点击"前往连接界面"按钮进入下一步。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Mesh Quality Assessment -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 网格质量评估</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                网格质量对计算结果的准确性有重要影响。软件提供以下质量评估指标：
            </p>

            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📈 节点和单元数量</h3>
                <p class="text-gray-600 mb-4">显示网格的总节点数和单元数，这反映了网格的密度。</p>

                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-red-400">
                        <h4 class="font-semibold text-red-800 mb-2">⚠️ 节点数过少</h4>
                        <p class="text-sm text-red-600">可能导致结果不准确</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                        <h4 class="font-semibold text-orange-800 mb-2">⏱️ 节点数过多</h4>
                        <p class="text-sm text-orange-600">会增加计算时间和资源消耗</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Detailed Parameter Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔧 网格参数详解</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📏 网格尺寸参数</h3>
                    <p class="text-gray-600 mb-4">网格尺寸是控制网格密度的关键参数，直接影响计算精度和效率：</p>

                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                                <h4 class="font-semibold text-blue-800 mb-2">全局尺寸</h4>
                                <p class="text-sm text-blue-600">控制整个模型的网格密度</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">局部尺寸</h4>
                                <p class="text-sm text-green-600">可在关键区域设置更小的网格尺寸</p>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                                <h4 class="font-semibold text-purple-800 mb-2">增长率</h4>
                                <p class="text-sm text-purple-600">控制网格尺寸从小到大的过渡速率</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                                <h4 class="font-semibold text-orange-800 mb-2">曲率因子</h4>
                                <p class="text-sm text-orange-600">控制曲面区域的网格细化程度</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-emerald-50 to-teal-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔲 网格类型选择指南</h3>
                    <p class="text-gray-600 mb-4">不同的网格类型适用于不同的分析场景：</p>

                    <div class="space-y-3">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <span class="w-3 h-3 bg-blue-500 rounded-full mr-3"></span>
                                <strong class="text-blue-800">四面体网格</strong>
                            </div>
                            <p class="text-sm text-blue-600">自动化程度高，适合复杂几何形状，但计算效率较低</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <span class="w-3 h-3 bg-green-500 rounded-full mr-3"></span>
                                <strong class="text-green-800">六面体网格</strong>
                            </div>
                            <p class="text-sm text-green-600">计算效率高，结果精度好，但需要更多的人工干预</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <span class="w-3 h-3 bg-purple-500 rounded-full mr-3"></span>
                                <strong class="text-purple-800">混合网格</strong>
                            </div>
                            <p class="text-sm text-purple-600">在不同区域使用不同类型的网格，平衡自动化和计算效率</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center mb-2">
                                <span class="w-3 h-3 bg-orange-500 rounded-full mr-3"></span>
                                <strong class="text-orange-800">壳单元网格</strong>
                            </div>
                            <p class="text-sm text-orange-600">适用于薄壁结构，可显著减少计算资源需求</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Advanced Mesh Control -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎛️ 高级网格控制</h2>

            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">🌊 边界层网格</h3>
                    <ul class="space-y-2 text-sm text-blue-600">
                        <li>• 在表面附近生成高质量的有序网格</li>
                        <li>• 适用于流体分析和热传导分析</li>
                        <li>• 可控制层数、增长率和初始层厚度</li>
                    </ul>
                </div>

                <div class="bg-green-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">🔗 接触区域网格控制</h3>
                    <ul class="space-y-2 text-sm text-green-600">
                        <li>• 在接触区域自动细化网格</li>
                        <li>• 确保接触面网格兼容性</li>
                        <li>• 提高接触分析的精度</li>
                    </ul>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 mb-4">🗺️ 网格映射</h3>
                    <ul class="space-y-2 text-sm text-purple-600">
                        <li>• 在相似几何区域创建一致的网格</li>
                        <li>• 确保结果的可比性</li>
                        <li>• 简化后处理分析</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Mesh Quality Improvement -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔧 网格质量改进技巧</h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔨 几何简化</h3>
                    <p class="text-gray-600 mb-4">复杂几何特征往往导致网格质量问题，可通过以下方式简化：</p>

                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-yellow-600 mr-3">🔧</span>
                            <span class="text-sm">移除小特征（如小孔、小圆角）</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-orange-600 mr-3">🔗</span>
                            <span class="text-sm">合并相近的面</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-red-600 mr-3">🩹</span>
                            <span class="text-sm">修复几何缺陷（如小缝隙、尖角）</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">⚡ 网格优化</h3>
                    <p class="text-gray-600 mb-4">生成网格后，可以使用以下技术改善网格质量：</p>

                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-green-600 mr-3">🌊</span>
                            <span class="text-sm">网格平滑处理 - 改善单元形状</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-blue-600 mr-3">📍</span>
                            <span class="text-sm">节点调整 - 手动或自动调整关键区域的节点位置</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-purple-600 mr-3">🔄</span>
                            <span class="text-sm">网格重构 - 重新生成问题区域的网格</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📏</span>如何选择合适的网格尺寸？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">网格尺寸通常建议设置为模型最大尺寸的1/20到1/50。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">选择策略：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 可以从较大的尺寸开始，根据需要逐步减小</li>
                                <li>• 直到获得满意的网格质量</li>
                                <li>• 考虑计算资源和时间的平衡</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">❌</span>网格生成失败怎么办？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">网格生成失败通常由几何问题或参数设置不当引起。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">排查步骤：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 首先检查网格尺寸是否合适</li>
                                <li>• 确认几何模型是否有问题</li>
                                <li>• 尝试增大网格尺寸或修复几何模型</li>
                                <li>• 检查模型的拓扑完整性</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⏱️</span>为什么生成网格需要很长时间？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">网格生成时间与模型复杂度和网格密度直接相关。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">优化建议：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 复杂模型或高密度网格需要更长的处理时间</li>
                                <li>• 可以考虑简化模型或增大网格尺寸来减少处理时间</li>
                                <li>• 使用局部细化而不是全局细化</li>
                                <li>• 确保计算机有足够的内存和处理能力</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>如何处理网格质量问题？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">对于质量较低的区域，可以尝试多种改进方法。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">改进方法：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 局部细化网格 - 在问题区域使用更小的网格尺寸</li>
                                <li>• 调整网格参数 - 修改增长率、曲率因子等</li>
                                <li>• 修改几何模型 - 简化复杂几何特征</li>
                                <li>• 使用网格优化工具 - 平滑处理和节点调整</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 网格尺寸过小会显著增加计算时间</li>
                    <li>• 网格尺寸过大可能影响计算精度</li>
                    <li>• 生成网格过程中请勿关闭软件</li>
                    <li>• 确保有足够的系统内存用于网格生成</li>
                    <li>• 对于大型模型，建议先使用较粗的网格进行初步分析</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 从粗网格开始，逐步细化以找到最佳平衡点</li>
                    <li>• 在关键区域使用局部细化而不是全局细化</li>
                    <li>• 定期检查网格质量指标和统计信息</li>
                    <li>• 保存不同网格设置的配置以便比较</li>
                    <li>• 考虑使用混合网格类型以优化性能</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">有限元网格生成系统 - 智能化网格质量控制与优化</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
