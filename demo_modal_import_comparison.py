"""
模态分析结果导入和对比功能完整演示

此演示展示：
1. 模态结果导入功能（JSON, CSV, TXT格式）
2. 导入结果与当前结果的对比显示
3. 完整的用户界面集成
4. 数据管理和持久化

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_application():
    """创建演示应用程序"""
    print("🚀 启动模态分析结果导入对比演示...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from ui.components.modal_chart_control_panel import ModalChartControlPanel
        
        app = QApplication.instance() or QApplication([])
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("Modal Analysis Results Import & Comparison Demo")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建控制面板（包含图表）
        control_panel = ModalChartControlPanel()
        layout.addWidget(control_panel)
        
        # 准备当前计算结果（模拟数据）
        current_results = [
            {
                'name': 'Current Fine Mesh',
                'size': 1.0,
                'frequencies': [42.5, 75.8, 108.2, 145.6, 189.3, 238.7, 294.1, 352.8],
                'node_count': 15000,
                'element_count': 12000
            },
            {
                'name': 'Current Medium Mesh',
                'size': 2.5,
                'frequencies': [41.8, 74.2, 106.5, 143.1, 186.2, 235.4, 290.8, 348.9],
                'node_count': 7500,
                'element_count': 6000
            },
            {
                'name': 'Current Coarse Mesh',
                'size': 5.0,
                'frequencies': [40.2, 71.9, 103.8, 139.7, 181.5, 229.8, 284.2, 341.6],
                'node_count': 3000,
                'element_count': 2400
            }
        ]
        
        # 更新当前数据
        control_panel.update_mesh_data(current_results)
        
        # 显示窗口
        main_window.show()
        
        print("✅ 演示应用程序启动成功！")
        print("\n📋 功能说明:")
        print("1. 点击 'Import Results' 导入示例数据文件")
        print("2. 使用 'Manage Imported' 管理导入的结果")
        print("3. 切换图表类型查看不同的对比效果")
        print("4. 使用复选框控制显示选项")
        print("5. 点击 'Save Chart' 保存高质量图表")
        
        print("\n📁 可用的示例数据文件:")
        print("- sample_modal_data.json (JSON格式)")
        print("- sample_modal_data.csv (CSV格式)")
        
        return app, main_window, control_panel
        
    except Exception as e:
        print(f"❌ 演示应用程序启动失败: {str(e)}")
        return None, None, None

def test_import_functionality():
    """测试导入功能"""
    print("\n🧪 测试导入功能...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建数据管理器
        data_manager = ModalDataManager("test_modal_data.pkl")
        
        # 测试JSON导入
        if os.path.exists("sample_modal_data.json"):
            success = data_manager.import_from_file("sample_modal_data.json")
            print(f"JSON导入: {'✅ 成功' if success else '❌ 失败'}")
        
        # 测试CSV导入
        if os.path.exists("sample_modal_data.csv"):
            success = data_manager.import_from_file("sample_modal_data.csv")
            print(f"CSV导入: {'✅ 成功' if success else '❌ 失败'}")
        
        # 显示导入结果
        imported_results = data_manager.get_imported_results()
        print(f"导入结果数量: {len(imported_results)}")
        
        for i, result in enumerate(imported_results):
            print(f"  {i+1}. {result.name} - {len(result.frequencies)} modes")
        
        # 测试导出功能
        if imported_results:
            export_success = data_manager.export_to_file("exported_results.json")
            print(f"导出测试: {'✅ 成功' if export_success else '❌ 失败'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入功能测试失败: {str(e)}")
        return False

def create_comparison_demo():
    """创建对比演示图表"""
    print("\n📊 创建对比演示图表...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_data_manager import ModalDataManager
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建数据管理器并导入数据
        data_manager = ModalDataManager("demo_comparison.pkl")
        
        if os.path.exists("sample_modal_data.json"):
            data_manager.import_from_file("sample_modal_data.json")
        
        # 创建图表组件
        chart_widget = ModalChartWidget(data_manager=data_manager)
        
        # 当前数据
        current_data = [
            {
                'name': 'Current Analysis',
                'size': 2.0,
                'frequencies': [42.0, 75.0, 107.5, 144.0, 187.5, 236.0, 291.0, 349.0],
                'node_count': 10000,
                'element_count': 8000
            }
        ]
        
        # 生成对比图表
        chart_types = [
            ("frequency_comparison", "Frequency Comparison with Imported Data"),
            ("mode_distribution", "Mode Distribution Comparison"),
            ("mesh_convergence", "Convergence Analysis with References")
        ]
        
        for chart_type, description in chart_types:
            print(f"  📊 生成 {description}...")
            
            chart_widget.update_chart(chart_type, current_data, {
                'show_current': True,
                'show_imported': True,
                'show_frequencies': True
            })
            
            filename = f"comparison_demo_{chart_type}.png"
            chart_widget.save_chart(filename, dpi=300)
            
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"    ✅ {description} 生成成功 ({file_size:,} 字节)")
            else:
                print(f"    ❌ {description} 生成失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比演示创建失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("🎯 模态分析结果导入和对比功能演示")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    # 测试导入功能
    import_ok = test_import_functionality()
    
    # 创建对比演示
    demo_ok = create_comparison_demo()
    
    # 创建演示应用程序
    app, main_window, control_panel = create_demo_application()
    
    print("\n" + "=" * 70)
    print("📋 演示结果:")
    print(f"导入功能测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"对比演示创建: {'✅ 成功' if demo_ok else '❌ 失败'}")
    print(f"演示应用程序: {'✅ 启动成功' if app else '❌ 启动失败'}")
    
    if app and main_window:
        print("\n🎉 完整的导入对比功能演示已准备就绪！")
        print("\n✨ 主要功能:")
        print("  ✅ 多格式文件导入 (JSON, CSV, TXT)")
        print("  ✅ 导入结果管理 (查看、删除、重命名)")
        print("  ✅ 当前结果与导入结果对比显示")
        print("  ✅ 三种专业图表类型")
        print("  ✅ 灵活的显示选项控制")
        print("  ✅ 高质量图表导出")
        print("  ✅ 数据持久化存储")
        
        print("\n🎯 使用说明:")
        print("  1. 应用程序已启动，可以直接使用")
        print("  2. 导入示例数据文件测试功能")
        print("  3. 切换图表类型查看对比效果")
        print("  4. 使用管理功能编辑导入结果")
        
        # 运行应用程序
        try:
            app.exec()
        except KeyboardInterrupt:
            print("\n👋 演示结束")
    else:
        print("\n⚠️ 部分功能演示失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
