"""
测试调整后的启动画面显示时间

此脚本验证启动画面的新时间设置：
1. 最小显示时间增加到4.5秒
2. 淡入动画时间增加到900ms
3. 淡出动画时间增加到700ms
4. 进度更新间隔增加到1.5-2秒

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_extended_display_time():
    """测试延长的显示时间"""
    print("⏰ 测试延长的启动画面显示时间...")
    print("新的时间设置：")
    print("  - 最小显示时间: 4.5秒")
    print("  - 淡入动画: 900ms")
    print("  - 淡出动画: 700ms")
    print("  - 进度更新间隔: 1.5-2秒")
    print()
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtGui import QIcon
        from core.splash_screen_simple import SimpleSplashScreenManager
        
        print("✅ 模块导入成功")
        
        # 创建应用程序
        app = QApplication([])
        
        # 设置应用程序图标
        icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
            print("✅ 应用图标加载成功")
        
        # 记录开始时间
        start_time = time.time()
        print(f"🕐 启动画面开始时间: {time.strftime('%H:%M:%S', time.localtime(start_time))}")
        
        # 创建启动画面管理器（使用默认配置，已调整时间）
        splash_manager = SimpleSplashScreenManager()
        
        # 显示启动画面
        splash = splash_manager.show_splash()
        print("✅ 启动画面显示成功（淡入动画: 900ms）")
        
        # 模拟真实的应用程序初始化过程
        init_steps = [
            (5, "初始化日志系统..."),
            (10, "创建应用程序实例..."),
            (15, "配置异常处理..."),
            (20, "加载关键样式..."),
            (30, "应用关键样式..."),
            (35, "初始化字体管理器..."),
            (50, "加载初始配置数据..."),
            (60, "创建窗口管理器..."),
            (70, "创建应用程序窗口..."),
            (75, "主窗口创建完成..."),
            (80, "注册窗口工厂..."),
            (85, "应用初始数据..."),
            (90, "启动API服务器..."),
            (95, "初始化槽函数..."),
            (100, "启动完成！")
        ]
        
        # 模拟初始化过程，使用不同的延迟时间
        delays = [1.5, 1.5, 1.2, 1.0, 1.2, 1.0, 1.5, 1.3, 1.8, 1.0, 1.0, 1.0, 1.5, 1.2, 2.0]
        
        for i, ((progress, status), delay) in enumerate(zip(init_steps, delays)):
            step_start = time.time()
            splash_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 步骤 {i+1:2d}: {progress:3d}% - {status}")
            print(f"   ⏱️  延迟时间: {delay}秒")
            
            time.sleep(delay)
            app.processEvents()
            
            step_end = time.time()
            print(f"   📊 实际用时: {step_end - step_start:.1f}秒")
            print()
        
        # 记录隐藏前的时间
        before_hide = time.time()
        total_display_time = before_hide - start_time
        print(f"🕐 准备隐藏时间: {time.strftime('%H:%M:%S', time.localtime(before_hide))}")
        print(f"📊 总显示时间: {total_display_time:.1f}秒")
        
        # 隐藏启动画面
        print("🔄 开始隐藏启动画面（淡出动画: 700ms）...")
        splash_manager.hide_splash()
        
        # 等待隐藏完成
        time.sleep(1)
        
        end_time = time.time()
        total_time = end_time - start_time
        print(f"🕐 完全结束时间: {time.strftime('%H:%M:%S', time.localtime(end_time))}")
        print(f"📊 总耗时: {total_time:.1f}秒")
        
        print("\n🎉 延长显示时间测试完成！")
        print(f"✅ 启动画面显示了 {total_display_time:.1f} 秒")
        print(f"✅ 最小显示时间要求: 4.5秒 - {'满足' if total_display_time >= 4.5 else '不满足'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_timing_config():
    """测试自定义时间配置"""
    print("\n⚙️ 测试自定义时间配置...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from core.splash_screen_simple import SimpleSplashScreenManager
        
        app = QApplication.instance() or QApplication([])
        
        # 超长显示时间配置
        extended_config = {
            "show_fade_in": True,
            "fade_in_duration": 1200,     # 1.2秒淡入
            "fade_out_duration": 1000,    # 1秒淡出
            "minimum_display_time": 6000, # 6秒最小显示时间
            "colors": {
                "primary": "#9b59b6",     # 紫色主题
                "secondary": "#3498db",
                "text": "#ffffff"
            }
        }
        
        print("自定义配置：")
        print(f"  - 淡入时间: {extended_config['fade_in_duration']}ms")
        print(f"  - 淡出时间: {extended_config['fade_out_duration']}ms")
        print(f"  - 最小显示时间: {extended_config['minimum_display_time']}ms")
        print()
        
        start_time = time.time()
        print(f"🕐 自定义配置测试开始: {time.strftime('%H:%M:%S', time.localtime(start_time))}")
        
        extended_manager = SimpleSplashScreenManager(extended_config)
        extended_splash = extended_manager.show_splash()
        
        print("✅ 自定义配置启动画面显示成功")
        
        # 快速进度更新测试
        quick_steps = [
            (25, "测试超长显示时间..."),
            (50, "验证自定义动画时间..."),
            (75, "检查紫色主题效果..."),
            (100, "自定义配置测试完成！")
        ]
        
        for progress, status in quick_steps:
            extended_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 快速进度: {progress}% - {status}")
            time.sleep(1.0)  # 较短的延迟，测试最小显示时间
            app.processEvents()
        
        before_hide = time.time()
        display_time = before_hide - start_time
        print(f"📊 当前显示时间: {display_time:.1f}秒")
        
        extended_manager.hide_splash()
        
        end_time = time.time()
        total_time = end_time - start_time
        print(f"📊 总时间: {total_time:.1f}秒")
        print(f"✅ 最小显示时间 6秒 - {'满足' if total_time >= 6.0 else '不满足'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("⏰ 启动画面延长显示时间测试")
    print("=" * 70)
    print("\n测试目标：")
    print("✨ 验证最小显示时间增加到4.5秒")
    print("✨ 验证淡入动画时间增加到900ms")
    print("✨ 验证淡出动画时间增加到700ms")
    print("✨ 验证进度更新间隔增加到1.5-2秒")
    print("✨ 测试自定义时间配置")
    print("=" * 70)
    
    success_count = 0
    total_tests = 2
    
    # 测试延长的显示时间
    print("\n1. 测试延长的显示时间")
    if test_extended_display_time():
        success_count += 1
    
    # 测试自定义时间配置
    print("\n2. 测试自定义时间配置")
    if test_custom_timing_config():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有时间设置测试通过！")
        print("✅ 启动画面现在显示更长时间")
        print("✅ 用户可以充分欣赏视觉效果")
        print("✅ 动画时间已优化")
        print("✅ 进度更新节奏更合理")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
    
    print("=" * 70)
    print("\n⏰ 启动画面时间调整完成！")
    print("现在启动画面将显示更长时间，提供更好的用户体验。")

if __name__ == "__main__":
    main()
