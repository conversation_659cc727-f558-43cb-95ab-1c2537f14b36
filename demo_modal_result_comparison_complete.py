"""
模态分析结果对比界面完整功能演示

此脚本演示重新设计后的模态分析结果对比界面的完整功能：
1. 基于实际模态数据的界面展示
2. matplotlib图表的三种类型展示
3. 用户交互功能演示
4. 图表保存和导出功能

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_demo_modal_data():
    """创建演示用的模态分析数据"""
    return [
        {
            'name': '超细网格 (高精度)',
            'size': 1.0,
            'frequencies': [42.8, 75.3, 108.7, 145.2, 189.6, 235.4, 284.1, 336.8, 392.5, 451.2],
            'node_count': 25000,
            'element_count': 20000,
            'calculation_time': 320.5
        },
        {
            'name': '细网格 (标准)',
            'size': 2.0,
            'frequencies': [42.5, 74.8, 107.9, 144.1, 188.2, 233.8, 282.1, 334.5, 389.7, 448.1],
            'node_count': 15000,
            'element_count': 12000,
            'calculation_time': 180.2
        },
        {
            'name': '中等网格 (平衡)',
            'size': 5.0,
            'frequencies': [41.9, 73.6, 106.2, 141.8, 185.4, 230.1, 277.8, 329.2, 384.1, 442.3],
            'node_count': 8000,
            'element_count': 6500,
            'calculation_time': 95.8
        },
        {
            'name': '粗网格 (快速)',
            'size': 10.0,
            'frequencies': [40.8, 71.2, 103.1, 137.9, 180.5, 224.7, 271.2, 321.1, 375.8, 434.2],
            'node_count': 3200,
            'element_count': 2800,
            'calculation_time': 42.3
        },
        {
            'name': '超粗网格 (概念)',
            'size': 20.0,
            'frequencies': [38.5, 67.8, 98.2, 131.4, 172.1, 215.8, 262.3, 311.5, 364.2, 420.1],
            'node_count': 1500,
            'element_count': 1200,
            'calculation_time': 18.7
        }
    ]

def demo_chart_widget():
    """演示图表组件功能"""
    print("🎯 演示图表组件功能...")
    
    try:
        from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
        from PySide6.QtCore import Qt
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建演示窗口
        demo_window = QMainWindow()
        demo_window.setWindowTitle("模态分析结果对比 - 完整功能演示")
        demo_window.resize(1200, 800)
        
        # 创建中央组件
        central_widget = QWidget()
        demo_window.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QHBoxLayout(central_widget)
        
        # 左侧控制面板
        control_panel = QWidget()
        control_panel.setMaximumWidth(300)
        control_layout = QVBoxLayout(control_panel)
        
        # 添加标题
        title_label = QLabel("📊 模态分析结果对比演示")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196f3; padding: 10px;")
        control_layout.addWidget(title_label)
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 获取演示数据
        demo_data = create_demo_modal_data()
        
        # 创建图表类型按钮
        chart_types = [
            ("频率对比图", "frequency_comparison"),
            ("模态分布图", "mode_distribution"),
            ("网格收敛性分析", "mesh_convergence")
        ]
        
        def update_chart(chart_type):
            """更新图表"""
            chart_widget.update_chart(chart_type, demo_data)
            info_label.setText(f"当前显示: {chart_type}\n数据集: {len(demo_data)} 个网格方案")
        
        for name, chart_type in chart_types:
            btn = QPushButton(name)
            btn.setMinimumHeight(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 12px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
                QPushButton:pressed {
                    background-color: #0d47a1;
                }
            """)
            btn.clicked.connect(lambda checked=False, ct=chart_type: update_chart(ct))
            control_layout.addWidget(btn)
        
        # 添加信息标签
        info_label = QLabel("点击上方按钮查看不同类型的图表")
        info_label.setStyleSheet("padding: 10px; background-color: #f5f5f5; border-radius: 4px; margin: 10px 0;")
        info_label.setWordWrap(True)
        control_layout.addWidget(info_label)
        
        # 添加数据信息
        data_info = QLabel()
        data_info_text = "📋 演示数据集:\n\n"
        for i, data in enumerate(demo_data, 1):
            data_info_text += f"{i}. {data['name']}\n"
            data_info_text += f"   尺寸: {data['size']}mm\n"
            data_info_text += f"   节点: {data['node_count']:,}\n"
            data_info_text += f"   模态: {len(data['frequencies'])}阶\n\n"
        
        data_info.setText(data_info_text)
        data_info.setStyleSheet("font-size: 10px; color: #666; padding: 10px; background-color: #fafafa; border-radius: 4px;")
        data_info.setWordWrap(True)
        control_layout.addWidget(data_info)
        
        control_layout.addStretch()
        
        # 添加保存按钮
        save_btn = QPushButton("💾 保存当前图表")
        save_btn.setMinimumHeight(40)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #388e3c;
            }
        """)
        
        def save_chart():
            """保存图表"""
            try:
                import tempfile
                import os
                
                # 创建临时文件
                temp_dir = tempfile.gettempdir()
                filename = f"modal_chart_demo_{chart_widget.current_chart_type}.png"
                filepath = os.path.join(temp_dir, filename)
                
                chart_widget.save_chart(filepath)
                
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(demo_window, "保存成功", f"图表已保存到:\n{filepath}")
                
            except Exception as e:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.critical(demo_window, "保存失败", f"保存图表失败:\n{str(e)}")
        
        save_btn.clicked.connect(save_chart)
        control_layout.addWidget(save_btn)
        
        # 添加组件到主布局
        main_layout.addWidget(control_panel)
        main_layout.addWidget(chart_widget, 1)
        
        # 初始显示频率对比图
        update_chart("frequency_comparison")
        
        # 显示窗口
        demo_window.show()
        
        print("✅ 演示窗口已打开")
        print("💡 使用说明:")
        print("  - 点击左侧按钮切换不同的图表类型")
        print("  - 查看基于真实模态数据的可视化效果")
        print("  - 点击'保存当前图表'按钮保存图表到文件")
        print("  - 关闭窗口结束演示")
        
        return demo_window
        
    except Exception as e:
        print(f"❌ 演示窗口创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def demo_integration_with_main_window():
    """演示与主窗口的集成"""
    print("\n🎯 演示与主窗口的集成...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow
        from core.mesh_manager import MeshManager, MeshParameter, MeshStatus
        from core.modal_results import ModalResults
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格窗口
        window = MeshWindow(None)
        
        # 添加演示数据到网格管理器
        demo_data = create_demo_modal_data()
        
        for data in demo_data:
            # 创建网格参数
            mesh = MeshParameter()
            mesh.name = data['name']
            mesh.size = data['size']
            mesh.status = MeshStatus.COMPLETED
            
            # 创建模态结果
            modal_results = ModalResults()
            modal_results.frequencies = data['frequencies']
            modal_results.calculation_time = data['calculation_time']
            mesh.modal_results = modal_results
            
            # 添加到管理器
            window.mesh_manager.add_mesh(mesh)
        
        # 刷新界面
        window._refresh_mesh_table()
        window._refresh_comparison_list()
        
        # 切换到结果对比标签页
        window.ui.tabWidget_main.setCurrentIndex(2)  # 结果对比标签页
        
        # 显示窗口
        window.show()
        
        print("✅ 主窗口集成演示已启动")
        print("💡 使用说明:")
        print("  - 当前已切换到'模态结果对比'标签页")
        print("  - 左侧列表显示了5个演示网格的模态结果")
        print("  - 选择多个网格结果，然后点击'更新图表'")
        print("  - 尝试不同的图表类型和显示选项")
        print("  - 使用'保存图表'功能导出结果")
        
        return window
        
    except Exception as e:
        print(f"❌ 主窗口集成演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主演示函数"""
    print("=" * 70)
    print("🎯 模态分析结果对比界面 - 完整功能演示")
    print("=" * 70)
    print("\n演示内容：")
    print("✨ 重新设计的模态分析结果对比界面")
    print("✨ 基于实际模态数据的matplotlib图表")
    print("✨ 三种专业图表类型的完整展示")
    print("✨ 图表保存和导出功能")
    print("✨ 与主界面的完整集成")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,
        format='%(levelname)s: %(message)s'
    )
    
    try:
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        
        print("\n请选择演示模式:")
        print("1. 独立图表组件演示")
        print("2. 主窗口集成演示")
        print("3. 两种演示都运行")
        
        choice = input("\n请输入选择 (1/2/3): ").strip()
        
        windows = []
        
        if choice in ['1', '3']:
            # 独立图表组件演示
            demo_window = demo_chart_widget()
            if demo_window:
                windows.append(demo_window)
        
        if choice in ['2', '3']:
            # 主窗口集成演示
            main_window = demo_integration_with_main_window()
            if main_window:
                windows.append(main_window)
        
        if windows:
            print(f"\n🎉 演示启动成功！共打开 {len(windows)} 个窗口")
            print("\n💡 演示特色功能:")
            print("• 基于真实模态分析数据的可视化")
            print("• 频率对比图：直观对比不同网格的模态频率")
            print("• 模态分布图：分析各频段的模态分布情况")
            print("• 网格收敛性分析：评估网格尺寸对计算精度的影响")
            print("• 完整的图表保存和导出功能")
            print("• 中文字体支持和交互功能")
            
            print("\n按 Ctrl+C 或关闭所有窗口结束演示")
            
            # 运行应用
            app.exec()
        else:
            print("❌ 演示启动失败")
    
    except KeyboardInterrupt:
        print("\n\n👋 演示已结束")
    except Exception as e:
        print(f"\n❌ 演示运行失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎉 模态分析结果对比界面演示完成！")
    print("=" * 70)

if __name__ == "__main__":
    main()
