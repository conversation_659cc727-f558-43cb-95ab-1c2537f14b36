# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh_merged.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QCheckBox, QComboBox,
    QDoubleSpinBox, QFormLayout, QGridLayout, QGroupBox,
    QHBoxLayout, QHeaderView, QLabel, QListWidget,
    QListWidgetItem, QMainWindow, QProgressBar, QPushButton,
    QRadioButton, QSizePolicy, QSpacerItem, QSpinBox, QTabWidget,
    QTableWidget, QTableWidgetItem, QTextEdit, QVBoxLayout,
    QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QSize(1400, 900))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(10)
        MainWindow.setFont(font)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_main = QVBoxLayout(self.centralwidget)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(15, 15, 15, 15)

        # 标题
        self.label_title = QLabel(self.centralwidget)
        self.label_title.setObjectName(u"label_title")
        self.label_title.setMinimumSize(QSize(0, 60))
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei UI"])
        font1.setPointSize(24)
        font1.setBold(True)
        self.label_title.setFont(font1)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.verticalLayout_main.addWidget(self.label_title)

        # 主标签页控件
        self.tabWidget_main = QTabWidget(self.centralwidget)
        self.tabWidget_main.setObjectName(u"tabWidget_main")

        # ==================== 标签页1: 网格管理 ====================
        self.tab_mesh_management = QWidget()
        self.tab_mesh_management.setObjectName(u"tab_mesh_management")
        self.horizontalLayout_mesh_mgmt = QHBoxLayout(self.tab_mesh_management)
        self.horizontalLayout_mesh_mgmt.setObjectName(u"horizontalLayout_mesh_mgmt")

        # 网格参数管理组
        self.groupBox_mesh_params = QGroupBox(self.tab_mesh_management)
        self.groupBox_mesh_params.setObjectName(u"groupBox_mesh_params")
        self.verticalLayout_params = QVBoxLayout(self.groupBox_mesh_params)
        self.verticalLayout_params.setObjectName(u"verticalLayout_params")

        # 工具栏
        self.horizontalLayout_toolbar = QHBoxLayout()
        self.horizontalLayout_toolbar.setObjectName(u"horizontalLayout_toolbar")

        self.btn_add_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_add_mesh.setObjectName(u"btn_add_mesh")
        self.btn_add_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_add_mesh)

        self.btn_import_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_import_mesh.setObjectName(u"btn_import_mesh")
        self.btn_import_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_import_mesh)

        self.btn_export_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_export_mesh.setObjectName(u"btn_export_mesh")
        self.btn_export_mesh.setMinimumSize(QSize(100, 35))
        self.horizontalLayout_toolbar.addWidget(self.btn_export_mesh)

        self.horizontalSpacer_toolbar = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_toolbar.addItem(self.horizontalSpacer_toolbar)
        self.verticalLayout_params.addLayout(self.horizontalLayout_toolbar)

        # 网格参数表格
        self.tableWidget_mesh_params = QTableWidget(self.groupBox_mesh_params)
        if (self.tableWidget_mesh_params.columnCount() < 6):
            self.tableWidget_mesh_params.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.tableWidget_mesh_params.setObjectName(u"tableWidget_mesh_params")
        self.tableWidget_mesh_params.setMinimumSize(QSize(0, 400))
        self.tableWidget_mesh_params.setAlternatingRowColors(True)
        self.tableWidget_mesh_params.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.tableWidget_mesh_params.setSortingEnabled(True)
        self.verticalLayout_params.addWidget(self.tableWidget_mesh_params)

        self.horizontalLayout_mesh_mgmt.addWidget(self.groupBox_mesh_params)

        # 网格统计信息组
        self.groupBox_mesh_stats = QGroupBox(self.tab_mesh_management)
        self.groupBox_mesh_stats.setObjectName(u"groupBox_mesh_stats")
        self.groupBox_mesh_stats.setMaximumSize(QSize(300, 16777215))
        self.verticalLayout_stats = QVBoxLayout(self.groupBox_mesh_stats)
        self.verticalLayout_stats.setObjectName(u"verticalLayout_stats")

        # 统计信息显示
        self.textEdit_mesh_stats = QTextEdit(self.groupBox_mesh_stats)
        self.textEdit_mesh_stats.setObjectName(u"textEdit_mesh_stats")
        self.textEdit_mesh_stats.setReadOnly(True)
        self.verticalLayout_stats.addWidget(self.textEdit_mesh_stats)

        self.horizontalLayout_mesh_mgmt.addWidget(self.groupBox_mesh_stats)
        self.tabWidget_main.addTab(self.tab_mesh_management, "")

        # ==================== 标签页2: 模态分析 ====================
        self.tab_generation_modal = QWidget()
        self.tab_generation_modal.setObjectName(u"tab_generation_modal")
        self.horizontalLayout_gen_modal = QHBoxLayout(self.tab_generation_modal)
        self.horizontalLayout_gen_modal.setObjectName(u"horizontalLayout_gen_modal")

        # 左侧：批量网格操作控制区域
        self.groupBox_batch_control = QGroupBox(self.tab_generation_modal)
        self.groupBox_batch_control.setObjectName(u"groupBox_batch_control")
        self.groupBox_batch_control.setMinimumSize(QSize(600, 0))
        self.verticalLayout_batch = QVBoxLayout(self.groupBox_batch_control)
        self.verticalLayout_batch.setObjectName(u"verticalLayout_batch")

        # 网格选择控制
        self.horizontalLayout_selection = QHBoxLayout()
        self.horizontalLayout_selection.setObjectName(u"horizontalLayout_selection")

        self.btn_select_all_meshes = QPushButton(self.groupBox_batch_control)
        self.btn_select_all_meshes.setObjectName(u"btn_select_all_meshes")
        self.btn_select_all_meshes.setMinimumSize(QSize(80, 30))
        self.horizontalLayout_selection.addWidget(self.btn_select_all_meshes)

        self.btn_select_none_meshes = QPushButton(self.groupBox_batch_control)
        self.btn_select_none_meshes.setObjectName(u"btn_select_none_meshes")
        self.btn_select_none_meshes.setMinimumSize(QSize(80, 30))
        self.horizontalLayout_selection.addWidget(self.btn_select_none_meshes)

        self.btn_select_inverse_meshes = QPushButton(self.groupBox_batch_control)
        self.btn_select_inverse_meshes.setObjectName(u"btn_select_inverse_meshes")
        self.btn_select_inverse_meshes.setMinimumSize(QSize(80, 30))
        self.horizontalLayout_selection.addWidget(self.btn_select_inverse_meshes)

        self.horizontalSpacer_selection = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_selection.addItem(self.horizontalSpacer_selection)

        self.label_selection_count = QLabel(self.groupBox_batch_control)
        self.label_selection_count.setObjectName(u"label_selection_count")
        self.label_selection_count.setStyleSheet(u"font-weight: bold; color: #0078d4;")
        self.horizontalLayout_selection.addWidget(self.label_selection_count)

        self.verticalLayout_batch.addLayout(self.horizontalLayout_selection)

        # 网格状态列表
        self.listWidget_mesh_status = QListWidget(self.groupBox_batch_control)
        self.listWidget_mesh_status.setObjectName(u"listWidget_mesh_status")
        self.listWidget_mesh_status.setMinimumSize(QSize(0, 300))
        self.listWidget_mesh_status.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.verticalLayout_batch.addWidget(self.listWidget_mesh_status)

        # 网格选择说明
        self.label_mesh_selection_info = QLabel(self.groupBox_batch_control)
        self.label_mesh_selection_info.setObjectName(u"label_mesh_selection_info")
        self.label_mesh_selection_info.setText("选择网格后可直接进行模态分析，系统将自动处理网格生成")
        self.label_mesh_selection_info.setStyleSheet(u"color: #666666; font-style: italic; padding: 10px;")
        self.label_mesh_selection_info.setWordWrap(True)
        self.verticalLayout_batch.addWidget(self.label_mesh_selection_info)

        self.horizontalLayout_gen_modal.addWidget(self.groupBox_batch_control)

        # 右侧：模态计算控制区域
        self.widget_right_panel = QWidget(self.tab_generation_modal)
        self.widget_right_panel.setObjectName(u"widget_right_panel")
        self.widget_right_panel.setMaximumSize(QSize(500, 16777215))
        self.verticalLayout_right = QVBoxLayout(self.widget_right_panel)
        self.verticalLayout_right.setObjectName(u"verticalLayout_right")

        # 模态计算参数组
        self.groupBox_modal_params = QGroupBox(self.widget_right_panel)
        self.groupBox_modal_params.setObjectName(u"groupBox_modal_params")
        self.formLayout_modal = QFormLayout(self.groupBox_modal_params)
        self.formLayout_modal.setObjectName(u"formLayout_modal")

        # 模态阶数
        self.label_modal_count = QLabel(self.groupBox_modal_params)
        self.label_modal_count.setObjectName(u"label_modal_count")
        self.formLayout_modal.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_modal_count)

        self.spinBox_modal_count = QSpinBox(self.groupBox_modal_params)
        self.spinBox_modal_count.setObjectName(u"spinBox_modal_count")
        self.spinBox_modal_count.setMinimum(1)
        self.spinBox_modal_count.setMaximum(100)
        self.spinBox_modal_count.setValue(10)
        self.formLayout_modal.setWidget(0, QFormLayout.ItemRole.FieldRole, self.spinBox_modal_count)

        # 频率限制
        self.checkBox_limit_freq = QCheckBox(self.groupBox_modal_params)
        self.checkBox_limit_freq.setObjectName(u"checkBox_limit_freq")
        self.checkBox_limit_freq.setChecked(True)
        self.formLayout_modal.setWidget(1, QFormLayout.ItemRole.SpanningRole, self.checkBox_limit_freq)

        # 频率范围
        self.label_freq_range = QLabel(self.groupBox_modal_params)
        self.label_freq_range.setObjectName(u"label_freq_range")
        self.formLayout_modal.setWidget(2, QFormLayout.ItemRole.LabelRole, self.label_freq_range)

        self.horizontalLayout_freq = QHBoxLayout()
        self.horizontalLayout_freq.setObjectName(u"horizontalLayout_freq")

        self.doubleSpinBox_freq_min = QDoubleSpinBox(self.groupBox_modal_params)
        self.doubleSpinBox_freq_min.setObjectName(u"doubleSpinBox_freq_min")
        self.doubleSpinBox_freq_min.setMaximum(9999.99)
        self.doubleSpinBox_freq_min.setValue(0.0)
        self.horizontalLayout_freq.addWidget(self.doubleSpinBox_freq_min)

        self.label_freq_to = QLabel(self.groupBox_modal_params)
        self.label_freq_to.setObjectName(u"label_freq_to")
        self.horizontalLayout_freq.addWidget(self.label_freq_to)

        self.doubleSpinBox_freq_max = QDoubleSpinBox(self.groupBox_modal_params)
        self.doubleSpinBox_freq_max.setObjectName(u"doubleSpinBox_freq_max")
        self.doubleSpinBox_freq_max.setMaximum(9999.99)
        self.doubleSpinBox_freq_max.setValue(1000.0)
        self.horizontalLayout_freq.addWidget(self.doubleSpinBox_freq_max)

        self.label_freq_unit = QLabel(self.groupBox_modal_params)
        self.label_freq_unit.setObjectName(u"label_freq_unit")
        self.horizontalLayout_freq.addWidget(self.label_freq_unit)

        self.formLayout_modal.setLayout(2, QFormLayout.ItemRole.FieldRole, self.horizontalLayout_freq)

        self.verticalLayout_right.addWidget(self.groupBox_modal_params)

        # 模态计算控制组
        self.groupBox_modal_control = QGroupBox(self.widget_right_panel)
        self.groupBox_modal_control.setObjectName(u"groupBox_modal_control")
        self.verticalLayout_modal_ctrl = QVBoxLayout(self.groupBox_modal_control)
        self.verticalLayout_modal_ctrl.setObjectName(u"verticalLayout_modal_ctrl")

        # 选中网格信息
        self.label_selected_info = QLabel(self.groupBox_modal_control)
        self.label_selected_info.setObjectName(u"label_selected_info")
        self.label_selected_info.setStyleSheet(u"font-weight: bold; color: #0078d4; padding: 5px;")
        self.verticalLayout_modal_ctrl.addWidget(self.label_selected_info)

        # 模态计算按钮
        self.btn_single_modal = QPushButton(self.groupBox_modal_control)
        self.btn_single_modal.setObjectName(u"btn_single_modal")
        self.btn_single_modal.setMinimumSize(QSize(0, 45))
        self.verticalLayout_modal_ctrl.addWidget(self.btn_single_modal)

        self.btn_batch_modal = QPushButton(self.groupBox_modal_control)
        self.btn_batch_modal.setObjectName(u"btn_batch_modal")
        self.btn_batch_modal.setMinimumSize(QSize(0, 45))
        self.verticalLayout_modal_ctrl.addWidget(self.btn_batch_modal)

        # 计算控制按钮
        self.horizontalLayout_calc_ctrl = QHBoxLayout()
        self.horizontalLayout_calc_ctrl.setObjectName(u"horizontalLayout_calc_ctrl")

        self.btn_pause_calculation = QPushButton(self.groupBox_modal_control)
        self.btn_pause_calculation.setObjectName(u"btn_pause_calculation")
        self.btn_pause_calculation.setMinimumSize(QSize(80, 35))
        self.btn_pause_calculation.setEnabled(False)
        self.horizontalLayout_calc_ctrl.addWidget(self.btn_pause_calculation)

        self.btn_stop_calculation = QPushButton(self.groupBox_modal_control)
        self.btn_stop_calculation.setObjectName(u"btn_stop_calculation")
        self.btn_stop_calculation.setMinimumSize(QSize(80, 35))
        self.btn_stop_calculation.setEnabled(False)
        self.horizontalLayout_calc_ctrl.addWidget(self.btn_stop_calculation)

        self.verticalLayout_modal_ctrl.addLayout(self.horizontalLayout_calc_ctrl)

        self.verticalLayout_right.addWidget(self.groupBox_modal_control)

        # 计算进度和状态显示组
        self.groupBox_calc_status = QGroupBox(self.widget_right_panel)
        self.groupBox_calc_status.setObjectName(u"groupBox_calc_status")
        self.verticalLayout_calc_status = QVBoxLayout(self.groupBox_calc_status)
        self.verticalLayout_calc_status.setObjectName(u"verticalLayout_calc_status")

        # 当前计算状态
        self.label_current_calc = QLabel(self.groupBox_calc_status)
        self.label_current_calc.setObjectName(u"label_current_calc")
        self.label_current_calc.setStyleSheet(u"color: #0078d4; font-style: italic;")
        self.verticalLayout_calc_status.addWidget(self.label_current_calc)

        # 计算进度条
        self.progressBar_calculation = QProgressBar(self.groupBox_calc_status)
        self.progressBar_calculation.setObjectName(u"progressBar_calculation")
        self.progressBar_calculation.setValue(0)
        self.verticalLayout_calc_status.addWidget(self.progressBar_calculation)

        # 计算统计信息
        self.textEdit_calc_stats = QTextEdit(self.groupBox_calc_status)
        self.textEdit_calc_stats.setObjectName(u"textEdit_calc_stats")
        self.textEdit_calc_stats.setMaximumSize(QSize(16777215, 120))
        self.textEdit_calc_stats.setReadOnly(True)
        self.verticalLayout_calc_status.addWidget(self.textEdit_calc_stats)

        self.verticalLayout_right.addWidget(self.groupBox_calc_status)

        # 计算结果选择按钮
        self.btn_select_results = QPushButton(self.widget_right_panel)
        self.btn_select_results.setObjectName(u"btn_select_results")
        self.btn_select_results.setMinimumSize(QSize(0, 45))
        self.btn_select_results.setEnabled(False)
        self.verticalLayout_right.addWidget(self.btn_select_results)

        # 添加垂直空间
        self.verticalSpacer_right = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.verticalLayout_right.addItem(self.verticalSpacer_right)

        self.horizontalLayout_gen_modal.addWidget(self.widget_right_panel)
        self.tabWidget_main.addTab(self.tab_generation_modal, "")

        # ==================== 标签页3: 结果对比 ====================
        self.tab_result_comparison = QWidget()
        self.tab_result_comparison.setObjectName(u"tab_result_comparison")
        self.horizontalLayout_comparison = QHBoxLayout(self.tab_result_comparison)
        self.horizontalLayout_comparison.setObjectName(u"horizontalLayout_comparison")

        # 对比控制组
        self.groupBox_comparison_control = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_control.setObjectName(u"groupBox_comparison_control")
        self.groupBox_comparison_control.setMaximumSize(QSize(350, 16777215))
        self.verticalLayout_comparison_ctrl = QVBoxLayout(self.groupBox_comparison_control)
        self.verticalLayout_comparison_ctrl.setObjectName(u"verticalLayout_comparison_ctrl")

        # 选择对比网格标签
        self.label_select_meshes = QLabel(self.groupBox_comparison_control)
        self.label_select_meshes.setObjectName(u"label_select_meshes")
        self.verticalLayout_comparison_ctrl.addWidget(self.label_select_meshes)

        # 对比网格列表
        self.listWidget_comparison_meshes = QListWidget(self.groupBox_comparison_control)
        self.listWidget_comparison_meshes.setObjectName(u"listWidget_comparison_meshes")
        self.listWidget_comparison_meshes.setMinimumSize(QSize(0, 200))
        self.listWidget_comparison_meshes.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.verticalLayout_comparison_ctrl.addWidget(self.listWidget_comparison_meshes)

        # 模态分析选项组
        self.groupBox_modal_options = QGroupBox(self.groupBox_comparison_control)
        self.groupBox_modal_options.setObjectName(u"groupBox_modal_options")
        self.verticalLayout_modal_options = QVBoxLayout(self.groupBox_modal_options)
        self.verticalLayout_modal_options.setObjectName(u"verticalLayout_modal_options")

        self.checkBox_show_frequencies = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_frequencies.setObjectName(u"checkBox_show_frequencies")
        self.checkBox_show_frequencies.setChecked(True)
        self.verticalLayout_modal_options.addWidget(self.checkBox_show_frequencies)

        self.checkBox_show_mode_shapes = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_mode_shapes.setObjectName(u"checkBox_show_mode_shapes")
        self.checkBox_show_mode_shapes.setChecked(True)
        self.verticalLayout_modal_options.addWidget(self.checkBox_show_mode_shapes)

        self.checkBox_show_mesh_info = QCheckBox(self.groupBox_modal_options)
        self.checkBox_show_mesh_info.setObjectName(u"checkBox_show_mesh_info")
        self.checkBox_show_mesh_info.setChecked(True)
        self.verticalLayout_modal_options.addWidget(self.checkBox_show_mesh_info)

        self.verticalLayout_comparison_ctrl.addWidget(self.groupBox_modal_options)

        # 图表类型选择组
        self.groupBox_chart_type = QGroupBox(self.groupBox_comparison_control)
        self.groupBox_chart_type.setObjectName(u"groupBox_chart_type")
        self.verticalLayout_chart_type = QVBoxLayout(self.groupBox_chart_type)
        self.verticalLayout_chart_type.setObjectName(u"verticalLayout_chart_type")

        self.radioButton_frequency_comparison = QRadioButton(self.groupBox_chart_type)
        self.radioButton_frequency_comparison.setObjectName(u"radioButton_frequency_comparison")
        self.radioButton_frequency_comparison.setChecked(True)
        self.verticalLayout_chart_type.addWidget(self.radioButton_frequency_comparison)

        self.radioButton_mode_distribution = QRadioButton(self.groupBox_chart_type)
        self.radioButton_mode_distribution.setObjectName(u"radioButton_mode_distribution")
        self.verticalLayout_chart_type.addWidget(self.radioButton_mode_distribution)

        self.radioButton_mesh_convergence = QRadioButton(self.groupBox_chart_type)
        self.radioButton_mesh_convergence.setObjectName(u"radioButton_mesh_convergence")
        self.verticalLayout_chart_type.addWidget(self.radioButton_mesh_convergence)

        self.verticalLayout_comparison_ctrl.addWidget(self.groupBox_chart_type)

        # 操作按钮
        self.btn_update_chart = QPushButton(self.groupBox_comparison_control)
        self.btn_update_chart.setObjectName(u"btn_update_chart")
        self.btn_update_chart.setMinimumSize(QSize(0, 40))
        self.btn_update_chart.setStyleSheet(u"QPushButton {\n"
"    background-color: #2196f3;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #1976d2;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #0d47a1;\n"
"}")
        self.verticalLayout_comparison_ctrl.addWidget(self.btn_update_chart)

        self.btn_import_results = QPushButton(self.groupBox_comparison_control)
        self.btn_import_results.setObjectName(u"btn_import_results")
        self.btn_import_results.setMinimumSize(QSize(0, 40))
        self.btn_import_results.setStyleSheet(u"QPushButton {\n"
"    background-color: #9c27b0;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 5px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #7b1fa2;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #4a148c;\n"
"}")
        self.verticalLayout_comparison_ctrl.addWidget(self.btn_import_results)

        self.btn_export_results = QPushButton(self.groupBox_comparison_control)
        self.btn_export_results.setObjectName(u"btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(0, 40))
        self.btn_export_results.setStyleSheet(u"QPushButton {\n"
"    background-color: #4caf50;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-weight: bold;\n"
"    font-size: 12px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #388e3c;\n"
"}\n"
"QPushButton:pressed {\n"
"    background-color: #2e7d32;\n"
"}")
        self.verticalLayout_comparison_ctrl.addWidget(self.btn_export_results)

        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_control)

        # 对比图表组
        self.groupBox_comparison_chart = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_chart.setObjectName(u"groupBox_comparison_chart")
        self.verticalLayout_chart = QVBoxLayout(self.groupBox_comparison_chart)
        self.verticalLayout_chart.setObjectName(u"verticalLayout_chart")
        self.verticalLayout_chart.setSpacing(10)

        # 图表工具栏
        self.horizontalLayout_chart_toolbar = QHBoxLayout()
        self.horizontalLayout_chart_toolbar.setObjectName(u"horizontalLayout_chart_toolbar")

        self.label_chart_title = QLabel(self.groupBox_comparison_chart)
        self.label_chart_title.setObjectName(u"label_chart_title")
        self.label_chart_title.setStyleSheet(u"font-size: 14px; font-weight: bold; color: #333;")
        self.horizontalLayout_chart_toolbar.addWidget(self.label_chart_title)

        self.horizontalSpacer_chart = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_chart_toolbar.addItem(self.horizontalSpacer_chart)

        self.btn_save_chart = QPushButton(self.groupBox_comparison_chart)
        self.btn_save_chart.setObjectName(u"btn_save_chart")
        self.btn_save_chart.setMaximumSize(QSize(100, 30))
        self.btn_save_chart.setStyleSheet(u"QPushButton {\n"
"    background-color: #ff9800;\n"
"    color: white;\n"
"    border: none;\n"
"    border-radius: 4px;\n"
"    font-size: 11px;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #f57c00;\n"
"}")
        self.horizontalLayout_chart_toolbar.addWidget(self.btn_save_chart)

        self.verticalLayout_chart.addLayout(self.horizontalLayout_chart_toolbar)

        # 图表容器
        self.widget_chart_container = QWidget(self.groupBox_comparison_chart)
        self.widget_chart_container.setObjectName(u"widget_chart_container")
        self.widget_chart_container.setMinimumSize(QSize(600, 500))
        self.widget_chart_container.setStyleSheet(u"QWidget {\n"
"    border: 2px dashed #cccccc;\n"
"    border-radius: 8px;\n"
"    background-color: #fafafa;\n"
"}")

        self.verticalLayout_chart_content = QVBoxLayout(self.widget_chart_container)
        self.verticalLayout_chart_content.setObjectName(u"verticalLayout_chart_content")

        self.label_chart_placeholder = QLabel(self.widget_chart_container)
        self.label_chart_placeholder.setObjectName(u"label_chart_placeholder")
        self.label_chart_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_chart_placeholder.setStyleSheet(u"color: #999999; \n"
"font-size: 14px; \n"
"line-height: 1.6;\n"
"border: none;")
        self.verticalLayout_chart_content.addWidget(self.label_chart_placeholder)

        self.verticalLayout_chart.addWidget(self.widget_chart_container)

        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_chart)
        self.tabWidget_main.addTab(self.tab_result_comparison, "")

        self.verticalLayout_main.addWidget(self.tabWidget_main)

        # ==================== 底部导航按钮 ====================
        self.horizontalLayout_navigation = QHBoxLayout()
        self.horizontalLayout_navigation.setObjectName(u"horizontalLayout_navigation")

        self.btn_generate_mesh = QPushButton(self.centralwidget)
        self.btn_generate_mesh.setObjectName(u"btn_generate_mesh")
        self.btn_generate_mesh.setMinimumSize(QSize(150, 50))
        self.horizontalLayout_navigation.addWidget(self.btn_generate_mesh)

        self.btn_view_results = QPushButton(self.centralwidget)
        self.btn_view_results.setObjectName(u"btn_view_results")
        self.btn_view_results.setMinimumSize(QSize(150, 50))
        self.horizontalLayout_navigation.addWidget(self.btn_view_results)

        self.horizontalSpacer_nav = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_nav)

        self.btn_previous = QPushButton(self.centralwidget)
        self.btn_previous.setObjectName(u"btn_previous")
        self.btn_previous.setMinimumSize(QSize(180, 50))
        self.horizontalLayout_navigation.addWidget(self.btn_previous)

        self.btn_next = QPushButton(self.centralwidget)
        self.btn_next.setObjectName(u"btn_next")
        self.btn_next.setMinimumSize(QSize(180, 50))
        self.horizontalLayout_navigation.addWidget(self.btn_next)

        self.btn_main_menu = QPushButton(self.centralwidget)
        self.btn_main_menu.setObjectName(u"btn_main_menu")
        self.btn_main_menu.setMinimumSize(QSize(120, 50))
        self.horizontalLayout_navigation.addWidget(self.btn_main_menu)

        self.verticalLayout_main.addLayout(self.horizontalLayout_navigation)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        self.tabWidget_main.setCurrentIndex(0)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"网格无关性验证系统", None))
        self.label_title.setText(QCoreApplication.translate("MainWindow", u"网格无关性验证系统", None))

        # 标签页1: 网格管理
        self.groupBox_mesh_params.setTitle(QCoreApplication.translate("MainWindow", u"网格参数管理", None))
        self.btn_add_mesh.setText(QCoreApplication.translate("MainWindow", u"添加网格", None))
        self.btn_import_mesh.setText(QCoreApplication.translate("MainWindow", u"导入配置", None))
        self.btn_export_mesh.setText(QCoreApplication.translate("MainWindow", u"导出配置", None))

        ___qtablewidgetitem = self.tableWidget_mesh_params.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"名称", None))
        ___qtablewidgetitem1 = self.tableWidget_mesh_params.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"尺寸(mm)", None))
        ___qtablewidgetitem2 = self.tableWidget_mesh_params.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"单元类型", None))
        ___qtablewidgetitem3 = self.tableWidget_mesh_params.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"状态", None))
        ___qtablewidgetitem4 = self.tableWidget_mesh_params.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"创建时间", None))
        ___qtablewidgetitem5 = self.tableWidget_mesh_params.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"操作", None))

        self.groupBox_mesh_stats.setTitle(QCoreApplication.translate("MainWindow", u"网格统计信息", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_mesh_management), QCoreApplication.translate("MainWindow", u"网格管理", None))

        # 标签页2: 网格生成与模态分析
        self.groupBox_batch_control.setTitle(QCoreApplication.translate("MainWindow", u"批量网格操作控制", None))
        self.btn_select_all_meshes.setText(QCoreApplication.translate("MainWindow", u"全选", None))
        self.btn_select_none_meshes.setText(QCoreApplication.translate("MainWindow", u"全不选", None))
        self.btn_select_inverse_meshes.setText(QCoreApplication.translate("MainWindow", u"反选", None))
        self.label_selection_count.setText(QCoreApplication.translate("MainWindow", u"已选择: 0 个网格", None))

        self.groupBox_modal_params.setTitle(QCoreApplication.translate("MainWindow", u"模态计算参数", None))
        self.label_modal_count.setText(QCoreApplication.translate("MainWindow", u"模态阶数:", None))
        self.checkBox_limit_freq.setText(QCoreApplication.translate("MainWindow", u"限制频率范围", None))
        self.label_freq_range.setText(QCoreApplication.translate("MainWindow", u"频率范围:", None))
        self.label_freq_to.setText(QCoreApplication.translate("MainWindow", u"~", None))
        self.label_freq_unit.setText(QCoreApplication.translate("MainWindow", u"Hz", None))

        self.groupBox_modal_control.setTitle(QCoreApplication.translate("MainWindow", u"模态计算控制", None))
        self.label_selected_info.setText(QCoreApplication.translate("MainWindow", u"请选择要进行模态计算的网格", None))
        self.btn_single_modal.setText(QCoreApplication.translate("MainWindow", u"单个模态计算", None))
        self.btn_batch_modal.setText(QCoreApplication.translate("MainWindow", u"批量模态计算", None))
        self.btn_pause_calculation.setText(QCoreApplication.translate("MainWindow", u"暂停", None))
        self.btn_stop_calculation.setText(QCoreApplication.translate("MainWindow", u"停止", None))

        self.groupBox_calc_status.setTitle(QCoreApplication.translate("MainWindow", u"计算进度和状态", None))
        self.label_current_calc.setText(QCoreApplication.translate("MainWindow", u"当前状态: 等待开始", None))
        self.btn_select_results.setText(QCoreApplication.translate("MainWindow", u"选择计算结果", None))

        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_generation_modal), QCoreApplication.translate("MainWindow", u"网格生成与模态分析", None))

        # 标签页3: 结果对比
        self.groupBox_comparison_control.setTitle(QCoreApplication.translate("MainWindow", u"模态分析结果控制", None))
        self.label_select_meshes.setText(QCoreApplication.translate("MainWindow", u"选择对比网格:", None))
        self.groupBox_modal_options.setTitle(QCoreApplication.translate("MainWindow", u"模态分析选项", None))
        self.checkBox_show_frequencies.setText(QCoreApplication.translate("MainWindow", u"显示频率数值", None))
        self.checkBox_show_mode_shapes.setText(QCoreApplication.translate("MainWindow", u"显示模态阶数", None))
        self.checkBox_show_mesh_info.setText(QCoreApplication.translate("MainWindow", u"显示网格信息", None))
        self.groupBox_chart_type.setTitle(QCoreApplication.translate("MainWindow", u"图表类型", None))
        self.radioButton_frequency_comparison.setText(QCoreApplication.translate("MainWindow", u"频率对比图", None))
        self.radioButton_mode_distribution.setText(QCoreApplication.translate("MainWindow", u"模态分布图", None))
        self.radioButton_mesh_convergence.setText(QCoreApplication.translate("MainWindow", u"网格收敛性分析", None))
        self.btn_update_chart.setText(QCoreApplication.translate("MainWindow", u"更新图表", None))
        self.btn_import_results.setText(QCoreApplication.translate("MainWindow", u"导入结果", None))
        self.btn_export_results.setText(QCoreApplication.translate("MainWindow", u"导出结果", None))
        self.groupBox_comparison_chart.setTitle(QCoreApplication.translate("MainWindow", u"模态分析结果图表", None))
        self.label_chart_title.setText(QCoreApplication.translate("MainWindow", u"模态频率对比分析", None))
        self.btn_save_chart.setText(QCoreApplication.translate("MainWindow", u"保存图表", None))
        self.label_chart_placeholder.setText(QCoreApplication.translate("MainWindow", u"模态分析结果图表将在此处显示\n\n📊 可用数据类型：\n• 网格名称和尺寸信息\n• 网格节点/单元数量\n• 模态阶数 (1-N阶)\n• 各阶模态频率结果\n\n💡 选择左侧网格结果并点击\"更新图表\"开始分析", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_result_comparison), QCoreApplication.translate("MainWindow", u"模态结果对比", None))

        # 底部导航按钮
        self.btn_generate_mesh.setText(QCoreApplication.translate("MainWindow", u"生成网格", None))
        self.btn_view_results.setText(QCoreApplication.translate("MainWindow", u"查看结果", None))
        self.btn_previous.setText(QCoreApplication.translate("MainWindow", u"上一步(约束设置)", None))
        self.btn_next.setText(QCoreApplication.translate("MainWindow", u"下一步(计算结果)", None))
        self.btn_main_menu.setText(QCoreApplication.translate("MainWindow", u"主菜单", None))
    # retranslateUi
