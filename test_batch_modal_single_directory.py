#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量模态计算单个输出目录验证脚本

此脚本用于验证修改后的批量模态计算配置文件生成功能，确保：
1. 使用单个输出目录而不是复数文件夹
2. 目录名称包含所有网格名称
3. 路径格式正确（使用正斜杠）
4. 符合用户期望的格式

作者: AI Assistant
日期: 2025-08-01
"""

import sys
import os
import logging
import tempfile
import json
from datetime import datetime
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_single_output_directory_format():
    """测试单个输出目录格式"""
    try:
        logger.info("开始测试单个输出目录格式")
        
        # 模拟网格参数
        class MockMesh:
            def __init__(self, name, size):
                self.name = name
                self.size = size
        
        test_meshes = [
            MockMesh("a1", 12.0),
            MockMesh("a2", 20.0),
            MockMesh("a3", 15.0)
        ]
        
        # 模拟工作目录
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 构建包含所有网格名称的目录名
        mesh_names = "_".join([mesh.name for mesh in test_meshes])
        
        # 创建单个输出目录，格式：batch_<mesh1>_<mesh2>_<mesh3>_modal_output__<timestamp>
        output_dir_name = f"batch_{mesh_names}_modal_output__{timestamp}"
        output_dir = os.path.join(work_dir, "temp", output_dir_name)
        
        # 转换为正斜杠格式
        output_dir = output_dir.replace("\\", "/")
        
        # 验证路径格式
        expected_pattern = f"batch_a1_a2_a3_modal_output__{timestamp}"
        assert expected_pattern in output_dir, f"目录名称应该包含{expected_pattern}"
        assert "/" in output_dir, "路径应该使用正斜杠"
        assert "\\" not in output_dir, "路径不应该包含反斜杠"
        assert output_dir.startswith(work_dir.replace("\\", "/")), "应该以工作目录开始"
        
        # 验证与用户期望格式的匹配
        # 用户期望：batch_a1_a2_a3_modal_output__20250731_235407
        expected_format = f"{work_dir}/temp/batch_a1_a2_a3_modal_output__{timestamp}".replace("\\", "/")
        assert output_dir == expected_format, f"路径格式不正确，期望: {expected_format}, 实际: {output_dir}"
        
        logger.info(f"✅ 单个输出目录格式验证通过")
        logger.info(f"  - 输出目录: {output_dir}")
        logger.info(f"  - 网格名称: {mesh_names}")
        logger.info(f"  - 时间戳: {timestamp}")
        logger.info(f"  - 路径格式: 正斜杠 ✓")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单个输出目录格式测试失败: {str(e)}", exc_info=True)
        return False

def test_batch_config_single_directory():
    """测试批量配置文件单个目录生成"""
    try:
        logger.info("开始测试批量配置文件单个目录生成")
        
        # 模拟网格参数
        class MockMesh:
            def __init__(self, name, size):
                self.name = name
                self.size = size
        
        test_meshes = [
            MockMesh("a1", 12.0),
            MockMesh("a2", 20.0),
            MockMesh("a3", 15.0)
        ]
        
        # 模拟计算参数
        calc_params = {
            'modal_count': 10,
            'limit_freq': True,
            'freq_min': 0.0,
            'freq_max': 1000.0
        }
        
        # 模拟输出目录
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        mesh_names = "_".join([mesh.name for mesh in test_meshes])
        output_dir_name = f"batch_{mesh_names}_modal_output__{timestamp}"
        output_dir = f"{work_dir}/temp/{output_dir_name}".replace("\\", "/")
        
        # 构建批量配置 - 使用单个输出目录，不创建子目录
        config = {
            "element_size": [mesh.size / 1000.0 for mesh in test_meshes],  # 转换为米
            "output_directory": output_dir,  # 单个输出目录
            "batch_mode": True,
            "mesh_names": [mesh.name for mesh in test_meshes],
            "calculation_params": calc_params
        }
        
        # 验证配置格式
        assert "element_size" in config, "应该包含element_size字段"
        assert "output_directory" in config, "应该包含output_directory字段"
        assert "batch_mode" in config, "应该包含batch_mode字段"
        assert "mesh_names" in config, "应该包含mesh_names字段"
        assert "calculation_params" in config, "应该包含calculation_params字段"
        
        # 验证不包含复数目录字段
        assert "mesh_output_directories" not in config, "不应该包含mesh_output_directories字段"
        
        # 验证数据格式
        assert isinstance(config["element_size"], list), "element_size应该是列表"
        assert len(config["element_size"]) == len(test_meshes), "element_size长度应该等于网格数量"
        assert config["batch_mode"] == True, "batch_mode应该为True"
        assert len(config["mesh_names"]) == len(test_meshes), "mesh_names长度应该等于网格数量"
        
        # 验证路径格式
        assert "/" in config["output_directory"], "输出目录应该使用正斜杠"
        assert "\\" not in config["output_directory"], "输出目录不应该包含反斜杠"
        assert f"batch_{mesh_names}_modal_output__" in config["output_directory"], "目录名称应该包含所有网格名称"
        
        # 保存配置文件进行测试
        os.makedirs("temp", exist_ok=True)
        config_path = "temp/test_batch_config_single_dir.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # 验证保存的配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        assert loaded_config == config, "保存和加载的配置应该一致"
        
        logger.info(f"✅ 批量配置文件单个目录生成验证通过")
        logger.info(f"  - 配置文件路径: {config_path}")
        logger.info(f"  - 网格数量: {len(test_meshes)}")
        logger.info(f"  - 网格尺寸列表: {config['element_size']}")
        logger.info(f"  - 输出目录: {config['output_directory']}")
        logger.info(f"  - 网格列表: {config['mesh_names']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 批量配置文件单个目录生成测试失败: {str(e)}", exc_info=True)
        return False

def test_mesh_config_latest_single_directory():
    """测试mesh_config_latest.json单个目录格式"""
    try:
        logger.info("开始测试mesh_config_latest.json单个目录格式")
        
        # 模拟批量配置
        batch_config = {
            "element_size": [0.012, 0.020, 0.015],
            "output_directory": "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/batch_a1_a2_a3_modal_output__20250731_235407",
            "batch_mode": True,
            "mesh_names": ["a1", "a2", "a3"]
        }
        
        # 创建mesh_config_latest.json文件（批量格式） - 使用单个输出目录
        mesh_config = {
            "element_size": batch_config["element_size"],  # 保持列表格式
            "output_directory": batch_config["output_directory"].replace("\\", "/")  # 确保使用正斜杠
        }
        
        # 验证配置格式
        assert "element_size" in mesh_config, "应该包含element_size字段"
        assert "output_directory" in mesh_config, "应该包含output_directory字段"
        
        # 验证不包含复数目录字段
        assert "mesh_output_directories" not in mesh_config, "不应该包含mesh_output_directories字段"
        
        # 验证路径格式
        assert "/" in mesh_config["output_directory"], "输出目录应该使用正斜杠"
        assert "\\" not in mesh_config["output_directory"], "输出目录不应该包含反斜杠"
        assert "batch_a1_a2_a3_modal_output__" in mesh_config["output_directory"], "目录名称应该包含所有网格名称"
        
        # 验证与用户期望格式的匹配
        expected_path = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/batch_a1_a2_a3_modal_output__20250731_235407"
        assert mesh_config["output_directory"] == expected_path, f"路径应该匹配用户期望格式"
        
        # 保存配置文件
        os.makedirs("temp/json", exist_ok=True)
        mesh_config_path = "temp/json/mesh_config_latest_single_dir.json"
        with open(mesh_config_path, 'w', encoding='utf-8') as f:
            json.dump(mesh_config, f, indent=4, ensure_ascii=False)
        
        logger.info(f"✅ mesh_config_latest.json单个目录格式验证通过")
        logger.info(f"  - 配置文件路径: {mesh_config_path}")
        logger.info(f"  - 网格尺寸列表: {mesh_config['element_size']}")
        logger.info(f"  - 输出目录: {mesh_config['output_directory']}")
        logger.info(f"  - 符合用户期望格式: ✓")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ mesh_config_latest.json单个目录格式测试失败: {str(e)}", exc_info=True)
        return False

def test_directory_name_variations():
    """测试不同网格组合的目录名称生成"""
    try:
        logger.info("开始测试不同网格组合的目录名称生成")
        
        # 测试不同的网格组合
        test_cases = [
            (["a1"], "batch_a1_modal_output__"),
            (["a1", "a2"], "batch_a1_a2_modal_output__"),
            (["a1", "a2", "a3"], "batch_a1_a2_a3_modal_output__"),
            (["mesh_12mm", "mesh_8mm"], "batch_mesh_12mm_mesh_8mm_modal_output__"),
            (["test1", "test2", "test3", "test4"], "batch_test1_test2_test3_test4_modal_output__")
        ]
        
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        timestamp = "20250731_235407"
        
        for mesh_names, expected_prefix in test_cases:
            # 构建目录名
            mesh_names_str = "_".join(mesh_names)
            output_dir_name = f"batch_{mesh_names_str}_modal_output__{timestamp}"
            output_dir = f"{work_dir}/temp/{output_dir_name}".replace("\\", "/")
            
            # 验证格式
            assert expected_prefix in output_dir_name, f"目录名称应该包含{expected_prefix}"
            assert timestamp in output_dir_name, f"目录名称应该包含时间戳{timestamp}"
            assert "/" in output_dir, "路径应该使用正斜杠"
            assert "\\" not in output_dir, "路径不应该包含反斜杠"
            
            logger.info(f"  ✓ 网格 {mesh_names} -> {output_dir_name}")
        
        logger.info(f"✅ 不同网格组合的目录名称生成验证通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 不同网格组合的目录名称生成测试失败: {str(e)}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始批量模态计算单个输出目录验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 运行测试
    tests = [
        ("单个输出目录格式测试", test_single_output_directory_format),
        ("批量配置文件单个目录生成测试", test_batch_config_single_directory),
        ("mesh_config_latest.json单个目录格式测试", test_mesh_config_latest_single_directory),
        ("不同网格组合的目录名称生成测试", test_directory_name_variations)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}", exc_info=True)
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！批量模态计算单个输出目录验证成功")
        logger.info("\n📋 修改要点总结:")
        logger.info("• ✅ 使用单个输出目录而不是复数文件夹")
        logger.info("• ✅ 目录名称包含所有网格名称")
        logger.info("• ✅ 路径格式正确（使用正斜杠）")
        logger.info("• ✅ 符合用户期望的格式")
        logger.info("• ✅ 移除了mesh_output_directories字段")
        logger.info("\n📝 示例输出目录格式:")
        logger.info("  batch_a1_a2_a3_modal_output__20250731_235407")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
