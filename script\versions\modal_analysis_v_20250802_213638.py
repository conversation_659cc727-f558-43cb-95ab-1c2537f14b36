# -*- coding: utf-8 -*-
import os
import math
import csv
import logging
import json
import time

# The following objects are assumed to be available in the runtime environment
# ExtAPI, Model, DataModel, etc.

class LoggerManager:
    """
    Manages logging configuration and provides a logger instance.
    """
    def __init__(self, target_directory):
        if not os.path.exists(target_directory):
            try:
                os.makedirs(target_directory)
                print("Log directory created: ", target_directory)
            except Exception as e:
                print("Failed to create target directory {}: {}".format(target_directory,e))
                raise
        
        if not os.access(target_directory, os.W_OK):
            raise PermissionError("Target directory is not writable: {}".format(target_directory))
        
        self.log_file_path = os.path.join(target_directory, "ansys_workbench.log")
        try:
            self.logger = self.setup_logger(self.log_file_path)
        except Exception as e:
            print("Error initializing logger: {}".format(e))
            raise

    
    def setup_logger(self, log_file):
        logger = logging.getLogger('AnsysWorkbenchLogger')
        if not logger.handlers:  # Prevent duplicate handler addition
            logger.setLevel(logging.DEBUG)
            fh = logging.FileHandler(log_file)
            fh.setLevel(logging.DEBUG)
            ch = logging.StreamHandler()
            ch.setLevel(logging.ERROR)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            fh.setFormatter(formatter)
            ch.setFormatter(formatter)
            logger.addHandler(fh)
            logger.addHandler(ch)
        return logger

    
    def get_logger(self):
        return self.logger
# --------------------------------------------------------------------
# Added MeshManager
# --------------------------------------------------------------------
class MeshManager(object):
    """
    Responsible for collecting Body IDs, unified and local mesh generation, exporting mesh info and screenshots
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model   = ExtAPI.DataModel.Project.Model
        self.all_body_ids = []

    # Collect all geometry body IDs
    def store_all_bodies(self):
        self.all_body_ids = []
        for asm in self.ExtAPI.DataModel.GeoData.Assemblies:
            for part in asm.Parts:
                for body in part.Bodies:
                    self.all_body_ids.append(body.Id)
        self.logger.info("All Body IDs cached, total count: %d", len(self.all_body_ids))
        return self.all_body_ids

    # Global automatic mesh + Sizing
    def generate_allbody_mesh(self, element_size_m=0.01):
        mesh = self.Model.Mesh
        mesh.ClearGeneratedData()
        # Clear old methods/Sizing
        for child in list(mesh.Children):
            child.Delete()

        sel = self.ExtAPI.SelectionManager.CreateSelectionInfo(
            SelectionTypeEnum.GeometryEntities)
        sel.Ids = self.all_body_ids

        method = mesh.AddAutomaticMethod()
        method.Location = sel

        sizing = mesh.AddSizing()
        sizing.Location = sel
        sizing.ElementSize = Quantity(element_size_m, "m")

        mesh.GenerateMesh()
        self.logger.info("Global mesh generation completed (ElementSize = %.4f m)", element_size_m)

    # Apply local Sizing to individual named selection
    def generate_local_sizing(self, named_dict, sel_name, elem_size_m):
        ids = named_dict.get(sel_name, [])
        if not ids:
            self.logger.warning("Named selection %s not found, skipping local mesh", sel_name)
            return

        mesh = self.Model.Mesh
        sel  = self.ExtAPI.SelectionManager.CreateSelectionInfo(
            SelectionTypeEnum.GeometryEntities)
        sel.Ids = ids
        sizing = mesh.AddSizing()
        sizing.Location = sel
        sizing.ElementSize = Quantity(elem_size_m, "m")
        mesh.Update()      # Only update mesh tree; do not recalculate immediately

    # Export mesh information and screenshots
    def export_mesh_info_png(self, png_path):
        mesh = self.Model.Mesh
        mesh.Activate()

        # Simple screenshot settings
        setting = Ansys.Mechanical.Graphics.GraphicsImageExportSettings()
        Graphics.Camera.SetSpecificViewOrientation(ViewOrientationType.Iso)
        Graphics.Camera.SceneHeight = Quantity(1.5, 'm')
        Graphics.ViewOptions.ShowRuler = False
        Graphics.ViewOptions.ShowTriad = False
        Graphics.ViewOptions.ShowLegend = False
        Graphics.ExportImage(png_path, GraphicsImageExportFormat.PNG, setting) # Export with default settings
        Graphics.ViewOptions.ShowRuler = True
        Graphics.ViewOptions.ShowTriad = True
        Graphics.ViewOptions.ShowLegend = True
        
        

        n_ele  = mesh.Elements
        n_node = mesh.Nodes
        self.logger.info("Screenshot saved to %s, Elements=%d, Nodes=%d", png_path, n_ele, n_node)
        return n_ele, n_node
    
class SelectionManagerWrapper:
    """
    Encapsulates selection operations and named selections retrieval.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model

    def get_named_selections_dict(self):
        named_selections_dict = {}
        for ns in self.Model.NamedSelections.Children:
            try:
                # Get IDs of geometric entities associated with named selection
                selection = ns.Location
                if selection and hasattr(selection, 'Ids'):
                    named_selections_dict[ns.Name] = selection.Ids
            except Exception as e:
                self.logger.warning("Could not get Ids for Named Selection '%s': %s", ns.Name, e)
        
        self.logger.info("Retrieved all named selections.")
        for name, ids in named_selections_dict.iteritems():
            self.logger.debug("Named Selection Name: %s, IDs: %s", name, ids)
        return named_selections_dict

    def create_selection(self, entity_ids, entity_type=SelectionTypeEnum.GeometryEntities):
        selection = self.ExtAPI.SelectionManager.CreateSelectionInfo(entity_type)
        selection.Ids = entity_ids
        return selection


class FileIO:
    """
    Handles file I/O operations, such as reading force files.
    """
    def __init__(self, logger):
        self.logger = logger

    def read_force_file(self, file_name):
        time_list = []
        force_list = []
        if not os.path.exists(file_name):
            self.logger.warning("File does not exist: %s", file_name)
            return time_list, force_list
        try:
            with open(file_name, "r") as file:
                reader = csv.reader(file, delimiter=' ')
                for row in reader:
                    if not row or row[0].startswith(('"', '(')):
                        continue
                    if len(row) >= 3:
                        try:
                            force = float(row[1])
                            t = float(row[2])
                            force_list.append(Quantity("{0} [N]".format(force)))
                            time_list.append(Quantity("{0} [s]".format(t)))
                        except ValueError:
                            self.logger.warning("Invalid data in file: %s", file_name)
            self.logger.info("Read force file: %s", file_name)
            return time_list, force_list
        except Exception as e:
            self.logger.error("Error occurred while reading file %s: %s", file_name, str(e), exc_info=True)
            return time_list, force_list


class GeometryHelper:
    """
    Stores node coordinates and finds the closest nodes to given target points.
    """
    def __init__(self, logger):
        self.logger = logger

    def store_node_coordinates(self, analysis):
        try:
            mesh = analysis.MeshData
            node_ids = mesh.NodeIds
            node_by_id = mesh.NodeById
            node_coordinates = {
                node_id: (node_by_id(node_id).X, node_by_id(node_id).Y, node_by_id(node_id).Z)
                for node_id in node_ids
            }
            self.logger.info("Stored coordinates for all nodes.")
            return node_coordinates
        except Exception as e:
            self.logger.error("Error in store_node_coordinates: %s", str(e), exc_info=True)
            return {}

    def find_closest_node(self, node_coordinates, tx, ty, tz):
        try:
            min_dist = float('inf')
            closest_id = None
            for node_id, (x, y, z) in node_coordinates.items():
                dx = x - tx
                dy = y - ty
                dz = z - tz
                dist_sq = dx*dx + dy*dy + dz*dz
                if dist_sq < min_dist:
                    min_dist = dist_sq
                    closest_id = node_id
            self.logger.info("Found closest node ID: %s for point (%s,%s,%s).", closest_id, tx, ty, tz)
            return closest_id
        except Exception as e:
            self.logger.error("Error in find_closest_node: %s", str(e), exc_info=True)
            return None

    def find_closest_nodes(self, node_coordinates, target_points):
        closest_nodes = {}
        try:
            for (tx, ty, tz) in target_points:
                cn = self.find_closest_node(node_coordinates, tx, ty, tz)
                closest_nodes[(tx, ty, tz)] = cn
            self.logger.info("Processed all target points to find closest nodes.")
            return closest_nodes
        except Exception as e:
            self.logger.error("Error in find_closest_nodes: %s", str(e), exc_info=True)
            return closest_nodes

class ConnectionManager:
    """
    Manages connection groups and joints.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model
        self.connection_group = None
        
    # ------------------ Bearing Section ------------------
    def load_bearing_config(self, config_file):
        """
        Reads a JSON file that contains multiple bearing configurations.
        """
        if not os.path.exists(config_file):
            self.logger.warning("Bearing config file not found: %s", config_file)
            return []

        try:
            with open(config_file, "r") as f:
                data = json.load(f)
            bearings = data.get("bearings", [])
            self.logger.info("Loaded %d bearing configurations from %s.", len(bearings), config_file)
            return bearings
        except Exception as e:
            self.logger.error("Error reading bearing config file %s: %s", config_file, str(e), exc_info=True)
            return []
    
    def create_bearing_in_batch(self, named_selections_dict, bearing_list):
        """
        Loops through the bearing_list and calls add_bearing_connection() for each item.
        """
        for item in bearing_list:
            ref_name = item.get("ref_name")
            mob_name = item.get("mob_name")
            if not (ref_name and mob_name):
                self.logger.warning("Skipping invalid bearing config: %s", item)
                continue

            self.add_bearing_connection(
                named_selections_dict,
                item,            # setting_dict
                ref_name, 
                mob_name
            )
    
    def add_bearing_connection(self, named_selections_dict, setting_dict, ref_name, mob_name):
        """
        Original bearing creation method, retained from older code with minimal changes.
        """
        try:
            self.logger.info("Adding bearing connection for: %s <-> %s", ref_name, mob_name)

            bearing = self.Model.Connections.AddBearing()
            bearing.ConnectionType = ConnectionScopingType.BodyToBody
            bearing.RenameBasedOnDefinition()

            # Set RotationPlane
            plane_setting = setting_dict.get("RotationPlane", "X-Y Plane")
            if plane_setting == "X-Y Plane":
                bearing.ReferenceRotationPlane = RotationPlane.XY
            elif plane_setting == "Y-Z Plane":
                bearing.ReferenceRotationPlane = RotationPlane.YZ
            else:
                bearing.ReferenceRotationPlane = RotationPlane.XZ

            # Stiffness
            bearing.StiffnessK11.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k11", 0.0))))
            bearing.StiffnessK22.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k22", 0.0))))
            bearing.StiffnessK12.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k12", 0.0))))
            bearing.StiffnessK21.Output.SetDiscreteValue(0, Quantity("{0} [N m^-1]".format(setting_dict.get("k21", 0.0))))

            # Damping
            bearing.DampingC11.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c11", 0.0))))
            bearing.DampingC22.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c22", 0.0))))
            bearing.DampingC12.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c12", 0.0))))
            bearing.DampingC21.Output.SetDiscreteValue(0, Quantity("{0} [N sec m^-1]".format(setting_dict.get("c21", 0.0))))

            # Reference & Mobile
            selection_ref = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_ref.Ids = named_selections_dict.get(ref_name, [])
            bearing.ReferenceLocation = selection_ref

            selection_mob = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_mob.Ids = named_selections_dict.get(mob_name, [])
            bearing.MobileLocation = selection_mob
            if plane_setting == "X-Y Plane":
                bearing.MobileZCoordinate = bearing.ReferenceZCoordinate
            elif plane_setting == "Y-Z Plane":
                bearing.MobileXCoordinate = bearing.ReferenceXCoordinate
            else:
                bearing.MobileYCoordinate = bearing.ReferenceYCoordinate

            self.logger.info("Bearing connection added successfully.")
            return bearing
        except Exception as e:
            self.logger.error("Error in add_bearing_connection: %s", str(e), exc_info=True)
            return None
    # ------------------ Bearing Section ------------------
    
    # ------------------ Bushing Section ------------------
    def load_bushing_config(self, config_file):
        """
        Example JSON structure:
        {
          "bushings": [ ... ]
        }
        """
        if not os.path.exists(config_file):
            self.logger.warning("Bushing config file not found: %s", config_file)
            return []

        try:
            with open(config_file, "r") as f:
                data = json.load(f)
            bushing_list = data.get("bushings", [])
            self.logger.info("Loaded %d bushing configs from %s.", len(bushing_list), config_file)
            return bushing_list
        except Exception as e:
            self.logger.error("Error reading bushing config file %s: %s", config_file, str(e), exc_info=True)
            return []

    def create_bushing_joints_in_batch(self, named_selections_dict, bushing_list):
        """
        Loops through the bushing_list from JSON and calls add_bushing_joint() for each item.
        """
        for item in bushing_list:
            mob_name = item.get("mob_name")
            if not mob_name:
                self.logger.warning("Skipping invalid bushing config (missing mob_name): %s", item)
                continue

            connection_type = item.get("connection_type", "BodyToGround")
            stiffness_matrix = item.get("stiffness_matrix", {})
            damping_matrix = item.get("damping_matrix", {})

            self.add_bushing_joint(
                named_selections_dict,
                mob_name,
                stiffness_matrix,
                damping_matrix,
                connection_type=connection_type
            )

    def add_bushing_joint(self, named_selections_dict, mob_name, stiffness_matrix, damping_matrix, connection_type="BodyToGround"):
        """
        Moderate encapsulation of Mechanical's original methods: AddJoint(), BushingWorksheet and other APIs remain unchanged.
        """
        try:
            # connection_group
            if not self.connection_group:
                connections = self.Model.Connections
                self.connection_group = connections.AddConnectionGroup()
                self.connection_group.Name = "Joints_bushing"
                self.logger.info("Connection group 'Joints_bushing' created.")
            else:
                self.logger.info("Using existing connection group 'Joints_bushing'.")

            joint = self.connection_group.AddJoint()
            joint.RenameBasedOnDefinition()
            joint.Type = JointType.Bushing

            if connection_type == "BodyToBody":
                joint.ConnectionType = JointScopingType.BodyToBody
            else:
                joint.ConnectionType = JointScopingType.BodyToGround

            # Mobile location
            selection_mob = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection_mob.Ids = named_selections_dict.get(mob_name, [])
            joint.MobileLocation = selection_mob

            # get BushingWorksheet
            worksheet = joint.BushingWorksheet

            # Set stiffness
            self.set_bushing_parameters(worksheet, stiffness_matrix, is_stiffness=True)
            # Set damping
            self.set_bushing_parameters(worksheet, damping_matrix, is_stiffness=False)

            self.logger.info("Bushing joint added successfully for reference: %s, type=%s", mob_name, connection_type)
            return joint
        except Exception as e:
            self.logger.error("Error adding bushing joint: %s", str(e), exc_info=True)
            return None

    def set_bushing_parameters(self, worksheet, parameter_matrix, is_stiffness=True):
        """
        Mechanical statements: SetBushingStiffnessPerUnitX/Y/Z(index, value)
                              and SetBushingDampingPerUnitX/Y/Z(index, value)
        """
        try:
            for direction, indices in parameter_matrix.iteritems():
                for str_index, value in indices.iteritems():
                    index = int(str_index)
                    method_name = ""
                    if is_stiffness:
                        if direction == 'X':
                            method_name = 'SetBushingStiffnessPerUnitX'
                        elif direction == 'Y':
                            method_name = 'SetBushingStiffnessPerUnitY'
                        elif direction == 'Z':
                            method_name = 'SetBushingStiffnessPerUnitZ'
                    else:
                        if direction == 'X':
                            method_name = 'SetBushingDampingPerUnitX'
                        elif direction == 'Y':
                            method_name = 'SetBushingDampingPerUnitY'
                        elif direction == 'Z':
                            method_name = 'SetBushingDampingPerUnitZ'

                    if not method_name:
                        self.logger.warning("Invalid direction: %s", direction)
                        continue

                    valid_indices = []
                    if direction == 'X':
                        valid_indices = range(6) # 0 to 5 for X
                    elif direction == 'Y':
                        valid_indices = range(6) # 0 to 5 for Y
                    elif direction == 'Z':
                        valid_indices = range(6) # 0 to 5 for Z

                    if index not in valid_indices:
                        self.logger.warning("Invalid index %s for direction %s. Skipping.", index, direction)
                        continue

                    set_method = getattr(worksheet, method_name, None)
                    if set_method:
                        set_method(index, value)
                        param_type = "Stiffness" if is_stiffness else "Damping"
                        self.logger.debug("Set %s for %s direction at index %s to %s.",
                                          param_type, direction, index, value)
                    else:
                        self.logger.warning("Method %s not found on worksheet.", method_name)
        except Exception as e:
            param_type = "Stiffness" if is_stiffness else "Damping"
            self.logger.error("Error setting %s parameters: %s", param_type, str(e), exc_info=True)
    # ------------------ Bushing Section ------------------

class AnalysisManager:
    """
    Manages adding various constraints (displacement, bearing, forces) to the analysis.
    """
    def __init__(self, logger, ExtAPI, file_io):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model
        self.file_io = file_io
        
    # ------------------ Displacement Section ------------------
    def create_displacement_constraint(self, analysis, named_selections_dict, name, value_dict):
        """
        Create a displacement constraint with specified parameters.
        """
        try:
            self.logger.info("Creating displacement constraint for %s.", name)
            
            selection = self._create_selection(named_selections_dict, name)
            if not selection:
                return

            displacement = analysis.AddDisplacement()
            displacement.Location = selection

            for direction, value in value_dict.items():
                self._set_displacement_component(displacement, direction, value)

            self.logger.info("Displacement constraint for %s created successfully.", name)
        except Exception as e:
            self.logger.error("Error creating displacement constraint for %s: %s", name, str(e), exc_info=True)

    def _set_displacement_component(self, constraint, direction, value):
        """
        Helper method to set displacement component values for a constraint.
        """
        try:
            component_map = {
                "X": constraint.XComponent,
                "Y": constraint.YComponent,
                "Z": constraint.ZComponent
            }
            if direction in component_map:
                component = component_map[direction]
                if value == "Free":
                    component.Output.DefinitionType = VariableDefinitionType.Free
                elif isinstance(value, (int, float)):
                    unit = "m"
                    component.Output.DiscreteValues = [Quantity("{} [{}]".format(value, unit))]
                else:
                    self.logger.warning("Invalid value type for %s: %s", direction, value)
            else:
                self.logger.warning("Direction %s is not valid for displacement components.", direction)
        except Exception as e:
            self.logger.error("Error setting displacement component %s: %s", direction, str(e), exc_info=True)


    # ------------------ Displacement Section ------------------
    
    # ------------------ Remote Displacement Section ------------------
    def create_remote_displacement(self, analysis, named_selections_dict, name, value_dict):
        """
        Create a remote displacement with specified parameters.
        """
        try:
            self.logger.info("Creating remote displacement for %s.", name)
            
            # Get the selection based on the named selection dictionary
            selection = self._create_selection(named_selections_dict, name)
            if not selection:
                return

            # Create the remote displacement
            remote_disp = analysis.AddRemoteDisplacement()
            remote_disp.Location = selection

            # Set values for each direction
            for direction, value in value_dict.items():
                self._set_remote_displacement_component(remote_disp, direction, value)

            # Set behavior to rigid
            remote_disp.Behavior = LoadBehavior.Rigid
            self.logger.info("Remote displacement for %s created successfully.", name)
        except Exception as e:
            self.logger.error("Error creating remote displacement for %s: %s", name, str(e), exc_info=True)

    def _set_remote_displacement_component(self, constraint, direction, value):
        """
        Helper method to set displacement component values for a constraint.
        """
        try:
            component_map = {
                "X": constraint.XComponent,
                "Y": constraint.YComponent,
                "Z": constraint.ZComponent,
                "XRot": constraint.RotationX,
                "YRot": constraint.RotationY,
                "ZRot": constraint.RotationZ
            }
            if direction in component_map:
                component = component_map[direction]
                if value == "Free":
                    component.Output.DefinitionType = VariableDefinitionType.Free
                elif isinstance(value, (int, float)):
                    # Use the correct units based on the direction
                    unit = "rad" if "Rot" in direction else "m"
                    component.Output.DiscreteValues = [Quantity("{} [{}]".format(value, unit))]
                else:
                    self.logger.warning("Invalid value type for %s: %s", direction, value)
            else:
                self.logger.warning("Direction %s is not valid for remote displacement components.", direction)
        except Exception as e:
            self.logger.error("Error setting remote displacement component %s: %s", direction, str(e), exc_info=True)
    # ------------------ Remote Displacement Section ------------------


    def _create_selection(self, named_selections_dict, name):
        """
        Helper method to create a selection based on the named selection dictionary.
        """
        try:
            selection = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
            selection.Ids = named_selections_dict.get(name, [])
            if not selection.Ids:
                self.logger.warning("No IDs found for named selection: %s", name)
            return selection
        except Exception as e:
            self.logger.error("Error creating selection for %s: %s", name, str(e), exc_info=True)
            return None

    # ------------------ Analysis Settings Section ------------------
    def configure_analysis_settings(self, analysis, **settings):
        """
        Configure analysis settings with flexible flags and parameters.
        """
        try:
            analysis.AnalysisSettings.Activate()
            analysis.AnalysisSettings.MaximumModesToFind = settings.get("MaximumModesToFind", 100)
            analysis.AnalysisSettings.Damped = False
            analysis.AnalysisSettings.LimitSearchToRange = settings.get("LimitSearchToRange", False)
            if analysis.AnalysisSettings.LimitSearchToRange:
                analysis.AnalysisSettings.SearchRangeMinimum = Quantity(settings.get("MinimumFrequency", 0), "Hz")
                analysis.AnalysisSettings.SearchRangeMaximum = Quantity(settings.get("MaximumFrequency", 0), "Hz")
            self.logger.info(
                "Analysis settings configured: MaximumModesToFind=%s",
                settings.get("MaximumModesToFind"),
            )
        except Exception as e:
            self.logger.error("Error configuring analysis settings: %s", str(e), exc_info=True)
    # ------------------ Analysis Settings Section ------------------
class MainApp:
    """
    Main application class coordinating all operations.
    """
    def __init__(self, ExtAPI):
        self.ExtAPI = ExtAPI
        self.Model=ExtAPI.DataModel.Project.Model
        try:
            target_directory = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
            self.logger = LoggerManager(target_directory).get_logger()
            self.logger.info("Logger initialized successfully.")
        except Exception as e:
            print("Failed to initialize logger: {}".format(e))
            raise
        
        self.analysis = None
        for analysis in ExtAPI.DataModel.Project.Model.Analyses:
            if analysis.AnalysisType == AnalysisType.Modal:
                self.analysis = analysis
                self.logger.info("Found Modal analysis: %s", analysis.Name)
                break
        if not self.analysis:
            self.analysis = self.Model.AddModalAnalysis()
            self.logger.info("Modal analysis created in the project.")
            

        self.file_io = FileIO(self.logger)
        self.geometry_helper = GeometryHelper(self.logger)
        self.analysis_manager = AnalysisManager(self.logger, ExtAPI, self.file_io)
        self.selection_mgr = SelectionManagerWrapper(self.logger, ExtAPI)
        self.connection_manager = ConnectionManager(self.logger, ExtAPI)
        self.mesh_mgr       = MeshManager(self.logger, ExtAPI)
        
        ### New Addition ###
        # Used to reference the current mesh size being processed during post-processing
        self.current_element_size = None
    
    def clear_previous_setup(self):
        """
        Systematically delete all old script-generated items before recreating all content.
        This ensures the repeatability of script execution.
        """
        self.logger.info("--- starting cleanup of previous settings ---")

        # 1. Clean up loads, constraints and conditions in the analysis environment
        #    This will delete all child objects such as Force, Displacement, Rotational Velocity, etc.
        items_to_delete = [item for item in self.analysis.Children]
        if items_to_delete:
            self.logger.info("Deleting %d old items from the analysis environment...", len(items_to_delete))
            for item in items_to_delete:
                try:
                    item.Delete()
                except Exception as e:
                    self.logger.warning("Error deleting item %s: %s", item.Name, e)

        # 3. Clean up connections in the model (Connections)
        #    This will delete all Bearings, Bushings, and their Connection Groups
        connection_items_to_delete = [conn for conn in self.Model.Connections.Children]
        if connection_items_to_delete:
            self.logger.info("Deleting %d old connection items...", len(connection_items_to_delete))
            for item in connection_items_to_delete:
                if item.Name == "Contacts":
                    print("baoliu Contacts")
                else:
                    item.Delete()

        self.logger.info("--- Cleanup of previous settings complete  ---")
    
    # Modify analysis settings to modal
    def run_analysis_setting(self):
        """
        Read configuration files like analysis_settings.json to set analysis parameters uniformly.
        """
        self.logger.info("Starting run_analysis_setting...")
        # 1) Read analysis configuration from external JSON
        ansys_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\analysis_modal_config_latest.json"
        analysis_params = {}
        
        if os.path.exists(ansys_result_path):
            import codecs
            try:
                with codecs.open(ansys_result_path, "r", "utf-8-sig") as f:
                    data = json.load(f)
                analysis_params = data.get("analysis_settings", {})
                self.logger.info("Loaded analysis settings from %s.", ansys_result_path)
            except Exception as e:
                self.logger.error("Error reading analysis config file %s: %s", ansys_result_path, str(e), exc_info=True)
        else:
            self.logger.warning("Analysis config file not found: %s. Using default parameters.", ansys_result_path)

        # 2) If some fields are not provided in the JSON file, use default values
        default_params = { "MaximumModesToFind": 12 }
        for key, default_val in default_params.items():
            if key not in analysis_params:
                analysis_params[key] = default_val
        
        # 3) Call configuration function
        try:
            self.analysis_manager.configure_analysis_settings(
                analysis=self.analysis,
                MaximumModesToFind=analysis_params.get("MaximumModesToFind"),
                LimitSearchToRange=analysis_params.get("LimitSearchToRange"),
                MinimumFrequency=analysis_params.get("MinimumFrequency"),
                MaximumFrequency=analysis_params.get("MaximumFrequency")
            )
            self.logger.info("Analysis settings configured successfully with parameters: %s", analysis_params)
        except Exception as e:
            self.logger.error("Error configuring analysis settings: %s", str(e), exc_info=True)
    
        self.logger.info("run_analysis_setting completed.")

    # Constraint settings
    def run_constrain_setting(self):
        """
        Configure displacement constraints based on a JSON file.
        """
        try:
            named_selections_dict = self.selection_mgr.get_named_selections_dict()
            constrain_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\constrain_config_latest.json"

            with open(constrain_result_path, "r") as f:
                data = json.load(f)

            for constraint_name, value_dict in data.items():
                if isinstance(value_dict, dict):
                    if value_dict.get("type") == "remote":
                        self.analysis_manager.create_remote_displacement(
                            self.analysis, named_selections_dict, constraint_name, value_dict.get("values", {})
                        )
                    elif value_dict.get("type") == "displacement":
                        self.analysis_manager.create_displacement_constraint(
                            self.analysis, named_selections_dict, constraint_name, value_dict.get("values", {})
                        )
                else:
                    self.logger.info("Skipping non-dictionary item: {} = {}".format(constraint_name, value_dict))
            self.logger.info("All constraints configured successfully.")
        except Exception as e:
            self.logger.error("Error in run_constrain_setting: %s", str(e), exc_info=True)
    
    # Connection settings
    def run_connection_setting(self):
        """
        Read external JSON to create bearing and bushing connections in batch.
        """
        try:
            named_selections_dict = self.selection_mgr.get_named_selections_dict()
            connection_result_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\connection_config_latest.json"
            
            bearing_list = self.connection_manager.load_bearing_config(connection_result_path)
            self.connection_manager.create_bearing_in_batch(named_selections_dict, bearing_list)

            bushing_list = self.connection_manager.load_bushing_config(connection_result_path)
            self.connection_manager.create_bushing_joints_in_batch(named_selections_dict, bushing_list)

            self.logger.info("All bearing and bushing connections created successfully.")
        except Exception as e:
            self.logger.error("Error in run_connection_setting: %s", str(e), exc_info=True)

    ### Modification Point ###
    def run_result_post(self, output_dir):
        import os, re, csv, json, codecs, glob

        try:
            analysis = self.analysis
            solver_dir = analysis.WorkingDir
            mesh = self.Model.Mesh
            n_ele = mesh.Elements
            n_node = mesh.Nodes
            if not solver_dir or not os.path.isdir(solver_dir):
                self.logger.error("run_result_post: Unable to locate solver directory: %s", solver_dir)
                return

            out_files = glob.glob(os.path.join(solver_dir, "*.out"))
            if not out_files:
                self.logger.error("run_result_post: No .out files found in %s.", solver_dir)
                return
            out_file = max(out_files, key=os.path.getmtime)
            self.logger.info("run_result_post: Parsing %s", out_file)

            text = ""
            for enc in ("utf-8", "utf-8-sig", "mbcs", "latin-1"):
                try:
                    with codecs.open(out_file, "r", enc, "ignore") as fin:
                        text = fin.read()
                    break
                except Exception:
                    continue

            block_re = re.compile(r"\*{5}\s+MODAL\s+MASSES.*?SUMMARY\s+\*{5}(.*?)(?:\n\s*\*{5}|\Z)", re.IGNORECASE | re.S)
            m = block_re.search(text)
            if not m:
                self.logger.error("run_result_post: Modal summary table not found in output file.")
                return
            block = m.group(1)

            freqs, rows = [], []
            for ln in block.splitlines():
                parts = ln.strip().split()
                if not parts or not parts[0].isdigit() or len(parts) < 2:
                    continue
                try:
                    freqs.append(float(parts[1]))
                    rows.append((int(parts[0]), float(parts[1])))
                except ValueError:
                    continue

            if not freqs:
                self.logger.warning("run_result_post: Data block found but frequency parsing failed.")
                return
            
            # --- Key modification: Save mesh size and frequencies together ---
            output_data = {
                "element_size_m": self.current_element_size,
                "frequencies_Hz": freqs,
                "element_count": n_ele,
                "node_count": n_node,
                "calculation_time_s": self.last_calculation_time
            }
            
            # --- Key modification: Use element_size to generate unique file names ---
            base_name = "modal_freq_{}".format(self.current_element_size)
            json_path = os.path.join(output_dir, base_name + ".json")
            csv_path = os.path.join(output_dir, base_name + ".csv")

            with open(json_path, "w") as jf:
                json.dump(output_data, jf, indent=4)

            with open(csv_path, "wb") as cf: # for python 2 use 'wb'
                w = csv.writer(cf)
                w.writerow(["Mode", "Frequency_Hz"])
                w.writerows(rows)

            self.logger.info("run_result_post: Successfully extracted %d modal frequencies -> %s / %s", len(freqs), json_path, csv_path)

        except Exception as e:
            self.logger.error("run_result_post: %s", str(e), exc_info=True)

    ### Modification Point ###
    def run_mesh_setting(self, element_size, output_dir):
        self.logger.info("[Mesh] Starting mesh generation with size %.4f m ...", element_size)
        
        # Record current mesh size for use by subsequent methods (such as run_result_post)
        self.current_element_size = element_size

        self.mesh_mgr.store_all_bodies()
        self.mesh_mgr.generate_allbody_mesh(element_size)

        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Use element_size to generate unique file names
        png_path = os.path.join(output_dir, "mesh_{}.png".format(element_size))
        n_ele, n_node = self.mesh_mgr.export_mesh_info_png(png_path)
    
        json_path = os.path.join(output_dir, "mesh_info_{}.json".format(element_size))
        with open(json_path, "w") as fp:
            json.dump({"Elements": n_ele, "Nodes": n_node, "ElementSize": element_size}, fp, indent=4)
            
        self.logger.info("[Mesh] Completed, mesh info saved to %s", output_dir)

    ### Modification Point: This is the core batch processing logic ###
    def run(self):
        """
        Main controller for running simulations with multiple mesh sizes.
        """
        app.clear_previous_setup()
        # 1. --- Load configuration ---
        cfg_path = r"D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\json\mesh_config_latest.json"
        try:
            with open(cfg_path, "r") as f:
                config = json.load(f)
        except Exception as e:
            self.logger.error("Unable to load mesh config file %s: %s", cfg_path, e)
            return
        
        sizes_to_test = config.get("element_size", [])
        if not sizes_to_test:
            self.logger.error("'element_size' list not found in config file.")
            return
            
        mesh_output_dir = config.get("output_directory")
        self.logger.info("Will run calculations for the following mesh sizes: %s", sizes_to_test)
    
        # 2. --- Execute one-time setup ---
        # These settings remain unchanged between calculations of different sizes
        self.logger.info("--- Starting one-time setup (analysis, constraints, connections) ---")
        self.run_analysis_setting()
        self.run_constrain_setting()  # Uncomment if needed
        self.run_connection_setting() # Uncomment if needed
        self.logger.info("--- One-time setup completed ---")
    
        # 3. --- Iterate through each grid size and execute the ‘grid-solve-postprocess’ loop. ---
        for size in sizes_to_test:
            self.logger.info("==================================================")
            self.logger.info("--- Starting processing mesh size: %s ---", size)
            
            try:
                # a. Apply new mesh settings and save information
                self.run_mesh_setting(element_size=size, output_dir=mesh_output_dir)
                
                # b. Solve analysis
                self.logger.info("Starting solution...")
                
                solution = self.analysis.Solution
                t0 = time.time()
                solution.Solve() # Use True for synchronous solving, wait for completion
                self.last_calculation_time = time.time() - t0
                self.logger.info("Solution completed in %.2f s.", self.last_calculation_time)
                self.logger.info("Solution completed.")
                
                # c. Post-process the results of this run
                self.logger.info("Starting post-processing...")
                self.run_result_post(output_dir=mesh_output_dir)
                self.logger.info("Post-processing completed.")
            except Exception as e:
                self.logger.error("Critical error occurred while processing size %s: %s", size, e, exc_info=True)
                self.logger.info("Skipping current size, continuing to next.")
                continue # Continue to next loop
            
        self.logger.info("==================================================")
        self.logger.info("All mesh size calculations completed.")


# --- Script Entry Point ---
app = MainApp(ExtAPI)
ExtAPI.Application.ActiveUnitSystem = MechanicalUnitSystem.StandardNMM
ExtAPI.Application.ActiveAngularVelocityUnit=AngularVelocityUnitType.RPM
app.run()
