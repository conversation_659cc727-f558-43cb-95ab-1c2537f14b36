"""
直接修复matplotlib中文字体显示问题

通过清除字体缓存和强制设置字体来解决中文显示问题
"""

import os
import sys
import matplotlib
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm

def clear_matplotlib_cache():
    """清除matplotlib字体缓存"""
    print("🧹 清除matplotlib字体缓存...")
    
    try:
        # 获取matplotlib缓存目录
        cache_dir = matplotlib.get_cachedir()
        print(f"缓存目录: {cache_dir}")
        
        # 删除字体缓存文件
        cache_files = ['fontlist-v330.json', 'fontlist-v320.json', 'fontlist-v310.json']
        
        for cache_file in cache_files:
            cache_path = os.path.join(cache_dir, cache_file)
            if os.path.exists(cache_path):
                os.remove(cache_path)
                print(f"✅ 删除缓存文件: {cache_file}")
        
        # 重建字体缓存
        fm._rebuild()
        print("✅ 字体缓存已重建")
        
    except Exception as e:
        print(f"⚠️ 缓存清理失败: {str(e)}")

def force_chinese_font():
    """强制设置中文字体"""
    print("🔤 强制设置中文字体...")
    
    try:
        # 直接指定字体文件路径
        chinese_font_paths = [
            r"C:\Windows\Fonts\msyh.ttc",      # Microsoft YaHei
            r"C:\Windows\Fonts\simhei.ttf",    # SimHei
            r"C:\Windows\Fonts\simsun.ttc",    # SimSun
        ]
        
        working_font = None
        for font_path in chinese_font_paths:
            if os.path.exists(font_path):
                try:
                    # 添加字体到matplotlib
                    fm.fontManager.addfont(font_path)
                    
                    # 获取字体名称
                    font_prop = fm.FontProperties(fname=font_path)
                    font_name = font_prop.get_name()
                    
                    print(f"✅ 找到字体: {font_name} ({font_path})")
                    working_font = font_name
                    break
                    
                except Exception as e:
                    print(f"⚠️ 字体加载失败 {font_path}: {str(e)}")
                    continue
        
        if working_font:
            # 设置matplotlib使用中文字体
            plt.rcParams['font.sans-serif'] = [working_font, 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            print(f"✅ 字体设置成功: {working_font}")
            return working_font
        else:
            print("❌ 未找到可用的中文字体")
            return None
            
    except Exception as e:
        print(f"❌ 字体设置失败: {str(e)}")
        return None

def test_chinese_display():
    """测试中文显示效果"""
    print("🧪 测试中文显示效果...")
    
    try:
        # 设置matplotlib使用Agg后端
        matplotlib.use('Agg')
        
        # 创建测试图表
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 测试文本
        test_texts = [
            "模态分析结果图表",
            "频率对比分析",
            "网格收敛性研究",
            "振动模态分布"
        ]
        
        # 绘制测试文本
        for i, text in enumerate(test_texts):
            ax.text(0.1, 0.8 - i*0.15, text, fontsize=16, transform=ax.transAxes)
        
        ax.set_title("中文字体显示测试", fontsize=20)
        ax.set_xlabel("横轴标签 (Hz)", fontsize=14)
        ax.set_ylabel("纵轴标签 (mm)", fontsize=14)
        
        # 添加一些数据
        x = [1, 2, 3, 4, 5]
        y = [10, 25, 30, 35, 20]
        ax.plot(x, y, 'o-', label="测试数据")
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 保存图表
        plt.tight_layout()
        plt.savefig("chinese_font_test_direct.png", dpi=150, bbox_inches='tight')
        plt.close()
        
        # 检查文件是否生成
        if os.path.exists("chinese_font_test_direct.png"):
            file_size = os.path.getsize("chinese_font_test_direct.png")
            print(f"✅ 测试图表已生成: chinese_font_test_direct.png ({file_size} 字节)")
            return True
        else:
            print("❌ 测试图表生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def update_chart_widget():
    """更新图表组件的字体配置"""
    print("🔧 更新图表组件字体配置...")
    
    try:
        # 读取当前的图表组件文件
        widget_file = "ui/components/modal_chart_widget.py"
        
        if not os.path.exists(widget_file):
            print(f"❌ 文件不存在: {widget_file}")
            return False
        
        with open(widget_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 创建新的字体配置代码
        new_font_config = '''    def _setup_chinese_font(self):
        """设置中文字体支持"""
        try:
            # 强制清除字体缓存并重新加载
            import matplotlib.font_manager as fm
            
            # 直接指定中文字体文件路径
            chinese_font_paths = [
                r"C:\\Windows\\Fonts\\msyh.ttc",      # Microsoft YaHei
                r"C:\\Windows\\Fonts\\simhei.ttf",    # SimHei
                r"C:\\Windows\\Fonts\\simsun.ttc",    # SimSun
            ]
            
            working_font = None
            for font_path in chinese_font_paths:
                if os.path.exists(font_path):
                    try:
                        # 添加字体到matplotlib
                        fm.fontManager.addfont(font_path)
                        
                        # 获取字体名称
                        font_prop = fm.FontProperties(fname=font_path)
                        font_name = font_prop.get_name()
                        
                        working_font = font_name
                        logger.info(f"成功加载中文字体: {font_name}")
                        break
                        
                    except Exception:
                        continue
            
            if working_font:
                # 强制设置字体
                plt.rcParams['font.sans-serif'] = [working_font, 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                
                # 设置图表样式
                plt.style.use('default')
                
                logger.info(f"中文字体配置成功: {working_font}")
            else:
                # 降级到英文字体
                plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
                logger.warning("未找到中文字体，使用英文字体")
                
        except Exception as e:
            logger.error(f"字体设置失败: {str(e)}")
            # 最基本的降级配置
            try:
                plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass'''
        
        # 查找并替换字体配置函数
        import re
        pattern = r'def _setup_chinese_font\(self\):.*?(?=\n    def|\n\nclass|\nclass|\Z)'
        
        if re.search(pattern, content, re.DOTALL):
            new_content = re.sub(pattern, new_font_config, content, flags=re.DOTALL)
            
            # 写回文件
            with open(widget_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ 图表组件字体配置已更新")
            return True
        else:
            print("❌ 未找到字体配置函数")
            return False
            
    except Exception as e:
        print(f"❌ 更新失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 matplotlib中文字体直接修复工具")
    print("=" * 60)
    
    # 1. 清除字体缓存
    clear_matplotlib_cache()
    
    # 2. 强制设置中文字体
    font_name = force_chinese_font()
    
    # 3. 测试中文显示
    test_ok = test_chinese_display()
    
    # 4. 更新图表组件
    update_ok = update_chart_widget()
    
    print("\n" + "=" * 60)
    print("📋 修复结果:")
    print(f"字体设置: {'✅ ' + font_name if font_name else '❌ 失败'}")
    print(f"显示测试: {'✅ 通过' if test_ok else '❌ 失败'}")
    print(f"组件更新: {'✅ 完成' if update_ok else '❌ 失败'}")
    
    if font_name and test_ok and update_ok:
        print("\n🎉 中文字体修复完成！")
        print("✅ 图表现在应该能正确显示中文")
        print("✅ 请重新运行测试验证效果")
    else:
        print("\n⚠️ 修复过程中遇到问题")
        print("建议手动安装Microsoft YaHei字体或使用英文标签")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
