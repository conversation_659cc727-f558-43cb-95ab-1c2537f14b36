#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模态分析配置生成函数测试脚本

此脚本用于验证 ctrl/modal_slot.py 中的配置生成函数是否能够：
1. 正确生成JSON配置文件
2. 与 originscript/modal.py 兼容
3. 处理各种边界条件和异常情况

作者: 振动传递计算软件开发团队
日期: 2025-01-29
"""

import os
import json
import tempfile
import logging
from typing import Dict, Any
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class MockMeshParameter:
    """模拟网格参数对象"""
    name: str
    size: float  # 毫米


def test_generate_analysis_config():
    """测试分析配置生成函数"""
    print("\n" + "="*60)
    print("测试 _generate_analysis_config() 函数")
    print("="*60)
    
    # 导入函数
    import sys
    sys.path.append('.')
    from ctrl.modal_slot import _generate_analysis_config
    
    # 测试用例1: 正常参数
    print("\n1. 测试正常参数...")
    calc_params = {
        'modal_count': 12,
        'limit_freq': True,
        'freq_min': 10.0,
        'freq_max': 500.0
    }
    
    try:
        _generate_analysis_config(calc_params)
        
        # 验证生成的文件
        config_path = r"D:/data/all-XM/autoworkbench/csdaima/analysis_config_latest.json"
        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            print(f"✅ 配置文件生成成功: {config_path}")
            print(f"   配置内容: {json.dumps(config, indent=2, ensure_ascii=False)}")
            
            # 验证字段
            analysis_settings = config.get("analysis_settings", {})
            assert analysis_settings.get("MaximumModesToFind") == 12
            assert analysis_settings.get("LimitSearchToRange") == True
            assert analysis_settings.get("MinimumFrequency") == 10.0
            assert analysis_settings.get("MaximumFrequency") == 500.0
            print("✅ 字段验证通过")
            
        else:
            print("❌ 配置文件未生成")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    # 测试用例2: 默认参数
    print("\n2. 测试默认参数...")
    calc_params = {}
    
    try:
        _generate_analysis_config(calc_params)
        print("✅ 默认参数测试通过")
    except Exception as e:
        print(f"❌ 默认参数测试失败: {str(e)}")
    
    # 测试用例3: 异常参数
    print("\n3. 测试异常参数...")
    calc_params = {
        'modal_count': -1,  # 无效值
        'freq_min': 100.0,
        'freq_max': 50.0    # 最大频率小于最小频率
    }
    
    try:
        _generate_analysis_config(calc_params)
        print("❌ 异常参数应该抛出异常")
    except ValueError as e:
        print(f"✅ 正确捕获异常: {str(e)}")
    except Exception as e:
        print(f"❌ 意外异常: {str(e)}")


def test_generate_modal_config():
    """测试网格配置生成函数"""
    print("\n" + "="*60)
    print("测试 _generate_modal_config() 函数")
    print("="*60)
    
    # 导入函数
    import sys
    sys.path.append('.')
    from ctrl.modal_slot import _generate_modal_config
    
    # 测试用例1: 正常参数
    print("\n1. 测试正常参数...")
    mesh_param = MockMeshParameter(name="mesh_2mm", size=2.0)
    calc_params = {
        'modal_count': 12,
        'limit_freq': False,
        'freq_min': 0.0,
        'freq_max': 1000.0
    }
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            result = _generate_modal_config(mesh_param, calc_params, temp_dir)
            
            print(f"✅ 函数执行成功")
            print(f"   返回值: {result}")
            
            # 验证返回值
            assert 'config_path' in result
            assert 'output_directory' in result
            assert 'element_size' in result
            assert result['element_size'] == 0.002  # 2mm = 0.002m
            print("✅ 返回值验证通过")
            
            # 验证生成的文件
            config_path = result['config_path']
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                
                print(f"✅ 配置文件生成成功: {config_path}")
                print(f"   配置内容: {json.dumps(config, indent=2, ensure_ascii=False)}")
                
                # 验证字段
                assert config.get("element_size") == [0.002]
                assert "output_directory" in config
                print("✅ 字段验证通过")
                
            else:
                print("❌ 配置文件未生成")
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    # 测试用例2: 异常参数
    print("\n2. 测试异常参数...")
    mesh_param = MockMeshParameter(name="invalid_mesh", size=-1.0)  # 无效尺寸
    
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            result = _generate_modal_config(mesh_param, calc_params, temp_dir)
            print("❌ 异常参数应该抛出异常")
        except ValueError as e:
            print(f"✅ 正确捕获异常: {str(e)}")
        except Exception as e:
            print(f"❌ 意外异常: {str(e)}")


def test_originscript_compatibility():
    """测试与 originscript/modal.py 的兼容性"""
    print("\n" + "="*60)
    print("测试与 originscript/modal.py 的兼容性")
    print("="*60)
    
    # 检查生成的配置文件是否能被正确读取
    config_path = r"D:/data/all-XM/autoworkbench/csdaima/analysis_config_latest.json"
    mesh_config_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
    
    print("\n1. 检查分析配置文件...")
    if os.path.exists(config_path):
        try:
            # 模拟 originscript/modal.py 的读取方式
            import codecs
            with codecs.open(config_path, "r", "utf-8-sig") as f:
                data = json.load(f)
            analysis_params = data.get("analysis_settings", {})
            
            print(f"✅ 分析配置文件读取成功")
            print(f"   读取到的参数: {analysis_params}")
            
            # 验证必需字段
            required_fields = ["MaximumModesToFind", "LimitSearchToRange", "MinimumFrequency", "MaximumFrequency"]
            for field in required_fields:
                if field in analysis_params:
                    print(f"   ✅ {field}: {analysis_params[field]}")
                else:
                    print(f"   ❌ 缺少字段: {field}")
                    
        except Exception as e:
            print(f"❌ 分析配置文件读取失败: {str(e)}")
    else:
        print(f"❌ 分析配置文件不存在: {config_path}")
    
    print("\n2. 检查网格配置文件...")
    if os.path.exists(mesh_config_path):
        try:
            with open(mesh_config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            print(f"✅ 网格配置文件读取成功")
            print(f"   配置内容: {config}")
            
            # 验证必需字段
            if "element_size" in config and isinstance(config["element_size"], list):
                print(f"   ✅ element_size: {config['element_size']} (数组格式)")
            else:
                print(f"   ❌ element_size 字段格式错误")
                
            if "output_directory" in config:
                print(f"   ✅ output_directory: {config['output_directory']}")
            else:
                print(f"   ❌ 缺少 output_directory 字段")
                
        except Exception as e:
            print(f"❌ 网格配置文件读取失败: {str(e)}")
    else:
        print(f"❌ 网格配置文件不存在: {mesh_config_path}")


def main():
    """主测试函数"""
    print("模态分析配置生成函数测试")
    print("="*60)
    
    # 确保输出目录存在
    output_dir = r"D:/data/all-XM/autoworkbench/csdaima"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 运行测试
        test_generate_analysis_config()
        test_generate_modal_config()
        test_originscript_compatibility()
        
        print("\n" + "="*60)
        print("测试完成")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
