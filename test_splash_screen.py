"""
启动画面测试脚本

此脚本用于测试启动画面的各种功能，包括：
1. 基本显示功能
2. 进度更新
3. 动画效果
4. 配置加载
5. 高DPI支持

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import time
import json
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import QTimer, Qt
from PySide6.QtGui import QFont

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.splash_screen import CustomSplashScreen, SplashScreenManager, get_splash_manager


class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("启动画面测试 - 主窗口")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标题
        title_label = QLabel("启动画面测试完成！")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 添加说明
        info_label = QLabel("如果您看到了这个窗口，说明启动画面功能正常工作。")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("重新测试启动画面")
        test_button.clicked.connect(self.test_splash_again)
        layout.addWidget(test_button)
        
        # 添加配置测试按钮
        config_button = QPushButton("测试自定义配置")
        config_button.clicked.connect(self.test_custom_config)
        layout.addWidget(config_button)
    
    def test_splash_again(self):
        """重新测试启动画面"""
        self.hide()
        test_splash_screen()
        self.show()
    
    def test_custom_config(self):
        """测试自定义配置"""
        self.hide()
        test_custom_config_splash()
        self.show()


def test_splash_screen():
    """测试基本启动画面功能"""
    print("开始测试基本启动画面功能...")
    
    # 创建启动画面管理器
    splash_manager = get_splash_manager()
    
    # 显示启动画面
    splash = splash_manager.show_splash()
    
    # 模拟初始化过程
    steps = [
        (10, "初始化系统..."),
        (20, "加载配置文件..."),
        (30, "连接数据库..."),
        (40, "加载用户界面..."),
        (50, "初始化插件..."),
        (60, "加载资源文件..."),
        (70, "配置网络连接..."),
        (80, "启动服务..."),
        (90, "完成初始化..."),
        (100, "启动完成！")
    ]
    
    for progress, status in steps:
        splash_manager.update_progress_by_percentage(progress, status)
        time.sleep(0.5)  # 模拟耗时操作
        QApplication.processEvents()
    
    # 等待一下再隐藏
    time.sleep(1)
    splash_manager.hide_splash()
    
    print("基本启动画面测试完成")


def test_custom_config_splash():
    """测试自定义配置的启动画面"""
    print("开始测试自定义配置启动画面...")
    
    # 创建自定义配置
    custom_config = {
        "enabled": True,
        "show_fade_in": True,
        "fade_in_duration": 800,
        "fade_out_duration": 500,
        "minimum_display_time": 3000,
        "show_rotation_animation": True,
        "colors": {
            "primary": "#e74c3c",  # 红色主题
            "secondary": "#f39c12",  # 橙色
            "text": "#2c3e50",
            "progress_background": "#ecf0f1"
        },
        "fonts": {
            "title_size": 18,
            "version_size": 12,
            "status_size": 10,
            "font_family": "Microsoft YaHei"
        },
        "layout": {
            "width": 520,
            "height": 360
        }
    }
    
    # 创建自定义启动画面管理器
    splash_manager = SplashScreenManager(custom_config)
    
    # 显示启动画面
    splash = splash_manager.show_splash()
    
    # 模拟不同的初始化过程
    steps = [
        (15, "正在加载自定义主题..."),
        (30, "正在初始化红色主题..."),
        (45, "正在配置用户界面..."),
        (60, "正在加载高级功能..."),
        (75, "正在优化性能..."),
        (90, "正在完成配置..."),
        (100, "自定义主题启动完成！")
    ]
    
    for progress, status in steps:
        splash_manager.update_progress_by_percentage(progress, status)
        time.sleep(0.7)  # 稍微慢一点
        QApplication.processEvents()
    
    # 等待一下再隐藏
    time.sleep(1.5)
    splash_manager.hide_splash()
    
    print("自定义配置启动画面测试完成")


def test_high_dpi_support():
    """测试高DPI支持"""
    print("测试高DPI支持...")
    
    # 设置高DPI支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 创建启动画面
    splash = CustomSplashScreen()
    splash.show()
    
    # 简单测试
    splash.update_progress(50, "测试高DPI显示...")
    time.sleep(2)
    
    splash.hide()
    print("高DPI支持测试完成")


def test_error_handling():
    """测试错误处理"""
    print("测试错误处理...")
    
    try:
        # 测试无效配置
        invalid_config = {
            "colors": {
                "primary": "invalid_color"
            }
        }
        
        splash = CustomSplashScreen(config=invalid_config)
        splash.show()
        splash.update_progress(50, "测试错误处理...")
        time.sleep(1)
        splash.hide()
        
        print("错误处理测试通过")
    except Exception as e:
        print(f"错误处理测试失败: {e}")


def main():
    """主测试函数"""
    print("=" * 50)
    print("启动画面功能测试")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序图标
    icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
    if os.path.exists(icon_path):
        from PySide6.QtGui import QIcon
        app.setWindowIcon(QIcon(icon_path))
    
    # 运行各种测试
    print("\n1. 测试基本启动画面功能")
    test_splash_screen()
    
    print("\n2. 测试自定义配置")
    test_custom_config_splash()
    
    print("\n3. 测试高DPI支持")
    test_high_dpi_support()
    
    print("\n4. 测试错误处理")
    test_error_handling()
    
    # 显示主窗口
    print("\n5. 显示测试主窗口")
    main_window = TestMainWindow()
    main_window.show()
    
    print("\n所有测试完成！主窗口已显示。")
    print("您可以在主窗口中进行更多测试。")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
