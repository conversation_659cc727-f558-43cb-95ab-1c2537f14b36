"""
结果显示界面控制器

此模块负责处理结果显示界面的所有操作，主要包括：
1. 界面跳转控制
2. 结果数据加载
3. 结果可视化
4. 数据导出功能

工作流程：
1. 初始化结果显示界面
2. 加载计算结果
3. 显示结果图表
4. 界面跳转管理

作者: [作者名]
日期: [日期]
"""

import os
import shutil
import subprocess
from typing import Optional, List
from dataclasses import dataclass
from datetime import datetime
from PySide6.QtWidgets import QWidget, QMessageBox, QPushButton
from PySide6.QtGui import QPixmap
from PySide6.QtCore import QSize
from window_manager import WindowManager, WindowType
from resource_manager import ResourceManager
from error_handler import (
    ErrorHandler, AppError, AnsysError, ValidationError,
    FileOperationError, ConfigurationError, ErrorSeverity
)
from ctrl.vibration_analysis import show_vibration_analysis
from core.workflow_state import get_workflow_state, WorkflowStep
import json
import glob


def cleanup_old_monitor_files(directory: str, max_files: int = 3) -> None:
    """清理目录中的旧监控点文件，只保留最新的几个文件

    Args:
        directory: 目录路径
        max_files: 保留的最大文件数量
    """
    try:
        # 查找所有监控点文件
        pattern = os.path.join(directory, "monitor_points_*.json")
        files = glob.glob(pattern)

        if len(files) <= max_files:
            return

        # 按修改时间排序，最新的在前
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        # 删除超出数量限制的旧文件
        files_to_delete = files[max_files:]
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
                print(f"已删除旧监控点文件: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"警告: 无法删除文件 {file_path}: {str(e)}")

        print(f"监控点文件清理完成，保留最新 {max_files} 个文件")

    except Exception as e:
        print(f"清理监控点文件时出错: {str(e)}")


def generate_monitor_points_file(resource_manager: ResourceManager,
                                constrain_config: dict,
                                output_dir: str) -> Optional[str]:
    """从约束配置中生成单独的监控点JSON文件

    Args:
        resource_manager: 资源管理器实例
        constrain_config: 约束配置字典
        output_dir: 输出目录

    Returns:
        Optional[str]: 生成的监控点文件路径，失败时返回None
    """
    try:
        # 提取监控点数据
        monitor_points = constrain_config.get("monitor_points", [])
        monitor_points_coordinates = constrain_config.get("monitor_points_coordinates", [])
        monitor_points_count = constrain_config.get("monitor_points_count", 0)
        monitor_points_source = constrain_config.get("monitor_points_source", "unknown")

        # 构建监控点JSON数据
        monitor_data = {}

        if monitor_points:
            # 新格式：使用详细的监控点信息
            monitor_data = {
                "monitor_points": monitor_points,
                "monitor_points_count": monitor_points_count,
                "monitor_points_source": monitor_points_source,
                "generated_from": "constrain_config",
                "generated_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            print(f"使用新格式监控点数据，共 {len(monitor_points)} 个点")

        elif monitor_points_coordinates:
            # 兼容格式：转换坐标列表为详细格式
            converted_points = []
            for i, coords in enumerate(monitor_points_coordinates):
                if isinstance(coords, list) and len(coords) == 3:
                    point = {
                        "name": f"Legacy_Point_{i+1}",
                        "coordinates": coords,
                        "id": i + 1,
                        "created_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "source": "legacy_coordinates"
                    }
                    converted_points.append(point)

            monitor_data = {
                "monitor_points": converted_points,
                "monitor_points_count": len(converted_points),
                "monitor_points_source": "legacy_coordinates",
                "generated_from": "constrain_config_coordinates",
                "generated_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            print(f"转换旧格式坐标数据，共 {len(converted_points)} 个点")

        else:
            print("警告: 未找到有效的监控点数据")
            return None

        # 清理输出目录中的旧监控点文件
        try:
            cleanup_old_monitor_files(output_dir, max_files=3)
        except Exception as e:
            print(f"警告: 清理旧监控点文件失败: {str(e)}")

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"monitor_points_{timestamp}.json"
        file_path = os.path.join(output_dir, filename)

        # 写入文件
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(monitor_data, f, indent=4, ensure_ascii=False)

        print(f"监控点文件生成成功: {file_path}")
        print(f"包含 {monitor_data['monitor_points_count']} 个监控点")

        return file_path

    except Exception as e:
        print(f"生成监控点文件失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


@dataclass
class ResultInfo:
    """结果信息类"""
    output_dir: str
    timestamp: datetime
    config_files: List[str]


class ResultTracker:
    """结果跟踪器类
    
    用于跟踪和管理计算结果信息。
    实现了单例模式，确保全局只有一个实例。
    """
    
    _instance = None
    _latest_result: Optional[ResultInfo] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ResultTracker, cls).__new__(cls)
        return cls._instance
    
    @property
    def latest_result(self) -> Optional[ResultInfo]:
        """获取最新的结果信息"""
        return self._latest_result
    
    def update_result(self, result: ResultInfo) -> None:
        """更新最新的结果信息
        
        Args:
            result: 新的结果信息
        """
        self._latest_result = result
    
    def clear_result(self) -> None:
        """清除结果信息"""
        self._latest_result = None


# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口


def cleanup_monitor_points_files(resource_manager: ResourceManager,
                               error_handler: ErrorHandler,
                               result_window: Optional[QWidget] = None,
                               exclude_paths: Optional[list[str]] = None) -> None:
    """专门清理监控点文件

    Args:
        resource_manager: 资源管理器实例
        error_handler: 错误处理器实例
        result_window: 结果窗口实例，用于显示错误消息
        exclude_paths: 不清理的文件路径列表
    """
    try:
        # 清理监控点文件（保留时间较长，因为可能需要重复使用）
        resource_manager.clean_temp_files(
            max_age_hours=6,  # 监控点文件保留6小时
            prefix="monitor_points_",
            suffix=".json",
            exclude_paths=exclude_paths
        )

        # 清理JSON目录中的旧监控点配置文件
        if hasattr(resource_manager, 'json_dir') and resource_manager.json_dir:
            cleanup_old_monitor_files(resource_manager.json_dir, max_files=5)

    except Exception as e:
        if result_window:
            error_handler.handle_exception(
                e, result_window, show_message=False
            )


def cleanup_temp_files(resource_manager: ResourceManager,
                      error_handler: ErrorHandler,
                      result_window: Optional[QWidget] = None,
                      exclude_paths: Optional[list[str]] = None) -> None:
    """清理临时文件

    Args:
        resource_manager: 资源管理器实例
        error_handler: 错误处理器实例
        result_window: 结果窗口实例，用于显示错误消息
        exclude_paths: 不清理的文件路径列表
    """
    try:
        # 1. 清理临时配置文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="result_config_",
            exclude_paths=exclude_paths
        )

        # 2. 清理临时脚本文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="final_wb_",
            exclude_paths=exclude_paths
        )

        # 3. 清理临时批处理文件
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="final_",
            suffix=".bat",
            exclude_paths=exclude_paths
        )

        # 4. 清理临时输出目录
        resource_manager.clean_temp_files(
            max_age_hours=1,
            prefix="result_output_",
            exclude_paths=exclude_paths
        )

        # 5. 清理监控点文件
        cleanup_monitor_points_files(
            resource_manager, error_handler, result_window, exclude_paths
        )

    except Exception as e:
        if result_window:
            error_handler.handle_exception(
                e, result_window, show_message=False
            )


def start_calculation(window_manager: WindowManager) -> None:
    """开始计算
    
    此函数首先检查所有前置步骤是否完成，然后开始计算过程。
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 获取窗口实例和错误处理器
    result_window = window_manager.get_window(WindowType.RESULT)
    main_window = window_manager.get_window(WindowType.MAIN)
    error_handler = ErrorHandler()
    result_tracker = ResultTracker()
    
    if not result_window or not main_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 检查前置步骤是否完成 - 修改为基于文件存在性的检查
    mesh_config_path = os.path.join(main_window.ANSYS_Work_Dir, "temp", "mesh_config_last.json")
    mesh_completed = os.path.exists(mesh_config_path)

    # 如果mesh_config_last.json存在，则更新ProcessStatus中的mesh完成状态
    if mesh_completed:
        window_manager.process_status.set_completed(WindowType.MESH)

    if not window_manager.process_status.check_previous_completed(WindowType.RESULT):
        # 构建详细的错误信息
        error_details = {
            'mesh_completed': mesh_completed,
            'connection_completed': window_manager.process_status.is_completed(WindowType.CONNECTION),
            'analysis_completed': window_manager.process_status.is_completed(WindowType.ANALYSIS),
            'constrain_completed': window_manager.process_status.is_completed(WindowType.CONSTRAIN)
        }

        # 生成用户友好的错误消息
        missing_steps = []
        if not mesh_completed:
            missing_steps.append("网格无关性验证（请先选择计算结果）")
        if not error_details['connection_completed']:
            missing_steps.append("连接设置")
        if not error_details['analysis_completed']:
            missing_steps.append("分析设置")
        if not error_details['constrain_completed']:
            missing_steps.append("约束设置")

        error_message = "请先完成以下前置步骤：\n" + "\n".join(f"• {step}" for step in missing_steps)

        error_handler.handle_error(
            AppError(
                error_message,
                ErrorSeverity.WARNING,
                details=error_details
            ),
            result_window
        )
        return
    
    # 用于存储需要保护的文件路径
    active_files = []
    
    try:
        # 1. 初始化资源管理器
        resource_manager = ResourceManager()
        try:
            resource_manager.initialize(main_window.ANSYS_Work_Dir)
            
            # 首先清理旧的临时文件
            cleanup_temp_files(resource_manager, error_handler, result_window)
            
        except Exception as e:
            raise ConfigurationError(
                "初始化资源管理器失败",
                details={'work_dir': main_window.ANSYS_Work_Dir}
            ) from e
            
        # 2. 创建临时输出目录
        try:
            temp_dir_name = resource_manager.create_temp_file(prefix="result_output_")
            active_files.append(temp_dir_name)
            os.remove(temp_dir_name)  # 删除生成的文件
            temp_output_dir = temp_dir_name  # 使用这个名字作为目录名
            os.makedirs(temp_output_dir, exist_ok=True)
            
            # 创建新的结果跟踪记录
            result_tracker.update_result(ResultInfo(
                output_dir=temp_output_dir,
                timestamp=datetime.now(),
                config_files=[]
            ))
            
        except Exception as e:
            raise FileOperationError(
                "创建临时输出目录失败",
                details={'error': str(e)}
            ) from e
            
        # 3. 获取配置文件路径
        try:
            ansys_result_path = os.path.join(resource_manager.json_dir, "analysis_config_latest.json")
            constrain_result_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            connection_result_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            
            # 检查文件是否存在
            for path in [ansys_result_path, constrain_result_path, connection_result_path]:
                if not os.path.exists(path):
                    raise FileOperationError(
                        "配置文件不存在",
                        details={'missing_file': path}
                    )
                # 存在则读取，并显示在result_window.ui.calc_all_check中
                else:
                    with open(path, "r", encoding="utf-8") as f:
                        content = f.read()
                        result_window.ui.calc_all_check.setPlainText(content)
            
            # 从constrain配置文件中提取监控点数据并生成单独的监控点文件
            monitor_point_file = None
            import json
            try:
                with open(constrain_result_path, "r", encoding="utf-8") as f:
                    constrain_config = json.load(f)

                # 检查是否有监控点数据
                monitor_points = constrain_config.get("monitor_points", [])
                monitor_points_coordinates = constrain_config.get("monitor_points_coordinates", [])

                if monitor_points or monitor_points_coordinates:
                    # 生成单独的监控点JSON文件
                    monitor_point_file = generate_monitor_points_file(
                        resource_manager, constrain_config, temp_output_dir
                    )
                    if monitor_point_file:
                        active_files.append(monitor_point_file)
                        print(f"成功生成监控点文件: {monitor_point_file}")
                    else:
                        print("警告: 监控点文件生成失败")
                else:
                    print("警告: 约束配置文件中未找到监控点数据")

            except Exception as e:
                print(f"警告: 处理监控点数据失败: {str(e)}")
                # 继续执行，不阻止其他配置的处理

        except Exception as e:
            raise FileOperationError(
                "获取配置文件路径失败",
                details={'error': str(e)}
            ) from e
            
        # 4. 准备源脚本
        try:
            source_script_path = os.path.join(resource_manager.base_dir, "originscript", "finalscript.py")
            with open(source_script_path, "r", encoding="utf-8") as f:
                script_content = f.read()
                
        except Exception as e:
            raise FileOperationError(
                "读取源脚本文件失败",
                details={'source_file': source_script_path}
            ) from e
            
        # 5. 修改脚本内容
        target_dir = main_window.ANSYS_Work_Dir.replace("\\", "/")
        old_target_dir = r'target_directory = r"D:/data/all-XM/autoworkbench/csdaima"'
        new_target_dir = f'target_directory = r"{target_dir}"'
        script_content = script_content.replace(old_target_dir, new_target_dir)
        
        # 生成mesh_config_last.json配置文件
        from core.mesh_config_generator import get_dynamic_config_path, replace_hardcoded_paths
        mesh_config_path = get_dynamic_config_path(main_window.ANSYS_Work_Dir)

        # 替换配置文件路径
        replacements = {
            r'cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"':
                f'cfg_path=r"{mesh_config_path}"',
            r'ansys_result_path = r"C:/Users/<USER>/Desktop/analysis_config.json"':
                f'ansys_result_path = r"{ansys_result_path}"',
            r'constrain_result_path = r"C:/Users/<USER>/Desktop/2.json"':
                f'constrain_result_path = r"{constrain_result_path}"',
            r'connection_result_path = r"D:/data/all-XM/autoworkbench/csdaima/connection_result.json"':
                f'connection_result_path = r"{connection_result_path}"'
        }

        # 使用动态路径替换功能处理mesh_config路径 - 结果分析计算使用result配置
        script_content = replace_hardcoded_paths(script_content, main_window.ANSYS_Work_Dir, config_type="result")
        
        # 添加监测点文件路径替换
        if monitor_point_file:
            monitor_point_file = monitor_point_file.replace("\\", "/")
            replacements[r'monitor_config_path = r"C:/Users/<USER>/Desktop/monitor_points.json"'] = \
                f'monitor_config_path = r"{monitor_point_file}"'
        
        for old, new in replacements.items():
            script_content = script_content.replace(old, new)
            
        # 6. 创建新版本的脚本
        try:
            script_file = resource_manager.create_script_version("finalscript_copy.py", script_content)
            active_files.append(script_file)
            
        except Exception as e:
            raise FileOperationError(
                "创建脚本文件失败",
                details={'script_name': "finalscript_copy.py"}
            ) from e
            
        # 7. 清理旧版本
        resource_manager.clean_old_versions("finalscript_copy.py")
        
        # 8. 创建Workbench控制脚本
        wb_script_content = f'''
# encoding: utf-8
Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
'''
        
        # 9. 获取最新的脚本路径
        script_file = script_file.replace("\\", "/")
        
        # 10. 添加执行命令
        wb_script_content += f'''
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{script_file}")')
model1.Exit()
Save(Overwrite=True)
'''

        # 11. 创建Workbench脚本文件
        try:
            wb_script_file = resource_manager.create_temp_file(prefix="final_wb", suffix=".py")
            active_files.append(wb_script_file)
            with open(wb_script_file, "w", encoding="utf-8") as f:
                f.write(wb_script_content)
                
        except Exception as e:
            raise FileOperationError(
                "创建Workbench脚本文件失败",
                details={'script_file': wb_script_file}
            ) from e
            
        # 12. 创建批处理文件
        try:
            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            wb_script_file = wb_script_file.replace("\\", "/")
            bat_content = (
                f'"{ansys_path}" '
                f'-B -R "{wb_script_file}"'
            )
            bat_file = resource_manager.create_temp_file(prefix="final", suffix=".bat")
            active_files.append(bat_file)
            with open(bat_file, "w", encoding="utf-8") as f:
                f.write(bat_content)
                
        except Exception as e:
            raise FileOperationError(
                "创建批处理文件失败",
                details={'bat_file': bat_file}
            ) from e
            
        # 13. 执行批处理文件
        try:
            result = subprocess.run(
                f'"{bat_file}"',
                shell=True,
                check=True,
                text=True,
                capture_output=True
            )
            
            # 检查ANSYS输出中的错误信息
            if "error" in result.stdout.lower() or "error" in result.stderr.lower():
                raise AnsysError(
                    "ANSYS执行过程中出现错误",
                    details={
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
                )
                
            error_handler.handle_error(
                AppError(
                    "计算完成，可以查看结果",
                    ErrorSeverity.INFO
                ),
                result_window
            )

            # 设置完成状态
            window_manager.process_status.set_completed(WindowType.RESULT)

            # 标记工作流程步骤完成
            workflow_state = get_workflow_state()
            workflow_state.mark_step_completed(WorkflowStep.RESULT, {
                'timestamp': datetime.now().isoformat(),
                'output_dir': temp_output_dir,
                'calculation_completed': True
            })
            
        except subprocess.CalledProcessError as e:
            result_tracker.clear_result()  # 清除失败的结果记录
            raise AnsysError(
                "ANSYS执行失败",
                details={
                    'return_code': e.returncode,
                    'stdout': e.stdout,
                    'stderr': e.stderr
                }
            ) from e
            
    except AppError as e:
        # 处理应用程序异常
        result_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_error(e, result_window)
    except Exception as e:
        # 处理其他未预期的异常
        result_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_exception(e, result_window)
    finally:
        # 清理临时文件，但排除当前正在使用的文件
        cleanup_temp_files(
            resource_manager, 
            error_handler,
            result_window,
            exclude_paths=active_files
        )


def result_slot(window_manager: WindowManager) -> None:
    """初始化结果显示界面的所有槽函数连接
    
    此函数负责将结果显示界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮和结果显示功能按钮。
    
    Args:
        window_manager: 窗口管理器实例
    """
    result_window = window_manager.get_window(WindowType.RESULT)
    if not result_window:
        ErrorHandler().handle_error(
            AppError("无法获取结果显示窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step

    result_window.ui.push_mainui.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 计算结果的上一步应该是网格无关性验证
    result_window.ui.push_constrainui.clicked.connect(
        lambda: navigate_to_previous_step(window_manager, WindowType.RESULT))

    # 开始计算按钮
    result_window.ui.push_finish.clicked.connect(
        lambda: start_calculation(window_manager))

    # 计算结果的下一步应该是后处理（如果有相应按钮）
    if hasattr(result_window.ui, 'push_postui'):
        result_window.ui.push_postui.clicked.connect(
            lambda: navigate_to_next_step(window_manager, WindowType.RESULT))
    
    # 在这里可以添加其他按钮连接和初始化代码
    