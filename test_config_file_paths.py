#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件路径诊断工具
用于诊断和修复单模态计算中的配置文件路径问题

使用方法:
1. 在应用程序运行时，在Python控制台中导入此模块
2. 调用相应的诊断函数

示例:
    import test_config_file_paths
    test_config_file_paths.diagnose_config_paths()
"""

import os
import json
import glob
from datetime import datetime

def diagnose_config_paths():
    """诊断配置文件路径问题"""
    print("\n" + "="*80)
    print("🔍 配置文件路径诊断工具")
    print("="*80)
    
    # 1. 检查ResourceManager
    try:
        from resource_manager import ResourceManager
        resource_manager = ResourceManager()
        
        print("\n📁 ResourceManager路径信息:")
        print(f"  base_dir: {resource_manager.base_dir}")
        print(f"  work_dir: {resource_manager.work_dir}")
        print(f"  json_dir: {resource_manager.json_dir}")
        print(f"  temp_dir: {resource_manager.temp_dir}")
        print(f"  output_dir: {resource_manager.output_dir}")
        
        if hasattr(resource_manager, 'work_script_dir'):
            print(f"  work_script_dir: {resource_manager.work_script_dir}")
            
    except Exception as e:
        print(f"❌ ResourceManager初始化失败: {e}")
        return False
    
    # 2. 检查目录存在性
    print("\n📂 目录存在性检查:")
    directories = [
        ("基础目录", resource_manager.base_dir),
        ("工作目录", resource_manager.work_dir),
        ("JSON目录", resource_manager.json_dir),
        ("临时目录", resource_manager.temp_dir),
        ("输出目录", resource_manager.output_dir)
    ]
    
    for name, path in directories:
        exists = os.path.exists(path)
        readable = os.access(path, os.R_OK) if exists else False
        writable = os.access(path, os.W_OK) if exists else False
        
        status = "✅" if exists else "❌"
        print(f"  {status} {name}: {path}")
        if exists:
            print(f"    权限: 读取={readable}, 写入={writable}")
        else:
            print(f"    状态: 目录不存在")
    
    # 3. 搜索配置文件
    print("\n🔍 配置文件搜索:")
    
    # 定义要搜索的配置文件模式
    config_patterns = [
        ("analysis_config", "analysis_config_*.json"),
        ("mesh_config", "mesh_config_*.json"),
        ("constrain_config", "constrain_config_*.json"),
        ("connection_config", "connection_config_*.json")
    ]
    
    # 定义要搜索的目录
    search_dirs = [
        ("JSON目录", resource_manager.json_dir),
        ("工作目录", resource_manager.work_dir),
        ("工作目录/json", os.path.join(resource_manager.work_dir, "json")),
        ("当前目录", os.getcwd()),
        ("当前目录/json", os.path.join(os.getcwd(), "json"))
    ]
    
    all_found_files = {}
    
    for dir_name, search_dir in search_dirs:
        if not os.path.exists(search_dir):
            print(f"  ⚠️ {dir_name} 不存在: {search_dir}")
            continue
            
        print(f"\n  📁 搜索 {dir_name}: {search_dir}")
        
        for config_name, pattern in config_patterns:
            full_pattern = os.path.join(search_dir, pattern)
            files = glob.glob(full_pattern)
            
            if files:
                # 按修改时间排序，最新的在前
                files.sort(key=os.path.getmtime, reverse=True)
                print(f"    ✅ {config_name}: 找到 {len(files)} 个文件")
                
                for i, file_path in enumerate(files[:3]):  # 只显示最新的3个
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    size = os.path.getsize(file_path)
                    print(f"      {i+1}. {os.path.basename(file_path)} ({size} bytes, {mtime})")
                
                # 记录找到的文件
                if config_name not in all_found_files:
                    all_found_files[config_name] = []
                all_found_files[config_name].extend([(f, search_dir) for f in files])
            else:
                print(f"    ❌ {config_name}: 未找到文件")
    
    # 4. 检查latest文件
    print("\n🔗 Latest文件检查:")
    latest_files = [
        ("analysis_config_latest.json", "分析配置"),
        ("mesh_config_latest.json", "网格配置"),
        ("constrain_config_latest.json", "约束配置"),
        ("connection_config_latest.json", "连接配置")
    ]
    
    for filename, config_name in latest_files:
        found_latest = False
        for dir_name, search_dir in search_dirs:
            if not os.path.exists(search_dir):
                continue
                
            latest_path = os.path.join(search_dir, filename)
            if os.path.exists(latest_path):
                try:
                    # 检查文件内容
                    with open(latest_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if content.strip():
                            json.loads(content)  # 验证JSON格式
                            size = len(content)
                            mtime = datetime.fromtimestamp(os.path.getmtime(latest_path))
                            print(f"  ✅ {config_name}: {latest_path}")
                            print(f"    大小: {size} bytes, 修改时间: {mtime}")
                            found_latest = True
                        else:
                            print(f"  ⚠️ {config_name}: 文件为空 - {latest_path}")
                except json.JSONDecodeError as e:
                    print(f"  ❌ {config_name}: JSON格式错误 - {latest_path}")
                    print(f"    错误: {str(e)}")
                except Exception as e:
                    print(f"  ❌ {config_name}: 读取失败 - {latest_path}")
                    print(f"    错误: {str(e)}")
        
        if not found_latest:
            print(f"  ❌ {config_name}: 未找到latest文件")
    
    # 5. 生成修复建议
    print("\n💡 修复建议:")
    
    if not os.path.exists(resource_manager.json_dir):
        print("  1. 创建JSON目录:")
        print(f"     mkdir -p {resource_manager.json_dir}")
    
    # 检查是否有配置文件但在错误的位置
    work_json_dir = os.path.join(resource_manager.work_dir, "json")
    if os.path.exists(work_json_dir) and work_json_dir != resource_manager.json_dir:
        print("  2. 配置文件可能在工作目录中，考虑:")
        print(f"     - 将文件从 {work_json_dir} 复制到 {resource_manager.json_dir}")
        print(f"     - 或修改代码使用工作目录: {work_json_dir}")
    
    if all_found_files:
        print("  3. 找到的配置文件可以用于创建latest链接")
        for config_name, files in all_found_files.items():
            if files:
                latest_file = max(files, key=lambda x: os.path.getmtime(x[0]))
                print(f"     {config_name}: 使用 {latest_file[0]}")
    
    print("\n" + "="*80)
    print("🔍 诊断完成")
    print("="*80)
    
    return True

def create_missing_latest_links():
    """创建缺失的latest链接文件"""
    print("\n🔗 创建latest链接文件...")
    
    try:
        from resource_manager import ResourceManager
        resource_manager = ResourceManager()
        
        # 确保json目录存在
        os.makedirs(resource_manager.json_dir, exist_ok=True)
        
        config_patterns = [
            ("analysis_config", "analysis_config_*.json", "analysis_config_latest.json"),
            ("mesh_config", "mesh_config_*.json", "mesh_config_latest.json"),
            ("constrain_config", "constrain_config_*.json", "constrain_config_latest.json"),
            ("connection_config", "connection_config_*.json", "connection_config_latest.json")
        ]
        
        for config_name, pattern, latest_name in config_patterns:
            # 搜索最新的配置文件
            full_pattern = os.path.join(resource_manager.json_dir, pattern)
            files = glob.glob(full_pattern)
            
            if files:
                # 找到最新的文件
                latest_file = max(files, key=os.path.getmtime)
                latest_link = os.path.join(resource_manager.json_dir, latest_name)
                
                # 删除旧的链接
                if os.path.exists(latest_link):
                    os.remove(latest_link)
                
                # 创建新的链接
                try:
                    import shutil
                    shutil.copy2(latest_file, latest_link)
                    print(f"  ✅ 创建 {latest_name} -> {os.path.basename(latest_file)}")
                except Exception as e:
                    print(f"  ❌ 创建 {latest_name} 失败: {e}")
            else:
                print(f"  ⚠️ 未找到 {config_name} 文件，无法创建latest链接")
                
    except Exception as e:
        print(f"❌ 创建latest链接失败: {e}")

def fix_config_paths():
    """修复配置文件路径问题"""
    print("\n🛠️ 开始修复配置文件路径问题...")
    
    # 1. 诊断当前状态
    if not diagnose_config_paths():
        return False
    
    # 2. 创建缺失的latest链接
    create_missing_latest_links()
    
    # 3. 再次验证
    print("\n🔍 验证修复结果...")
    return diagnose_config_paths()

if __name__ == "__main__":
    print("这是一个配置文件路径诊断工具")
    print("使用方法:")
    print("  import test_config_file_paths")
    print("  test_config_file_paths.diagnose_config_paths()")
    print("  test_config_file_paths.fix_config_paths()")
