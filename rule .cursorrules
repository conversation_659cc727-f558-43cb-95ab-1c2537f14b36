# Use concise, technical responses with accurate Python examples.
# Prioritize readability and maintainability in your project's architecture.

# General Rules
- Use Python 3.12, ensure compatibility with PySide6 and ANSYS Workbench 2024 R2.
- Use functional programming where appropriate; minimize unnecessary classes.

# Directory and File Naming
- Use snake_case for file names and methods.
- Use CamelCase for class names.

# Code Style
- Follow PEP 8 style guidelines for Python code.
- Use descriptive variable names reflecting the data they contain.

# Error Handling
- Implement data validation and error handling.
- Use try-except blocks to handle exceptions, especially during file operations and user inputs.

# Testing
- Follow the Arrange-Act-Assert convention.
- Write unit tests for each public function and application module.

# GUI Development
- Use Qt Designer for interface design and PySide6 for implementation.
- Keep UI responsive and accessible, avoid hardcoded values.

# Integration with ANSYS
- Automate ANSYS script execution.
- Configure directory paths properly, avoiding spaces and special characters.

# Performance Optimization
- Profile and optimize performance-critical code.
- Use efficient data structures and algorithms to manage large datasets.
