#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包并验证脚本

此脚本会执行PyInstaller打包，然后立即验证打包结果，
特别关注originscript目录和meshpy.py文件是否正确包含。
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理旧的构建文件...")
    
    dirs_to_clean = ['dist', 'build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"   清理目录: {dir_name}")
            shutil.rmtree(dir_name)
        else:
            print(f"   目录不存在: {dir_name}")

def run_pyinstaller():
    """运行PyInstaller"""
    print("📦 开始PyInstaller打包...")
    
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "qt_new.spec"]
    
    print(f"🔧 执行命令: {' '.join(cmd)}")
    print("⏳ 打包中，请稍候...")
    
    start_time = time.time()
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        end_time = time.time()
        
        print(f"✅ 打包成功! 耗时: {end_time - start_time:.1f}秒")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败! 返回码: {e.returncode}")
        print("标准输出:")
        print(e.stdout)
        print("标准错误:")
        print(e.stderr)
        return False

def verify_package_structure():
    """验证打包结果"""
    print("🔍 验证打包结果...")
    
    dist_dir = Path("dist/vibration_transfer")
    if not dist_dir.exists():
        print(f"❌ 输出目录不存在: {dist_dir}")
        return False
    
    exe_file = dist_dir / "振动传递计算软件.exe"
    if not exe_file.exists():
        print(f"❌ 可执行文件不存在: {exe_file}")
        return False
    
    print(f"✅ 可执行文件存在: {exe_file}")
    
    # 检查_internal目录
    internal_dir = dist_dir / "_internal"
    if not internal_dir.exists():
        print(f"❌ _internal目录不存在: {internal_dir}")
        return False
    
    print(f"✅ _internal目录存在: {internal_dir}")
    
    # 检查关键目录
    critical_dirs = [
        "originscript",
        "script", 
        "assets",
        "help",
        "config",
        "ui",
        "core",
        "views",
        "ctrl"
    ]
    
    print(f"\n📋 检查关键目录:")
    missing_dirs = []
    
    for dir_name in critical_dirs:
        dir_path = internal_dir / dir_name
        if dir_path.exists():
            # 统计目录中的文件数量
            try:
                files = list(dir_path.rglob('*'))
                file_count = len([f for f in files if f.is_file()])
                print(f"   ✅ {dir_name} ({file_count} 个文件)")
                
                # 如果是originscript目录，详细列出文件
                if dir_name == "originscript":
                    print(f"      📄 originscript 目录内容:")
                    py_files = list(dir_path.glob('*.py'))
                    if py_files:
                        for file in sorted(py_files):
                            size = file.stat().st_size
                            print(f"         - {file.name} ({size} bytes)")
                    else:
                        print(f"         ⚠️ 没有找到.py文件")
                        
            except Exception as e:
                print(f"   ⚠️ {dir_name} (无法读取: {e})")
        else:
            missing_dirs.append(dir_name)
            print(f"   ❌ {dir_name} - 目录不存在")
    
    # 特别检查meshpy.py文件
    print(f"\n🎯 特别检查 meshpy.py 文件:")
    meshpy_path = internal_dir / "originscript" / "meshpy.py"
    if meshpy_path.exists():
        try:
            size = meshpy_path.stat().st_size
            print(f"   ✅ meshpy.py 存在 ({size} bytes)")
            
            # 尝试读取文件内容的前几行
            with open(meshpy_path, 'r', encoding='utf-8') as f:
                first_lines = [f.readline().strip() for _ in range(3)]
            print(f"   📄 文件前3行:")
            for i, line in enumerate(first_lines, 1):
                print(f"      {i}: {line}")
                
        except Exception as e:
            print(f"   ❌ meshpy.py 无法读取: {e}")
    else:
        print(f"   ❌ meshpy.py 不存在")
        print(f"   🔍 查找路径: {meshpy_path}")
        
        # 尝试在其他位置查找
        print(f"   🔍 在_internal目录中搜索meshpy.py:")
        meshpy_files = list(internal_dir.rglob('meshpy.py'))
        if meshpy_files:
            for file in meshpy_files:
                print(f"      找到: {file}")
        else:
            print(f"      未找到任何meshpy.py文件")
    
    # 检查所有Python文件
    print(f"\n📄 检查所有Python文件:")
    all_py_files = list(internal_dir.rglob('*.py'))
    print(f"   总共找到 {len(all_py_files)} 个Python文件")
    
    # 按目录分组显示
    py_by_dir = {}
    for py_file in all_py_files:
        rel_path = py_file.relative_to(internal_dir)
        dir_name = str(rel_path.parent)
        if dir_name not in py_by_dir:
            py_by_dir[dir_name] = []
        py_by_dir[dir_name].append(rel_path.name)
    
    for dir_name, files in sorted(py_by_dir.items()):
        print(f"   📁 {dir_name}: {len(files)} 个文件")
        if dir_name == "originscript":
            for file in sorted(files):
                print(f"      - {file}")
    
    # 总结
    print(f"\n📊 验证总结:")
    print(f"   总目录数: {len(critical_dirs)}")
    print(f"   存在目录数: {len(critical_dirs) - len(missing_dirs)}")
    print(f"   缺失目录数: {len(missing_dirs)}")
    
    if missing_dirs:
        print(f"   ⚠️ 缺失目录: {', '.join(missing_dirs)}")
    
    # 检查meshpy.py是否存在
    meshpy_exists = meshpy_path.exists()
    print(f"   meshpy.py: {'✅ 存在' if meshpy_exists else '❌ 缺失'}")
    
    return len(missing_dirs) == 0 and meshpy_exists

def main():
    """主函数"""
    print("🚀 打包并验证工具")
    print("=" * 60)
    print("专门检查originscript目录和meshpy.py文件")
    print("=" * 60)
    
    try:
        # 1. 清理旧文件
        clean_build_dirs()
        
        # 2. 执行打包
        if not run_pyinstaller():
            print("\n❌ 打包失败!")
            sys.exit(1)
        
        # 3. 验证结果
        if not verify_package_structure():
            print("\n❌ 打包验证失败!")
            sys.exit(1)
        
        print("\n🎉 打包和验证都成功!")
        print("💡 提示: 可执行文件位于 dist/vibration_transfer/ 目录")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
