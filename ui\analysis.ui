<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1000</width>
    <height>500</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1000</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MainWindow</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_4">
    <item>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="label">
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Times New Roman</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="text">
           <string>Step End Time</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_stependline">
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>30</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="label_2">
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>40</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>Times New Roman</family>
            <pointsize>20</pointsize>
           </font>
          </property>
          <property name="text">
           <string>Time Step</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_timestep">
          <property name="minimumSize">
           <size>
            <width>250</width>
            <height>30</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Orientation::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_8">
      <item>
       <widget class="QGroupBox" name="groupBox_2">
        <property name="minimumSize">
         <size>
          <width>300</width>
          <height>0</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Times New Roman</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="title">
         <string>Output Controls</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <item>
          <layout class="QVBoxLayout" name="verticalLayout">
           <item>
            <widget class="QCheckBox" name="checkBox_stress">
             <property name="enabled">
              <bool>true</bool>
             </property>
             <property name="sizePolicy">
              <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Stress</string>
             </property>
             <property name="iconSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
             <property name="autoRepeat">
              <bool>false</bool>
             </property>
             <property name="tristate">
              <bool>false</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_strain">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Strain</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_contactdata">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Contact Data</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_volumeandenergy">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Volume and Energy</string>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QCheckBox" name="checkBox_eulerangles">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>20</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Euler Angles</string>
             </property>
             <property name="iconSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
             <property name="checked">
              <bool>true</bool>
             </property>
             <property name="autoRepeatDelay">
              <number>300</number>
             </property>
             <property name="autoRepeatInterval">
              <number>100</number>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QGroupBox" name="groupBox_dampingcontrols">
        <property name="font">
         <font>
          <family>Times New Roman</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="title">
         <string>Damping Controls</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <item>
            <widget class="QLabel" name="label_3">
             <property name="minimumSize">
              <size>
               <width>300</width>
               <height>50</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>20</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Stiffness Coefficient</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_stiffness">
             <property name="minimumSize">
              <size>
               <width>250</width>
               <height>40</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <item>
            <widget class="QLabel" name="label_4">
             <property name="minimumSize">
              <size>
               <width>300</width>
               <height>50</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Times New Roman</family>
               <pointsize>20</pointsize>
              </font>
             </property>
             <property name="text">
              <string>Mass Coefficient</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="lineEdit_mass">
             <property name="minimumSize">
              <size>
               <width>250</width>
               <height>40</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="push_finish">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>完成设置</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_connectionui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>上一步(连接设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_constrainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>下一步(约束设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_mainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
