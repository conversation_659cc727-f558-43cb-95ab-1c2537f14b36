"""
材料管理器模块

此模块提供了材料库管理的核心数据模型和业务逻辑，包括：
1. Material - 单个材料数据模型
2. MaterialLibrary - 材料库管理器
3. MaterialAssignment - 材料分配管理器
4. 材料数据的序列化和反序列化

作者: [作者名]
日期: [日期]
"""

import json
import logging
import os
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass, asdict, field
from PySide6.QtCore import QObject, Signal, QMutex, QMutexLocker

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MaterialCategory(Enum):
    """材料分类枚举"""
    GENERAL = "一般材料库"
    THERMAL = "热材料库"
    COMPOSITE = "复合材料库"
    CUSTOM = "自定义材料库"


@dataclass
class Material:
    """材料数据模型类"""
    
    def __init__(self, name: str = "", young_modulus: float = 200.0, 
                 density: float = 7850.0, poisson_ratio: float = 0.3, 
                 category: MaterialCategory = MaterialCategory.CUSTOM):
        """初始化材料
        
        Args:
            name: 材料名称
            young_modulus: 杨氏模量 (GPa)
            density: 密度 (kg/m³)
            poisson_ratio: 泊松比
            category: 材料分类
        """
        self.id = str(uuid.uuid4())  # 唯一标识符
        self.name = name
        self.young_modulus = young_modulus
        self.density = density
        self.poisson_ratio = poisson_ratio
        self.category = category
        self.is_readonly = category != MaterialCategory.CUSTOM
        self.created_time = datetime.now()
        self.updated_time = datetime.now()
        self.description = ""  # 材料描述
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'young_modulus': self.young_modulus,
            'density': self.density,
            'poisson_ratio': self.poisson_ratio,
            'category': self.category.value,
            'is_readonly': self.is_readonly,
            'created_time': self.created_time.isoformat(),
            'updated_time': self.updated_time.isoformat(),
            'description': self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Material':
        """从字典创建材料对象"""
        material = cls(
            name=data.get('name', ''),
            young_modulus=data.get('young_modulus', 200.0),
            density=data.get('density', 7850.0),
            poisson_ratio=data.get('poisson_ratio', 0.3),
            category=MaterialCategory(data.get('category', MaterialCategory.CUSTOM.value))
        )
        material.id = data.get('id', str(uuid.uuid4()))
        material.description = data.get('description', '')
        return material
    
    def copy(self) -> 'Material':
        """创建材料副本"""
        new_material = Material(
            name=f"{self.name}_副本",
            young_modulus=self.young_modulus,
            density=self.density,
            poisson_ratio=self.poisson_ratio,
            category=MaterialCategory.CUSTOM  # 副本总是自定义材料
        )
        new_material.description = self.description
        return new_material


class MaterialLibrary(QObject):
    """材料库管理器"""

    # 信号定义
    material_added = Signal(object)  # 材料添加信号
    material_updated = Signal(object)  # 材料更新信号
    material_removed = Signal(str)  # 材料删除信号

    def __init__(self):
        super().__init__()
        self.materials: Dict[str, Material] = {}
        self._mutex = QMutex()  # 线程安全锁

        # 设置数据文件路径
        self._data_dir = Path(__file__).parent.parent / "data"
        self._custom_materials_file = self._data_dir / "custom_materials.json"

        # 确保数据目录存在
        self._ensure_data_directory()

        # 加载材料数据
        self._load_default_materials()
        self._load_custom_materials()

        logger.debug("材料库管理器初始化完成")

    def _ensure_data_directory(self):
        """确保数据目录存在"""
        try:
            self._data_dir.mkdir(parents=True, exist_ok=True)
            logger.debug(f"数据目录已确保存在: {self._data_dir}")
        except Exception as e:
            logger.error(f"创建数据目录失败: {e}")

    def _load_default_materials(self):
        """加载默认材料"""
        # 预置材料库 - 一般材料库
        structural_steel = Material(
            name="Structural Steel",
            young_modulus=200.0,  # GPa
            density=7850.0,  # kg/m³
            poisson_ratio=0.3,
            category=MaterialCategory.GENERAL
        )
        structural_steel.description = "结构钢，常用于建筑和机械结构"
        
        aluminum_alloy = Material(
            name="Aluminum Alloy",
            young_modulus=70.0,  # GPa
            density=2700.0,  # kg/m³
            poisson_ratio=0.33,
            category=MaterialCategory.GENERAL
        )
        aluminum_alloy.description = "铝合金，轻质高强度材料"
        
        concrete = Material(
            name="Concrete",
            young_modulus=30.0,  # GPa
            density=2400.0,  # kg/m³
            poisson_ratio=0.2,
            category=MaterialCategory.GENERAL
        )
        concrete.description = "混凝土，常用建筑材料"
        
        # 预置材料库 - 热材料库
        stainless_steel = Material(
            name="Stainless Steel",
            young_modulus=200.0,  # GPa
            density=8000.0,  # kg/m³
            poisson_ratio=0.3,
            category=MaterialCategory.THERMAL
        )
        stainless_steel.description = "不锈钢，耐高温耐腐蚀"
        
        # 预置材料库 - 复合材料库
        carbon_fiber = Material(
            name="Carbon Fiber",
            young_modulus=230.0,  # GPa
            density=1600.0,  # kg/m³
            poisson_ratio=0.2,
            category=MaterialCategory.COMPOSITE
        )
        carbon_fiber.description = "碳纤维复合材料，高强度轻质"
        
        # 添加到材料库
        for material in [structural_steel, aluminum_alloy, concrete, stainless_steel, carbon_fiber]:
            self.materials[material.id] = material
        
        logger.info(f"加载了 {len(self.materials)} 个默认材料")

    def _load_custom_materials(self):
        """加载自定义材料"""
        try:
            if not self._custom_materials_file.exists():
                logger.debug("自定义材料文件不存在，跳过加载")
                return

            with open(self._custom_materials_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            custom_materials_data = data.get('custom_materials', [])
            loaded_count = 0

            for material_data in custom_materials_data:
                try:
                    # 验证材料数据
                    if self._validate_material_data(material_data):
                        material = Material.from_dict(material_data)
                        # 确保是自定义材料
                        if material.category == MaterialCategory.CUSTOM:
                            self.materials[material.id] = material
                            loaded_count += 1
                        else:
                            logger.warning(f"跳过非自定义材料: {material.name}")
                    else:
                        logger.warning(f"材料数据验证失败，跳过: {material_data.get('name', 'Unknown')}")

                except Exception as e:
                    logger.error(f"加载单个材料失败: {e}")
                    continue

            logger.info(f"成功加载了 {loaded_count} 个自定义材料")

        except FileNotFoundError:
            logger.debug("自定义材料文件不存在")
        except json.JSONDecodeError as e:
            logger.error(f"自定义材料文件JSON格式错误: {e}")
        except Exception as e:
            logger.error(f"加载自定义材料失败: {e}")

    def _validate_material_data(self, data: Dict[str, Any]) -> bool:
        """验证材料数据的完整性和有效性

        Args:
            data: 材料数据字典

        Returns:
            bool: 验证是否通过
        """
        required_fields = ['id', 'name', 'young_modulus', 'density', 'poisson_ratio', 'category']

        # 检查必需字段
        for field in required_fields:
            if field not in data:
                logger.warning(f"材料数据缺少必需字段: {field}")
                return False

        # 检查数值范围
        try:
            young_modulus = float(data['young_modulus'])
            density = float(data['density'])
            poisson_ratio = float(data['poisson_ratio'])

            if young_modulus <= 0:
                logger.warning(f"杨氏模量必须大于0: {young_modulus}")
                return False

            if density <= 0:
                logger.warning(f"密度必须大于0: {density}")
                return False

            if not (0 <= poisson_ratio <= 0.5):
                logger.warning(f"泊松比必须在0-0.5之间: {poisson_ratio}")
                return False

        except (ValueError, TypeError) as e:
            logger.warning(f"材料数值数据无效: {e}")
            return False

        # 检查名称
        name = data.get('name', '').strip()
        if not name:
            logger.warning("材料名称不能为空")
            return False

        return True

    def _save_custom_materials(self):
        """保存自定义材料到文件"""
        try:
            with QMutexLocker(self._mutex):
                # 收集自定义材料
                custom_materials = []
                for material in self.materials.values():
                    if material.category == MaterialCategory.CUSTOM:
                        custom_materials.append(material.to_dict())

                # 准备保存数据
                save_data = {
                    'custom_materials': custom_materials,
                    'saved_time': datetime.now().isoformat(),
                    'version': '1.0'
                }

                # 创建临时文件，确保原子性写入
                temp_file = self._custom_materials_file.with_suffix('.tmp')

                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(save_data, f, indent=2, ensure_ascii=False)

                # 原子性替换
                temp_file.replace(self._custom_materials_file)

                logger.info(f"成功保存了 {len(custom_materials)} 个自定义材料")

        except Exception as e:
            logger.error(f"保存自定义材料失败: {e}")
            # 清理临时文件
            temp_file = self._custom_materials_file.with_suffix('.tmp')
            if temp_file.exists():
                try:
                    temp_file.unlink()
                except:
                    pass

    def add_material(self, material: Material) -> bool:
        """添加材料

        Args:
            material: 材料对象

        Returns:
            bool: 是否成功添加
        """
        try:
            if self.get_material_by_name(material.name):
                logger.warning(f"材料名称已存在: {material.name}")
                return False

            self.materials[material.id] = material
            self.material_added.emit(material)

            # 如果是自定义材料，自动保存
            if material.category == MaterialCategory.CUSTOM:
                self._save_custom_materials()

            logger.info(f"添加材料: {material.name}")
            return True

        except Exception as e:
            logger.error(f"添加材料失败: {e}")
            return False
    
    def remove_material(self, material_id: str) -> bool:
        """删除材料

        Args:
            material_id: 材料ID

        Returns:
            bool: 是否成功删除
        """
        try:
            if material_id not in self.materials:
                logger.warning(f"材料不存在: {material_id}")
                return False

            material = self.materials[material_id]
            if material.is_readonly:
                logger.warning(f"无法删除只读材料: {material.name}")
                return False

            # 保存材料信息用于日志
            material_name = material.name
            is_custom = material.category == MaterialCategory.CUSTOM

            del self.materials[material_id]
            self.material_removed.emit(material_id)

            # 如果是自定义材料，自动保存
            if is_custom:
                self._save_custom_materials()

            logger.info(f"删除材料: {material_name}")
            return True

        except Exception as e:
            logger.error(f"删除材料失败: {e}")
            return False
    
    def get_material(self, material_id: str) -> Optional[Material]:
        """获取材料
        
        Args:
            material_id: 材料ID
            
        Returns:
            Material: 材料对象，如果不存在返回None
        """
        return self.materials.get(material_id)
    
    def get_material_by_name(self, name: str) -> Optional[Material]:
        """根据名称获取材料
        
        Args:
            name: 材料名称
            
        Returns:
            Material: 材料对象，如果不存在返回None
        """
        for material in self.materials.values():
            if material.name == name:
                return material
        return None
    
    def get_materials_by_category(self, category: MaterialCategory) -> List[Material]:
        """根据分类获取材料列表
        
        Args:
            category: 材料分类
            
        Returns:
            List[Material]: 材料列表
        """
        return [material for material in self.materials.values() 
                if material.category == category]
    
    def search_materials(self, keyword: str) -> List[Material]:
        """搜索材料
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            List[Material]: 匹配的材料列表
        """
        if not keyword:
            return list(self.materials.values())
        
        keyword = keyword.lower()
        results = []
        
        for material in self.materials.values():
            if (keyword in material.name.lower() or 
                keyword in material.description.lower()):
                results.append(material)
        
        return results
    
    def update_material(self, material: Material) -> bool:
        """更新材料

        Args:
            material: 材料对象

        Returns:
            bool: 是否成功更新
        """
        try:
            if material.id not in self.materials:
                logger.warning(f"材料不存在: {material.id}")
                return False

            old_material = self.materials[material.id]
            if old_material.is_readonly:
                logger.warning(f"无法更新只读材料: {old_material.name}")
                return False

            material.updated_time = datetime.now()
            self.materials[material.id] = material
            self.material_updated.emit(material)

            # 如果是自定义材料，自动保存
            if material.category == MaterialCategory.CUSTOM:
                self._save_custom_materials()

            logger.info(f"更新材料: {material.name}")
            return True

        except Exception as e:
            logger.error(f"更新材料失败: {e}")
            return False

    def save_custom_materials(self) -> bool:
        """手动保存自定义材料

        Returns:
            bool: 是否保存成功
        """
        try:
            self._save_custom_materials()
            return True
        except Exception as e:
            logger.error(f"手动保存自定义材料失败: {e}")
            return False

    def reload_custom_materials(self) -> bool:
        """重新加载自定义材料

        Returns:
            bool: 是否加载成功
        """
        try:
            # 移除现有的自定义材料
            custom_material_ids = [
                material_id for material_id, material in self.materials.items()
                if material.category == MaterialCategory.CUSTOM
            ]

            for material_id in custom_material_ids:
                del self.materials[material_id]

            # 重新加载自定义材料
            self._load_custom_materials()

            logger.info("重新加载自定义材料完成")
            return True

        except Exception as e:
            logger.error(f"重新加载自定义材料失败: {e}")
            return False

    def get_custom_materials_count(self) -> int:
        """获取自定义材料数量

        Returns:
            int: 自定义材料数量
        """
        return len([
            material for material in self.materials.values()
            if material.category == MaterialCategory.CUSTOM
        ])

    def get_data_file_path(self) -> str:
        """获取数据文件路径

        Returns:
            str: 数据文件路径
        """
        return str(self._custom_materials_file)

    def export_custom_materials(self, file_path: str) -> bool:
        """导出自定义材料到指定文件

        Args:
            file_path: 导出文件路径

        Returns:
            bool: 是否导出成功
        """
        try:
            # 收集自定义材料
            custom_materials = []
            for material in self.materials.values():
                if material.category == MaterialCategory.CUSTOM:
                    custom_materials.append(material.to_dict())

            # 准备导出数据
            export_data = {
                'custom_materials': custom_materials,
                'exported_time': datetime.now().isoformat(),
                'version': '1.0',
                'total_count': len(custom_materials)
            }

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)

            logger.info(f"成功导出 {len(custom_materials)} 个自定义材料到: {file_path}")
            return True

        except Exception as e:
            logger.error(f"导出自定义材料失败: {e}")
            return False

    def import_custom_materials(self, file_path: str) -> int:
        """从指定文件导入自定义材料

        Args:
            file_path: 导入文件路径

        Returns:
            int: 成功导入的材料数量
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            custom_materials_data = data.get('custom_materials', [])
            imported_count = 0

            for material_data in custom_materials_data:
                try:
                    if self._validate_material_data(material_data):
                        material = Material.from_dict(material_data)

                        # 检查名称冲突
                        if self.get_material_by_name(material.name):
                            # 生成新名称
                            base_name = material.name
                            counter = 1
                            while self.get_material_by_name(material.name):
                                material.name = f"{base_name}_导入{counter}"
                                counter += 1

                        # 确保是自定义材料
                        material.category = MaterialCategory.CUSTOM
                        material.is_readonly = False

                        self.materials[material.id] = material
                        imported_count += 1

                except Exception as e:
                    logger.error(f"导入单个材料失败: {e}")
                    continue

            # 保存导入的材料
            if imported_count > 0:
                self._save_custom_materials()

            logger.info(f"成功导入 {imported_count} 个自定义材料")
            return imported_count

        except Exception as e:
            logger.error(f"导入自定义材料失败: {e}")
            return 0


class MaterialAssignment(QObject):
    """材料分配管理器"""
    
    # 信号定义
    assignment_changed = Signal(str, str)  # 分配变更信号 (structure, material_id)
    
    def __init__(self):
        super().__init__()
        self.assignments: Dict[str, str] = {}  # {structure_name: material_id}
        logger.debug("材料分配管理器初始化完成")
    
    def assign_material(self, structure: str, material_id: str) -> bool:
        """分配材料到结构体
        
        Args:
            structure: 结构体名称
            material_id: 材料ID
            
        Returns:
            bool: 是否成功分配
        """
        try:
            self.assignments[structure] = material_id
            self.assignment_changed.emit(structure, material_id)
            logger.info(f"分配材料 {material_id} 到结构体 {structure}")
            return True
            
        except Exception as e:
            logger.error(f"分配材料失败: {e}")
            return False
    
    def get_assignment(self, structure: str) -> Optional[str]:
        """获取结构体的材料分配
        
        Args:
            structure: 结构体名称
            
        Returns:
            str: 材料ID，如果未分配返回None
        """
        return self.assignments.get(structure)
    
    def apply_to_all(self, material_id: str, structures: List[str]) -> bool:
        """将材料应用到所有结构体
        
        Args:
            material_id: 材料ID
            structures: 结构体列表
            
        Returns:
            bool: 是否成功应用
        """
        try:
            for structure in structures:
                self.assignments[structure] = material_id
                self.assignment_changed.emit(structure, material_id)
            
            logger.info(f"将材料 {material_id} 应用到 {len(structures)} 个结构体")
            return True
            
        except Exception as e:
            logger.error(f"批量应用材料失败: {e}")
            return False
    
    def clear_assignments(self):
        """清空所有分配"""
        self.assignments.clear()
        logger.info("清空所有材料分配")


# 全局材料管理器实例
_global_material_library: Optional[MaterialLibrary] = None
_global_material_assignment: Optional[MaterialAssignment] = None


def get_material_library() -> MaterialLibrary:
    """获取全局材料库管理器实例"""
    global _global_material_library
    if _global_material_library is None:
        _global_material_library = MaterialLibrary()
    return _global_material_library


def get_material_assignment() -> MaterialAssignment:
    """获取全局材料分配管理器实例"""
    global _global_material_assignment
    if _global_material_assignment is None:
        _global_material_assignment = MaterialAssignment()
    return _global_material_assignment
