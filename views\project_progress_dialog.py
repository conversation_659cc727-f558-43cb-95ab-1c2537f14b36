"""
项目创建进度监控对话框模块 (优化版)

此模块定义了项目创建进度监控对话框类，负责：
1. 实时监控日志文件
2. 显示进度检查列表
3. 处理UTF-8编码的中文日志
4. 提供取消操作支持
5. 正确的按钮状态管理

优化内容：
- 修复按钮状态管理问题
- 改进线程安全性
- 优化UI响应性
- 增强错误处理

作者: [作者名]
日期: [日期]
"""

import os
import logging
import time
from PySide6.QtCore import Qt, QCoreApplication, QThread, Signal
from PySide6.QtGui import QFont, QColor
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QListWidget, QListWidgetItem,
    QProgressBar, QTextEdit, QFrame, QGroupBox
)

logger = logging.getLogger(__name__)

def tr(text: str) -> str:
    """翻译函数的便捷包装"""
    return QCoreApplication.translate("ProjectProgressDialog", text)

class LogMonitorThread(QThread):
    """日志监控线程 (架构不变)"""
    log_updated = Signal(str)
    progress_updated = Signal(str)
    
    def __init__(self, log_file_path: str, encoding: str = 'utf-8'):
        super().__init__()
        self.log_file_path = log_file_path
        self.encoding = encoding
        self.running = True
        self.last_position = 0
        
    def run(self):
        """运行日志监控"""
        while self.running and not os.path.exists(self.log_file_path):
            time.sleep(0.2)

        while self.running:
            try:
                if os.path.exists(self.log_file_path):
                    # 使用指定编码读取日志文件
                    with open(self.log_file_path, 'r', encoding=self.encoding) as f:
                        f.seek(self.last_position)
                        new_content = f.read()
                        if new_content:
                            self.last_position = f.tell()
                            self.log_updated.emit(new_content)

                            # 逐行处理，发送进度更新信号
                            for line in new_content.split('\n'):
                                line = line.strip()
                                if line:
                                    self.progress_updated.emit(line)

                time.sleep(0.5)
            except Exception as e:
                logger.error(f"日志监控线程错误: {e}")
                time.sleep(1)
    
    def stop(self):
        """停止监控"""
        self.running = False

class ProjectProgressDialog(QDialog):
    """项目创建进度对话框类"""
    
    def __init__(self, log_file_path: str, parent=None, auto_start_monitoring=True, encoding: str = 'utf-8'):
        super().__init__(parent)
        self.log_file_path = log_file_path
        self.encoding = encoding
        self.log_monitor = None
        self.is_completed = False
        self.is_cancelled = False

        self.progress_items = [
            ("脚本开始执行", False),
            ("创建分析系统", False),
            ("导入几何文件", False),
            ("几何编辑完成", False),
            ("模型组件刷新", False),
            ("模型编辑完成", False),
            ("项目保存成功", False),
            ("脚本执行完毕", False)
        ]

        self.progress_keywords = {
            "脚本开始执行": 0,
            "分析系统创建成功": 1,
            "几何文件导入完成": 2,
            "几何编辑完成": 3,
            "模型组件刷新完成": 4,
            "模型编辑完成": 5,
            "项目保存成功": 6,
            "脚本执行完毕": 7
        }

        self.setup_ui()
        self.setup_connections()
        self.apply_styles()

        # 只有在 auto_start_monitoring 为 True 时才自动启动监控
        if auto_start_monitoring:
            self.start_monitoring()

        logger.info("项目进度对话框初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle(tr("创建项目进度"))
        self.setModal(True)
        self.setMinimumSize(720, 600)
        self.resize(800, 680)
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(18)
        main_layout.setContentsMargins(28, 28, 28, 28)
        
        self.title_label = QLabel(tr("正在创建ANSYS Workbench项目..."))
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setObjectName("titleLabel")
        main_layout.addWidget(self.title_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, len(self.progress_items))
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(False)
        self.progress_bar.setObjectName("progressBar")
        main_layout.addWidget(self.progress_bar)
        
        progress_group = QGroupBox(tr("执行进度"))
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_list = QListWidget()
        self.progress_list.setObjectName("progressList")
        
        for item_text, _ in self.progress_items:
            list_item = QListWidgetItem(f"[PENDING] {item_text}")
            list_item.setData(Qt.UserRole, False)
            list_item.setFlags(list_item.flags() & ~Qt.ItemIsSelectable)
            self.progress_list.addItem(list_item)
        
        progress_layout.addWidget(self.progress_list)
        main_layout.addWidget(progress_group)
        
        log_group = QGroupBox(tr("详细日志"))
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setObjectName("logText")
        log_layout.addWidget(self.log_text)
        main_layout.addWidget(log_group, 1)
        
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setObjectName("separatorLine")
        main_layout.addWidget(line)
        
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton(tr("❌ 取消"))
        self.cancel_btn.setObjectName("cancelButton")
        self.cancel_btn.setMinimumWidth(120)
        self.cancel_btn.setMinimumHeight(40)

        self.close_btn = QPushButton(tr("✅ 关闭"))
        self.close_btn.setEnabled(False)
        self.close_btn.setObjectName("closeButton")
        self.close_btn.setMinimumWidth(120)
        self.close_btn.setMinimumHeight(40)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(button_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        self.cancel_btn.clicked.connect(self.cancel_operation)
        self.close_btn.clicked.connect(self.accept)
    
    def apply_styles(self):
        """应用全新、现代化的样式表"""
        self.setStyleSheet("""
            /* --- 全局与基础 --- */
            ProjectProgressDialog {
                background-color: #f7f8fc;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            /* --- 标题 --- */
            #titleLabel {
                color: #1f2937;
                margin-bottom: 8px;
            }

            /* --- 进度条 --- */
            #progressBar {
                border: none;
                border-radius: 8px;
                background-color: #e5e7eb;
                height: 10px;
            }
            #progressBar::chunk {
                background-color: #4f46e5;
                border-radius: 8px;
            }
            #progressBar[status="completed"]::chunk { background-color: #16a34a; }
            #progressBar[status="cancelled"]::chunk { background-color: #dc2626; }

            /* --- 分组框 --- */
            QGroupBox {
                font-weight: 600;
                font-size: 15px;
                color: #374151;
                border: 1px solid #d1d5db;
                border-radius: 12px;
                margin-top: 1em;
                padding: 18px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                left: 16px;
                padding: 0 8px;
                background-color: #f7f8fc;
            }
            
            /* --- 进度列表 --- */
            #progressList {
                border: none;
                background-color: #ffffff;
                outline: none;
                font-size: 14px;
            }
            #progressList::item {
                padding: 10px 12px;
                border-bottom: 1px solid #f3f4f6;
                color: #374151;
            }
            /* [修正] 移除 [completed="true"] 选择器，因为样式在代码中直接设置 */
            #progressList::item:last-child {
                border-bottom: none;
            }

            /* --- 日志文本 --- */
            #logText {
                border: 1px solid #374151;
                border-radius: 8px;
                background-color: #1e293b;
                color: #e2e8f0;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                padding: 12px;
                selection-background-color: #475569;
            }

            /* --- 按钮基础样式 --- */
            QPushButton {
                padding: 12px 24px;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                background-color: #ffffff;
                color: #374151;
                min-width: 120px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #f9fafb;
                border-color: #9ca3af;
            }
            QPushButton:pressed {
                background-color: #f3f4f6;
            }
            QPushButton:disabled {
                background-color: #f9fafb;
                color: #d1d5db;
                border-color: #e5e7eb;
            }

            /* --- 成功/关闭按钮 --- */
            #closeButton {
                background-color: #16a34a;
                color: white;
                border-color: #16a34a;
                font-weight: 700;
            }
            #closeButton:hover {
                background-color: #15803d;
                border-color: #15803d;
            }
            #closeButton:pressed {
                background-color: #166534;
            }
            #closeButton:disabled {
                background-color: #d1fae5;
                border-color: #d1fae5;
                color: #a7f3d0;
            }

            /* --- 警告/取消按钮 --- */
            #cancelButton {
                background-color: #fef2f2;
                color: #dc2626;
                border-color: #fecaca;
                font-weight: 600;
            }
            #cancelButton:hover {
                background-color: #fee2e2;
                border-color: #fca5a5;
            }
            #cancelButton:pressed {
                background-color: #fca5a5;
            }
            #cancelButton:disabled {
                background-color: #f9fafb;
                color: #d1d5db;
                border-color: #e5e7eb;
            }

            /* --- 分隔线 --- */
            #separatorLine {
                border: none;
                height: 1px;
                background-color: #e5e7eb;
                margin: 8px 0;
            }
        """)
    
    def start_monitoring(self):
        """开始监控日志"""
        self.log_monitor = LogMonitorThread(self.log_file_path, self.encoding)
        self.log_monitor.log_updated.connect(self.update_log)
        self.log_monitor.progress_updated.connect(self.update_progress)
        self.log_monitor.start()
        logger.info(f"开始监控日志文件: {self.log_file_path} (编码: {self.encoding})")

        # 调试信息：显示当前的关键词映射
        logger.info("当前进度关键词映射:")
        for keyword, index in self.progress_keywords.items():
            logger.info(f"  {index}: '{keyword}'")
    
    def update_log(self, new_content: str):
        """更新日志显示"""
        self.log_text.append(new_content.strip())
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def update_progress(self, log_line: str):
        """根据日志内容更新进度"""
        try:
            # 清理日志行，移除时间戳和多余空格
            clean_line = log_line.strip()

            # 移除logging模块的时间戳格式，例如 "2025-06-24 19:00:00,123 - INFO - 脚本开始执行..."
            if ' - INFO - ' in clean_line:
                clean_line = clean_line.split(' - INFO - ', 1)[1].strip()

            # 进行关键词匹配
            for keyword, index in self.progress_keywords.items():
                if keyword in clean_line:
                    logger.info(f"[SUCCESS] 匹配到关键词 '{keyword}' 在日志行: '{clean_line}'")
                    self.mark_progress_completed(index)
                    break

        except Exception as e:
            logger.error(f"进度更新处理错误: {e}, 原始日志行: {repr(log_line)}")


    
    def mark_progress_completed(self, index: int):
        """标记进度项目为完成 (线程安全版本)"""
        if not (0 <= index < len(self.progress_items)):
            logger.warning(f"无效的进度索引: {index}")
            return

        # 检查是否已经完成，避免重复处理
        if self.progress_items[index][1]:
            return

        # 更新进度状态
        self.progress_items[index] = (self.progress_items[index][0], True)

        # 更新UI显示
        item = self.progress_list.item(index)
        if item:
            item.setText(f"✅ {self.progress_items[index][0]}")

            # 设置完成项目的视觉样式
            item.setForeground(QColor("#16a34a"))  # 绿色表示完成
            font = item.font()
            font.setBold(True)
            item.setFont(font)

        # 计算完成数量并更新进度条
        completed_count = sum(1 for _, completed in self.progress_items if completed)
        self.progress_bar.setValue(completed_count)

        logger.info(f"进度更新: {self.progress_items[index][0]} 完成 ({completed_count}/{len(self.progress_items)})")

        # 检查是否全部完成
        if completed_count == len(self.progress_items):
            logger.info("所有进度项目已完成，调用operation_completed")
            self.operation_completed()
    
    def operation_completed(self):
        """操作成功完成 - 更新UI状态"""
        if self.is_completed:
            return  # 避免重复调用

        self.is_completed = True

        # 更新标题和进度条状态
        self.title_label.setText(tr("🎉 项目创建成功！"))
        self.title_label.setStyleSheet("color: #16a34a; font-weight: bold;")

        # 设置进度条为完成状态
        self.progress_bar.setProperty("status", "completed")
        self.progress_bar.style().unpolish(self.progress_bar)
        self.progress_bar.style().polish(self.progress_bar)

        # 关键：正确设置按钮状态
        self.cancel_btn.setEnabled(False)
        self.cancel_btn.setText(tr("已完成"))

        self.close_btn.setEnabled(True)
        self.close_btn.setDefault(True)
        self.close_btn.setText(tr("关闭"))

        # 强制刷新按钮样式
        self.cancel_btn.style().unpolish(self.cancel_btn)
        self.cancel_btn.style().polish(self.cancel_btn)
        self.close_btn.style().unpolish(self.close_btn)
        self.close_btn.style().polish(self.close_btn)

        logger.info("项目创建操作完成 - UI状态已更新")
        self._debug_button_states("operation_completed")
    
    def cancel_operation(self):
        """取消操作"""
        if self.is_cancelled or self.is_completed:
            return  # 避免重复取消或在完成后取消

        self.is_cancelled = True

        # 停止日志监控线程
        if self.log_monitor and self.log_monitor.isRunning():
            self.log_monitor.stop()
            self.log_monitor.wait(1000)

        # 更新UI状态
        self.title_label.setText(tr("⚠️ 操作已取消"))
        self.title_label.setStyleSheet("color: #dc2626; font-weight: bold;")

        self.progress_bar.setProperty("status", "cancelled")
        self.progress_bar.style().unpolish(self.progress_bar)
        self.progress_bar.style().polish(self.progress_bar)

        logger.warning("用户取消项目创建操作")
        self._debug_button_states("cancel_operation")
        self.reject()
    
    def closeEvent(self, event):
        """处理窗口关闭事件"""
        if not self.is_completed and not self.is_cancelled:
            self.cancel_operation()
        else:
            if self.log_monitor and self.log_monitor.isRunning():
                self.log_monitor.stop()
                self.log_monitor.wait(1000)
            event.accept()

    def __del__(self):
        """析构函数，确保线程在对象销毁时停止"""
        if hasattr(self, 'log_monitor') and self.log_monitor and self.log_monitor.isRunning():
            self.log_monitor.stop()
            self.log_monitor.wait(500)
    
    def _debug_button_states(self, context: str):
        """调试按钮状态 - 帮助诊断问题"""
        cancel_enabled = self.cancel_btn.isEnabled()
        close_enabled = self.close_btn.isEnabled()
        logger.debug(f"[{context}] 按钮状态 - 取消: {cancel_enabled}, 关闭: {close_enabled}, 完成: {self.is_completed}, 取消: {self.is_cancelled}")

    def get_progress_status(self) -> dict:
        """获取当前进度状态 - 用于调试"""
        completed_count = sum(1 for _, completed in self.progress_items if completed)
        return {
            'completed_count': completed_count,
            'total_count': len(self.progress_items),
            'is_completed': self.is_completed,
            'is_cancelled': self.is_cancelled,
            'cancel_button_enabled': self.cancel_btn.isEnabled(),
            'close_button_enabled': self.close_btn.isEnabled()
        }

    def is_operation_completed(self) -> bool:
        """检查操作是否成功完成"""
        return self.is_completed

    def is_operation_cancelled(self) -> bool:
        """检查操作是否被取消"""
        return self.is_cancelled