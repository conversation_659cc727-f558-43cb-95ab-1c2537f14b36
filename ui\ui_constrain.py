# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'constrain.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QGroupBox,
    QHBoxLayout, QHeaderView, QLabel, QLineEdit, QMainWindow,
    QPlainTextEdit, QPushButton, QSizePolicy, QSpacerItem,
    QStatusBar, QTabWidget, QTableWidget, QTableWidgetItem,
    QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1200, 600)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.verticalLayout_10 = QVBoxLayout(self.tab)
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.horizontalLayout_24 = QHBoxLayout()
        self.horizontalLayout_24.setObjectName(u"horizontalLayout_24")
        self.pushButton_force = QPushButton(self.tab)
        self.pushButton_force.setObjectName(u"pushButton_force")
        self.pushButton_force.setMinimumSize(QSize(200, 75))
        font = QFont()
        font.setFamilies([u"\u5b8b\u4f53"])
        font.setPointSize(15)
        self.pushButton_force.setFont(font)

        self.horizontalLayout_24.addWidget(self.pushButton_force)

        self.lineEdit_force = QLineEdit(self.tab)
        self.lineEdit_force.setObjectName(u"lineEdit_force")
        self.lineEdit_force.setMinimumSize(QSize(750, 60))
        font1 = QFont()
        font1.setFamilies([u"Times New Roman"])
        font1.setPointSize(15)
        self.lineEdit_force.setFont(font1)

        self.horizontalLayout_24.addWidget(self.lineEdit_force)

        self.horizontalSpacer = QSpacerItem(488, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_24.addItem(self.horizontalSpacer)


        self.verticalLayout_10.addLayout(self.horizontalLayout_24)

        self.plainTextEdit_force = QPlainTextEdit(self.tab)
        self.plainTextEdit_force.setObjectName(u"plainTextEdit_force")
        self.plainTextEdit_force.setMinimumSize(QSize(1000, 250))
        self.plainTextEdit_force.setUndoRedoEnabled(True)
        self.plainTextEdit_force.setReadOnly(True)

        self.verticalLayout_10.addWidget(self.plainTextEdit_force)

        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        self.horizontalLayout_8 = QHBoxLayout(self.tab_2)
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.verticalLayout_2 = QVBoxLayout()
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.label_7 = QLabel(self.tab_2)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setMinimumSize(QSize(400, 100))
        font2 = QFont()
        font2.setFamilies([u"\u5b8b\u4f53"])
        font2.setPointSize(20)
        self.label_7.setFont(font2)
        self.label_7.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_2.addWidget(self.label_7)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label = QLabel(self.tab_2)
        self.label.setObjectName(u"label")
        self.label.setMinimumSize(QSize(100, 50))
        self.label.setFont(font1)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.label)

        self.xcomponent_flange1 = QComboBox(self.tab_2)
        self.xcomponent_flange1.addItem("")
        self.xcomponent_flange1.addItem("")
        self.xcomponent_flange1.setObjectName(u"xcomponent_flange1")
        self.xcomponent_flange1.setMinimumSize(QSize(100, 50))
        self.xcomponent_flange1.setFont(font1)

        self.horizontalLayout_2.addWidget(self.xcomponent_flange1)


        self.verticalLayout_2.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_2 = QLabel(self.tab_2)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setMinimumSize(QSize(100, 50))
        self.label_2.setFont(font1)
        self.label_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_3.addWidget(self.label_2)

        self.ycomponent_flange1 = QComboBox(self.tab_2)
        self.ycomponent_flange1.addItem("")
        self.ycomponent_flange1.addItem("")
        self.ycomponent_flange1.setObjectName(u"ycomponent_flange1")
        self.ycomponent_flange1.setMinimumSize(QSize(100, 50))
        self.ycomponent_flange1.setFont(font1)

        self.horizontalLayout_3.addWidget(self.ycomponent_flange1)


        self.verticalLayout_2.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_3 = QLabel(self.tab_2)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setMinimumSize(QSize(100, 50))
        self.label_3.setFont(font1)
        self.label_3.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_4.addWidget(self.label_3)

        self.zcomponent_flange1 = QComboBox(self.tab_2)
        self.zcomponent_flange1.addItem("")
        self.zcomponent_flange1.addItem("")
        self.zcomponent_flange1.setObjectName(u"zcomponent_flange1")
        self.zcomponent_flange1.setMinimumSize(QSize(100, 50))
        self.zcomponent_flange1.setFont(font1)

        self.horizontalLayout_4.addWidget(self.zcomponent_flange1)


        self.verticalLayout_2.addLayout(self.horizontalLayout_4)


        self.horizontalLayout_8.addLayout(self.verticalLayout_2)

        self.verticalLayout_3 = QVBoxLayout()
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.label_8 = QLabel(self.tab_2)
        self.label_8.setObjectName(u"label_8")
        self.label_8.setMinimumSize(QSize(400, 100))
        self.label_8.setFont(font2)
        self.label_8.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_3.addWidget(self.label_8)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_4 = QLabel(self.tab_2)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setMinimumSize(QSize(100, 50))
        self.label_4.setFont(font1)
        self.label_4.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_5.addWidget(self.label_4)

        self.xcomponent_flange2 = QComboBox(self.tab_2)
        self.xcomponent_flange2.addItem("")
        self.xcomponent_flange2.addItem("")
        self.xcomponent_flange2.setObjectName(u"xcomponent_flange2")
        self.xcomponent_flange2.setMinimumSize(QSize(100, 50))
        self.xcomponent_flange2.setFont(font1)

        self.horizontalLayout_5.addWidget(self.xcomponent_flange2)


        self.verticalLayout_3.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.label_6 = QLabel(self.tab_2)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setMinimumSize(QSize(100, 50))
        self.label_6.setFont(font1)
        self.label_6.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_6.addWidget(self.label_6)

        self.ycomponent_flange2 = QComboBox(self.tab_2)
        self.ycomponent_flange2.addItem("")
        self.ycomponent_flange2.addItem("")
        self.ycomponent_flange2.setObjectName(u"ycomponent_flange2")
        self.ycomponent_flange2.setMinimumSize(QSize(100, 50))
        self.ycomponent_flange2.setFont(font1)

        self.horizontalLayout_6.addWidget(self.ycomponent_flange2)


        self.verticalLayout_3.addLayout(self.horizontalLayout_6)

        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.label_5 = QLabel(self.tab_2)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setMinimumSize(QSize(100, 50))
        self.label_5.setFont(font1)
        self.label_5.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_7.addWidget(self.label_5)

        self.zcomponent_flange2 = QComboBox(self.tab_2)
        self.zcomponent_flange2.addItem("")
        self.zcomponent_flange2.addItem("")
        self.zcomponent_flange2.setObjectName(u"zcomponent_flange2")
        self.zcomponent_flange2.setMinimumSize(QSize(100, 50))
        self.zcomponent_flange2.setFont(font1)

        self.horizontalLayout_7.addWidget(self.zcomponent_flange2)


        self.verticalLayout_3.addLayout(self.horizontalLayout_7)


        self.horizontalLayout_8.addLayout(self.verticalLayout_3)

        self.tabWidget.addTab(self.tab_2, "")
        self.tab_3 = QWidget()
        self.tab_3.setObjectName(u"tab_3")
        self.horizontalLayout_23 = QHBoxLayout(self.tab_3)
        self.horizontalLayout_23.setObjectName(u"horizontalLayout_23")
        self.verticalLayout_8 = QVBoxLayout()
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.label_11 = QLabel(self.tab_3)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setMinimumSize(QSize(400, 100))
        self.label_11.setFont(font2)
        self.label_11.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_8.addWidget(self.label_11)

        self.horizontalLayout_21 = QHBoxLayout()
        self.horizontalLayout_21.setObjectName(u"horizontalLayout_21")
        self.verticalLayout_4 = QVBoxLayout()
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.label_12 = QLabel(self.tab_3)
        self.label_12.setObjectName(u"label_12")
        self.label_12.setMinimumSize(QSize(100, 50))
        self.label_12.setFont(font1)
        self.label_12.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_9.addWidget(self.label_12)

        self.xcomponent_bearing1 = QComboBox(self.tab_3)
        self.xcomponent_bearing1.addItem("")
        self.xcomponent_bearing1.addItem("")
        self.xcomponent_bearing1.setObjectName(u"xcomponent_bearing1")
        self.xcomponent_bearing1.setMinimumSize(QSize(125, 50))
        self.xcomponent_bearing1.setFont(font1)

        self.horizontalLayout_9.addWidget(self.xcomponent_bearing1)


        self.verticalLayout_4.addLayout(self.horizontalLayout_9)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.label_10 = QLabel(self.tab_3)
        self.label_10.setObjectName(u"label_10")
        self.label_10.setMinimumSize(QSize(100, 50))
        self.label_10.setFont(font1)
        self.label_10.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_10.addWidget(self.label_10)

        self.ycomponent_bearing1 = QComboBox(self.tab_3)
        self.ycomponent_bearing1.addItem("")
        self.ycomponent_bearing1.addItem("")
        self.ycomponent_bearing1.setObjectName(u"ycomponent_bearing1")
        self.ycomponent_bearing1.setMinimumSize(QSize(125, 50))
        self.ycomponent_bearing1.setFont(font1)

        self.horizontalLayout_10.addWidget(self.ycomponent_bearing1)


        self.verticalLayout_4.addLayout(self.horizontalLayout_10)

        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.label_9 = QLabel(self.tab_3)
        self.label_9.setObjectName(u"label_9")
        self.label_9.setMinimumSize(QSize(100, 50))
        self.label_9.setFont(font1)
        self.label_9.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_11.addWidget(self.label_9)

        self.zcomponent_bearing1 = QComboBox(self.tab_3)
        self.zcomponent_bearing1.addItem("")
        self.zcomponent_bearing1.addItem("")
        self.zcomponent_bearing1.setObjectName(u"zcomponent_bearing1")
        self.zcomponent_bearing1.setMinimumSize(QSize(125, 50))
        self.zcomponent_bearing1.setFont(font1)

        self.horizontalLayout_11.addWidget(self.zcomponent_bearing1)


        self.verticalLayout_4.addLayout(self.horizontalLayout_11)


        self.horizontalLayout_21.addLayout(self.verticalLayout_4)

        self.verticalLayout_5 = QVBoxLayout()
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.label_13 = QLabel(self.tab_3)
        self.label_13.setObjectName(u"label_13")
        self.label_13.setMinimumSize(QSize(100, 50))
        self.label_13.setFont(font1)
        self.label_13.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_12.addWidget(self.label_13)

        self.xrotation_bearing1 = QComboBox(self.tab_3)
        self.xrotation_bearing1.addItem("")
        self.xrotation_bearing1.addItem("")
        self.xrotation_bearing1.setObjectName(u"xrotation_bearing1")
        self.xrotation_bearing1.setMinimumSize(QSize(125, 50))
        self.xrotation_bearing1.setFont(font1)

        self.horizontalLayout_12.addWidget(self.xrotation_bearing1)


        self.verticalLayout_5.addLayout(self.horizontalLayout_12)

        self.horizontalLayout_13 = QHBoxLayout()
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.label_14 = QLabel(self.tab_3)
        self.label_14.setObjectName(u"label_14")
        self.label_14.setMinimumSize(QSize(100, 50))
        self.label_14.setFont(font1)
        self.label_14.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_13.addWidget(self.label_14)

        self.yrotation_bearing1 = QComboBox(self.tab_3)
        self.yrotation_bearing1.addItem("")
        self.yrotation_bearing1.addItem("")
        self.yrotation_bearing1.setObjectName(u"yrotation_bearing1")
        self.yrotation_bearing1.setMinimumSize(QSize(125, 50))
        self.yrotation_bearing1.setFont(font1)

        self.horizontalLayout_13.addWidget(self.yrotation_bearing1)


        self.verticalLayout_5.addLayout(self.horizontalLayout_13)

        self.horizontalLayout_14 = QHBoxLayout()
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.label_15 = QLabel(self.tab_3)
        self.label_15.setObjectName(u"label_15")
        self.label_15.setMinimumSize(QSize(100, 50))
        self.label_15.setFont(font1)
        self.label_15.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_14.addWidget(self.label_15)

        self.zrotation_bearing1 = QComboBox(self.tab_3)
        self.zrotation_bearing1.addItem("")
        self.zrotation_bearing1.addItem("")
        self.zrotation_bearing1.setObjectName(u"zrotation_bearing1")
        self.zrotation_bearing1.setMinimumSize(QSize(125, 50))
        self.zrotation_bearing1.setFont(font1)

        self.horizontalLayout_14.addWidget(self.zrotation_bearing1)


        self.verticalLayout_5.addLayout(self.horizontalLayout_14)


        self.horizontalLayout_21.addLayout(self.verticalLayout_5)


        self.verticalLayout_8.addLayout(self.horizontalLayout_21)


        self.horizontalLayout_23.addLayout(self.verticalLayout_8)

        self.verticalLayout_9 = QVBoxLayout()
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.label_16 = QLabel(self.tab_3)
        self.label_16.setObjectName(u"label_16")
        self.label_16.setMinimumSize(QSize(400, 100))
        self.label_16.setFont(font2)
        self.label_16.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_9.addWidget(self.label_16)

        self.horizontalLayout_22 = QHBoxLayout()
        self.horizontalLayout_22.setObjectName(u"horizontalLayout_22")
        self.verticalLayout_6 = QVBoxLayout()
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_15 = QHBoxLayout()
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        self.label_19 = QLabel(self.tab_3)
        self.label_19.setObjectName(u"label_19")
        self.label_19.setMinimumSize(QSize(100, 50))
        self.label_19.setFont(font1)
        self.label_19.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_15.addWidget(self.label_19)

        self.xcomponent_bearing2 = QComboBox(self.tab_3)
        self.xcomponent_bearing2.addItem("")
        self.xcomponent_bearing2.addItem("")
        self.xcomponent_bearing2.setObjectName(u"xcomponent_bearing2")
        self.xcomponent_bearing2.setMinimumSize(QSize(125, 50))
        self.xcomponent_bearing2.setFont(font1)

        self.horizontalLayout_15.addWidget(self.xcomponent_bearing2)


        self.verticalLayout_6.addLayout(self.horizontalLayout_15)

        self.horizontalLayout_18 = QHBoxLayout()
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.label_17 = QLabel(self.tab_3)
        self.label_17.setObjectName(u"label_17")
        self.label_17.setMinimumSize(QSize(100, 50))
        self.label_17.setFont(font1)
        self.label_17.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_18.addWidget(self.label_17)

        self.ycomponent_bearing2 = QComboBox(self.tab_3)
        self.ycomponent_bearing2.addItem("")
        self.ycomponent_bearing2.addItem("")
        self.ycomponent_bearing2.setObjectName(u"ycomponent_bearing2")
        self.ycomponent_bearing2.setMinimumSize(QSize(125, 50))
        self.ycomponent_bearing2.setFont(font1)

        self.horizontalLayout_18.addWidget(self.ycomponent_bearing2)


        self.verticalLayout_6.addLayout(self.horizontalLayout_18)

        self.horizontalLayout_19 = QHBoxLayout()
        self.horizontalLayout_19.setObjectName(u"horizontalLayout_19")
        self.label_22 = QLabel(self.tab_3)
        self.label_22.setObjectName(u"label_22")
        self.label_22.setMinimumSize(QSize(100, 50))
        self.label_22.setFont(font1)
        self.label_22.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_19.addWidget(self.label_22)

        self.zcomponent_bearing2 = QComboBox(self.tab_3)
        self.zcomponent_bearing2.addItem("")
        self.zcomponent_bearing2.addItem("")
        self.zcomponent_bearing2.setObjectName(u"zcomponent_bearing2")
        self.zcomponent_bearing2.setMinimumSize(QSize(125, 50))
        self.zcomponent_bearing2.setFont(font1)

        self.horizontalLayout_19.addWidget(self.zcomponent_bearing2)


        self.verticalLayout_6.addLayout(self.horizontalLayout_19)


        self.horizontalLayout_22.addLayout(self.verticalLayout_6)

        self.verticalLayout_7 = QVBoxLayout()
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.horizontalLayout_16 = QHBoxLayout()
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.label_20 = QLabel(self.tab_3)
        self.label_20.setObjectName(u"label_20")
        self.label_20.setMinimumSize(QSize(100, 50))
        self.label_20.setFont(font1)
        self.label_20.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_16.addWidget(self.label_20)

        self.xrotation_bearing2 = QComboBox(self.tab_3)
        self.xrotation_bearing2.addItem("")
        self.xrotation_bearing2.addItem("")
        self.xrotation_bearing2.setObjectName(u"xrotation_bearing2")
        self.xrotation_bearing2.setMinimumSize(QSize(125, 50))
        self.xrotation_bearing2.setFont(font1)

        self.horizontalLayout_16.addWidget(self.xrotation_bearing2)


        self.verticalLayout_7.addLayout(self.horizontalLayout_16)

        self.horizontalLayout_17 = QHBoxLayout()
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.label_21 = QLabel(self.tab_3)
        self.label_21.setObjectName(u"label_21")
        self.label_21.setMinimumSize(QSize(100, 50))
        self.label_21.setFont(font1)
        self.label_21.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_17.addWidget(self.label_21)

        self.yrotation_bearing2 = QComboBox(self.tab_3)
        self.yrotation_bearing2.addItem("")
        self.yrotation_bearing2.addItem("")
        self.yrotation_bearing2.setObjectName(u"yrotation_bearing2")
        self.yrotation_bearing2.setMinimumSize(QSize(125, 50))
        self.yrotation_bearing2.setFont(font1)

        self.horizontalLayout_17.addWidget(self.yrotation_bearing2)


        self.verticalLayout_7.addLayout(self.horizontalLayout_17)

        self.horizontalLayout_20 = QHBoxLayout()
        self.horizontalLayout_20.setObjectName(u"horizontalLayout_20")
        self.label_18 = QLabel(self.tab_3)
        self.label_18.setObjectName(u"label_18")
        self.label_18.setMinimumSize(QSize(100, 50))
        self.label_18.setFont(font1)
        self.label_18.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_20.addWidget(self.label_18)

        self.zrotation_bearing2 = QComboBox(self.tab_3)
        self.zrotation_bearing2.addItem("")
        self.zrotation_bearing2.addItem("")
        self.zrotation_bearing2.setObjectName(u"zrotation_bearing2")
        self.zrotation_bearing2.setMinimumSize(QSize(125, 50))
        self.zrotation_bearing2.setFont(font1)

        self.horizontalLayout_20.addWidget(self.zrotation_bearing2)


        self.verticalLayout_7.addLayout(self.horizontalLayout_20)


        self.horizontalLayout_22.addLayout(self.verticalLayout_7)


        self.verticalLayout_9.addLayout(self.horizontalLayout_22)


        self.horizontalLayout_23.addLayout(self.verticalLayout_9)

        self.tabWidget.addTab(self.tab_3, "")
        self.tab_4 = QWidget()
        self.tab_4.setObjectName(u"tab_4")
        self.verticalLayout_11 = QVBoxLayout(self.tab_4)
        self.verticalLayout_11.setObjectName(u"verticalLayout_11")
        self.horizontalLayout_25 = QHBoxLayout()
        self.horizontalLayout_25.setObjectName(u"horizontalLayout_25")
        self.label_24 = QLabel(self.tab_4)
        self.label_24.setObjectName(u"label_24")
        self.label_24.setMinimumSize(QSize(500, 50))
        font3 = QFont()
        font3.setFamilies([u"Times New Roman"])
        font3.setPointSize(25)
        self.label_24.setFont(font3)
        self.label_24.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_25.addWidget(self.label_24)

        self.lineEdit_rotation_speed = QLineEdit(self.tab_4)
        self.lineEdit_rotation_speed.setObjectName(u"lineEdit_rotation_speed")
        self.lineEdit_rotation_speed.setMinimumSize(QSize(400, 50))
        self.lineEdit_rotation_speed.setFont(font3)

        self.horizontalLayout_25.addWidget(self.lineEdit_rotation_speed)


        self.verticalLayout_11.addLayout(self.horizontalLayout_25)

        self.horizontalLayout_26 = QHBoxLayout()
        self.horizontalLayout_26.setObjectName(u"horizontalLayout_26")
        self.label_23 = QLabel(self.tab_4)
        self.label_23.setObjectName(u"label_23")
        self.label_23.setMinimumSize(QSize(100, 50))
        self.label_23.setFont(font3)
        self.label_23.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_26.addWidget(self.label_23)

        self.rotation_axis = QComboBox(self.tab_4)
        self.rotation_axis.addItem("")
        self.rotation_axis.addItem("")
        self.rotation_axis.addItem("")
        self.rotation_axis.setObjectName(u"rotation_axis")
        self.rotation_axis.setMinimumSize(QSize(400, 50))
        self.rotation_axis.setFont(font3)
        self.rotation_axis.setLayoutDirection(Qt.LayoutDirection.LeftToRight)

        self.horizontalLayout_26.addWidget(self.rotation_axis)


        self.verticalLayout_11.addLayout(self.horizontalLayout_26)

        self.tabWidget.addTab(self.tab_4, "")
        self.tab_5 = QWidget()
        self.tab_5.setObjectName(u"tab_5")

        # 创建监控点管理的主布局
        self.monitor_main_layout = QGridLayout(self.tab_5)
        self.monitor_main_layout.setObjectName(u"monitor_main_layout")
        self.monitor_main_layout.setContentsMargins(15, 15, 15, 15)
        self.monitor_main_layout.setSpacing(15)

        # 创建左侧监控点创建与导入组
        self.monitor_create_import_group = QGroupBox(self.tab_5)
        self.monitor_create_import_group.setObjectName(u"monitor_create_import_group")
        self.monitor_create_import_group.setMinimumSize(QSize(400, 450))
        self.monitor_create_import_group.setMaximumSize(QSize(450, 16777215))

        # 左侧组布局
        self.monitor_left_layout = QVBoxLayout(self.monitor_create_import_group)
        self.monitor_left_layout.setObjectName(u"monitor_left_layout")
        self.monitor_left_layout.setSpacing(15)

        # 手动单点创建子组
        self.monitor_manual_create_group = QGroupBox(self.monitor_create_import_group)
        self.monitor_manual_create_group.setObjectName(u"monitor_manual_create_group")

        # 手动创建布局
        self.monitor_manual_layout = QVBoxLayout(self.monitor_manual_create_group)
        self.monitor_manual_layout.setObjectName(u"monitor_manual_layout")
        self.monitor_manual_layout.setSpacing(8)

        # 点位名称输入
        self.monitor_name_label = QLabel(self.monitor_manual_create_group)
        self.monitor_name_label.setObjectName(u"monitor_name_label")
        self.monitor_manual_layout.addWidget(self.monitor_name_label)

        self.monitor_name_input = QLineEdit(self.monitor_manual_create_group)
        self.monitor_name_input.setObjectName(u"monitor_name_input")
        self.monitor_name_input.setMinimumHeight(25)
        self.monitor_manual_layout.addWidget(self.monitor_name_input)

        # X坐标输入
        self.monitor_x_label = QLabel(self.monitor_manual_create_group)
        self.monitor_x_label.setObjectName(u"monitor_x_label")
        self.monitor_manual_layout.addWidget(self.monitor_x_label)

        self.monitor_x_input = QLineEdit(self.monitor_manual_create_group)
        self.monitor_x_input.setObjectName(u"monitor_x_input")
        self.monitor_x_input.setMinimumHeight(25)
        self.monitor_manual_layout.addWidget(self.monitor_x_input)

        # Y坐标输入
        self.monitor_y_label = QLabel(self.monitor_manual_create_group)
        self.monitor_y_label.setObjectName(u"monitor_y_label")
        self.monitor_manual_layout.addWidget(self.monitor_y_label)

        self.monitor_y_input = QLineEdit(self.monitor_manual_create_group)
        self.monitor_y_input.setObjectName(u"monitor_y_input")
        self.monitor_y_input.setMinimumHeight(25)
        self.monitor_manual_layout.addWidget(self.monitor_y_input)

        # Z坐标输入
        self.monitor_z_label = QLabel(self.monitor_manual_create_group)
        self.monitor_z_label.setObjectName(u"monitor_z_label")
        self.monitor_manual_layout.addWidget(self.monitor_z_label)

        self.monitor_z_input = QLineEdit(self.monitor_manual_create_group)
        self.monitor_z_input.setObjectName(u"monitor_z_input")
        self.monitor_z_input.setMinimumHeight(25)
        self.monitor_manual_layout.addWidget(self.monitor_z_input)

        # 添加点位按钮
        self.monitor_add_point_btn = QPushButton(self.monitor_manual_create_group)
        self.monitor_add_point_btn.setObjectName(u"monitor_add_point_btn")
        self.monitor_add_point_btn.setMinimumHeight(35)
        monitor_button_font = QFont()
        monitor_button_font.setFamilies([u"\u5b8b\u4f53"])
        monitor_button_font.setPointSize(12)
        monitor_button_font.setBold(True)
        self.monitor_add_point_btn.setFont(monitor_button_font)
        self.monitor_add_point_btn.setStyleSheet("""
            QPushButton {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #3367d6;
            }
            QPushButton:pressed {
                background-color: #2851a3;
            }
        """)
        self.monitor_manual_layout.addWidget(self.monitor_add_point_btn)

        self.monitor_left_layout.addWidget(self.monitor_manual_create_group)

        # 文件批量导入子组
        self.monitor_file_import_group = QGroupBox(self.monitor_create_import_group)
        self.monitor_file_import_group.setObjectName(u"monitor_file_import_group")

        # 文件导入布局
        self.monitor_import_layout = QVBoxLayout(self.monitor_file_import_group)
        self.monitor_import_layout.setObjectName(u"monitor_import_layout")
        self.monitor_import_layout.setSpacing(8)

        # 文件导入按钮
        self.monitor_import_file_btn = QPushButton(self.monitor_file_import_group)
        self.monitor_import_file_btn.setObjectName(u"monitor_import_file_btn")
        self.monitor_import_file_btn.setMinimumHeight(35)
        self.monitor_import_file_btn.setFont(monitor_button_font)
        self.monitor_import_file_btn.setStyleSheet("""
            QPushButton {
                background-color: #34a853;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #2d8f47;
            }
            QPushButton:pressed {
                background-color: #1e5f31;
            }
        """)
        self.monitor_import_layout.addWidget(self.monitor_import_file_btn)

        # 文件格式说明
        self.monitor_format_label = QLabel(self.monitor_file_import_group)
        self.monitor_format_label.setObjectName(u"monitor_format_label")
        self.monitor_format_label.setWordWrap(True)
        self.monitor_format_label.setStyleSheet("color: #666; font-size: 10px;")
        self.monitor_import_layout.addWidget(self.monitor_format_label)

        self.monitor_left_layout.addWidget(self.monitor_file_import_group)

        # 添加弹性空间
        monitor_spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.monitor_left_layout.addItem(monitor_spacer)

        # 将左侧组添加到主布局
        self.monitor_main_layout.addWidget(self.monitor_create_import_group, 0, 0, 1, 1)

        # 创建右侧监控点列表组
        self.monitor_points_list_group = QGroupBox(self.tab_5)
        self.monitor_points_list_group.setObjectName(u"monitor_points_list_group")
        self.monitor_points_list_group.setMinimumSize(QSize(500, 350))

        # 右侧组布局
        self.monitor_right_layout = QVBoxLayout(self.monitor_points_list_group)
        self.monitor_right_layout.setObjectName(u"monitor_right_layout")
        self.monitor_right_layout.setSpacing(10)

        # 监控点表格
        self.monitor_points_table = QTableWidget(self.monitor_points_list_group)
        self.monitor_points_table.setObjectName(u"monitor_points_table")
        self.monitor_points_table.setColumnCount(6)
        self.monitor_points_table.setAlternatingRowColors(True)
        self.monitor_points_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.monitor_points_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)
        self.monitor_points_table.setSortingEnabled(True)

        # 设置表格头
        monitor_headers = [u"序号", u"点位名称", u"X坐标", u"Y坐标", u"Z坐标", u"操作"]
        self.monitor_points_table.setHorizontalHeaderLabels(monitor_headers)

        # 设置列宽
        monitor_header = self.monitor_points_table.horizontalHeader()
        monitor_header.setStretchLastSection(False)
        monitor_header.resizeSection(0, 50)   # 序号
        monitor_header.resizeSection(1, 120)  # 点位名称
        monitor_header.resizeSection(2, 80)   # X坐标
        monitor_header.resizeSection(3, 80)   # Y坐标
        monitor_header.resizeSection(4, 80)   # Z坐标
        monitor_header.resizeSection(5, 70)   # 操作

        self.monitor_right_layout.addWidget(self.monitor_points_table)

        # 表格操作按钮布局
        self.monitor_table_buttons_layout = QHBoxLayout()
        self.monitor_table_buttons_layout.setObjectName(u"monitor_table_buttons_layout")

        # 添加弹性空间
        monitor_table_spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.monitor_table_buttons_layout.addItem(monitor_table_spacer)

        # 清空全部按钮
        self.monitor_clear_all_btn = QPushButton(self.monitor_points_list_group)
        self.monitor_clear_all_btn.setObjectName(u"monitor_clear_all_btn")
        self.monitor_clear_all_btn.setMinimumHeight(30)
        self.monitor_clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ea4335;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
            }
            QPushButton:hover {
                background-color: #d33b2c;
            }
            QPushButton:pressed {
                background-color: #b52d20;
            }
        """)
        self.monitor_table_buttons_layout.addWidget(self.monitor_clear_all_btn)

        self.monitor_right_layout.addLayout(self.monitor_table_buttons_layout)

        # 将右侧组添加到主布局
        self.monitor_main_layout.addWidget(self.monitor_points_list_group, 0, 1, 1, 1)

        self.tabWidget.addTab(self.tab_5, "")

        self.verticalLayout.addWidget(self.tabWidget)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.push_finish = QPushButton(self.centralwidget)
        self.push_finish.setObjectName(u"push_finish")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.push_finish.sizePolicy().hasHeightForWidth())
        self.push_finish.setSizePolicy(sizePolicy)
        self.push_finish.setMinimumSize(QSize(200, 65))
        self.push_finish.setFont(font2)

        self.horizontalLayout.addWidget(self.push_finish)

        self.push_analysisui = QPushButton(self.centralwidget)
        self.push_analysisui.setObjectName(u"push_analysisui")
        sizePolicy.setHeightForWidth(self.push_analysisui.sizePolicy().hasHeightForWidth())
        self.push_analysisui.setSizePolicy(sizePolicy)
        self.push_analysisui.setMinimumSize(QSize(250, 65))
        self.push_analysisui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_analysisui)

        self.push_resultui = QPushButton(self.centralwidget)
        self.push_resultui.setObjectName(u"push_resultui")
        sizePolicy.setHeightForWidth(self.push_resultui.sizePolicy().hasHeightForWidth())
        self.push_resultui.setSizePolicy(sizePolicy)
        self.push_resultui.setMinimumSize(QSize(250, 65))
        self.push_resultui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_resultui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_mainui)


        self.verticalLayout.addLayout(self.horizontalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        self.tabWidget.setCurrentIndex(4)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.pushButton_force.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u529b\u6587\u4ef6\u5939", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"Tab 1", None))
        self.label_7.setText(QCoreApplication.translate("MainWindow", u"\u8fdb\u53e3\u6cd5\u5170", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"X Component", None))
        self.xcomponent_flange1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.xcomponent_flange1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_2.setText(QCoreApplication.translate("MainWindow", u"Y Component", None))
        self.ycomponent_flange1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.ycomponent_flange1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_3.setText(QCoreApplication.translate("MainWindow", u"Z Component", None))
        self.zcomponent_flange1.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.zcomponent_flange1.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.label_8.setText(QCoreApplication.translate("MainWindow", u"\u51fa\u53e3\u6cd5\u5170", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"X Component", None))
        self.xcomponent_flange2.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.xcomponent_flange2.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.label_6.setText(QCoreApplication.translate("MainWindow", u"Y Component", None))
        self.ycomponent_flange2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.ycomponent_flange2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_5.setText(QCoreApplication.translate("MainWindow", u"Z Component", None))
        self.zcomponent_flange2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.zcomponent_flange2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), QCoreApplication.translate("MainWindow", u"Tab 2", None))
        self.label_11.setText(QCoreApplication.translate("MainWindow", u"\u8f74\u627f1", None))
        self.label_12.setText(QCoreApplication.translate("MainWindow", u"X Component", None))
        self.xcomponent_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.xcomponent_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_10.setText(QCoreApplication.translate("MainWindow", u"Y Component", None))
        self.ycomponent_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.ycomponent_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_9.setText(QCoreApplication.translate("MainWindow", u"Z Component", None))
        self.zcomponent_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.zcomponent_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.label_13.setText(QCoreApplication.translate("MainWindow", u"Rotation X", None))
        self.xrotation_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.xrotation_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_14.setText(QCoreApplication.translate("MainWindow", u"Rotation Y", None))
        self.yrotation_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.yrotation_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_15.setText(QCoreApplication.translate("MainWindow", u"Rotation Z", None))
        self.zrotation_bearing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.zrotation_bearing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.label_16.setText(QCoreApplication.translate("MainWindow", u"\u8f74\u627f2", None))
        self.label_19.setText(QCoreApplication.translate("MainWindow", u"X Component", None))
        self.xcomponent_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.xcomponent_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_17.setText(QCoreApplication.translate("MainWindow", u"Y Component", None))
        self.ycomponent_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.ycomponent_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_22.setText(QCoreApplication.translate("MainWindow", u"Z Component", None))
        self.zcomponent_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.zcomponent_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.label_20.setText(QCoreApplication.translate("MainWindow", u"Rotation X", None))
        self.xrotation_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.xrotation_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_21.setText(QCoreApplication.translate("MainWindow", u"Rotation Y", None))
        self.yrotation_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Free", None))
        self.yrotation_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Constant", None))

        self.label_18.setText(QCoreApplication.translate("MainWindow", u"Rotation Z", None))
        self.zrotation_bearing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Constant", None))
        self.zrotation_bearing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Free", None))

        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_3), QCoreApplication.translate("MainWindow", u"\u9875", None))
        self.label_24.setText(QCoreApplication.translate("MainWindow", u"Rotation speed", None))
        self.label_23.setText(QCoreApplication.translate("MainWindow", u"Rotation axis", None))
        self.rotation_axis.setItemText(0, QCoreApplication.translate("MainWindow", u"z", None))
        self.rotation_axis.setItemText(1, QCoreApplication.translate("MainWindow", u"y", None))
        self.rotation_axis.setItemText(2, QCoreApplication.translate("MainWindow", u"x", None))

        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_4), QCoreApplication.translate("MainWindow", u"转速设置", None))

        # 监控点管理tab_5的文本翻译
        self.monitor_create_import_group.setTitle(QCoreApplication.translate("MainWindow", u"监控点创建与导入", None))
        self.monitor_manual_create_group.setTitle(QCoreApplication.translate("MainWindow", u"手动单点创建", None))

        self.monitor_name_label.setText(QCoreApplication.translate("MainWindow", u"点位名称:", None))
        self.monitor_name_input.setPlaceholderText(QCoreApplication.translate("MainWindow", u"例如：监控点A", None))

        self.monitor_x_label.setText(QCoreApplication.translate("MainWindow", u"X坐标:", None))
        self.monitor_x_input.setPlaceholderText(QCoreApplication.translate("MainWindow", u"输入X坐标值", None))

        self.monitor_y_label.setText(QCoreApplication.translate("MainWindow", u"Y坐标:", None))
        self.monitor_y_input.setPlaceholderText(QCoreApplication.translate("MainWindow", u"输入Y坐标值", None))

        self.monitor_z_label.setText(QCoreApplication.translate("MainWindow", u"Z坐标:", None))
        self.monitor_z_input.setPlaceholderText(QCoreApplication.translate("MainWindow", u"输入Z坐标值", None))

        self.monitor_add_point_btn.setText(QCoreApplication.translate("MainWindow", u"➕ 添加点位", None))

        # 文件导入组
        self.monitor_file_import_group.setTitle(QCoreApplication.translate("MainWindow", u"从文件批量导入", None))
        self.monitor_import_file_btn.setText(QCoreApplication.translate("MainWindow", u"📁 选择文件导入", None))
        self.monitor_format_label.setText(QCoreApplication.translate("MainWindow", u"支持 .csv, .txt, .xlsx 格式\n每行格式：名称,X,Y,Z 或 X,Y,Z", None))

        # 右侧组标题
        self.monitor_points_list_group.setTitle(QCoreApplication.translate("MainWindow", u"已创建监控点列表 (共 0 个)", None))
        self.monitor_clear_all_btn.setText(QCoreApplication.translate("MainWindow", u"清空全部", None))

        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_5), QCoreApplication.translate("MainWindow", u"监控点管理", None))
        self.push_finish.setText(QCoreApplication.translate("MainWindow", u"\u5b8c\u6210\u8bbe\u7f6e", None))
        self.push_analysisui.setText(QCoreApplication.translate("MainWindow", u"上一步(分析设置)", None))
        self.push_resultui.setText(QCoreApplication.translate("MainWindow", u"下一步(网格无关性验证)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

