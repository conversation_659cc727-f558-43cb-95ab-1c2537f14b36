"""
资源管理器模块

此模块负责管理项目中的资源文件，包括：
1. 临时文件的创建和清理
2. 脚本文件的版本控制
3. 工作目录的管理
4. 输出文件的组织

作者: [作者名]
日期: [日期]
"""

import os
import shutil
import json
import logging
from datetime import datetime
from typing import Optional, Dict, List
from pathlib import Path


class ResourceManager:
    """资源管理器类
    
    负责管理所有资源文件，包括临时文件、脚本文件、输出文件等。
    实现了单例模式，确保全局只有一个资源管理器实例。
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ResourceManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return

        # 获取正确的基础目录和工作目录，支持打包后的环境
        self.base_dir = self._get_base_directory()  # 用于读取资源文件
        self.work_dir = self._get_work_directory()  # 用于输出文件

        # 临时文件和输出文件使用工作目录
        self.temp_dir = os.path.join(self.work_dir, "temp")
        self.output_dir = os.path.join(self.work_dir, "output")

        # 脚本和JSON文件使用基础目录（只读）
        self.script_dir = os.path.join(self.base_dir, "script")
        self.json_dir = os.path.join(self.base_dir, "json")
        
        # 初始化脚本版本管理
        self.script_versions: Dict[str, List[str]] = {}
        
        # 确保可写目录存在（不创建只读的资源目录）
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置日志
        self._setup_logging()
        
        self._initialized = True

    def _get_base_directory(self) -> str:
        """获取正确的基础目录，支持打包后的环境"""
        import sys

        # 检查是否在PyInstaller打包环境中
        if hasattr(sys, '_MEIPASS'):
            # 在打包环境中，资源文件在_MEIPASS目录下
            return sys._MEIPASS
        else:
            # 在开发环境中，使用当前工作目录
            return os.getcwd()

    def _get_work_directory(self) -> str:
        """获取工作目录，用于输出文件等可写操作"""
        import sys

        # 检查是否在PyInstaller打包环境中
        if hasattr(sys, '_MEIPASS'):
            # 在打包环境中，工作目录应该是可执行文件所在的目录
            return os.path.dirname(sys.executable)
        else:
            # 在开发环境中，使用当前工作目录
            return os.getcwd()

    def _setup_logging(self) -> None:
        """设置日志系统"""
        self.logger = logging.getLogger('ResourceManager')
        self.logger.setLevel(logging.INFO)

        # 确保日志目录存在
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)

        # 创建日志文件处理器
        log_file = log_dir / 'resource_manager.log'
        handler = logging.FileHandler(str(log_file))
        handler.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)

    def get_resource_path(self, *path_parts: str) -> str:
        """获取资源文件路径，支持PyInstaller打包环境

        Args:
            *path_parts: 路径组件

        Returns:
            str: 完整的资源文件路径
        """
        return os.path.join(self.base_dir, *path_parts)

    def get_work_path(self, *path_parts: str) -> str:
        """获取工作文件路径

        Args:
            *path_parts: 路径组件

        Returns:
            str: 完整的工作文件路径
        """
        work_path = os.path.join(self.work_dir, *path_parts)

        # 确保目标目录存在
        target_dir = os.path.dirname(work_path)
        if not os.path.exists(target_dir):
            try:
                os.makedirs(target_dir, exist_ok=True)
                self.logger.info(f"创建工作目录: {target_dir}")
            except Exception as e:
                self.logger.error(f"创建工作目录失败: {target_dir}, 错误: {e}")

        return work_path

    def initialize(self, work_dir: str) -> None:
        """初始化资源管理器

        Args:
            work_dir: 工作目录路径
        """
        # 更新工作目录相关路径，但保持资源目录不变
        self.work_dir = work_dir
        self.temp_dir = os.path.join(work_dir, 'temp')
        self.output_dir = os.path.join(work_dir, 'output')

        # 注意：不重新设置script_dir和json_dir，保持它们指向资源目录
        # self.script_dir 和 self.json_dir 在构造函数中已正确设置为base_dir下的路径

        # 创建可写目录（不创建只读的资源目录）
        work_script_dir = os.path.join(work_dir, 'script')
        for dir_path in [self.temp_dir, self.output_dir, work_script_dir]:
            os.makedirs(dir_path, exist_ok=True)

        self.logger.info(f"初始化工作目录: {work_dir}")
        self.logger.info(f"创建目录: temp={self.temp_dir}, output={self.output_dir}, script={work_script_dir}")

        # 如果需要在工作目录创建script目录用于输出脚本，使用不同的名称
        self.work_script_dir = os.path.join(work_dir, 'script')
        os.makedirs(self.work_script_dir, exist_ok=True)

        self.logger.info(f"资源管理器初始化完成，工作目录：{work_dir}")
        self.logger.info(f"资源目录 (script_dir): {self.script_dir}")
        self.logger.info(f"工作脚本目录 (work_script_dir): {self.work_script_dir}")
    
    def create_temp_file(self, prefix: str = '', suffix: str = '') -> str:
        """创建临时文件
        
        Args:
            prefix: 文件名前缀
            suffix: 文件扩展名
            
        Returns:
            临时文件的完整路径
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{prefix}_{timestamp}{suffix}"
        temp_file = os.path.join(self.temp_dir, filename)
        
        Path(temp_file).touch()
        self.logger.info(f"创建临时文件：{temp_file}")
        
        return temp_file
    
    def clean_temp_files(self, max_age_days: int = 7, max_age_hours: int = None, prefix: str = None, suffix: str = None, exclude_paths: List[str] = None) -> None:
        """清理临时文件
        
        Args:
            max_age_days: 文件保留的最大天数
            max_age_hours: 文件保留的最大小时数（优先于天数）
            prefix: 要清理的文件前缀，如果指定则只清理匹配前缀的文件
            suffix: 要清理的文件后缀，如果指定则只清理匹配后缀的文件
            exclude_paths: 不清理的文件路径列表
        """
        if not os.path.exists(self.temp_dir):
            return
            
        current_time = datetime.now()
        exclude_paths = exclude_paths or []
        
        for file_name in os.listdir(self.temp_dir):
            file_path = os.path.join(self.temp_dir, file_name)
            if not os.path.isfile(file_path):
                continue
                
            # 如果文件在排除列表中，则跳过
            if file_path in exclude_paths:
                continue
                
            # 如果指定了前缀，但文件不匹配，则跳过
            if prefix and not file_name.startswith(prefix):
                continue
                
            # 如果指定了后缀，但文件不匹配，则跳过
            if suffix and not file_name.endswith(suffix):
                continue
                
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            if max_age_hours is not None:
                # 按小时计算文件年龄
                age_hours = (current_time - file_time).total_seconds() / 3600
                should_delete = age_hours > max_age_hours
            else:
                # 按天数计算文件年龄
                age_days = (current_time - file_time).days
                should_delete = age_days > max_age_days
                
            if should_delete:
                try:
                    os.remove(file_path)
                    self.logger.info(f"删除临时文件：{file_path}")
                except Exception as e:
                    self.logger.error(f"删除文件失败：{file_path}, 错误：{e}")
    
    def create_script_version(self, script_name: str, content: str) -> str:
        """创建脚本的新版本

        Args:
            script_name: 脚本文件名
            content: 脚本内容

        Returns:
            新版本脚本的完整路径
        """
        # 生成版本号
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        version = f"v_{timestamp}"

        # 使用工作脚本目录而不是资源脚本目录
        script_dir = getattr(self, 'work_script_dir', self.script_dir)

        # 创建版本目录
        version_dir = os.path.join(script_dir, 'versions')
        os.makedirs(version_dir, exist_ok=True)

        # 创建新版本文件
        base_name, ext = os.path.splitext(script_name)
        version_file = os.path.join(version_dir, f"{base_name}_{version}{ext}")

        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 更新版本记录
        if script_name not in self.script_versions:
            self.script_versions[script_name] = []
        self.script_versions[script_name].append(version_file)
        
        # 更新当前版本 - 使用与版本文件相同的脚本目录
        current_file = os.path.join(script_dir, script_name)
        shutil.copy2(version_file, current_file)

        self.logger.info(f"创建脚本新版本：{version_file}")
        self.logger.info(f"复制到当前脚本文件：{current_file}")

        # 验证文件是否成功创建
        if os.path.exists(current_file):
            self.logger.info(f"当前脚本文件创建成功：{current_file}")
        else:
            self.logger.error(f"当前脚本文件创建失败：{current_file}")

        return current_file
    
    def get_script_versions(self, script_name: str) -> List[str]:
        """获取脚本的所有版本
        
        Args:
            script_name: 脚本文件名
            
        Returns:
            版本文件路径列表
        """
        return self.script_versions.get(script_name, [])
    
    def clean_old_versions(self, script_name: str, keep_versions: int = 5) -> None:
        """清理旧版本脚本

        Args:
            script_name: 脚本文件名
            keep_versions: 保留的版本数量
        """
        # 使用与create_script_version相同的脚本目录逻辑
        script_dir = getattr(self, 'work_script_dir', self.script_dir)
        version_dir = os.path.join(script_dir, 'versions')

        if not os.path.exists(version_dir):
            return

        # 查找所有版本文件
        base_name, ext = os.path.splitext(script_name)
        pattern = f"{base_name}_v_*{ext}"
        version_files = []

        for file_name in os.listdir(version_dir):
            if file_name.startswith(f"{base_name}_v_") and file_name.endswith(ext):
                version_files.append(os.path.join(version_dir, file_name))

        if len(version_files) <= keep_versions:
            return

        # 按时间排序，保留最新的版本
        version_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

        # 删除旧版本
        for version_file in version_files[keep_versions:]:
            try:
                os.remove(version_file)
                self.logger.info(f"删除旧版本脚本：{version_file}")
            except Exception as e:
                self.logger.error(f"删除文件失败：{version_file}, 错误：{e}")

        # 更新版本记录
        self.script_versions[script_name] = version_files[:keep_versions]
    
    def organize_output_files(self) -> None:
        """整理输出文件
        
        将输出文件按日期组织到子目录中
        """
        if not os.path.exists(self.output_dir):
            return
            
        current_date = datetime.now().strftime('%Y%m%d')
        date_dir = os.path.join(self.output_dir, current_date)
        os.makedirs(date_dir, exist_ok=True)
        
        # 移动输出文件到日期目录
        for file_name in os.listdir(self.output_dir):
            if file_name == current_date:
                continue
                
            file_path = os.path.join(self.output_dir, file_name)
            if not os.path.isfile(file_path):
                continue
                
            try:
                shutil.move(file_path, os.path.join(date_dir, file_name))
                self.logger.info(f"移动输出文件：{file_name} -> {date_dir}")
            except Exception as e:
                self.logger.error(f"移动文件失败：{file_path}, 错误：{e}")
    
    def cleanup(self) -> None:
        """清理所有资源
        
        在程序退出时调用，清理临时文件和整理输出文件。
        会清理所有临时文件，不考虑时间限制。
        """
        if os.path.exists(self.temp_dir):
            try:
                # 清理所有临时文件
                shutil.rmtree(self.temp_dir)
                os.makedirs(self.temp_dir)
                self.logger.info("清理所有临时文件")
            except Exception as e:
                self.logger.error(f"清理临时目录失败：{e}")
        
        self.organize_output_files()
        self.logger.info("资源清理完成")