"""
字体管理器模块

此模块提供界面字体的动态调整功能，包括：
1. 字体大小动态调整
2. 字体样式管理
3. 不同组件的字体配置
4. 字体设置持久化

作者: [作者名]
日期: [日期]
"""

import os
import json
import logging
from typing import Dict, Optional, Union
from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QFont
from PySide6.QtCore import QSettings


class FontManager:
    """字体管理器"""
    
    _instance: Optional['FontManager'] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FontManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.logger = logging.getLogger(__name__)
        self.config_path = "config/font_config.json"
        
        # 默认字体配置
        self.default_fonts = {
            'base_size': 9,
            'small_size': 8,
            'large_size': 11,
            'title_size': 12,
            'font_family': 'Microsoft YaHei UI',
            'components': {
                'QPushButton': 9,
                'QLabel': 9,
                'QLineEdit': 9,
                'QComboBox': 9,
                'QGroupBox': 9,
                'QTabWidget': 9,
                'QTreeWidget': 9,
                'QTableWidget': 9,
                'QTextEdit': 9,
                'QPlainTextEdit': 9,
                'QMenuBar': 9,
                'QMenu': 9,
                'QStatusBar': 8,
                'QToolTip': 8
            }
        }
        
        # 当前字体配置
        self.current_fonts = self.default_fonts.copy()
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
        
        # 加载配置
        self.load_font_config()
        
        self._initialized = True
    
    def load_font_config(self):
        """加载字体配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                
                # 更新当前配置
                self.current_fonts.update(saved_config)
                self.logger.info(f"字体配置已从 {self.config_path} 加载")
            else:
                # 保存默认配置
                self.save_font_config()
                self.logger.info(f"创建默认字体配置文件: {self.config_path}")
                
        except Exception as e:
            self.logger.error(f"加载字体配置失败: {e}")
            self.current_fonts = self.default_fonts.copy()
    
    def save_font_config(self):
        """保存字体配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.current_fonts, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"字体配置已保存到 {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"保存字体配置失败: {e}")
    
    def set_base_font_size(self, size: int):
        """设置基础字体大小"""
        if size < 6 or size > 24:
            self.logger.warning(f"字体大小 {size} 超出合理范围 (6-24)")
            return
        
        old_size = self.current_fonts['base_size']
        scale_factor = size / old_size
        
        # 更新基础大小
        self.current_fonts['base_size'] = size
        self.current_fonts['small_size'] = max(6, int(size * 0.9))
        self.current_fonts['large_size'] = min(24, int(size * 1.2))
        self.current_fonts['title_size'] = min(24, int(size * 1.3))
        
        # 按比例调整组件字体
        for component in self.current_fonts['components']:
            old_component_size = self.current_fonts['components'][component]
            new_component_size = max(6, min(24, int(old_component_size * scale_factor)))
            self.current_fonts['components'][component] = new_component_size
        
        self.logger.info(f"基础字体大小已调整: {old_size} -> {size}")
    
    def set_component_font_size(self, component: str, size: int):
        """设置特定组件的字体大小"""
        if size < 6 or size > 24:
            self.logger.warning(f"字体大小 {size} 超出合理范围 (6-24)")
            return
        
        if component in self.current_fonts['components']:
            old_size = self.current_fonts['components'][component]
            self.current_fonts['components'][component] = size
            self.logger.info(f"{component} 字体大小已调整: {old_size} -> {size}")
        else:
            self.logger.warning(f"未知组件: {component}")
    
    def set_font_family(self, family: str):
        """设置字体族"""
        self.current_fonts['font_family'] = family
        self.logger.info(f"字体族已设置为: {family}")
    
    def get_font_size(self, component: str = None, size_type: str = 'base') -> int:
        """获取字体大小"""
        if component and component in self.current_fonts['components']:
            return self.current_fonts['components'][component]
        
        size_key = f"{size_type}_size"
        return self.current_fonts.get(size_key, self.current_fonts['base_size'])
    
    def get_font_family(self) -> str:
        """获取字体族"""
        return self.current_fonts['font_family']
    
    def generate_font_stylesheet(self) -> str:
        """生成字体样式表"""
        font_family = self.current_fonts['font_family']
        styles = []
        
        # 为每个组件生成字体样式
        for component, size in self.current_fonts['components'].items():
            style = f"{component} {{ font-family: '{font_family}'; font-size: {size}pt; }}"
            styles.append(style)
        
        return '\n'.join(styles)
    
    def apply_fonts_to_application(self, app: QApplication = None):
        """将字体设置应用到应用程序"""
        if app is None:
            app = QApplication.instance()
        
        if app is None:
            self.logger.error("无法获取QApplication实例")
            return
        
        # 设置应用程序默认字体
        default_font = QFont(
            self.current_fonts['font_family'], 
            self.current_fonts['base_size']
        )
        app.setFont(default_font)
        
        # 生成并应用字体样式表
        font_stylesheet = self.generate_font_stylesheet()
        
        # 获取当前样式表并添加字体样式
        current_stylesheet = app.styleSheet()
        
        # 移除旧的字体样式（如果存在）
        lines = current_stylesheet.split('\n')
        filtered_lines = []
        
        for line in lines:
            # 跳过包含font-family或font-size的行
            if 'font-family:' not in line and 'font-size:' not in line:
                filtered_lines.append(line)
        
        # 添加新的字体样式
        new_stylesheet = '\n'.join(filtered_lines) + '\n' + font_stylesheet
        app.setStyleSheet(new_stylesheet)
        
        self.logger.info("字体设置已应用到应用程序")
    
    def reset_to_default(self):
        """重置为默认字体设置"""
        self.current_fonts = self.default_fonts.copy()
        self.logger.info("字体设置已重置为默认值")
    
    def get_available_sizes(self) -> list:
        """获取可用的字体大小列表"""
        return list(range(6, 25))
    
    def get_font_config_info(self) -> Dict:
        """获取字体配置信息"""
        return {
            'current_config': self.current_fonts.copy(),
            'config_file': self.config_path,
            'config_exists': os.path.exists(self.config_path),
            'available_sizes': self.get_available_sizes()
        }


# 全局字体管理器实例
_global_font_manager: Optional[FontManager] = None


def get_font_manager() -> FontManager:
    """获取全局字体管理器实例"""
    global _global_font_manager
    if _global_font_manager is None:
        _global_font_manager = FontManager()
    return _global_font_manager


def set_base_font_size(size: int):
    """设置基础字体大小的便捷函数"""
    get_font_manager().set_base_font_size(size)


def set_component_font_size(component: str, size: int):
    """设置组件字体大小的便捷函数"""
    get_font_manager().set_component_font_size(component, size)


def apply_fonts():
    """应用字体设置的便捷函数"""
    get_font_manager().apply_fonts_to_application()


def save_font_settings():
    """保存字体设置的便捷函数"""
    get_font_manager().save_font_config()
