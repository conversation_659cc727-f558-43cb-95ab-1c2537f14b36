<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MeshParameterDialog</class>
 <widget class="QDialog" name="MeshParameterDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>450</width>
    <height>300</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>450</width>
    <height>300</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Microsoft YaHei UI</family>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>网格参数设置</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_main">
   <property name="spacing">
    <number>15</number>
   </property>
   <property name="leftMargin">
    <number>20</number>
   </property>
   <property name="topMargin">
    <number>20</number>
   </property>
   <property name="rightMargin">
    <number>20</number>
   </property>
   <property name="bottomMargin">
    <number>20</number>
   </property>
   <item>
    <widget class="QLabel" name="label_title">
     <property name="font">
      <font>
       <family>Microsoft YaHei UI</family>
       <pointsize>16</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string>网格参数配置</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignmentFlag::AlignCenter</set>
     </property>
     <property name="styleSheet">
      <string>QLabel {
    color: #34495e;
    padding: 10px;
    border-bottom: 2px solid #3498db;
}</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_basic_params">
     <property name="title">
      <string>基本参数</string>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
     </property>
     <layout class="QFormLayout" name="formLayout_basic">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>15</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="label_mesh_name">
        <property name="text">
         <string>网格名称:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QLineEdit" name="lineEdit_mesh_name">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="placeholderText">
         <string>请输入网格名称，如"粗网格"、"细网格"</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_mesh_size">
        <property name="text">
         <string>网格尺寸(mm):</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_mesh_size">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="decimals">
         <number>2</number>
        </property>
        <property name="minimum">
         <double>0.100000000000000</double>
        </property>
        <property name="maximum">
         <double>1000.000000000000000</double>
        </property>
        <property name="value">
         <double>10.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_element_type">
        <property name="text">
         <string>单元类型:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QComboBox" name="comboBox_element_type">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <item>
         <property name="text">
          <string>四面体</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>六面体</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>混合</string>
         </property>
        </item>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_quality_settings">
     <property name="title">
      <string>质量设置</string>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
     </property>
     <layout class="QFormLayout" name="formLayout_quality">
      <property name="horizontalSpacing">
       <number>15</number>
      </property>
      <property name="verticalSpacing">
       <number>15</number>
      </property>
      <item row="0" column="0">
       <widget class="QLabel" name="label_skewness">
        <property name="text">
         <string>偏斜度:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_skewness">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="decimals">
         <number>3</number>
        </property>
        <property name="maximum">
         <double>1.000000000000000</double>
        </property>
        <property name="singleStep">
         <double>0.010000000000000</double>
        </property>
        <property name="value">
         <double>0.900000000000000</double>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="label_aspect_ratio">
        <property name="text">
         <string>长宽比:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QDoubleSpinBox" name="doubleSpinBox_aspect_ratio">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="decimals">
         <number>1</number>
        </property>
        <property name="minimum">
         <double>1.000000000000000</double>
        </property>
        <property name="maximum">
         <double>100.000000000000000</double>
        </property>
        <property name="value">
         <double>20.000000000000000</double>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="label_smoothing">
        <property name="text">
         <string>平滑迭代:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="spinBox_smoothing">
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximum">
         <number>10</number>
        </property>
        <property name="value">
         <number>3</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="groupBox_advanced_settings">
     <property name="title">
      <string>高级设置</string>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_advanced">
      <item>
       <widget class="QCheckBox" name="checkBox_auto_sizing">
        <property name="text">
         <string>自动尺寸调整</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="checkBox_capture_curvature">
        <property name="text">
         <string>捕获曲率</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="checkBox_capture_proximity">
        <property name="text">
         <string>捕获邻近性</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Orientation::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_buttons">
     <item>
      <spacer name="horizontalSpacer_buttons">
       <property name="orientation">
        <enum>Qt::Orientation::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="btn_reset">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>40</height>
        </size>
       </property>
       <property name="text">
        <string>重置</string>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #f39c12;
    font-size: 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #e67e22;
}</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_cancel">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>40</height>
        </size>
       </property>
       <property name="text">
        <string>取消</string>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #7f8c8d;
    font-size: 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #6c7b7d;
}</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="btn_ok">
       <property name="minimumSize">
        <size>
         <width>100</width>
         <height>40</height>
        </size>
       </property>
       <property name="text">
        <string>确定</string>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
       <property name="styleSheet">
        <string>QPushButton {
    background-color: #3498db;
    font-size: 12px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #2980b9;
}</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections>
  <connection>
   <sender>btn_ok</sender>
   <signal>clicked()</signal>
   <receiver>MeshParameterDialog</receiver>
   <slot>accept()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>400</x>
     <y>560</y>
    </hint>
    <hint type="destinationlabel">
     <x>250</x>
     <y>300</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>btn_cancel</sender>
   <signal>clicked()</signal>
   <receiver>MeshParameterDialog</receiver>
   <slot>reject()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>300</x>
     <y>560</y>
    </hint>
    <hint type="destinationlabel">
     <x>250</x>
     <y>300</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
