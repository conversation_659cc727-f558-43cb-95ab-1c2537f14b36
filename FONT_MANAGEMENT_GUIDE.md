# Qt项目字体管理系统使用指南

## 📋 概述

Qt项目现已集成完整的字体管理系统，支持动态调整界面中所有组件的字体大小和字体族。该系统提供了用户友好的图形界面和灵活的配置选项。

## 🎯 主要功能

### ✅ 已实现的功能

1. **动态字体调整**
   - 基础字体大小调整（6-24pt）
   - 14种UI组件的独立字体设置
   - 字体族选择（8种常用字体）
   - 实时预览效果

2. **图形化设置界面**
   - 直观的选项卡式界面
   - 滑块和数字框双重控制
   - 预设字体大小快速选择
   - 实时预览窗口

3. **配置管理**
   - 自动保存设置到JSON文件
   - 一键重置为默认设置
   - 支持配置导入导出
   - 设置持久化存储

4. **系统集成**
   - 与现有样式系统完美集成
   - 支持优化模式和传统模式
   - 菜单栏快捷访问
   - 热键支持（Ctrl+Shift+F）

## 🚀 使用方法

### 方法一：通过菜单访问

1. **打开字体设置**
   - 启动Qt应用程序
   - 在菜单栏选择 `工具` → `字体设置`
   - 或使用快捷键 `Ctrl+Shift+F`

2. **基础设置选项卡**
   - **字体族选择**：从下拉菜单选择字体（如Microsoft YaHei UI、Arial等）
   - **基础大小调整**：使用滑块或数字框设置基础字体大小
   - **预设大小**：点击预设按钮快速设置（小8pt、正常9pt、大11pt、特大13pt）

3. **组件设置选项卡**
   - 为14种不同的UI组件单独设置字体大小：
     - 按钮 (QPushButton)
     - 标签 (QLabel)
     - 输入框 (QLineEdit)
     - 下拉框 (QComboBox)
     - 分组框 (QGroupBox)
     - 选项卡 (QTabWidget)
     - 树形控件 (QTreeWidget)
     - 表格控件 (QTableWidget)
     - 文本编辑器 (QTextEdit)
     - 纯文本编辑器 (QPlainTextEdit)
     - 菜单栏 (QMenuBar)
     - 菜单 (QMenu)
     - 状态栏 (QStatusBar)
     - 工具提示 (QToolTip)

4. **预览选项卡**
   - 查看字体设置的实时效果
   - 显示当前配置信息
   - 预览示例文本和按钮

5. **应用设置**
   - **预览**：临时应用设置查看效果
   - **应用**：保存并应用设置
   - **重置**：恢复为默认设置
   - **确定**：应用设置并关闭对话框
   - **取消**：放弃更改并关闭

### 方法二：程序化调用

```python
# 导入字体管理器
from core.font_manager import get_font_manager

# 获取字体管理器实例
font_manager = get_font_manager()

# 设置基础字体大小
font_manager.set_base_font_size(11)

# 设置特定组件字体大小
font_manager.set_component_font_size('QPushButton', 12)

# 设置字体族
font_manager.set_font_family('Arial')

# 应用设置到应用程序
font_manager.apply_fonts_to_application()

# 保存设置
font_manager.save_font_config()
```

### 方法三：便捷函数

```python
# 使用便捷函数
from core.font_manager import set_base_font_size, apply_fonts, save_font_settings

# 设置字体大小
set_base_font_size(12)

# 应用字体
apply_fonts()

# 保存设置
save_font_settings()
```

## 📁 配置文件

### 配置文件位置
```
config/font_config.json
```

### 配置文件格式
```json
{
  "base_size": 9,
  "small_size": 8,
  "large_size": 11,
  "title_size": 12,
  "font_family": "Microsoft YaHei UI",
  "components": {
    "QPushButton": 9,
    "QLabel": 9,
    "QLineEdit": 9,
    "QComboBox": 9,
    "QGroupBox": 9,
    "QTabWidget": 9,
    "QTreeWidget": 9,
    "QTableWidget": 9,
    "QTextEdit": 9,
    "QPlainTextEdit": 9,
    "QMenuBar": 9,
    "QMenu": 9,
    "QStatusBar": 8,
    "QToolTip": 8
  }
}
```

## 🎨 支持的字体

### 内置字体选项
1. **Microsoft YaHei UI** (默认) - 现代中文界面字体
2. **Microsoft YaHei** - 标准中文字体
3. **SimSun** - 宋体
4. **SimHei** - 黑体
5. **Arial** - 经典英文字体
6. **Tahoma** - 清晰的英文字体
7. **Verdana** - 易读的英文字体
8. **Calibri** - 现代英文字体

### 字体大小范围
- **最小值**：6pt
- **最大值**：24pt
- **默认值**：9pt
- **推荐范围**：8-13pt

## 🔧 高级功能

### 批量字体调整
```python
# 按比例调整所有字体
font_manager = get_font_manager()
font_manager.set_base_font_size(12)  # 会自动按比例调整其他组件
```

### 字体配置备份
```python
# 获取当前配置
config_info = font_manager.get_font_config_info()
current_config = config_info['current_config']

# 保存配置到文件
import json
with open('font_backup.json', 'w') as f:
    json.dump(current_config, f, indent=2)
```

### 字体配置恢复
```python
# 从备份文件恢复配置
import json
with open('font_backup.json', 'r') as f:
    backup_config = json.load(f)

# 应用备份配置
font_manager.current_fonts = backup_config
font_manager.save_font_config()
font_manager.apply_fonts_to_application()
```

## 🐛 故障排除

### 常见问题

1. **字体设置不生效**
   - 确保点击了"应用"按钮
   - 检查配置文件是否正确保存
   - 重启应用程序

2. **字体显示异常**
   - 检查系统是否安装了所选字体
   - 尝试重置为默认设置
   - 检查字体大小是否在合理范围内

3. **配置文件丢失**
   - 字体管理器会自动创建默认配置
   - 检查config目录的写入权限
   - 手动创建config目录

### 调试信息

```python
# 获取调试信息
font_manager = get_font_manager()
debug_info = font_manager.get_font_config_info()

print("调试信息:")
print(f"配置文件路径: {debug_info['config_file']}")
print(f"配置文件存在: {debug_info['config_exists']}")
print(f"当前配置: {debug_info['current_config']}")
print(f"可用字体大小: {debug_info['available_sizes']}")
```

## 📝 注意事项

1. **字体大小限制**：为了保证界面可用性，字体大小限制在6-24pt范围内
2. **字体兼容性**：建议使用系统内置字体以确保跨平台兼容性
3. **配置持久化**：字体设置会自动保存，下次启动时自动应用
4. **性能影响**：字体更改会触发界面重绘，在低性能设备上可能有轻微延迟

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完整的字体管理系统
- ✅ 图形化设置界面
- ✅ 14种组件的独立字体控制
- ✅ 配置文件持久化
- ✅ 菜单栏集成
- ✅ 快捷键支持
- ✅ 实时预览功能

---

**使用愉快！如有问题，请查看故障排除部分或联系技术支持。**
