"""
分析设置界面控制器

此模块负责处理分析设置界面的所有操作，主要包括：
1. 界面跳转控制
2. 分析类型设置
3. 分析参数配置
4. 求解设置

工作流程：
1. 初始化分析设置界面
2. 配置分析类型和参数
3. 设置求解选项
4. 界面跳转管理

作者: [作者名]
日期: [日期]
"""

import json
import os
import shutil
from datetime import datetime
from typing import Optional
from PySide6.QtWidgets import QWidget, QMessageBox
from window_manager import WindowManager, WindowType
from error_handler import ErrorHandler, AppError, ErrorSeverity, ValidationError, FileOperationError
from resource_manager import ResourceManager
from core.workflow_state import get_workflow_state, WorkflowStep
import glob


# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口

def get_analysis_json(window_manager: WindowManager) -> None:
    """获取分析设置界面的配置信息并保存为JSON文件
    
    此函数完成以下任务：
    1. 获取界面上的分析参数
    2. 验证参数有效性
    3. 生成配置JSON
    4. 保存到文件
    5. 更新window_manager中的initial_data
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 获取窗口实例和错误处理器
    analysis_window = window_manager.get_window(WindowType.ANALYSIS)
    main_window = window_manager.get_window(WindowType.MAIN)
    error_handler = ErrorHandler()
    resource_manager = ResourceManager()
    
    if not analysis_window or not main_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    try:
        # 初始化资源管理器
        resource_manager.initialize(main_window.ANSYS_Work_Dir)
        
        # 1. 获取并验证分析参数
        try:
            # 获取时间步长和结束时间
            end_time = float(analysis_window.ui.lineEdit_stependline.text())
            time_step = float(analysis_window.ui.lineEdit_timestep.text())
            
            # 验证时间参数
            if end_time <= 0 or time_step <= 0:
                raise ValidationError(
                    "时间参数必须大于0",
                    details={
                        'end_time': end_time,
                        'time_step': time_step
                    }
                )
            if time_step >= end_time:
                raise ValidationError(
                    "时间步长必须小于结束时间",
                    details={
                        'end_time': end_time,
                        'time_step': time_step
                    }
                )
                
            # 获取系数参数
            stiffness_coefficient = float(analysis_window.ui.lineEdit_stiffness.text())
            mass_coefficient = float(analysis_window.ui.lineEdit_mass.text())
            
            # 验证系数参数
            if stiffness_coefficient < 0 or mass_coefficient < 0:
                raise ValidationError(
                    "刚度系数和质量系数必须大于等于0",
                    details={
                        'stiffness_coefficient': stiffness_coefficient,
                        'mass_coefficient': mass_coefficient
                    }
                )
                
            # 获取分析选项
            analysis_options = {
                "Stress_flag": analysis_window.ui.checkBox_stress.isChecked(),
                "Strain_flag": analysis_window.ui.checkBox_strain.isChecked(),
                "ContactData_flag": analysis_window.ui.checkBox_contactdata.isChecked(),
                "CalculateVolumeEnergy_flag": analysis_window.ui.checkBox_volumeandenergy.isChecked(),
                "EulerAngles_flag": analysis_window.ui.checkBox_eulerangles.isChecked()
            }
            
        except ValueError as e:
            raise ValidationError("参数必须是有效的数字") from e
            
        # 2. 构建配置数据
        analysis_config = {
            "analysis_settings": {
                **analysis_options,
                "time_step": f"{time_step} [s]",
                "end_time": f"{end_time} [s]",
                "StiffnessCoefficient": stiffness_coefficient,
                "MassCoefficient": mass_coefficient
            }
        }
        
        # 3. 使用ResourceManager保存配置文件
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)
            
            # 清理旧的配置文件，只保留最新的5个
            pattern = os.path.join(resource_manager.json_dir, "analysis_config_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            for old_file in json_files[5:]:  # 保留最新的5个文件
                try:
                    os.remove(old_file)
                    print(f"清理旧配置文件: {old_file}")
                except Exception as e:
                    print(f"警告: 无法删除旧的配置文件 {old_file}: {str(e)}")
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"analysis_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)
            
            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(analysis_config, f, indent=4)
                
            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "analysis_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")
                    
            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
            
            # 更新window_manager中的initial_data
            if hasattr(window_manager, 'initial_data'):
                window_manager.initial_data.update({
                    'timeStep': time_step,
                    'endTime': end_time,
                    'stiffnessCoefficient': stiffness_coefficient,
                    'massCoefficient': mass_coefficient
                })
                print(f"已更新window_manager中的initial_data: timeStep={time_step}, endTime={end_time}")
            
        except Exception as e:
            raise FileOperationError(
                "保存配置文件失败",
                details={'file': config_file}
            ) from e
            
        # 4. 显示成功消息
        error_handler.handle_error(
            AppError(
                "分析配置已保存",
                ErrorSeverity.INFO
            ),
            analysis_window
        )

        # 设置完成状态
        window_manager.process_status.set_completed(WindowType.ANALYSIS)

        # 标记工作流程步骤完成
        workflow_state = get_workflow_state()
        workflow_state.mark_step_completed(WorkflowStep.ANALYSIS, {
            'config_file': config_file,
            'timestamp': datetime.now().isoformat(),
            'time_step': time_step,
            'end_time': end_time
        })
        
    except AppError as e:
        # 处理应用程序异常
        error_handler.handle_error(e, analysis_window)
    except Exception as e:
        # 处理其他未预期的异常
        error_handler.handle_exception(e, analysis_window)

def analysis_slot(window_manager: WindowManager) -> None:
    """初始化分析设置界面的所有槽函数连接
    
    此函数负责将分析设置界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮和分析设置功能按钮。
    
    Args:
        window_manager: 窗口管理器实例
    """
    analysis_window = window_manager.get_window(WindowType.ANALYSIS)
    if not analysis_window:
        ErrorHandler().handle_error(
            AppError("无法获取分析设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step

    analysis_window.ui.push_mainui.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 分析设置的上一步应该是连接设置
    analysis_window.ui.push_connectionui.clicked.connect(
        lambda: navigate_to_previous_step(window_manager, WindowType.ANALYSIS))

    # 分析设置的下一步应该是约束设置
    analysis_window.ui.push_constrainui.clicked.connect(
        lambda: navigate_to_next_step(window_manager, WindowType.ANALYSIS))

    # 完成分析设置
    analysis_window.ui.push_finish.clicked.connect(
        lambda: get_analysis_json(window_manager))
