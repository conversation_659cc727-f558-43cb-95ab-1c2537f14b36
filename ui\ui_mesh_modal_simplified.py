"""
简化的网格与模态分析界面

此文件定义了简化后的网格与模态分析界面，整合了网格生成和模态分析流程：
1. 移除独立的网格生成步骤
2. 保留网格尺寸选择功能
3. 提供一体化的模态分析功能

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

from PySide6.QtCore import QSize, Qt
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QLabel, QListWidget, QPushButton, QProgressBar,
                               QFormLayout, QDoubleSpinBox, QComboBox, QSpacerItem,
                               QSizePolicy, QAbstractItemView, QTabWidget)


class Ui_MeshModalSimplified:
    """简化的网格与模态分析界面类"""
    
    def setupUi(self, MeshModalWindow):
        """设置UI界面"""
        if not MeshModalWindow.objectName():
            MeshModalWindow.setObjectName("MeshModalWindow")
        
        MeshModalWindow.resize(1200, 800)
        MeshModalWindow.setWindowTitle("网格与模态分析")
        
        # 主布局
        self.centralwidget = QWidget(MeshModalWindow)
        self.centralwidget.setObjectName("centralwidget")
        MeshModalWindow.setCentralWidget(self.centralwidget)
        
        self.main_layout = QVBoxLayout(self.centralwidget)
        self.main_layout.setObjectName("main_layout")
        
        # 创建标签页
        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName("tabWidget")
        
        # 模态分析标签页
        self.tab_modal_analysis = QWidget()
        self.tab_modal_analysis.setObjectName("tab_modal_analysis")
        self.setup_modal_analysis_tab()
        
        self.tabWidget.addTab(self.tab_modal_analysis, "模态分析")
        self.main_layout.addWidget(self.tabWidget)
    
    def setup_modal_analysis_tab(self):
        """设置模态分析标签页"""
        self.horizontal_layout_modal = QHBoxLayout(self.tab_modal_analysis)
        self.horizontal_layout_modal.setObjectName("horizontal_layout_modal")
        
        # 左侧：网格选择与参数设置
        self.setup_left_panel()
        
        # 右侧：模态分析控制
        self.setup_right_panel()
    
    def setup_left_panel(self):
        """设置左侧面板 - 网格选择与参数"""
        self.groupBox_mesh_selection = QGroupBox(self.tab_modal_analysis)
        self.groupBox_mesh_selection.setObjectName("groupBox_mesh_selection")
        self.groupBox_mesh_selection.setTitle("网格选择与参数设置")
        self.groupBox_mesh_selection.setMinimumSize(QSize(600, 0))
        
        self.vertical_layout_left = QVBoxLayout(self.groupBox_mesh_selection)
        self.vertical_layout_left.setObjectName("vertical_layout_left")
        
        # 网格选择列表
        self.label_mesh_list = QLabel("选择要分析的网格:")
        self.label_mesh_list.setObjectName("label_mesh_list")
        self.vertical_layout_left.addWidget(self.label_mesh_list)
        
        self.listWidget_meshes = QListWidget(self.groupBox_mesh_selection)
        self.listWidget_meshes.setObjectName("listWidget_meshes")
        self.listWidget_meshes.setMinimumSize(QSize(0, 250))
        self.listWidget_meshes.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
        self.vertical_layout_left.addWidget(self.listWidget_meshes)
        
        # 网格选择操作按钮
        self.setup_mesh_selection_buttons()
        
        # 网格参数设置
        self.setup_mesh_parameters()
        
        self.horizontal_layout_modal.addWidget(self.groupBox_mesh_selection)
    
    def setup_mesh_selection_buttons(self):
        """设置网格选择按钮"""
        self.horizontal_layout_mesh_btns = QHBoxLayout()
        self.horizontal_layout_mesh_btns.setObjectName("horizontal_layout_mesh_btns")
        
        self.btn_select_all = QPushButton("全选")
        self.btn_select_all.setObjectName("btn_select_all")
        self.btn_select_all.setMinimumSize(QSize(80, 35))
        self.horizontal_layout_mesh_btns.addWidget(self.btn_select_all)
        
        self.btn_clear_selection = QPushButton("清空选择")
        self.btn_clear_selection.setObjectName("btn_clear_selection")
        self.btn_clear_selection.setMinimumSize(QSize(80, 35))
        self.horizontal_layout_mesh_btns.addWidget(self.btn_clear_selection)
        
        # 添加弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        self.horizontal_layout_mesh_btns.addItem(spacer)
        
        # 选择计数标签
        self.label_selection_count = QLabel("已选择: 0 个网格")
        self.label_selection_count.setObjectName("label_selection_count")
        self.label_selection_count.setStyleSheet("font-weight: bold; color: #0078d4;")
        self.horizontal_layout_mesh_btns.addWidget(self.label_selection_count)
        
        self.vertical_layout_left.addLayout(self.horizontal_layout_mesh_btns)
    
    def setup_mesh_parameters(self):
        """设置网格参数"""
        self.groupBox_mesh_params = QGroupBox("网格生成参数")
        self.groupBox_mesh_params.setObjectName("groupBox_mesh_params")
        
        self.form_layout_params = QFormLayout(self.groupBox_mesh_params)
        self.form_layout_params.setObjectName("form_layout_params")
        
        # 网格尺寸
        self.label_mesh_size = QLabel("网格尺寸:")
        self.doubleSpinBox_mesh_size = QDoubleSpinBox()
        self.doubleSpinBox_mesh_size.setObjectName("doubleSpinBox_mesh_size")
        self.doubleSpinBox_mesh_size.setRange(0.1, 100.0)
        self.doubleSpinBox_mesh_size.setValue(1.0)
        self.doubleSpinBox_mesh_size.setSuffix(" mm")
        self.doubleSpinBox_mesh_size.setDecimals(2)
        self.form_layout_params.addRow(self.label_mesh_size, self.doubleSpinBox_mesh_size)
        
        # 网格质量
        self.label_mesh_quality = QLabel("网格质量:")
        self.comboBox_mesh_quality = QComboBox()
        self.comboBox_mesh_quality.setObjectName("comboBox_mesh_quality")
        self.comboBox_mesh_quality.addItems(["粗糙", "中等", "精细", "极精细"])
        self.comboBox_mesh_quality.setCurrentIndex(1)  # 默认中等
        self.form_layout_params.addRow(self.label_mesh_quality, self.comboBox_mesh_quality)
        
        # 元素类型
        self.label_element_type = QLabel("元素类型:")
        self.comboBox_element_type = QComboBox()
        self.comboBox_element_type.setObjectName("comboBox_element_type")
        self.comboBox_element_type.addItems(["四面体", "六面体", "自动选择"])
        self.comboBox_element_type.setCurrentIndex(2)  # 默认自动选择
        self.form_layout_params.addRow(self.label_element_type, self.comboBox_element_type)
        
        self.vertical_layout_left.addWidget(self.groupBox_mesh_params)
    
    def setup_right_panel(self):
        """设置右侧面板 - 模态分析控制"""
        self.widget_right_panel = QWidget(self.tab_modal_analysis)
        self.widget_right_panel.setObjectName("widget_right_panel")
        self.widget_right_panel.setMaximumSize(QSize(500, 16777215))
        
        self.vertical_layout_right = QVBoxLayout(self.widget_right_panel)
        self.vertical_layout_right.setObjectName("vertical_layout_right")
        
        # 模态分析参数设置
        self.setup_modal_parameters()
        
        # 分析控制按钮
        self.setup_analysis_controls()
        
        # 进度显示
        self.setup_progress_display()
        
        # 结果显示区域
        self.setup_results_display()
        
        self.horizontal_layout_modal.addWidget(self.widget_right_panel)
    
    def setup_modal_parameters(self):
        """设置模态分析参数"""
        self.groupBox_modal_params = QGroupBox("模态分析参数")
        self.groupBox_modal_params.setObjectName("groupBox_modal_params")
        
        self.form_layout_modal = QFormLayout(self.groupBox_modal_params)
        self.form_layout_modal.setObjectName("form_layout_modal")
        
        # 模态数量
        self.label_modal_count = QLabel("模态数量:")
        self.spinBox_modal_count = QDoubleSpinBox()
        self.spinBox_modal_count.setObjectName("spinBox_modal_count")
        self.spinBox_modal_count.setRange(1, 100)
        self.spinBox_modal_count.setValue(10)
        self.spinBox_modal_count.setDecimals(0)
        self.form_layout_modal.addRow(self.label_modal_count, self.spinBox_modal_count)
        
        # 频率范围
        self.label_freq_range = QLabel("频率范围:")
        self.doubleSpinBox_max_freq = QDoubleSpinBox()
        self.doubleSpinBox_max_freq.setObjectName("doubleSpinBox_max_freq")
        self.doubleSpinBox_max_freq.setRange(1.0, 10000.0)
        self.doubleSpinBox_max_freq.setValue(1000.0)
        self.doubleSpinBox_max_freq.setSuffix(" Hz")
        self.form_layout_modal.addRow(self.label_freq_range, self.doubleSpinBox_max_freq)
        
        self.vertical_layout_right.addWidget(self.groupBox_modal_params)
    
    def setup_analysis_controls(self):
        """设置分析控制按钮"""
        self.groupBox_controls = QGroupBox("分析控制")
        self.groupBox_controls.setObjectName("groupBox_controls")
        
        self.vertical_layout_controls = QVBoxLayout(self.groupBox_controls)
        self.vertical_layout_controls.setObjectName("vertical_layout_controls")
        
        # 主要分析按钮
        self.btn_start_modal_analysis = QPushButton("开始模态分析")
        self.btn_start_modal_analysis.setObjectName("btn_start_modal_analysis")
        self.btn_start_modal_analysis.setMinimumSize(QSize(0, 50))
        self.btn_start_modal_analysis.setStyleSheet("""
            QPushButton {
                background-color: #0078d4;
                color: white;
                font-weight: bold;
                font-size: 14px;
                border: none;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        self.vertical_layout_controls.addWidget(self.btn_start_modal_analysis)
        
        # 控制按钮组
        self.horizontal_layout_ctrl_btns = QHBoxLayout()
        self.horizontal_layout_ctrl_btns.setObjectName("horizontal_layout_ctrl_btns")
        
        self.btn_pause_analysis = QPushButton("暂停")
        self.btn_pause_analysis.setObjectName("btn_pause_analysis")
        self.btn_pause_analysis.setMinimumSize(QSize(80, 35))
        self.btn_pause_analysis.setEnabled(False)
        self.horizontal_layout_ctrl_btns.addWidget(self.btn_pause_analysis)
        
        self.btn_stop_analysis = QPushButton("停止")
        self.btn_stop_analysis.setObjectName("btn_stop_analysis")
        self.btn_stop_analysis.setMinimumSize(QSize(80, 35))
        self.btn_stop_analysis.setEnabled(False)
        self.horizontal_layout_ctrl_btns.addWidget(self.btn_stop_analysis)
        
        self.vertical_layout_controls.addLayout(self.horizontal_layout_ctrl_btns)
        
        self.vertical_layout_right.addWidget(self.groupBox_controls)
    
    def setup_progress_display(self):
        """设置进度显示"""
        self.groupBox_progress = QGroupBox("分析进度")
        self.groupBox_progress.setObjectName("groupBox_progress")
        
        self.vertical_layout_progress = QVBoxLayout(self.groupBox_progress)
        self.vertical_layout_progress.setObjectName("vertical_layout_progress")
        
        # 当前步骤标签
        self.label_current_step = QLabel("准备就绪")
        self.label_current_step.setObjectName("label_current_step")
        self.label_current_step.setStyleSheet("color: #0078d4; font-weight: bold;")
        self.vertical_layout_progress.addWidget(self.label_current_step)
        
        # 进度条
        self.progressBar_analysis = QProgressBar()
        self.progressBar_analysis.setObjectName("progressBar_analysis")
        self.progressBar_analysis.setValue(0)
        self.progressBar_analysis.setMinimumHeight(25)
        self.vertical_layout_progress.addWidget(self.progressBar_analysis)
        
        # 详细状态标签
        self.label_detailed_status = QLabel("等待开始分析...")
        self.label_detailed_status.setObjectName("label_detailed_status")
        self.label_detailed_status.setStyleSheet("color: #666666; font-size: 12px;")
        self.label_detailed_status.setWordWrap(True)
        self.vertical_layout_progress.addWidget(self.label_detailed_status)
        
        self.vertical_layout_right.addWidget(self.groupBox_progress)
    
    def setup_results_display(self):
        """设置结果显示区域"""
        self.groupBox_results = QGroupBox("分析结果")
        self.groupBox_results.setObjectName("groupBox_results")
        
        self.vertical_layout_results = QVBoxLayout(self.groupBox_results)
        self.vertical_layout_results.setObjectName("vertical_layout_results")
        
        # 结果列表
        self.listWidget_results = QListWidget()
        self.listWidget_results.setObjectName("listWidget_results")
        self.listWidget_results.setMinimumSize(QSize(0, 200))
        self.vertical_layout_results.addWidget(self.listWidget_results)
        
        # 结果操作按钮
        self.horizontal_layout_result_btns = QHBoxLayout()
        self.horizontal_layout_result_btns.setObjectName("horizontal_layout_result_btns")
        
        self.btn_view_results = QPushButton("查看结果")
        self.btn_view_results.setObjectName("btn_view_results")
        self.btn_view_results.setMinimumSize(QSize(80, 35))
        self.btn_view_results.setEnabled(False)
        self.horizontal_layout_result_btns.addWidget(self.btn_view_results)
        
        self.btn_export_results = QPushButton("导出结果")
        self.btn_export_results.setObjectName("btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(80, 35))
        self.btn_export_results.setEnabled(False)
        self.horizontal_layout_result_btns.addWidget(self.btn_export_results)
        
        self.vertical_layout_results.addLayout(self.horizontal_layout_result_btns)
        
        self.vertical_layout_right.addWidget(self.groupBox_results)
        
        # 添加弹性空间
        spacer = QSpacerItem(20, 40, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Expanding)
        self.vertical_layout_right.addItem(spacer)


def retranslateUi(MeshModalWindow):
    """设置界面文本"""
    pass  # 文本已在创建时设置
