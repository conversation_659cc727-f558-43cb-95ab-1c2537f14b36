"""
图标管理器模块

此模块负责管理材料管理界面中使用的图标资源，包括：
1. 材料类型图标
2. 操作按钮图标
3. 状态指示图标
4. 图标缓存和优化

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Dict, Optional
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QFont
from PySide6.QtCore import Qt, QSize
from PySide6.QtWidgets import QStyle, QApplication

from core.material_manager import MaterialCategory

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class IconManager:
    """图标管理器类"""
    
    def __init__(self):
        """初始化图标管理器"""
        self._icon_cache: Dict[str, QIcon] = {}
        self._material_category_icons: Dict[MaterialCategory, QIcon] = {}
        self._operation_icons: Dict[str, QIcon] = {}
        
        # 初始化图标
        self._initialize_icons()
        
        logger.debug("图标管理器初始化完成")
    
    def _initialize_icons(self):
        """初始化所有图标"""
        try:
            # 初始化材料类型图标
            self._create_material_category_icons()
            
            # 初始化操作图标
            self._create_operation_icons()
            
            # 初始化状态图标
            self._create_status_icons()
            
            logger.info("图标初始化完成")
            
        except Exception as e:
            logger.error(f"图标初始化失败: {e}")
    
    def _create_material_category_icons(self):
        """创建材料类型图标"""
        # 材料类型图标配置
        category_configs = {
            MaterialCategory.GENERAL: {
                'color': '#4caf50',
                'symbol': '⚙',
                'name': 'general'
            },
            MaterialCategory.THERMAL: {
                'color': '#ff5722',
                'symbol': '🔥',
                'name': 'thermal'
            },
            MaterialCategory.COMPOSITE: {
                'color': '#9c27b0',
                'symbol': '🧬',
                'name': 'composite'
            },
            MaterialCategory.CUSTOM: {
                'color': '#2196f3',
                'symbol': '✏',
                'name': 'custom'
            }
        }
        
        for category, config in category_configs.items():
            icon = self._create_text_icon(
                text=config['symbol'],
                color=config['color'],
                size=16
            )
            self._material_category_icons[category] = icon
            self._icon_cache[f"material_{config['name']}"] = icon
    
    def _create_operation_icons(self):
        """创建操作图标"""
        # 操作图标配置
        operation_configs = {
            'new': {'symbol': '➕', 'color': '#4caf50'},
            'copy': {'symbol': '📋', 'color': '#2196f3'},
            'delete': {'symbol': '🗑', 'color': '#f44336'},
            'assign': {'symbol': '🔗', 'color': '#ff9800'},
            'apply_all': {'symbol': '📤', 'color': '#ff9800'},
            'search': {'symbol': '🔍', 'color': '#757575'},
            'folder_open': {'symbol': '📂', 'color': '#ffc107'},
            'folder_closed': {'symbol': '📁', 'color': '#ffc107'},
            'material': {'symbol': '🧱', 'color': '#795548'}
        }
        
        for operation, config in operation_configs.items():
            icon = self._create_text_icon(
                text=config['symbol'],
                color=config['color'],
                size=14
            )
            self._operation_icons[operation] = icon
            self._icon_cache[f"operation_{operation}"] = icon
    
    def _create_status_icons(self):
        """创建状态图标"""
        # 状态图标配置
        status_configs = {
            'readonly': {'symbol': '🔒', 'color': '#757575'},
            'editable': {'symbol': '✏', 'color': '#4caf50'},
            'loading': {'symbol': '⏳', 'color': '#ff9800'},
            'success': {'symbol': '✅', 'color': '#4caf50'},
            'error': {'symbol': '❌', 'color': '#f44336'},
            'warning': {'symbol': '⚠', 'color': '#ff9800'}
        }
        
        for status, config in status_configs.items():
            icon = self._create_text_icon(
                text=config['symbol'],
                color=config['color'],
                size=12
            )
            self._icon_cache[f"status_{status}"] = icon
    
    def _create_text_icon(self, text: str, color: str, size: int = 16) -> QIcon:
        """创建基于文本的图标
        
        Args:
            text: 图标文本
            color: 图标颜色
            size: 图标大小
            
        Returns:
            QIcon: 创建的图标
        """
        try:
            # 创建像素图
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            # 创建画笔
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 设置字体
            font = QFont("Segoe UI Emoji", size - 4)
            painter.setFont(font)
            
            # 设置颜色
            painter.setPen(QColor(color))
            
            # 绘制文本
            painter.drawText(pixmap.rect(), Qt.AlignCenter, text)
            painter.end()
            
            return QIcon(pixmap)
            
        except Exception as e:
            logger.error(f"创建文本图标失败: {e}")
            return QIcon()
    
    def _create_system_icon(self, icon_type: QStyle.StandardPixmap) -> QIcon:
        """创建系统图标
        
        Args:
            icon_type: 系统图标类型
            
        Returns:
            QIcon: 系统图标
        """
        try:
            app = QApplication.instance()
            if app:
                style = app.style()
                return style.standardIcon(icon_type)
            return QIcon()
            
        except Exception as e:
            logger.error(f"创建系统图标失败: {e}")
            return QIcon()
    
    def get_material_category_icon(self, category: MaterialCategory) -> QIcon:
        """获取材料类型图标
        
        Args:
            category: 材料类型
            
        Returns:
            QIcon: 材料类型图标
        """
        return self._material_category_icons.get(category, QIcon())
    
    def get_operation_icon(self, operation: str) -> QIcon:
        """获取操作图标
        
        Args:
            operation: 操作名称
            
        Returns:
            QIcon: 操作图标
        """
        return self._operation_icons.get(operation, QIcon())
    
    def get_icon(self, icon_name: str) -> QIcon:
        """获取缓存的图标
        
        Args:
            icon_name: 图标名称
            
        Returns:
            QIcon: 图标对象
        """
        return self._icon_cache.get(icon_name, QIcon())
    
    def get_readonly_icon(self) -> QIcon:
        """获取只读图标"""
        return self.get_icon("status_readonly")
    
    def get_editable_icon(self) -> QIcon:
        """获取可编辑图标"""
        return self.get_icon("status_editable")
    
    def get_folder_open_icon(self) -> QIcon:
        """获取打开文件夹图标"""
        return self.get_icon("operation_folder_open")
    
    def get_folder_closed_icon(self) -> QIcon:
        """获取关闭文件夹图标"""
        return self.get_icon("operation_folder_closed")
    
    def get_material_icon(self) -> QIcon:
        """获取材料图标"""
        return self.get_icon("operation_material")
    
    def create_colored_icon(self, base_icon: QIcon, color: QColor, size: QSize = QSize(16, 16)) -> QIcon:
        """创建着色图标
        
        Args:
            base_icon: 基础图标
            color: 着色颜色
            size: 图标大小
            
        Returns:
            QIcon: 着色后的图标
        """
        try:
            pixmap = base_icon.pixmap(size)
            
            # 创建着色版本
            colored_pixmap = QPixmap(size)
            colored_pixmap.fill(Qt.transparent)
            
            painter = QPainter(colored_pixmap)
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            painter.drawPixmap(0, 0, pixmap)
            
            painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
            painter.fillRect(colored_pixmap.rect(), color)
            painter.end()
            
            return QIcon(colored_pixmap)
            
        except Exception as e:
            logger.error(f"创建着色图标失败: {e}")
            return base_icon
    
    def clear_cache(self):
        """清空图标缓存"""
        self._icon_cache.clear()
        self._material_category_icons.clear()
        self._operation_icons.clear()
        logger.debug("图标缓存已清空")


# 全局图标管理器实例
_global_icon_manager: Optional[IconManager] = None


def get_icon_manager() -> IconManager:
    """获取全局图标管理器实例
    
    Returns:
        IconManager: 图标管理器实例
    """
    global _global_icon_manager
    if _global_icon_manager is None:
        _global_icon_manager = IconManager()
    return _global_icon_manager


def get_material_category_icon(category: MaterialCategory) -> QIcon:
    """获取材料类型图标的便捷函数
    
    Args:
        category: 材料类型
        
    Returns:
        QIcon: 材料类型图标
    """
    return get_icon_manager().get_material_category_icon(category)


def get_operation_icon(operation: str) -> QIcon:
    """获取操作图标的便捷函数
    
    Args:
        operation: 操作名称
        
    Returns:
        QIcon: 操作图标
    """
    return get_icon_manager().get_operation_icon(operation)
