@echo off
echo Starting ANSYS Workbench Batch Modal Calculation...
echo Script: D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\script\modal.py
echo Output: temp\modal_output_batch_20250801_001034

cd /d "D:/app-many/ANC23/ANSYS Inc/v232/Framework/bin/Win64"
"D:/app-many/ANC23/ANSYS Inc/v232/Framework/bin/Win64/RunWB2.exe" -B -R "D:\data\all-XM\autoworkbench\qtauto\qt-cs\qtproject\script\modal.py"

echo ANSYS Workbench execution completed.
pause
