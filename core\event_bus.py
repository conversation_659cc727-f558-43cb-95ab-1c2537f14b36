"""
事件总线模块

此模块实现了事件驱动的架构模式，用于解耦模块间的依赖关系。
通过事件总线，各模块可以发布和订阅事件，而无需直接依赖其他模块。

主要功能：
1. 事件发布和订阅机制
2. 异步事件处理
3. 事件优先级管理
4. 错误处理和恢复

作者: [作者名]
日期: [日期]
"""

import logging
import threading
import time
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Any, Callable, Dict, List, Optional, Set
from concurrent.futures import ThreadPoolExecutor
from PySide6.QtCore import QObject, QTimer, Signal

logger = logging.getLogger(__name__)


class EventPriority(Enum):
    """事件优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class Event:
    """事件基类"""
    event_type: str
    data: Any = None
    priority: EventPriority = EventPriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    source: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class WindowSwitchEvent(Event):
    """窗口切换事件"""
    from_window: Optional[str] = None
    to_window: Optional[str] = None
    
    def __post_init__(self):
        super().__post_init__()
        self.event_type = "window_switch"


@dataclass
class WindowRegistrationEvent(Event):
    """窗口注册事件"""
    window_type: str = None
    window_instance: Any = None
    
    def __post_init__(self):
        super().__post_init__()
        self.event_type = "window_registration"


@dataclass
class DataUpdateEvent(Event):
    """数据更新事件"""
    update_type: str = None
    params: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        super().__post_init__()
        self.event_type = "data_update"


class EventHandler(ABC):
    """事件处理器抽象基类"""
    
    @abstractmethod
    def handle(self, event: Event) -> bool:
        """处理事件
        
        Args:
            event: 要处理的事件
            
        Returns:
            bool: 是否成功处理事件
        """
        pass
    
    @abstractmethod
    def can_handle(self, event_type: str) -> bool:
        """检查是否能处理指定类型的事件
        
        Args:
            event_type: 事件类型
            
        Returns:
            bool: 是否能处理
        """
        pass


class EventBusSignaler(QObject):
    """Qt信号发射器，用于线程安全的事件通知"""
    event_published = Signal(object)  # Event对象


class EventBus:
    """事件总线类
    
    实现发布-订阅模式，支持异步事件处理和优先级管理。
    """
    
    def __init__(self, max_workers: int = 4):
        self._subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self._handlers: Dict[str, List[EventHandler]] = defaultdict(list)
        self._event_queue: deque = deque()
        self._processing_queue: deque = deque()
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._signaler = EventBusSignaler()
        self._timer = QTimer()
        self._timer.timeout.connect(self._process_events)
        self._timer.start(50)  # 50ms间隔处理事件
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0
        }
        
        logger.info(f"EventBus initialized with {max_workers} workers")
    
    def subscribe(self, event_type: str, callback: Callable[[Event], None]) -> None:
        """订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self._lock:
            if callback not in self._subscribers[event_type]:
                self._subscribers[event_type].append(callback)
                logger.debug(f"Subscribed to event type: {event_type}")
    
    def unsubscribe(self, event_type: str, callback: Callable[[Event], None]) -> None:
        """取消订阅事件
        
        Args:
            event_type: 事件类型
            callback: 回调函数
        """
        with self._lock:
            if callback in self._subscribers[event_type]:
                self._subscribers[event_type].remove(callback)
                logger.debug(f"Unsubscribed from event type: {event_type}")
    
    def register_handler(self, handler: EventHandler) -> None:
        """注册事件处理器
        
        Args:
            handler: 事件处理器实例
        """
        with self._lock:
            # 获取处理器支持的事件类型
            for event_type in self._get_supported_event_types(handler):
                if handler not in self._handlers[event_type]:
                    self._handlers[event_type].append(handler)
                    logger.debug(f"Registered handler for event type: {event_type}")
    
    def publish(self, event: Event) -> None:
        """发布事件
        
        Args:
            event: 要发布的事件
        """
        with self._lock:
            # 根据优先级插入队列
            if event.priority == EventPriority.CRITICAL:
                self._event_queue.appendleft(event)
            else:
                self._event_queue.append(event)
            
            self._stats['events_published'] += 1
            logger.debug(f"Published event: {event.event_type} with priority {event.priority}")
    
    def publish_sync(self, event: Event) -> List[Any]:
        """同步发布事件并等待处理完成
        
        Args:
            event: 要发布的事件
            
        Returns:
            List[Any]: 处理结果列表
        """
        results = []
        
        # 处理订阅者
        for callback in self._subscribers.get(event.event_type, []):
            try:
                result = callback(event)
                results.append(result)
            except Exception as e:
                logger.error(f"Callback error for event {event.event_type}: {e}")
                self._stats['events_failed'] += 1
        
        # 处理事件处理器
        for handler in self._handlers.get(event.event_type, []):
            try:
                result = handler.handle(event)
                results.append(result)
            except Exception as e:
                logger.error(f"Handler error for event {event.event_type}: {e}")
                self._stats['events_failed'] += 1
        
        self._stats['events_processed'] += 1
        return results
    
    def _process_events(self) -> None:
        """处理事件队列"""
        if not self._event_queue:
            return
        
        with self._lock:
            # 批量处理事件，避免阻塞UI
            batch_size = min(5, len(self._event_queue))
            events_to_process = []
            
            for _ in range(batch_size):
                if self._event_queue:
                    events_to_process.append(self._event_queue.popleft())
        
        # 异步处理事件
        for event in events_to_process:
            self._executor.submit(self._handle_event, event)
    
    def _handle_event(self, event: Event) -> None:
        """处理单个事件
        
        Args:
            event: 要处理的事件
        """
        try:
            # 处理订阅者
            for callback in self._subscribers.get(event.event_type, []):
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Callback error for event {event.event_type}: {e}")
                    self._stats['events_failed'] += 1
            
            # 处理事件处理器
            for handler in self._handlers.get(event.event_type, []):
                try:
                    if handler.can_handle(event.event_type):
                        handler.handle(event)
                except Exception as e:
                    logger.error(f"Handler error for event {event.event_type}: {e}")
                    self._stats['events_failed'] += 1
            
            self._stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"Unexpected error processing event {event.event_type}: {e}")
            self._stats['events_failed'] += 1
    
    def _get_supported_event_types(self, handler: EventHandler) -> List[str]:
        """获取处理器支持的事件类型
        
        Args:
            handler: 事件处理器
            
        Returns:
            List[str]: 支持的事件类型列表
        """
        # 这里可以通过反射或配置来获取支持的事件类型
        # 简化实现，返回常见的事件类型
        common_types = ["window_switch", "window_registration", "data_update"]
        return [event_type for event_type in common_types if handler.can_handle(event_type)]
    
    def get_stats(self) -> Dict[str, int]:
        """获取事件总线统计信息
        
        Returns:
            Dict[str, int]: 统计信息
        """
        return self._stats.copy()
    
    def clear_stats(self) -> None:
        """清除统计信息"""
        self._stats = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0
        }
    
    def shutdown(self) -> None:
        """关闭事件总线"""
        self._timer.stop()
        self._executor.shutdown(wait=True)
        logger.info("EventBus shutdown completed")


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线实例
    
    Returns:
        EventBus: 全局事件总线实例
    """
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = EventBus()
    return _global_event_bus


def shutdown_event_bus() -> None:
    """关闭全局事件总线"""
    global _global_event_bus
    if _global_event_bus is not None:
        _global_event_bus.shutdown()
        _global_event_bus = None
