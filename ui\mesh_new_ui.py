# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh_new.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QCheckBox, QComboBox,
    QDoubleSpinBox, QGridLayout, QGroupBox, QHBoxLayout,
    QHeaderView, QLabel, QListWidget, QListWidgetItem,
    QMainWindow, QProgressBar, QPushButton, QSizePolicy,
    QSpacerItem, QSpinBox, QTabWidget, QTableWidget,
    QTableWidgetItem, QTextEdit, QVBoxLayout, QWidget)
class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QSize(1400, 900))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(10)
        MainWindow.setFont(font)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_main = QVBoxLayout(self.centralwidget)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(15, 15, 15, 15)
        self.label_title = QLabel(self.centralwidget)
        self.label_title.setObjectName(u"label_title")
        self.label_title.setMinimumSize(QSize(0, 60))
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei UI"])
        font1.setPointSize(24)
        font1.setBold(True)
        self.label_title.setFont(font1)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_main.addWidget(self.label_title)

        self.tabWidget_main = QTabWidget(self.centralwidget)
        self.tabWidget_main.setObjectName(u"tabWidget_main")
        self.tab_mesh_management = QWidget()
        self.tab_mesh_management.setObjectName(u"tab_mesh_management")
        self.horizontalLayout_mesh_mgmt = QHBoxLayout(self.tab_mesh_management)
        self.horizontalLayout_mesh_mgmt.setSpacing(15)
        self.horizontalLayout_mesh_mgmt.setObjectName(u"horizontalLayout_mesh_mgmt")
        self.widget_left_panel = QWidget(self.tab_mesh_management)
        self.widget_left_panel.setObjectName(u"widget_left_panel")
        self.widget_left_panel.setMinimumSize(QSize(600, 0))
        self.widget_left_panel.setMaximumSize(QSize(650, 16777215))
        self.verticalLayout_left = QVBoxLayout(self.widget_left_panel)
        self.verticalLayout_left.setObjectName(u"verticalLayout_left")
        self.groupBox_mesh_params = QGroupBox(self.widget_left_panel)
        self.groupBox_mesh_params.setObjectName(u"groupBox_mesh_params")
        self.verticalLayout_params = QVBoxLayout(self.groupBox_mesh_params)
        self.verticalLayout_params.setObjectName(u"verticalLayout_params")
        self.horizontalLayout_toolbar = QHBoxLayout()
        self.horizontalLayout_toolbar.setObjectName(u"horizontalLayout_toolbar")
        self.btn_add_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_add_mesh.setObjectName(u"btn_add_mesh")
        self.btn_add_mesh.setMinimumSize(QSize(100, 35))
        icon = QIcon()
        icon.addFile(u"../assets/icons/add.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_add_mesh.setIcon(icon)

        self.horizontalLayout_toolbar.addWidget(self.btn_add_mesh)

        self.btn_import_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_import_mesh.setObjectName(u"btn_import_mesh")
        self.btn_import_mesh.setMinimumSize(QSize(100, 35))

        self.horizontalLayout_toolbar.addWidget(self.btn_import_mesh)

        self.btn_export_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_export_mesh.setObjectName(u"btn_export_mesh")
        self.btn_export_mesh.setMinimumSize(QSize(100, 35))

        self.horizontalLayout_toolbar.addWidget(self.btn_export_mesh)

        self.horizontalSpacer_toolbar = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_toolbar.addItem(self.horizontalSpacer_toolbar)


        self.verticalLayout_params.addLayout(self.horizontalLayout_toolbar)

        self.tableWidget_mesh_params = QTableWidget(self.groupBox_mesh_params)
        if (self.tableWidget_mesh_params.columnCount() < 6):
            self.tableWidget_mesh_params.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.tableWidget_mesh_params.setObjectName(u"tableWidget_mesh_params")
        self.tableWidget_mesh_params.setMinimumSize(QSize(0, 300))
        self.tableWidget_mesh_params.setAlternatingRowColors(True)
        self.tableWidget_mesh_params.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.tableWidget_mesh_params.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_mesh_params.setSortingEnabled(True)

        self.verticalLayout_params.addWidget(self.tableWidget_mesh_params)


        self.verticalLayout_left.addWidget(self.groupBox_mesh_params)


        self.horizontalLayout_mesh_mgmt.addWidget(self.widget_left_panel)

        self.widget_right_panel = QWidget(self.tab_mesh_management)
        self.widget_right_panel.setObjectName(u"widget_right_panel")
        self.verticalLayout_right = QVBoxLayout(self.widget_right_panel)
        self.verticalLayout_right.setObjectName(u"verticalLayout_right")
        self.groupBox_mesh_preview = QGroupBox(self.widget_right_panel)
        self.groupBox_mesh_preview.setObjectName(u"groupBox_mesh_preview")
        self.verticalLayout_preview = QVBoxLayout(self.groupBox_mesh_preview)
        self.verticalLayout_preview.setObjectName(u"verticalLayout_preview")
        self.horizontalLayout_preview_ctrl = QHBoxLayout()
        self.horizontalLayout_preview_ctrl.setObjectName(u"horizontalLayout_preview_ctrl")
        self.label_select_mesh = QLabel(self.groupBox_mesh_preview)
        self.label_select_mesh.setObjectName(u"label_select_mesh")

        self.horizontalLayout_preview_ctrl.addWidget(self.label_select_mesh)

        self.comboBox_mesh_select = QComboBox(self.groupBox_mesh_preview)
        self.comboBox_mesh_select.setObjectName(u"comboBox_mesh_select")
        self.comboBox_mesh_select.setMinimumSize(QSize(200, 30))

        self.horizontalLayout_preview_ctrl.addWidget(self.comboBox_mesh_select)

        self.horizontalSpacer_preview = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_preview_ctrl.addItem(self.horizontalSpacer_preview)


        self.verticalLayout_preview.addLayout(self.horizontalLayout_preview_ctrl)

        self.label_mesh_preview = QLabel(self.groupBox_mesh_preview)
        self.label_mesh_preview.setObjectName(u"label_mesh_preview")
        self.label_mesh_preview.setMinimumSize(QSize(400, 300))
        font2 = QFont()
        font2.setFamilies([u"Microsoft YaHei UI"])
        font2.setPointSize(16)
        self.label_mesh_preview.setFont(font2)
        self.label_mesh_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_preview.addWidget(self.label_mesh_preview)

        self.groupBox_mesh_stats = QGroupBox(self.groupBox_mesh_preview)
        self.groupBox_mesh_stats.setObjectName(u"groupBox_mesh_stats")
        self.groupBox_mesh_stats.setMinimumSize(QSize(0, 120))
        self.gridLayout_stats = QGridLayout(self.groupBox_mesh_stats)
        self.gridLayout_stats.setObjectName(u"gridLayout_stats")
        self.label_nodes = QLabel(self.groupBox_mesh_stats)
        self.label_nodes.setObjectName(u"label_nodes")
        font3 = QFont()
        font3.setFamilies([u"Microsoft YaHei UI"])
        font3.setPointSize(12)
        font3.setBold(True)
        self.label_nodes.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_nodes, 0, 0, 1, 1)

        self.label_nodes_value = QLabel(self.groupBox_mesh_stats)
        self.label_nodes_value.setObjectName(u"label_nodes_value")
        font4 = QFont()
        font4.setFamilies([u"Microsoft YaHei UI"])
        font4.setPointSize(12)
        self.label_nodes_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_nodes_value, 0, 1, 1, 1)

        self.label_elements = QLabel(self.groupBox_mesh_stats)
        self.label_elements.setObjectName(u"label_elements")
        self.label_elements.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_elements, 1, 0, 1, 1)

        self.label_elements_value = QLabel(self.groupBox_mesh_stats)
        self.label_elements_value.setObjectName(u"label_elements_value")
        self.label_elements_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_elements_value, 1, 1, 1, 1)

        self.label_quality = QLabel(self.groupBox_mesh_stats)
        self.label_quality.setObjectName(u"label_quality")
        self.label_quality.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_quality, 2, 0, 1, 1)

        self.label_quality_value = QLabel(self.groupBox_mesh_stats)
        self.label_quality_value.setObjectName(u"label_quality_value")
        self.label_quality_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_quality_value, 2, 1, 1, 1)


        self.verticalLayout_preview.addWidget(self.groupBox_mesh_stats)


        self.verticalLayout_right.addWidget(self.groupBox_mesh_preview)


        self.horizontalLayout_mesh_mgmt.addWidget(self.widget_right_panel)

        self.tabWidget_main.addTab(self.tab_mesh_management, "")
        self.tab_mesh_generation = QWidget()
        self.tab_mesh_generation.setObjectName(u"tab_mesh_generation")
        self.horizontalLayout_generation = QHBoxLayout(self.tab_mesh_generation)
        self.horizontalLayout_generation.setObjectName(u"horizontalLayout_generation")
        self.groupBox_batch_control = QGroupBox(self.tab_mesh_generation)
        self.groupBox_batch_control.setObjectName(u"groupBox_batch_control")
        self.groupBox_batch_control.setMinimumSize(QSize(400, 0))
        self.groupBox_batch_control.setMaximumSize(QSize(450, 16777215))
        self.verticalLayout_batch = QVBoxLayout(self.groupBox_batch_control)
        self.verticalLayout_batch.setObjectName(u"verticalLayout_batch")
        self.checkBox_select_all = QCheckBox(self.groupBox_batch_control)
        self.checkBox_select_all.setObjectName(u"checkBox_select_all")

        self.verticalLayout_batch.addWidget(self.checkBox_select_all)

        self.listWidget_selected_meshes = QListWidget(self.groupBox_batch_control)
        self.listWidget_selected_meshes.setObjectName(u"listWidget_selected_meshes")
        self.listWidget_selected_meshes.setMinimumSize(QSize(0, 150))

        self.verticalLayout_batch.addWidget(self.listWidget_selected_meshes)

        self.horizontalLayout_batch_buttons = QHBoxLayout()
        self.horizontalLayout_batch_buttons.setObjectName(u"horizontalLayout_batch_buttons")
        self.btn_batch_generate = QPushButton(self.groupBox_batch_control)
        self.btn_batch_generate.setObjectName(u"btn_batch_generate")
        self.btn_batch_generate.setMinimumSize(QSize(120, 40))

        self.horizontalLayout_batch_buttons.addWidget(self.btn_batch_generate)

        self.btn_stop_generation = QPushButton(self.groupBox_batch_control)
        self.btn_stop_generation.setObjectName(u"btn_stop_generation")
        self.btn_stop_generation.setMinimumSize(QSize(120, 40))

        self.horizontalLayout_batch_buttons.addWidget(self.btn_stop_generation)


        self.verticalLayout_batch.addLayout(self.horizontalLayout_batch_buttons)

        self.groupBox_progress = QGroupBox(self.groupBox_batch_control)
        self.groupBox_progress.setObjectName(u"groupBox_progress")
        self.verticalLayout_progress = QVBoxLayout(self.groupBox_progress)
        self.verticalLayout_progress.setObjectName(u"verticalLayout_progress")
        self.progressBar_generation = QProgressBar(self.groupBox_progress)
        self.progressBar_generation.setObjectName(u"progressBar_generation")
        self.progressBar_generation.setValue(0)

        self.verticalLayout_progress.addWidget(self.progressBar_generation)

        self.label_progress_text = QLabel(self.groupBox_progress)
        self.label_progress_text.setObjectName(u"label_progress_text")
        self.label_progress_text.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_progress.addWidget(self.label_progress_text)


        self.verticalLayout_batch.addWidget(self.groupBox_progress)


        self.horizontalLayout_generation.addWidget(self.groupBox_batch_control)

        self.groupBox_generation_log = QGroupBox(self.tab_mesh_generation)
        self.groupBox_generation_log.setObjectName(u"groupBox_generation_log")
        self.verticalLayout_log = QVBoxLayout(self.groupBox_generation_log)
        self.verticalLayout_log.setObjectName(u"verticalLayout_log")
        self.textEdit_generation_log = QTextEdit(self.groupBox_generation_log)
        self.textEdit_generation_log.setObjectName(u"textEdit_generation_log")
        self.textEdit_generation_log.setMinimumSize(QSize(0, 300))
        font5 = QFont()
        font5.setFamilies([u"Consolas"])
        font5.setPointSize(9)
        self.textEdit_generation_log.setFont(font5)
        self.textEdit_generation_log.setReadOnly(True)

        self.verticalLayout_log.addWidget(self.textEdit_generation_log)

        self.tableWidget_mesh_comparison = QTableWidget(self.groupBox_generation_log)
        if (self.tableWidget_mesh_comparison.columnCount() < 4):
            self.tableWidget_mesh_comparison.setColumnCount(4)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(0, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(1, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(2, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(3, __qtablewidgetitem9)
        self.tableWidget_mesh_comparison.setObjectName(u"tableWidget_mesh_comparison")
        self.tableWidget_mesh_comparison.setMinimumSize(QSize(0, 200))
        self.tableWidget_mesh_comparison.setAlternatingRowColors(True)

        self.verticalLayout_log.addWidget(self.tableWidget_mesh_comparison)


        self.horizontalLayout_generation.addWidget(self.groupBox_generation_log)

        self.tabWidget_main.addTab(self.tab_mesh_generation, "")
        self.tab_modal_analysis = QWidget()
        self.tab_modal_analysis.setObjectName(u"tab_modal_analysis")
        self.verticalLayout_modal = QVBoxLayout(self.tab_modal_analysis)
        self.verticalLayout_modal.setObjectName(u"verticalLayout_modal")
        self.groupBox_modal_control = QGroupBox(self.tab_modal_analysis)
        self.groupBox_modal_control.setObjectName(u"groupBox_modal_control")
        self.groupBox_modal_control.setMinimumSize(QSize(0, 150))
        self.horizontalLayout_modal_control = QHBoxLayout(self.groupBox_modal_control)
        self.horizontalLayout_modal_control.setObjectName(u"horizontalLayout_modal_control")
        self.verticalLayout_modal_params = QVBoxLayout()
        self.verticalLayout_modal_params.setObjectName(u"verticalLayout_modal_params")
        self.horizontalLayout_modal_count = QHBoxLayout()
        self.horizontalLayout_modal_count.setObjectName(u"horizontalLayout_modal_count")
        self.label_modal_count = QLabel(self.groupBox_modal_control)
        self.label_modal_count.setObjectName(u"label_modal_count")

        self.horizontalLayout_modal_count.addWidget(self.label_modal_count)

        self.spinBox_modal_count = QSpinBox(self.groupBox_modal_control)
        self.spinBox_modal_count.setObjectName(u"spinBox_modal_count")
        self.spinBox_modal_count.setMinimum(0)
        self.spinBox_modal_count.setMaximum(200)
        self.spinBox_modal_count.setValue(5)

        self.horizontalLayout_modal_count.addWidget(self.spinBox_modal_count)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_modal_count)

        self.horizontalLayout_freq_option = QHBoxLayout()
        self.horizontalLayout_freq_option.setObjectName(u"horizontalLayout_freq_option")
        self.checkBox_limit_freq = QCheckBox(self.groupBox_modal_control)
        self.checkBox_limit_freq.setObjectName(u"checkBox_limit_freq")
        self.checkBox_limit_freq.setChecked(True)

        self.horizontalLayout_freq_option.addWidget(self.checkBox_limit_freq)

        self.horizontalSpacer_freq_option = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_freq_option.addItem(self.horizontalSpacer_freq_option)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_freq_option)

        self.horizontalLayout_freq_range = QHBoxLayout()
        self.horizontalLayout_freq_range.setObjectName(u"horizontalLayout_freq_range")
        self.label_freq_range = QLabel(self.groupBox_modal_control)
        self.label_freq_range.setObjectName(u"label_freq_range")

        self.horizontalLayout_freq_range.addWidget(self.label_freq_range)

        self.doubleSpinBox_freq_min = QDoubleSpinBox(self.groupBox_modal_control)
        self.doubleSpinBox_freq_min.setObjectName(u"doubleSpinBox_freq_min")
        self.doubleSpinBox_freq_min.setMaximum(100000.000000000000000)
        self.doubleSpinBox_freq_min.setValue(0.000000000000000)

        self.horizontalLayout_freq_range.addWidget(self.doubleSpinBox_freq_min)

        self.label_to = QLabel(self.groupBox_modal_control)
        self.label_to.setObjectName(u"label_to")

        self.horizontalLayout_freq_range.addWidget(self.label_to)

        self.doubleSpinBox_freq_max = QDoubleSpinBox(self.groupBox_modal_control)
        self.doubleSpinBox_freq_max.setObjectName(u"doubleSpinBox_freq_max")
        self.doubleSpinBox_freq_max.setMaximum(100000.000000000000000)
        self.doubleSpinBox_freq_max.setValue(1000.000000000000000)

        self.horizontalLayout_freq_range.addWidget(self.doubleSpinBox_freq_max)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_freq_range)


        self.horizontalLayout_modal_control.addLayout(self.verticalLayout_modal_params)

        self.verticalLayout_modal_buttons = QVBoxLayout()
        self.verticalLayout_modal_buttons.setObjectName(u"verticalLayout_modal_buttons")
        self.btn_single_modal = QPushButton(self.groupBox_modal_control)
        self.btn_single_modal.setObjectName(u"btn_single_modal")
        self.btn_single_modal.setMinimumSize(QSize(150, 40))

        self.verticalLayout_modal_buttons.addWidget(self.btn_single_modal)

        self.btn_batch_modal = QPushButton(self.groupBox_modal_control)
        self.btn_batch_modal.setObjectName(u"btn_batch_modal")
        self.btn_batch_modal.setMinimumSize(QSize(150, 40))

        self.verticalLayout_modal_buttons.addWidget(self.btn_batch_modal)


        self.horizontalLayout_modal_control.addLayout(self.verticalLayout_modal_buttons)

        self.horizontalSpacer_modal = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_modal_control.addItem(self.horizontalSpacer_modal)


        self.verticalLayout_modal.addWidget(self.groupBox_modal_control)

        self.groupBox_convergence_chart = QGroupBox(self.tab_modal_analysis)
        self.groupBox_convergence_chart.setObjectName(u"groupBox_convergence_chart")
        self.verticalLayout_chart = QVBoxLayout(self.groupBox_convergence_chart)
        self.verticalLayout_chart.setObjectName(u"verticalLayout_chart")
        self.label_convergence_chart = QLabel(self.groupBox_convergence_chart)
        self.label_convergence_chart.setObjectName(u"label_convergence_chart")
        self.label_convergence_chart.setMinimumSize(QSize(0, 400))
        self.label_convergence_chart.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_chart.addWidget(self.label_convergence_chart)


        self.verticalLayout_modal.addWidget(self.groupBox_convergence_chart)

        self.tabWidget_main.addTab(self.tab_modal_analysis, "")
        self.tab_result_comparison = QWidget()
        self.tab_result_comparison.setObjectName(u"tab_result_comparison")
        self.horizontalLayout_comparison = QHBoxLayout(self.tab_result_comparison)
        self.horizontalLayout_comparison.setObjectName(u"horizontalLayout_comparison")
        self.groupBox_comparison_control = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_control.setObjectName(u"groupBox_comparison_control")
        self.groupBox_comparison_control.setMinimumSize(QSize(300, 0))
        self.groupBox_comparison_control.setMaximumSize(QSize(350, 16777215))
        self.verticalLayout_comparison_ctrl = QVBoxLayout(self.groupBox_comparison_control)
        self.verticalLayout_comparison_ctrl.setObjectName(u"verticalLayout_comparison_ctrl")
        self.label_select_meshes = QLabel(self.groupBox_comparison_control)
        self.label_select_meshes.setObjectName(u"label_select_meshes")

        self.verticalLayout_comparison_ctrl.addWidget(self.label_select_meshes)

        self.listWidget_comparison_meshes = QListWidget(self.groupBox_comparison_control)
        self.listWidget_comparison_meshes.setObjectName(u"listWidget_comparison_meshes")
        self.listWidget_comparison_meshes.setMinimumSize(QSize(0, 200))
        self.listWidget_comparison_meshes.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)

        self.verticalLayout_comparison_ctrl.addWidget(self.listWidget_comparison_meshes)

        self.groupBox_display_options = QGroupBox(self.groupBox_comparison_control)
        self.groupBox_display_options.setObjectName(u"groupBox_display_options")
        self.verticalLayout_display = QVBoxLayout(self.groupBox_display_options)
        self.verticalLayout_display.setObjectName(u"verticalLayout_display")
        self.checkBox_show_frequency = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_frequency.setObjectName(u"checkBox_show_frequency")
        self.checkBox_show_frequency.setChecked(True)

        self.verticalLayout_display.addWidget(self.checkBox_show_frequency)

        self.checkBox_show_convergence = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_convergence.setObjectName(u"checkBox_show_convergence")
        self.checkBox_show_convergence.setChecked(True)

        self.verticalLayout_display.addWidget(self.checkBox_show_convergence)

        self.checkBox_show_grid = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_grid.setObjectName(u"checkBox_show_grid")

        self.verticalLayout_display.addWidget(self.checkBox_show_grid)


        self.verticalLayout_comparison_ctrl.addWidget(self.groupBox_display_options)

        self.btn_export_results = QPushButton(self.groupBox_comparison_control)
        self.btn_export_results.setObjectName(u"btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(0, 40))

        self.verticalLayout_comparison_ctrl.addWidget(self.btn_export_results)


        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_control)

        self.groupBox_comparison_chart = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_chart.setObjectName(u"groupBox_comparison_chart")
        self.verticalLayout_comparison_chart = QVBoxLayout(self.groupBox_comparison_chart)
        self.verticalLayout_comparison_chart.setObjectName(u"verticalLayout_comparison_chart")
        self.label_comparison_chart = QLabel(self.groupBox_comparison_chart)
        self.label_comparison_chart.setObjectName(u"label_comparison_chart")
        self.label_comparison_chart.setMinimumSize(QSize(0, 600))
        self.label_comparison_chart.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_comparison_chart.addWidget(self.label_comparison_chart)


        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_chart)

        self.tabWidget_main.addTab(self.tab_result_comparison, "")

        self.verticalLayout_main.addWidget(self.tabWidget_main)

        self.horizontalLayout_navigation = QHBoxLayout()
        self.horizontalLayout_navigation.setSpacing(15)
        self.horizontalLayout_navigation.setObjectName(u"horizontalLayout_navigation")
        self.btn_generate_mesh = QPushButton(self.centralwidget)
        self.btn_generate_mesh.setObjectName(u"btn_generate_mesh")
        self.btn_generate_mesh.setMinimumSize(QSize(150, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_generate_mesh)

        self.btn_view_results = QPushButton(self.centralwidget)
        self.btn_view_results.setObjectName(u"btn_view_results")
        self.btn_view_results.setMinimumSize(QSize(150, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_view_results)

        self.horizontalSpacer_nav = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_nav)

        self.btn_previous = QPushButton(self.centralwidget)
        self.btn_previous.setObjectName(u"btn_previous")
        self.btn_previous.setMinimumSize(QSize(180, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_previous)

        self.btn_next = QPushButton(self.centralwidget)
        self.btn_next.setObjectName(u"btn_next")
        self.btn_next.setMinimumSize(QSize(180, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_next)

        self.btn_main_menu = QPushButton(self.centralwidget)
        self.btn_main_menu.setObjectName(u"btn_main_menu")
        self.btn_main_menu.setMinimumSize(QSize(120, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_main_menu)


        self.verticalLayout_main.addLayout(self.horizontalLayout_navigation)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        self.tabWidget_main.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u591a\u7f51\u683c\u7ba1\u7406\u7cfb\u7edf", None))
        self.label_title.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u65e0\u5173\u6027\u9a8c\u8bc1\u7cfb\u7edf", None))
        self.label_title.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    color: #34495e;\n"
"    background-color: transparent;\n"
"    border-bottom: 2px solid #3498db;\n"
"    padding-bottom: 10px;\n"
"}", None))
        self.tabWidget_main.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTabWidget::pane {\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 0 6px 6px 6px;\n"
"    background: white;\n"
"    top: -1px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background: #f5f7fa;\n"
"    border: 1px solid #dcdfe6;\n"
"    border-bottom: none;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 120px;\n"
"    padding: 12px 20px;\n"
"    font-weight: 500;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background: white;\n"
"    border-bottom-color: white;\n"
"    color: #3498db;\n"
"}\n"
"\n"
"QTabBar::tab:hover:!selected {\n"
"    background: #ecf0f1;\n"
"}", None))
        self.groupBox_mesh_params.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u53c2\u6570\u7ba1\u7406", None))
        self.groupBox_mesh_params.setStyleSheet(QCoreApplication.translate("MainWindow", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.btn_add_mesh.setText(QCoreApplication.translate("MainWindow", u"\u6dfb\u52a0\u7f51\u683c", None))
        self.btn_import_mesh.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u5165\u914d\u7f6e", None))
        self.btn_export_mesh.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u51fa\u914d\u7f6e", None))
        ___qtablewidgetitem = self.tableWidget_mesh_params.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u540d\u79f0", None));
        ___qtablewidgetitem1 = self.tableWidget_mesh_params.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"\u5c3a\u5bf8(mm)", None));
        ___qtablewidgetitem2 = self.tableWidget_mesh_params.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"\u72b6\u6001", None));
        ___qtablewidgetitem3 = self.tableWidget_mesh_params.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570", None));
        ___qtablewidgetitem4 = self.tableWidget_mesh_params.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570", None));
        ___qtablewidgetitem5 = self.tableWidget_mesh_params.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"\u64cd\u4f5c", None));
        self.tableWidget_mesh_params.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTableWidget {\n"
"    border: 1px solid #dcdfe6;\n"
"    background-color: white;\n"
"    gridline-color: #e9eaec;\n"
"    selection-background-color: #3498db;\n"
"    selection-color: white;\n"
"    alternate-background-color: #f5f7fa;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: #f5f7fa;\n"
"    border: 1px solid #dcdfe6;\n"
"    padding: 8px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}", None))
        self.groupBox_mesh_preview.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9884\u89c8\u4e0e\u8be6\u60c5", None))
        self.groupBox_mesh_preview.setStyleSheet(QCoreApplication.translate("MainWindow", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.label_select_mesh.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u7f51\u683c:", None))
        self.label_mesh_preview.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9884\u89c8\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_mesh_preview.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.groupBox_mesh_stats.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u7edf\u8ba1\u4fe1\u606f", None))
        self.label_nodes.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570:", None))
        self.label_nodes_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_nodes_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #3498db;", None))
        self.label_elements.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570:", None))
        self.label_elements_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_elements_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #3498db;", None))
        self.label_quality.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u8d28\u91cf:", None))
        self.label_quality_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_quality_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #2ecc71;", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_mesh_management), QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u7ba1\u7406", None))
        self.groupBox_batch_control.setTitle(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u64cd\u4f5c\u63a7\u5236", None))
        self.checkBox_select_all.setText(QCoreApplication.translate("MainWindow", u"\u5168\u9009\u7f51\u683c", None))
        self.btn_batch_generate.setText(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u751f\u6210", None))
        self.btn_batch_generate.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.btn_stop_generation.setText(QCoreApplication.translate("MainWindow", u"\u505c\u6b62\u751f\u6210", None))
        self.btn_stop_generation.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #e74c3c;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #c0392b;\n"
"}", None))
        self.groupBox_progress.setTitle(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u8fdb\u5ea6", None))
        self.label_progress_text.setText(QCoreApplication.translate("MainWindow", u"\u7b49\u5f85\u5f00\u59cb...", None))
        self.groupBox_generation_log.setTitle(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u65e5\u5fd7\u4e0e\u8d28\u91cf\u5bf9\u6bd4", None))
        self.textEdit_generation_log.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTextEdit {\n"
"    border: 1px solid #dcdfe6;\n"
"    background-color: #2c3e50;\n"
"    color: #ecf0f1;\n"
"    selection-background-color: #3498db;\n"
"}", None))
        ___qtablewidgetitem6 = self.tableWidget_mesh_comparison.horizontalHeaderItem(0)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u540d\u79f0", None));
        ___qtablewidgetitem7 = self.tableWidget_mesh_comparison.horizontalHeaderItem(1)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570", None));
        ___qtablewidgetitem8 = self.tableWidget_mesh_comparison.horizontalHeaderItem(2)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570", None));
        ___qtablewidgetitem9 = self.tableWidget_mesh_comparison.horizontalHeaderItem(3)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("MainWindow", u"\u8d28\u91cf", None));
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_mesh_generation), QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u751f\u6210", None))
        self.groupBox_modal_control.setTitle(QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u8ba1\u7b97\u63a7\u5236", None))
        self.label_modal_count.setText(QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u9636\u6570:", None))
        self.checkBox_limit_freq.setText(QCoreApplication.translate("MainWindow", u"\u9650\u5236\u9891\u7387\u8303\u56f4", None))
        self.label_freq_range.setText(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u8303\u56f4:", None))
        self.doubleSpinBox_freq_min.setSuffix(QCoreApplication.translate("MainWindow", u" Hz", None))
        self.label_to.setText(QCoreApplication.translate("MainWindow", u"-", None))
        self.doubleSpinBox_freq_max.setSuffix(QCoreApplication.translate("MainWindow", u" Hz", None))
        self.btn_single_modal.setText(QCoreApplication.translate("MainWindow", u"\u5355\u4e2a\u6a21\u6001\u8ba1\u7b97", None))
        self.btn_batch_modal.setText(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u6a21\u6001\u8ba1\u7b97", None))
        self.btn_batch_modal.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.groupBox_convergence_chart.setTitle(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u6536\u655b\u6027\u5206\u6790", None))
        self.label_convergence_chart.setText(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u6536\u655b\u6027\u56fe\u8868\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_convergence_chart.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_modal_analysis), QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u5206\u6790", None))
        self.groupBox_comparison_control.setTitle(QCoreApplication.translate("MainWindow", u"\u5bf9\u6bd4\u63a7\u5236", None))
        self.label_select_meshes.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u5bf9\u6bd4\u7f51\u683c:", None))
        self.groupBox_display_options.setTitle(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u9009\u9879", None))
        self.checkBox_show_frequency.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u9891\u7387\u503c", None))
        self.checkBox_show_convergence.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u6536\u655b\u7ebf", None))
        self.checkBox_show_grid.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u7f51\u683c\u7ebf", None))
        self.btn_export_results.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u51fa\u7ed3\u679c", None))
        self.groupBox_comparison_chart.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u65e0\u5173\u6027\u5206\u6790\u56fe\u8868", None))
        self.label_comparison_chart.setText(QCoreApplication.translate("MainWindow", u"\u591a\u7f51\u683c\u7ed3\u679c\u5bf9\u6bd4\u56fe\u8868\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_comparison_chart.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_result_comparison), QCoreApplication.translate("MainWindow", u"\u7ed3\u679c\u5bf9\u6bd4", None))
        self.btn_generate_mesh.setText(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u7f51\u683c", None))
        self.btn_generate_mesh.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.btn_view_results.setText(QCoreApplication.translate("MainWindow", u"\u67e5\u770b\u7ed3\u679c", None))
        self.btn_view_results.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}", None))
        self.btn_previous.setText(QCoreApplication.translate("MainWindow", u"上一步(约束设置)", None))
        self.btn_previous.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #7f8c8d;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6c7b7d;\n"
"}", None))
        self.btn_next.setText(QCoreApplication.translate("MainWindow", u"下一步(计算结果)", None))
        self.btn_next.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}", None))
        self.btn_main_menu.setText(QCoreApplication.translate("MainWindow", u"\u4e3b\u83dc\u5355", None))
        self.btn_main_menu.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #7f8c8d;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6c7b7d;\n"
"}", None))
    # retranslateUi

