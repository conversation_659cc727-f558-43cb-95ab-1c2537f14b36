"""
前处理界面控制器

此模块负责处理前处理界面的所有操作，主要包括：
1. 界面跳转控制
2. 前处理参数配置
3. 几何模型导入导出

工作流程：
1. 初始化前处理界面
2. 处理几何模型
3. 配置前处理参数
4. 界面跳转管理

作者: [作者名]
日期: [日期]
"""

import json
import logging
import os
import time
import threading
from typing import Optional
from PySide6.QtWidgets import QWidget, QProgressDialog, QListWidgetItem, QDialog
from window_manager import WindowManager, WindowType
from error_handler import (
    ErrorHandler, AppError, ErrorSeverity,
    ValidationError, FileOperationError, AnsysError
)
from PySide6.QtCore import QCoreApplication, QTimer, Qt
from resource_manager import ResourceManager
import subprocess
import glob
from datetime import datetime
import sys

class RealTimeLogWriter:
    """实时日志写入器，用于捕获进程输出并实时写入日志文件"""

    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.log_file = None
        self.running = False

    def start(self):
        """开始日志写入"""
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file_path)
            if not os.path.exists(log_dir):
                os.makedirs(log_dir)

            # 统一使用UTF-8编码，确保中文字符正确处理
            self.log_file = open(self.log_file_path, 'w', encoding='utf-8', buffering=1)  # 行缓冲
            self.running = True
            return True
        except Exception as e:
            print(f"启动日志写入器失败: {e}")
            return False

    def write_line(self, line: str):
        """写入一行日志"""
        if self.running and self.log_file:
            try:
                # 添加时间戳
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                formatted_line = f"[{timestamp}] {line}"

                # 写入文件并立即刷新
                self.log_file.write(formatted_line + '\n')
                self.log_file.flush()

                # 同时输出到控制台
                print(formatted_line)

                # 强制刷新标准输出，确保实时显示
                import sys
                if hasattr(sys.stdout, 'flush'):
                    sys.stdout.flush()

            except Exception as e:
                print(f"写入日志失败: {e}")

    def stop(self):
        """停止日志写入"""
        self.running = False
        if self.log_file:
            try:
                self.log_file.close()
            except:
                pass
            self.log_file = None

def capture_process_output(process, log_writer):
    """捕获进程输出的线程函数"""
    try:
        # 读取标准输出
        for line in iter(process.stdout.readline, ''):
            if not line:
                break
            # 统一编码处理，确保中文正确显示
            try:
                clean_line = line.strip()
                if clean_line:
                    # 确保字符串是正确的UTF-8编码
                    if isinstance(clean_line, bytes):
                        # 如果是字节串，尝试多种编码解码
                        for encoding in ['utf-8', 'gbk', 'cp936']:
                            try:
                                clean_line = clean_line.decode(encoding)
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            # 如果所有编码都失败，使用错误容忍模式
                            clean_line = clean_line.decode('utf-8', errors='replace')

                    log_writer.write_line(clean_line)
            except Exception as e:
                # 记录编码错误但继续处理
                print(f"编码处理错误: {e}, 原始行: {repr(line)}")
                log_writer.write_line(f"[编码错误] {repr(line.strip())}")

        # 读取标准错误
        for line in iter(process.stderr.readline, ''):
            if not line:
                break
            try:
                clean_line = line.strip()
                if clean_line:
                    # 同样的编码处理逻辑
                    if isinstance(clean_line, bytes):
                        for encoding in ['utf-8', 'gbk', 'cp936']:
                            try:
                                clean_line = clean_line.decode(encoding)
                                break
                            except UnicodeDecodeError:
                                continue
                        else:
                            clean_line = clean_line.decode('utf-8', errors='replace')

                    log_writer.write_line(f"ERROR: {clean_line}")
            except Exception as e:
                print(f"错误输出编码处理错误: {e}")
                log_writer.write_line(f"ERROR: [编码错误] {repr(line.strip())}")

    except Exception as e:
        log_writer.write_line(f"捕获输出时出错: {e}")
    finally:
        # 等待进程结束
        process.wait()

# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口

def cleanup_lock_files(workbench_project_path: str) -> None:
    """清理Workbench项目的lock文件
    
    Args:
        workbench_project_path: Workbench项目文件路径
    """
    # 获取项目文件所在目录
    project_dir = os.path.dirname(workbench_project_path)
    # 构建files目录路径（将.wbpj替换为_files）
    files_dir = os.path.join(project_dir, os.path.splitext(os.path.basename(workbench_project_path))[0] + "_files")
    
    if os.path.exists(files_dir):
        for file in os.listdir(files_dir):
            file_path = os.path.join(files_dir, file)
            if os.path.isfile(file_path) and file.endswith('.lock'):
                try:
                    os.remove(file_path)
                    print(f"已删除lock文件: {file_path}")
                except Exception as e:
                    print(f"警告: 无法删除lock文件 {file_path}: {str(e)}")

def generate_face_json(window_manager: WindowManager) -> None:
    """生成面处理配置文件
    
    此函数完成以下任务：
    1. 读取原始脚本
    2. 修改目标路径
    3. 创建并执行Workbench脚本
    4. 生成face.json文件
    
    Args:
        window_manager: 窗口管理器实例
    """
    main_window = window_manager.get_window(WindowType.MAIN)
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()
    resource_manager = ResourceManager()
    active_files = []  # 跟踪需要清理的临时文件
    
    if not main_window or not pre_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    try:
        # 1. 读取原始脚本
        try:
            source_file = resource_manager.get_resource_path("originscript", "prescript.py")
            print(f"读取源文件: {source_file}")  # 添加调试信息
            with open(source_file, "r", encoding="utf-8") as f:
                script_content = f.read()
                if not script_content:
                    raise FileOperationError(
                        "源脚本文件内容为空",
                        details={'source_file': source_file}
                    )
                print(f"源文件内容长度: {len(script_content)} 字节")  # 添加调试信息
        except Exception as e:
            raise FileOperationError(
                "读取源脚本文件失败",
                details={'source_file': source_file}
            ) from e

        # 2. 修改脚本内容
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_filename = f"face_{timestamp}.json"
        target_dir = os.path.join(resource_manager.output_dir, json_filename)
        target_dir = target_dir.replace("\\", "/")
        
        # 清理旧的JSON文件
        try:
            pattern = os.path.join(resource_manager.output_dir, "face_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            # 保留最新的5个版本
            for old_file in json_files[5:]:
                try:
                    os.remove(old_file)
                except Exception as e:
                    print(f"警告: 无法删除旧的JSON文件 {old_file}: {str(e)}")
        except Exception as e:
            print(f"警告: 清理旧JSON文件时出错: {str(e)}")
        
        # 修改脚本中的输出路径
        script_content = script_content.replace(
            'OUTPUT_FILE_PATH = r"D:/data/output/face_selections.json"',
            f'OUTPUT_FILE_PATH = r"{target_dir}"'
        )
        
        # 生成输出文件路径
        output_dir = os.path.dirname(target_dir)
        log_output_path = os.path.join(output_dir, "ansys_automation_log.log").replace("\\", "/")

        # 生成JSON输出文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file_path = os.path.join(output_dir, f"face_{timestamp}.json").replace("\\", "/")

        # 3. 读取原始脚本并进行路径替换
        try:
            # 直接使用原始脚本
            source_script_path = resource_manager.get_resource_path("originscript", "prescript.py")
            pre_script = resource_manager.get_work_path("script", "prepy_copy.py")

            # 读取原始脚本
            with open(source_script_path, "r", encoding="utf-8") as f:
                script_content = f.read()

            # 使用正则表达式进行准确的路径替换
            import re

            # 替换日志文件路径 - 匹配任何LOG_FILE_PATH赋值
            log_pattern = r'LOG_FILE_PATH = r"[^"]*"'
            script_content = re.sub(log_pattern, f'LOG_FILE_PATH = r"{log_output_path}"', script_content)

            # 替换输出文件路径 - 匹配任何OUTPUT_FILE_PATH赋值
            output_pattern = r'OUTPUT_FILE_PATH = r"[^"]*"'
            script_content = re.sub(output_pattern, f'OUTPUT_FILE_PATH = r"{output_file_path}"', script_content)

            # 写入修改后的脚本
            with open(pre_script, "w", encoding="utf-8") as f:
                f.write(script_content)

        except Exception as e:
            raise FileOperationError(
                "读取和修改原始脚本失败",
                details={'source_script': source_script_path, 'target_script': pre_script}
            ) from e

        # 4. 创建Workbench控制脚本
        wb_script_content = f'''
# encoding: utf-8
Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
'''
        # 5. 添加执行命令
        pre_script = pre_script.replace("\\", "/")
        wb_script_content += f'''
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{pre_script}")')
model1.Exit()
Save(Overwrite=True)
'''

        # 6. 创建Workbench脚本文件
        try:
            wb_script_file = resource_manager.create_temp_file(prefix="pre_wb", suffix=".py")
            active_files.append(wb_script_file)
            with open(wb_script_file, "w", encoding="utf-8") as f:
                f.write(wb_script_content)
        except Exception as e:
            raise FileOperationError(
                "创建Workbench脚本文件失败",
                details={'script_file': wb_script_file}
            ) from e

        # 7. 创建批处理文件和日志文件路径
        try:
            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            wb_script_file = wb_script_file.replace("\\", "/")

            # 创建日志文件路径
            log_file_path = os.path.join(resource_manager.output_dir, f"pre_process_{timestamp}.log")
            log_file_path = log_file_path.replace("\\", "/")

            # 修改批处理文件，移除输出重定向以便实时捕获
            # 添加编码设置以改善中文显示
            bat_content = f'''@echo off
chcp 65001 >nul 2>&1
"{ansys_path}" -B -R "{wb_script_file}"'''
            bat_file = resource_manager.create_temp_file(prefix="pre", suffix=".bat")
            active_files.append(bat_file)
            with open(bat_file, "w", encoding="utf-8") as f:
                f.write(bat_content)
        except Exception as e:
            raise FileOperationError(
                "创建批处理文件失败",
                details={'bat_file': bat_file}
            ) from e

        # 8. 清理lock文件
        try:
            cleanup_lock_files(main_window.WORKBENCH_Project_File)
        except Exception as e:
            print(f"警告: 清理lock文件时出错: {str(e)}")

        # 9. 创建并显示进度对话框
        from views.project_progress_dialog import ProjectProgressDialog
        # 创建进度对话框，但先不启动监控
        progress_dialog = ProjectProgressDialog(log_file_path, pre_window, auto_start_monitoring=False)

        progress_dialog.setWindowTitle("前处理进度")
        progress_dialog.title_label.setText("正在执行ANSYS前处理操作...")

        # 配置前处理进度项目（界面中文化，后台英文化）
        progress_dialog.progress_items = [
            ("脚本开始执行", False),
            ("清理数字命名选择", False),
            ("创建ROTOR命名选择", False),
            ("统一命名选择为小写", False),
            ("导出面选择到JSON", False),
            ("刷新UI树", False),
            ("脚本执行完毕", False)
        ]

        # 配置进度关键词映射（与脚本英文输出精确匹配，但界面显示中文）
        progress_dialog.progress_keywords = {
            "======== Starting Four-in-One Automation Script ========": 0,
            "Task 1: Starting cleanup of numeric Named Selections.": 1,
            "Task 2: Creating/updating Named Selection for 'ROTOR'": 2,
            "Task 3: Starting to unify Named Selection names to lowercase.": 3,
            "Task 4: Starting export of specified face selections.": 4,
            "UI Tree has been refreshed.": 5,
            "======== All automation tasks have been executed successfully ========": 6
        }

        # 重置进度列表UI
        progress_dialog.progress_list.clear()
        for item_text, _ in progress_dialog.progress_items:
            list_item = QListWidgetItem(f"⏳ {item_text}")
            list_item.setData(Qt.UserRole, False)
            list_item.setFlags(list_item.flags() & ~Qt.ItemIsSelectable)
            progress_dialog.progress_list.addItem(list_item)

        # 重置进度条
        progress_dialog.progress_bar.setRange(0, len(progress_dialog.progress_items))
        progress_dialog.progress_bar.setValue(0)

        # 现在重新启动监控，使用正确的关键词映射
        progress_dialog.start_monitoring()

        # 创建实时日志写入器
        log_writer = RealTimeLogWriter(log_file_path)
        if not log_writer.start():
            raise FileOperationError(
                "启动日志写入器失败",
                details={'log_file': log_file_path}
            )

        # 写入初始日志
        log_writer.write_line("=== 开始执行ANSYS前处理脚本 ===")
        log_writer.write_line(f"执行命令: {bat_content}")
        log_writer.write_line("开始执行四合一自动化脚本")

        # 确保 ANSYS 脚本输出的日志文件存在并且为空
        ansys_log_path = os.path.join(resource_manager.output_dir, "ansys_automation_log.log")
        try:
            # 统一使用UTF-8编码创建 ANSYS 日志文件
            with open(ansys_log_path, 'w', encoding='utf-8') as f:
                f.write("")
        except Exception as e:
            print(f"无法初始化 ANSYS 日志文件: {e}")

        # 启动 ANSYS 日志文件监控线程
        def monitor_ansys_log():
            """监控 ANSYS 脚本直接写入的日志文件"""
            last_size = 0
            while True:
                try:
                    if os.path.exists(ansys_log_path):
                        current_size = os.path.getsize(ansys_log_path)
                        if current_size > last_size:
                            # 使用UTF-8编码读取ANSYS日志文件
                            try:
                                with open(ansys_log_path, 'r', encoding='utf-8') as f:
                                    f.seek(last_size)
                                    new_content = f.read()
                                    if new_content.strip():
                                        # 将 ANSYS 日志内容转发到主日志文件
                                        for line in new_content.strip().split('\n'):
                                            if line.strip():
                                                log_writer.write_line(line.strip())
                                        last_size = current_size
                            except Exception as e:
                                print(f"读取ANSYS日志文件失败: {e}")

                    time.sleep(0.1)  # 更频繁的检查
                except Exception as e:
                    print(f"ANSYS 日志监控错误: {e}")
                    time.sleep(1)

        # 启动 ANSYS 日志监控线程
        ansys_log_thread = threading.Thread(target=monitor_ansys_log, daemon=True)
        ansys_log_thread.start()

        # 执行批处理文件，使用实时输出捕获
        try:
            process = subprocess.Popen(
                f'"{bat_file}"',
                shell=True,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,  # 行缓冲
                universal_newlines=True,
                encoding='utf-8',  # 明确指定编码
                errors='ignore'    # 忽略编码错误
            )

            # 启动输出捕获线程
            output_thread = threading.Thread(
                target=capture_process_output,
                args=(process, log_writer),
                daemon=True
            )
            output_thread.start()

            # 显示进度对话框
            result = progress_dialog.exec()

        finally:
            # 确保日志写入器停止
            log_writer.stop()
        
        # 处理对话框结果
        if result == QDialog.Accepted and progress_dialog.is_operation_completed():
            # 成功完成，等待进程结束
            try:
                if process.poll() is None:
                    log_writer.write_line("等待进程正常结束...")
                    process.wait(timeout=10)
                log_writer.write_line("=== ANSYS前处理脚本执行完成 ===")
            except subprocess.TimeoutExpired:
                log_writer.write_line("进程超时，强制终止")
                process.kill()

            # 清理临时文件
            for file in active_files:
                try:
                    if os.path.exists(file):
                        os.remove(file)
                except Exception as e:
                    print(f"警告: 无法删除临时文件 {file}: {str(e)}")

            # 调用finish_pre完成前处理并初始化连接界面
            finish_pre(window_manager)
        else:
            # 用户取消或操作失败
            if process.poll() is None:
                # 如果进程仍在运行，尝试终止它
                try:
                    log_writer.write_line("用户取消操作，正在终止进程...")
                    process.terminate()
                    process.wait(timeout=2)
                    log_writer.write_line("进程已终止")
                except subprocess.TimeoutExpired:
                    log_writer.write_line("强制终止进程")
                    process.kill()
                except Exception as e:
                    log_writer.write_line(f"终止进程时出错: {str(e)}")

            error_handler.handle_error(
                AppError(
                    "前处理操作已取消或失败",
                    ErrorSeverity.WARNING
                ),
                pre_window
            )
            
    except AppError as e:
        # 处理应用程序异常
        error_handler.handle_error(e, pre_window)
    except Exception as e:
        # 处理其他未预期的异常
        error_handler.handle_exception(e, pre_window)

def finish_pre(window_manager: WindowManager) -> None:
    """完成前处理界面的配置并初始化连接界面
    
    此函数完成以下任务：
    1. 获取连接窗口实例
    2. 读取面处理的JSON配置文件
    3. 初始化连接界面的下拉选项
    4. 设置前处理完成状态
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 获取窗口实例和错误处理器
    connection_window = window_manager.get_window(WindowType.CONNECTION)
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()
    resource_manager = ResourceManager()
    
    if not connection_window:
        error_handler.handle_error(
            AppError("无法获取连接窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    try:
        # 1. 读取最新的面处理配置文件
        try:
            pattern = os.path.join(resource_manager.output_dir, "face_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            
            if not json_files:
                raise FileOperationError(
                    "找不到面处理配置文件",
                    details={'search_pattern': pattern}
                )
            
            pre_json_path = json_files[0]  # 获取最新的文件
            with open(pre_json_path, 'r', encoding='utf-8') as f:
                pre_json = json.load(f)
        except FileNotFoundError:
            raise FileOperationError(
                "找不到面处理配置文件",
                details={'file_path': pre_json_path}
            )
        except json.JSONDecodeError:
            raise FileOperationError(
                "面处理配置文件格式错误",
                details={'file_path': pre_json_path}
            )
        
        # 2. 获取配置项
        pre_json_keys = pre_json.keys()
        if not pre_json_keys:
            raise ValidationError("面处理配置文件中没有有效的选项")
            
        # 3. 初始化下拉选项
        try:
            options = [QCoreApplication.translate("MainWindow", key, None) for key in pre_json_keys]
            
            # 轴承连接选项
            connection_window.ui.reference_zhou1.clear()
            connection_window.ui.reference_zhou2.clear()
            connection_window.ui.mobile_zhou1.clear()
            connection_window.ui.mobile_zhou2.clear()
            
            # Bushing连接选项
            connection_window.ui.mobile_bushing1.clear()
            connection_window.ui.mobile_bushing2.clear()
            connection_window.ui.mobile_bushing3.clear()
            connection_window.ui.mobile_bushing4.clear()
            
            # 添加选项
            # 定义映射关系：选项名称 -> 对应的下拉框控件
            dropdown_mapping = {
                "bearing_1": connection_window.ui.reference_zhou1,
                "bearing_2": connection_window.ui.reference_zhou2,
                "dianji_1": connection_window.ui.mobile_zhou1,
                "dianji_2": connection_window.ui.mobile_zhou2,
                "bushing_1": connection_window.ui.mobile_bushing1,
                "bushing_2": connection_window.ui.mobile_bushing2,
                "bushing_3": connection_window.ui.mobile_bushing3,
                "bushing_4": connection_window.ui.mobile_bushing4
            }
            
            # 遍历所有选项，如果在映射中存在则添加到对应下拉框
            for text in options:
                if text in dropdown_mapping:
                    dropdown_mapping[text].addItem(text)

        except Exception as e:
            raise AppError(
                "初始化连接选项失败",
                ErrorSeverity.CRITICAL,
                details={'error': str(e)}
            )
            
        # 4. 设置完成状态
        window_manager.process_status.set_completed(WindowType.PRE)
        
        # 5. 显示成功消息
        error_handler.handle_error(
            AppError(
                "前处理配置已完成",
                ErrorSeverity.INFO
            ),
            pre_window
        )
        
        
    except AppError as e:
        # 处理应用程序异常
        error_handler.handle_error(e, pre_window)
    except Exception as e:
        # 处理其他未预期的异常
        error_handler.handle_exception(e, pre_window)

def pre_slot(window_manager: WindowManager) -> None:
    """初始化前处理界面的所有槽函数连接
    
    此函数负责将前处理界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮和前处理功能按钮。
    
    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    
    if not pre_window:
        ErrorHandler().handle_error(
            AppError("无法获取前处理窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step

    pre_window.ui.push_mainui.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 前处理的下一步应该是连接设置（按钮文本已更新为"下一步(连接设置)"）
    pre_window.ui.push_meshui.clicked.connect(
        lambda: navigate_to_next_step(window_manager, WindowType.PRE))
    
    # 只连接generate_face_json，不再直接连接finish_pre
    pre_window.ui.push_finish.clicked.connect(
        lambda: generate_face_json(window_manager))

    # 连接材料管理相关槽函数
    connect_material_management_slots(window_manager)


def connect_material_management_slots(window_manager: WindowManager) -> None:
    """连接材料管理相关的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    if not pre_window:
        error_handler.handle_error(
            AppError("无法获取前处理窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    try:
        # 材料操作按钮连接
        pre_window.ui.pushButton_material_new.clicked.connect(
            lambda: create_new_material(window_manager))

        pre_window.ui.pushButton_material_copy.clicked.connect(
            lambda: copy_selected_material(window_manager))

        pre_window.ui.pushButton_material_delete.clicked.connect(
            lambda: delete_selected_material(window_manager))

        # 材料分配按钮连接
        pre_window.ui.pushButton_assign_material.clicked.connect(
            lambda: assign_material_to_structure(window_manager))

        pre_window.ui.pushButton_apply_all.clicked.connect(
            lambda: apply_material_to_all_structures(window_manager))

        logging.info("材料管理槽函数连接完成")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"材料管理槽函数连接失败: {e}", ErrorSeverity.ERROR)
        )


def create_new_material(window_manager: WindowManager) -> None:
    """创建新材料

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    if not pre_window:
        error_handler.handle_error(
            AppError("无法获取前处理窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    try:
        from views.material_create_dialog import MaterialCreateDialog
        from core.material_manager import get_material_library

        # 获取材料库实例
        material_library = get_material_library()

        # 获取已存在的材料名称列表
        existing_names = [material.name for material in material_library.materials.values()]

        # 显示材料创建对话框
        new_material = MaterialCreateDialog.create_material(
            parent=pre_window,
            existing_names=existing_names
        )

        if new_material:
            # 添加材料到材料库
            success = material_library.add_material(new_material)

            if success:
                # 刷新材料库树形视图
                pre_window.refresh_material_tree()

                # 选中新创建的材料
                pre_window.select_material_by_id(new_material.id)

                # 显示成功消息
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.information(
                    pre_window,
                    "创建成功",
                    f"材料 '{new_material.name}' 创建成功！"
                )

                logging.info(f"成功创建新材料: {new_material.name}")
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(
                    pre_window,
                    "创建失败",
                    f"无法创建材料 '{new_material.name}'，可能名称已存在。"
                )
                logging.warning(f"创建材料失败: {new_material.name}")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"创建新材料失败: {e}", ErrorSeverity.ERROR)
        )
        logging.error(f"创建新材料时发生异常: {e}")


def copy_selected_material(window_manager: WindowManager) -> None:
    """复制选中的材料

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    if not pre_window:
        error_handler.handle_error(
            AppError("无法获取前处理窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    try:
        from PySide6.QtWidgets import QMessageBox
        from core.material_manager import get_material_library

        # 获取当前选中的材料
        selected_material = pre_window.get_selected_material()

        if not selected_material:
            QMessageBox.warning(
                pre_window,
                "复制失败",
                "请先选择要复制的材料。"
            )
            return

        # 获取材料库实例
        material_library = get_material_library()

        # 创建材料副本
        copied_material = selected_material.copy()

        # 确保副本名称不重复
        base_name = copied_material.name
        counter = 1
        while material_library.get_material_by_name(copied_material.name):
            copied_material.name = f"{base_name}_{counter}"
            counter += 1

        # 添加副本到材料库
        success = material_library.add_material(copied_material)

        if success:
            # 刷新材料库树形视图
            pre_window.refresh_material_tree()

            # 选中新复制的材料
            pre_window.select_material_by_id(copied_material.id)

            # 显示成功消息
            QMessageBox.information(
                pre_window,
                "复制成功",
                f"材料 '{selected_material.name}' 已复制为 '{copied_material.name}'！"
            )

            logging.info(f"成功复制材料: {selected_material.name} -> {copied_material.name}")
        else:
            QMessageBox.warning(
                pre_window,
                "复制失败",
                f"无法复制材料 '{selected_material.name}'。"
            )
            logging.warning(f"复制材料失败: {selected_material.name}")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"复制材料失败: {e}", ErrorSeverity.ERROR)
        )
        logging.error(f"复制材料时发生异常: {e}")


def delete_selected_material(window_manager: WindowManager) -> None:
    """删除选中的材料

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    if not pre_window:
        error_handler.handle_error(
            AppError("无法获取前处理窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    try:
        from PySide6.QtWidgets import QMessageBox
        from core.material_manager import get_material_library

        # 获取当前选中的材料
        selected_material = pre_window.get_selected_material()

        if not selected_material:
            QMessageBox.warning(
                pre_window,
                "删除失败",
                "请先选择要删除的材料。"
            )
            return

        # 检查是否为只读材料（预置材料）
        if selected_material.is_readonly:
            QMessageBox.warning(
                pre_window,
                "删除失败",
                f"材料 '{selected_material.name}' 是预置材料，不允许删除。\n\n"
                "只有自定义材料库中的材料可以删除。"
            )
            return

        # 显示确认对话框
        reply = QMessageBox.question(
            pre_window,
            "确认删除",
            f"确定要删除材料 '{selected_material.name}' 吗？\n\n"
            "此操作无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply != QMessageBox.StandardButton.Yes:
            return

        # 获取材料库实例
        material_library = get_material_library()

        # 删除材料
        success = material_library.remove_material(selected_material.id)

        if success:
            # 保存被删除材料的信息
            deleted_material_name = selected_material.name
            deleted_material_id = selected_material.id

            # 刷新材料库树形视图
            pre_window.refresh_material_tree()

            # 选中下一个材料
            pre_window.select_next_material_after_deletion(deleted_material_id)

            # 显示成功消息
            QMessageBox.information(
                pre_window,
                "删除成功",
                f"材料 '{deleted_material_name}' 已成功删除！"
            )

            logging.info(f"成功删除材料: {deleted_material_name}")
        else:
            QMessageBox.warning(
                pre_window,
                "删除失败",
                f"无法删除材料 '{selected_material.name}'。"
            )
            logging.warning(f"删除材料失败: {selected_material.name}")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"删除材料失败: {e}", ErrorSeverity.ERROR)
        )
        logging.error(f"删除材料时发生异常: {e}")


def assign_material_to_structure(window_manager: WindowManager) -> None:
    """分配材料到结构体（占位符实现）

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    try:
        # 占位符实现：显示提示信息
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(
            pre_window,
            "分配材料",
            "分配材料到结构体功能正在开发中...\n\n这是一个占位符实现。"
        )
        logging.info("分配材料功能被调用")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"分配材料失败: {e}", ErrorSeverity.ERROR)
        )


def apply_material_to_all_structures(window_manager: WindowManager) -> None:
    """应用材料到所有结构体（占位符实现）

    Args:
        window_manager: 窗口管理器实例
    """
    pre_window = window_manager.get_window(WindowType.PRE)
    error_handler = ErrorHandler()

    try:
        # 占位符实现：显示提示信息
        from PySide6.QtWidgets import QMessageBox
        QMessageBox.information(
            pre_window,
            "应用到全部",
            "应用材料到所有结构体功能正在开发中...\n\n这是一个占位符实现。"
        )
        logging.info("应用材料到全部功能被调用")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"应用材料到全部失败: {e}", ErrorSeverity.ERROR)
        )
