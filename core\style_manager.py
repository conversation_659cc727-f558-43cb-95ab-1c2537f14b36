"""
样式管理器模块

此模块提供样式表的优化加载和管理，包括：
1. 样式表缓存机制
2. 样式压缩和优化
3. 关键样式优先加载
4. 样式分离和延迟加载

作者: [作者名]
日期: [日期]
"""

import os
import re
import logging
from typing import Optional, Dict, List
from PySide6.QtWidgets import QApplication
from core.optimization_config import is_optimization_enabled, get_config_value


class StyleManager:
    """样式管理器"""
    
    _instance: Optional['StyleManager'] = None
    _style_cache: Optional[str] = None
    _critical_style_cache: Optional[str] = None
    _non_critical_style_cache: Optional[str] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(StyleManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.logger = logging.getLogger(__name__)
        self.style_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "assets", "styles", "style.qss")
        self._initialized = True
    
    def get_style(self, critical_only: bool = False) -> str:
        """获取样式表
        
        Args:
            critical_only: 是否只获取关键样式
            
        Returns:
            样式表字符串
        """
        if not is_optimization_enabled('style_cache'):
            # 如果缓存被禁用，直接读取文件
            return self._load_style_from_file()
        
        if critical_only:
            return self._get_critical_style()
        else:
            return self._get_full_style()
    
    def _get_critical_style(self) -> str:
        """获取关键样式"""
        if self._critical_style_cache is None:
            self._load_and_split_styles()
        return self._critical_style_cache or ""
    
    def _get_full_style(self) -> str:
        """获取完整样式"""
        if self._style_cache is None:
            self._load_and_cache_style()
        return self._style_cache or ""
    
    def _load_and_cache_style(self) -> None:
        """加载并缓存样式"""
        try:
            raw_style = self._load_style_from_file()
            
            if is_optimization_enabled('style_compression'):
                self._style_cache = self._compress_style(raw_style)
            else:
                self._style_cache = raw_style
                
            self.logger.info("样式表已加载并缓存")
            
        except Exception as e:
            self.logger.error(f"加载样式表失败: {e}")
            self._style_cache = ""
    
    def _load_and_split_styles(self) -> None:
        """加载并分离关键样式和非关键样式"""
        try:
            raw_style = self._load_style_from_file()
            
            if is_optimization_enabled('critical_styles_first'):
                critical, non_critical = self._split_critical_styles(raw_style)
                
                if is_optimization_enabled('style_compression'):
                    self._critical_style_cache = self._compress_style(critical)
                    self._non_critical_style_cache = self._compress_style(non_critical)
                else:
                    self._critical_style_cache = critical
                    self._non_critical_style_cache = non_critical
            else:
                # 如果不分离，所有样式都是关键样式
                if is_optimization_enabled('style_compression'):
                    self._critical_style_cache = self._compress_style(raw_style)
                else:
                    self._critical_style_cache = raw_style
                self._non_critical_style_cache = ""
            
            # 合并为完整样式缓存
            self._style_cache = self._critical_style_cache + self._non_critical_style_cache
            
            self.logger.info("样式表已分离并缓存")
            
        except Exception as e:
            self.logger.error(f"分离样式表失败: {e}")
            self._critical_style_cache = ""
            self._non_critical_style_cache = ""
            self._style_cache = ""
    
    def _load_style_from_file(self) -> str:
        """从文件加载原始样式"""
        try:
            with open(self.style_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"读取样式文件失败: {e}")
            return ""
    
    def _compress_style(self, style: str) -> str:
        """压缩样式表"""
        if not style:
            return ""
        
        # 移除注释
        style = re.sub(r'/\*.*?\*/', '', style, flags=re.DOTALL)
        
        # 移除多余的空白字符
        style = re.sub(r'\s+', ' ', style)
        
        # 移除行首行尾空白
        style = style.strip()
        
        # 移除分号前的空格
        style = re.sub(r'\s*;\s*', ';', style)
        
        # 移除大括号周围的空格
        style = re.sub(r'\s*{\s*', '{', style)
        style = re.sub(r'\s*}\s*', '}', style)
        
        # 移除冒号周围的空格
        style = re.sub(r'\s*:\s*', ':', style)
        
        return style
    
    def _split_critical_styles(self, style: str) -> tuple[str, str]:
        """分离关键样式和非关键样式"""
        # 定义关键选择器（影响主要布局和基本外观的样式）
        critical_selectors = [
            'QMainWindow',
            'QPushButton',
            'QLabel',
            'QLineEdit',
            'QComboBox',
            'QGroupBox'
        ]
        
        critical_styles = []
        non_critical_styles = []
        
        # 简单的CSS规则分离（基于选择器）
        rules = self._parse_css_rules(style)
        
        for rule in rules:
            selector = rule.get('selector', '').strip()
            is_critical = any(crit_sel in selector for crit_sel in critical_selectors)
            
            if is_critical:
                critical_styles.append(rule['raw'])
            else:
                non_critical_styles.append(rule['raw'])
        
        return '\n'.join(critical_styles), '\n'.join(non_critical_styles)
    
    def _parse_css_rules(self, style: str) -> List[Dict[str, str]]:
        """解析CSS规则"""
        rules = []
        
        # 简单的CSS解析（匹配选择器和规则块）
        pattern = r'([^{]+)\s*\{([^}]*)\}'
        matches = re.finditer(pattern, style, re.DOTALL)
        
        for match in matches:
            selector = match.group(1).strip()
            properties = match.group(2).strip()
            raw = match.group(0)
            
            rules.append({
                'selector': selector,
                'properties': properties,
                'raw': raw
            })
        
        return rules
    
    def apply_critical_styles(self, app: QApplication) -> None:
        """应用关键样式"""
        critical_style = self._get_critical_style()
        if critical_style:
            app.setStyleSheet(critical_style)
            self.logger.info("关键样式已应用")
    
    def apply_full_styles(self, app: QApplication) -> None:
        """应用完整样式"""
        full_style = self._get_full_style()
        if full_style:
            app.setStyleSheet(full_style)
            self.logger.info("完整样式已应用")
    
    def preload_styles(self) -> None:
        """预加载样式（异步调用）"""
        if is_optimization_enabled('style_cache'):
            self._load_and_split_styles()
    
    def clear_cache(self) -> None:
        """清除样式缓存"""
        self._style_cache = None
        self._critical_style_cache = None
        self._non_critical_style_cache = None
        self.logger.info("样式缓存已清除")
    
    def get_cache_info(self) -> Dict[str, any]:
        """获取缓存信息"""
        return {
            'style_cached': self._style_cache is not None,
            'critical_cached': self._critical_style_cache is not None,
            'non_critical_cached': self._non_critical_style_cache is not None,
            'cache_enabled': is_optimization_enabled('style_cache'),
            'compression_enabled': is_optimization_enabled('style_compression'),
            'critical_first_enabled': is_optimization_enabled('critical_styles_first'),
            'style_file_exists': os.path.exists(self.style_path)
        }


# 全局样式管理器实例
_global_style_manager: Optional[StyleManager] = None


def get_style_manager() -> StyleManager:
    """获取全局样式管理器实例"""
    global _global_style_manager
    if _global_style_manager is None:
        _global_style_manager = StyleManager()
    return _global_style_manager


def get_style(critical_only: bool = False) -> str:
    """获取样式表的便捷函数"""
    return get_style_manager().get_style(critical_only)


def apply_critical_styles(app: QApplication) -> None:
    """应用关键样式的便捷函数"""
    get_style_manager().apply_critical_styles(app)


def apply_full_styles(app: QApplication) -> None:
    """应用完整样式的便捷函数"""
    get_style_manager().apply_full_styles(app)
