# encoding: utf-8
# 2023 R2

# ==================== 日志记录设置 ====================
import logging
import os

# 定义日志文件的保存路径和名称
log_file_path = "D:/data/cs2425_run.log"

# 创建一个日志记录器 (logger)
# BasicConfig可以快速完成设置
logging.basicConfig(
    level=logging.INFO,  # 记录INFO级别及以上的消息 (INFO, WARNING, ERROR, CRITICAL)
    format='%(asctime)s - %(levelname)s - %(message)s',  # 日志格式：时间 - 级别 - 消息
    filename=log_file_path,  # 日志输出到的文件
    filemode='w',  # 'w'表示每次运行都覆盖旧日志, 'a'表示追加
    encoding='utf-8'  # 使用UTF-8编码，确保与进度对话框的读取编码一致
)

# 这样设置后，后续所有 logging.info() 的内容都会写入到上面的文件中
# ======================================================


logging.info("Script execution started...")

try:
    SetScriptVersion(Version="23.2.142")

    logging.info("Creating 'Transient Structural' analysis system...")
    template1 = GetTemplate(
        TemplateName="Transient Structural",
        Solver="ANSYS")
    system1 = template1.CreateSystem()
    logging.info("Analysis system created successfully.")

    geometry1 = system1.GetContainer(ComponentName="Geometry")

    geometry_file = "C:/Users/<USER>/Desktop/tempPart.stp"
    logging.info("Preparing to import geometry file: {}".format(geometry_file))
    geometry1.SetFile(FilePath=geometry_file)
    logging.info("Geometry file import completed.")

    # 由于 -B 模式下没有GUI，Edit() 和 Exit() 可能会快速执行或被跳过
    # 但我们仍然记录这些步骤
    logging.info("Editing geometry (SpaceClaim)...")
    geometry1.Edit(IsSpaceClaimGeometry=True)
    geometry1.Exit()
    logging.info("Geometry editing completed.")

    modelComponent1 = system1.GetComponent(Name="Model")
    logging.info("Refreshing model component...")
    modelComponent1.Refresh()
    logging.info("Model component refresh completed.")

    model1 = system1.GetContainer(ComponentName="Model")
    logging.info("Editing model (Mechanical)...")
    model1.Edit()
    model1.Exit()
    logging.info("Model editing completed.")

    save_path = "D:/data/cs2425.wbpj"
    logging.info("Preparing to save project to: {}".format(save_path))
    Save(
        FilePath=save_path,
        Overwrite=True)
    logging.info("Project saved successfully.")

except Exception as e:
    # 如果脚本中间发生任何错误，将其记录到日志中
    logging.error("Error occurred during script execution!")
    logging.error(str(e))

finally:
    # 无论成功与否，都记录脚本结束
    logging.info("Script execution completed.")