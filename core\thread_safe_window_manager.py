"""
线程安全的窗口管理器包装器

此模块提供线程安全的窗口管理器操作，确保：
1. UI更新只在主线程中执行
2. 共享数据访问使用适当的同步机制
3. 跨线程操作的错误处理

作者: [作者名]
日期: [日期]
"""

import threading
import logging
from typing import Dict, Any, Optional
from PySide6.QtCore import QObject, Signal, QMetaObject, Qt, QThread
from PySide6.QtWidgets import QApplication
from window_manager import WindowManager, WindowType
from .thread_safe_error_handler import get_global_error_handler, report_thread_error, validate_thread_operation

logger = logging.getLogger(__name__)


class ThreadSafeWindowManagerWrapper(QObject):
    """线程安全的窗口管理器包装器
    
    提供线程安全的窗口管理器操作，确保所有UI相关操作
    都在主线程中执行。
    """
    
    # 信号定义
    ui_update_signal = Signal(dict)
    window_data_update_signal = Signal(object, dict)
    
    def __init__(self, window_manager: WindowManager):
        super().__init__()
        self._window_manager = window_manager
        self._data_lock = threading.RLock()  # 可重入锁
        self._ui_thread_id = QThread.currentThreadId()
        
        # 连接信号到槽函数
        self.ui_update_signal.connect(self._safe_update_ui, Qt.QueuedConnection)
        self.window_data_update_signal.connect(self._safe_update_window_data, Qt.QueuedConnection)
        
        logger.info("ThreadSafeWindowManagerWrapper 初始化完成")
    
    def is_main_thread(self) -> bool:
        """检查当前是否在主线程中
        
        Returns:
            bool: 是否在主线程中
        """
        return QThread.currentThreadId() == self._ui_thread_id
    
    def update_ui_from_api(self, params: Dict[str, Any]) -> None:
        """线程安全的UI更新方法

        Args:
            params: 从API接收的参数
        """
        try:
            # 验证线程操作安全性
            if not validate_thread_operation("ui_update_from_api"):
                logger.warning("UI更新操作验证失败")

            if self.is_main_thread():
                # 如果已经在主线程中，直接执行
                self._safe_update_ui(params)
            else:
                # 如果在工作线程中，通过信号机制切换到主线程
                logger.debug("从工作线程请求UI更新，切换到主线程")
                self.ui_update_signal.emit(params)

        except Exception as e:
            # 报告线程错误
            report_thread_error(e, {'operation': 'update_ui_from_api', 'params': params})
    
    def _safe_update_ui(self, params: Dict[str, Any]) -> None:
        """安全的UI更新实现（在主线程中执行）
        
        Args:
            params: 从API接收的参数
        """
        try:
            # 验证当前在主线程中
            if not self.is_main_thread():
                logger.error("_safe_update_ui 不在主线程中执行！")
                return
            
            logger.info(f"在主线程中更新UI: {params}")
            
            # 使用锁保护共享数据访问
            with self._data_lock:
                # 更新所有相关窗口
                self._update_analysis_window(params)
                self._update_constrain_window(params)
                self._update_current_visible_window()
                
                # 更新保存的初始数据
                self._update_initial_data(params)
                
        except Exception as e:
            logger.error(f"UI更新过程中发生错误: {e}", exc_info=True)
    
    def _update_analysis_window(self, params: Dict[str, Any]) -> None:
        """更新分析窗口
        
        Args:
            params: 参数字典
        """
        analysis_window = self._window_manager.get_window(WindowType.ANALYSIS)
        if analysis_window:
            self._update_window_with_params(analysis_window, params, "分析设置")
    
    def _update_constrain_window(self, params: Dict[str, Any]) -> None:
        """更新约束窗口
        
        Args:
            params: 参数字典
        """
        constrain_window = self._window_manager.get_window(WindowType.CONSTRAIN)
        if constrain_window:
            self._update_window_with_params(constrain_window, params, "约束设置")
    
    def _update_current_visible_window(self) -> None:
        """更新当前可见窗口（线程安全）"""
        try:
            current_window_type = None
            # 使用线程安全的方法获取所有窗口
            all_windows = self._window_manager.safe_get_all_windows()

            for window_type, window in all_windows.items():
                if window.isVisible():
                    current_window_type = window_type
                    break

            if current_window_type:
                window = self._window_manager.get_window(current_window_type)
                if window:
                    window.update()
        except Exception as e:
            logger.error(f"更新当前可见窗口时出错: {e}")
            report_thread_error(e, {'operation': 'update_current_visible_window'})
    
    def _update_initial_data(self, params: Dict[str, Any]) -> None:
        """更新初始数据（线程安全）

        Args:
            params: 参数字典
        """
        try:
            # 使用线程安全的方法更新数据
            self._window_manager.safe_update_initial_data(params)
        except Exception as e:
            logger.error(f"更新初始数据时出错: {e}")
            report_thread_error(e, {'operation': 'update_initial_data', 'params': params})
    
    def _update_window_with_params(self, window, params: Dict[str, Any], window_title: str) -> None:
        """根据窗口类型更新相应的界面
        
        Args:
            window: 窗口实例
            params: 参数字典
            window_title: 窗口标题
        """
        try:
            if window_title == "分析设置":
                self._update_analysis_window_params(window, params)
            elif window_title == "约束设置":
                self._update_constrain_window_params(window, params)
        except Exception as e:
            logger.error(f"更新窗口 {window_title} 参数时出错: {e}")
    
    def _update_analysis_window_params(self, window, params: Dict[str, Any]) -> None:
        """更新分析窗口参数
        
        Args:
            window: 分析窗口实例
            params: 参数字典
        """
        if 'timeStep' in params:
            window.ui.lineEdit_timestep.setText(str(params['timeStep']))
        if 'endTime' in params:
            window.ui.lineEdit_stependline.setText(str(params['endTime']))
        if 'stiffnessCoefficient' in params and params['stiffnessCoefficient'] is not None:
            window.ui.lineEdit_stiffness.setText(str(params['stiffnessCoefficient']))
        if 'massCoefficient' in params and params['massCoefficient'] is not None:
            window.ui.lineEdit_mass.setText(str(params['massCoefficient']))
    
    def _update_constrain_window_params(self, window, params: Dict[str, Any]) -> None:
        """更新约束窗口参数
        
        Args:
            window: 约束窗口实例
            params: 参数字典
        """
        if 'forceOutputFolder' in params and params['forceOutputFolder']:
            force_dir = params['forceOutputFolder']
            import os
            if os.path.exists(force_dir):
                window.ui.lineEdit_force.setText(force_dir)
                logger.debug(f"设置力文件夹: {force_dir}")
        
        if 'rotationSpeed' in params and params['rotationSpeed'] is not None:
            window.ui.lineEdit_rotation_speed.setText(str(params['rotationSpeed']))
    
    def _safe_update_window_data(self, window, params: Dict[str, Any]) -> None:
        """安全的窗口数据更新（在主线程中执行）
        
        Args:
            window: 窗口实例
            params: 参数字典
        """
        try:
            if not self.is_main_thread():
                logger.error("_safe_update_window_data 不在主线程中执行！")
                return
            
            with self._data_lock:
                # 执行窗口数据更新
                pass  # 具体实现根据需要添加
                
        except Exception as e:
            logger.error(f"窗口数据更新过程中发生错误: {e}", exc_info=True)
    
    def get_thread_info(self) -> Dict[str, Any]:
        """获取线程信息（用于调试）
        
        Returns:
            Dict[str, Any]: 线程信息
        """
        return {
            'current_thread_id': QThread.currentThreadId(),
            'ui_thread_id': self._ui_thread_id,
            'is_main_thread': self.is_main_thread(),
            'active_thread_count': threading.active_count()
        }


def validate_thread_safety() -> bool:
    """验证线程安全性
    
    Returns:
        bool: 是否通过线程安全验证
    """
    try:
        # 检查是否有QApplication实例
        app = QApplication.instance()
        if app is None:
            logger.error("没有QApplication实例，无法进行线程安全验证")
            return False
        
        # 检查当前线程是否为主线程
        main_thread = app.thread()
        current_thread = QThread.currentThread()
        
        if main_thread != current_thread:
            logger.warning("当前不在主线程中")
            return False
        
        logger.info("线程安全验证通过")
        return True
        
    except Exception as e:
        logger.error(f"线程安全验证失败: {e}")
        return False
