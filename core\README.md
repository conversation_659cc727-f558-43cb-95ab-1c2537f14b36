# 全局异常处理模块

本模块实现了全局异常捕获和处理机制，提供统一的错误处理、日志记录和用户界面反馈。

## 主要功能

1. **全局异常捕获**：捕获所有未处理的异常，包括主线程和子线程中的异常
2. **统一日志记录**：以统一格式记录异常日志，便于问题定位
3. **友好的错误提示**：向用户显示友好的错误对话框，而不是直接崩溃
4. **自定义异常类型**：提供一组自定义异常类型，便于业务逻辑中抛出特定类型的异常

## 使用方法

### 1. 初始化全局异常处理

在应用程序启动时，调用 `setup_exception_handling()` 函数：

```python
from core import setup_exception_handling

# 初始化应用程序
app = QApplication([])

# 设置全局异常处理
exception_signaler = setup_exception_handling()
```

### 2. 连接异常信号

在主窗口类中，连接异常信号到处理函数：

```python
from core import exception_signaler

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 连接异常信号
        exception_signaler.exception_caught.connect(self.show_exception_dialog)
    
    def show_exception_dialog(self, severity, title, message, details):
        """显示异常对话框"""
        from core import ErrorDialog
        dialog = ErrorDialog(severity, title, message, details, self)
        dialog.exec()
```

### 3. 使用自定义异常

在代码中使用自定义异常类型，提供更具体的错误信息：

```python
from core import ConfigurationError, FileOperationError, ValidationError, APIError

try:
    # 尝试读取配置文件
    with open("config.json", "r") as f:
        config = json.load(f)
except FileNotFoundError:
    # 抛出自定义异常
    raise ConfigurationError("无法找到配置文件", {"file": "config.json"})
except json.JSONDecodeError as e:
    # 抛出自定义异常
    raise ConfigurationError("配置文件格式无效", {"file": "config.json", "error": str(e)})
```

## 自定义异常类型

本模块提供以下自定义异常类型：

1. **ApplicationError**：应用程序自定义异常基类
2. **ConfigurationError**：配置错误
3. **FileOperationError**：文件操作错误
4. **ValidationError**：数据验证错误
5. **APIError**：API调用错误

所有自定义异常都支持传递详细信息字典，便于记录更多上下文信息：

```python
raise ValidationError("输入数据无效", {
    "field": "timeStep",
    "value": -1,
    "expected": "正数"
})
```

## 异常严重性级别

异常可以有不同的严重性级别，影响显示样式：

1. **ExceptionSeverity.INFO**：信息
2. **ExceptionSeverity.WARNING**：警告
3. **ExceptionSeverity.ERROR**：错误
4. **ExceptionSeverity.CRITICAL**：严重错误

## 日志记录

所有捕获的异常都会记录到 `logs/exceptions.log` 文件中，包括完整的堆栈跟踪和详细信息。 