"""
测试UI对象删除问题的修复

验证批量删除功能中UI对象已删除错误的修复方案

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_safety_checks():
    """测试UI安全检查"""
    print("🧪 测试UI安全检查...")
    
    try:
        # 模拟UI对象已删除的情况
        class MockUI:
            def __init__(self, has_label=True):
                if has_label:
                    self.label_chart_placeholder = MockLabel()
                else:
                    # 模拟UI对象不存在的情况
                    pass
        
        class MockLabel:
            def __init__(self, deleted=False):
                self.deleted = deleted
                self.text = ""
            
            def setText(self, text):
                if self.deleted:
                    raise RuntimeError("Internal C++ object (PySide6.QtWidgets.QLabel) already deleted.")
                self.text = text
        
        # 测试正常情况
        print("  测试1: 正常UI对象")
        ui_normal = MockUI(has_label=True)
        try:
            if hasattr(ui_normal, 'label_chart_placeholder') and ui_normal.label_chart_placeholder:
                ui_normal.label_chart_placeholder.setText("测试文本")
            print("    正常情况: ✅ 成功")
        except Exception as e:
            print(f"    正常情况: ❌ 失败 - {str(e)}")
        
        # 测试UI对象不存在的情况
        print("  测试2: UI对象不存在")
        ui_missing = MockUI(has_label=False)
        try:
            if hasattr(ui_missing, 'label_chart_placeholder') and ui_missing.label_chart_placeholder:
                ui_missing.label_chart_placeholder.setText("测试文本")
            print("    对象不存在: ✅ 安全跳过")
        except Exception as e:
            print(f"    对象不存在: ❌ 失败 - {str(e)}")
        
        # 测试UI对象已删除的情况
        print("  测试3: UI对象已删除")
        ui_deleted = MockUI(has_label=True)
        ui_deleted.label_chart_placeholder.deleted = True
        try:
            if hasattr(ui_deleted, 'label_chart_placeholder') and ui_deleted.label_chart_placeholder:
                ui_deleted.label_chart_placeholder.setText("测试文本")
            print("    对象已删除: ❌ 应该捕获异常")
        except RuntimeError as re:
            print("    对象已删除: ✅ 正确捕获RuntimeError")
        except Exception as e:
            print(f"    对象已删除: ❌ 意外异常 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ UI安全检查测试失败: {str(e)}")
        return False

def test_batch_delete_with_ui_fix():
    """测试带UI修复的批量删除功能"""
    print("\n🧪 测试带UI修复的批量删除功能...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建数据管理器并导入数据
        data_manager = ModalDataManager("test_ui_fix.pkl")
        
        print("  步骤1: 导入测试数据")
        if os.path.exists("test_data/reference_models.json"):
            success = data_manager.import_from_file("test_data/reference_models.json")
            print(f"    导入结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                imported_results = data_manager.get_imported_results()
                print(f"    导入数量: {len(imported_results)}")
                
                # 测试批量删除
                print("\n  步骤2: 测试批量删除")
                if len(imported_results) >= 2:
                    # 删除前两个结果
                    indices_to_delete = [0, 1]
                    success_count = 0
                    
                    for index in sorted(indices_to_delete, reverse=True):
                        if data_manager.remove_imported_result(index):
                            success_count += 1
                    
                    print(f"    删除结果: {success_count}/{len(indices_to_delete)} 成功")
                    
                    remaining_results = data_manager.get_imported_results()
                    print(f"    剩余数量: {len(remaining_results)}")
                    
                    # 测试清空功能
                    print("\n  步骤3: 测试清空功能")
                    if len(remaining_results) > 0:
                        clear_success = data_manager.clear_all_imported_results()
                        print(f"    清空结果: {'✅ 成功' if clear_success else '❌ 失败'}")
                        
                        if clear_success:
                            final_count = len(data_manager.get_imported_results())
                            print(f"    最终数量: {final_count}")
                            
                            return final_count == 0
        
        return False
        
    except Exception as e:
        print(f"  ❌ 批量删除测试失败: {str(e)}")
        return False

def test_error_handling_robustness():
    """测试错误处理的健壮性"""
    print("\n🧪 测试错误处理健壮性...")
    
    try:
        # 模拟各种异常情况
        test_cases = [
            ("RuntimeError", RuntimeError("Internal C++ object already deleted.")),
            ("AttributeError", AttributeError("'NoneType' object has no attribute 'setText'")),
            ("TypeError", TypeError("setText() missing 1 required positional argument")),
        ]
        
        success_count = 0
        for case_name, exception in test_cases:
            try:
                # 模拟安全的UI更新逻辑
                def safe_ui_update(ui_object, text):
                    try:
                        if hasattr(ui_object, 'label_chart_placeholder') and ui_object.label_chart_placeholder:
                            ui_object.label_chart_placeholder.setText(text)
                    except RuntimeError as re:
                        # UI对象已被删除，忽略此错误
                        print(f"      {case_name}: ✅ 正确处理RuntimeError")
                        return True
                    except Exception as e:
                        print(f"      {case_name}: ⚠️ 其他异常 - {str(e)}")
                        return False
                    return True
                
                # 创建会抛出异常的模拟对象
                class FailingUI:
                    def __init__(self, exception_to_raise):
                        self.label_chart_placeholder = FailingLabel(exception_to_raise)
                
                class FailingLabel:
                    def __init__(self, exception_to_raise):
                        self.exception = exception_to_raise
                    
                    def setText(self, text):
                        raise self.exception
                
                ui_object = FailingUI(exception)
                result = safe_ui_update(ui_object, "测试文本")
                
                if result:
                    success_count += 1
                
            except Exception as e:
                print(f"      {case_name}: ❌ 未处理的异常 - {str(e)}")
        
        print(f"  错误处理测试: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"  ❌ 错误处理测试失败: {str(e)}")
        return False

def simulate_ui_lifecycle():
    """模拟UI生命周期中的对象删除"""
    print("\n🧪 模拟UI生命周期...")
    
    try:
        # 模拟UI对象的生命周期
        class UILifecycle:
            def __init__(self):
                self.ui_objects = {}
                self.deleted_objects = set()
            
            def create_object(self, name):
                self.ui_objects[name] = f"UI对象_{name}"
                print(f"    创建UI对象: {name}")
            
            def delete_object(self, name):
                if name in self.ui_objects:
                    del self.ui_objects[name]
                    self.deleted_objects.add(name)
                    print(f"    删除UI对象: {name}")
            
            def safe_access(self, name, operation):
                try:
                    if name in self.ui_objects and name not in self.deleted_objects:
                        print(f"      安全访问 {name}: ✅ 成功")
                        return True
                    else:
                        print(f"      安全访问 {name}: ⚠️ 对象不存在或已删除，跳过操作")
                        return True
                except Exception as e:
                    print(f"      安全访问 {name}: ❌ 异常 - {str(e)}")
                    return False
        
        # 模拟UI生命周期
        print("  模拟UI对象生命周期:")
        lifecycle = UILifecycle()
        
        # 创建对象
        lifecycle.create_object("label_chart_placeholder")
        lifecycle.create_object("listWidget_comparison_meshes")
        
        # 正常访问
        lifecycle.safe_access("label_chart_placeholder", "setText")
        
        # 删除对象
        lifecycle.delete_object("label_chart_placeholder")
        
        # 尝试访问已删除的对象
        result = lifecycle.safe_access("label_chart_placeholder", "setText")
        
        return result
        
    except Exception as e:
        print(f"  ❌ UI生命周期模拟失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🎯 UI对象删除问题修复验证")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 测试各个组件
    ui_safety_ok = test_ui_safety_checks()
    batch_delete_ok = test_batch_delete_with_ui_fix()
    error_handling_ok = test_error_handling_robustness()
    lifecycle_ok = simulate_ui_lifecycle()
    
    print("\n" + "=" * 70)
    print("📋 UI修复验证结果:")
    print(f"UI安全检查: {'✅ 通过' if ui_safety_ok else '❌ 失败'}")
    print(f"批量删除功能: {'✅ 正常' if batch_delete_ok else '❌ 异常'}")
    print(f"错误处理健壮性: {'✅ 健壮' if error_handling_ok else '❌ 脆弱'}")
    print(f"UI生命周期: {'✅ 安全' if lifecycle_ok else '❌ 不安全'}")
    
    if all([ui_safety_ok, batch_delete_ok, error_handling_ok, lifecycle_ok]):
        print("\n🎉 UI对象删除问题修复成功！")
        print("\n✨ 修复内容:")
        print("  ✅ 添加了UI对象存在性检查")
        print("  ✅ 添加了RuntimeError异常捕获")
        print("  ✅ 使用hasattr()安全检查属性")
        print("  ✅ 优雅处理UI对象生命周期问题")
        
        print("\n🛡️ 安全机制:")
        print("  • 检查UI对象是否存在")
        print("  • 捕获C++对象删除异常")
        print("  • 记录调试信息而非错误")
        print("  • 不影响主要功能执行")
        
        print("\n🎯 修复效果:")
        print("  • 消除RuntimeError错误日志")
        print("  • 提高批量删除操作的稳定性")
        print("  • 改善用户体验的流畅性")
        print("  • 增强代码的健壮性")
        
    else:
        print("\n⚠️ 部分修复验证失败，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
