/*
主界面现代化样式表 (Main Window Modern Stylesheet)
为振动传递计算软件主界面提供现代化的卡片式设计

作者: [作者名]
日期: [日期]
版本: 1.0
*/

/* =============================================================================
   全局样式 (Global Styles)
   ============================================================================= */

QMainWindow {
    background-color: #f5f5f5;
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
}

QWidget {
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
    color: #333333;
}

/* =============================================================================
   主标题区域样式 (Main Title Area Styles)
   ============================================================================= */

QLabel#titleLabel {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #0078d4, stop: 1 #106ebe);
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
    padding: 20px 30px;
    border-radius: 8px;
    margin: 15px;
    qproperty-alignment: AlignCenter;
}

QLabel#subtitleLabel {
    color: #666666;
    font-size: 14px;
    font-weight: normal;
    padding: 8px 30px;
    margin: 5px 15px 15px 15px;
    qproperty-alignment: AlignCenter;
}

/* =============================================================================
   功能卡片样式 (Function Card Styles)
   ============================================================================= */

QFrame[class="functionCard"] {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
    min-height: 150px;
    max-height: 200px;
    min-width: 200px;
    max-width: 300px;
}

QFrame[class="functionCard"]:hover {
    border-color: #0078d4;
    background-color: #fafafa;
    border-width: 3px;
}

QFrame[class="functionCard"]:pressed {
    background-color: #f0f0f0;
    border-color: #005a9e;
}

/* 卡片图标样式 */
QLabel[class="cardIcon"] {
    font-size: 32px;
    color: #0078d4;
    background-color: #deecf9;
    border-radius: 30px;
    padding: 12px;
    margin: 5px;
    min-width: 60px;
    max-width: 60px;
    min-height: 60px;
    max-height: 60px;
    qproperty-alignment: AlignCenter;
}

/* 卡片标题样式 */
QLabel[class="cardTitle"] {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    margin: 5px;
    qproperty-alignment: AlignCenter;
}

/* 卡片描述样式 */
QLabel[class="cardDescription"] {
    font-size: 11px;
    color: #666666;
    margin: 5px;
    qproperty-alignment: AlignCenter;
    qproperty-wordWrap: true;
}

/* =============================================================================
   特定功能卡片的色彩主题 (Specific Function Card Color Themes)
   ============================================================================= */

/* 前处理卡片 - 绿色主题 */
QFrame#preprocessingCard .cardIcon {
    color: #4caf50;
    background-color: #e8f5e8;
}

QFrame#preprocessingCard:hover {
    border-color: #4caf50;
}

/* 网格验证卡片 - 橙色主题 */
QFrame#meshCard .cardIcon {
    color: #ff9800;
    background-color: #fff3e0;
}

QFrame#meshCard:hover {
    border-color: #ff9800;
}

/* 连接设置卡片 - 紫色主题 */
QFrame#connectionCard .cardIcon {
    color: #9c27b0;
    background-color: #f3e5f5;
}

QFrame#connectionCard:hover {
    border-color: #9c27b0;
}

/* 分析设置卡片 - 蓝色主题 */
QFrame#analysisCard .cardIcon {
    color: #2196f3;
    background-color: #e3f2fd;
}

QFrame#analysisCard:hover {
    border-color: #2196f3;
}

/* 约束设置卡片 - 红色主题 */
QFrame#constrainCard .cardIcon {
    color: #f44336;
    background-color: #ffebee;
}

QFrame#constrainCard:hover {
    border-color: #f44336;
}

/* 计算结果卡片 - 青色主题 */
QFrame#resultCard .cardIcon {
    color: #00bcd4;
    background-color: #e0f2f1;
}

QFrame#resultCard:hover {
    border-color: #00bcd4;
}

/* 后处理卡片 - 深蓝色主题 */
QFrame#postCard .cardIcon {
    color: #3f51b5;
    background-color: #e8eaf6;
}

QFrame#postCard:hover {
    border-color: #3f51b5;
}

/* =============================================================================
   网格布局容器样式 (Grid Layout Container Styles)
   ============================================================================= */

QWidget#cardsContainer {
    background-color: transparent;
    padding: 20px;
}

/* =============================================================================
   菜单栏样式 (Menu Bar Styles)
   ============================================================================= */

QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    padding: 4px 8px;
    font-size: 14px;
}

QMenuBar::item {
    background-color: transparent;
    padding: 8px 16px;
    border-radius: 6px;
    margin: 2px;
}

QMenuBar::item:selected {
    background-color: #deecf9;
    color: #0078d4;
}

QMenuBar::item:pressed {
    background-color: #c7e0f4;
}

/* =============================================================================
   菜单样式 (Menu Styles)
   ============================================================================= */

QMenu {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px;
    font-size: 14px;
}

QMenu::item {
    background-color: transparent;
    padding: 8px 24px;
    border-radius: 6px;
    margin: 2px;
}

QMenu::item:selected {
    background-color: #deecf9;
    color: #0078d4;
}

QMenu::separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 4px 8px;
}

/* =============================================================================
   状态栏样式 (Status Bar Styles)
   ============================================================================= */

QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e0e0e0;
    padding: 8px 16px;
    font-size: 12px;
    color: #666666;
}

QStatusBar::item {
    border: none;
}

/* =============================================================================
   滚动条样式 (Scrollbar Styles)
   ============================================================================= */

QScrollBar:vertical {
    background-color: #f5f5f5;
    border: none;
    border-radius: 6px;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #d0d0d0;
    border-radius: 6px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #c0c0c0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    border: none;
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #f5f5f5;
    border: none;
    border-radius: 6px;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #d0d0d0;
    border-radius: 6px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #c0c0c0;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    border: none;
    width: 0px;
}

/* =============================================================================
   响应式设计 (Responsive Design)
   ============================================================================= */

/* 大屏幕 - 3列布局 */
QWidget[screenSize="large"] #cardsContainer {
    /* 通过Python代码动态设置网格列数 */
}

/* 中等屏幕 - 2列布局 */
QWidget[screenSize="medium"] #cardsContainer {
    /* 通过Python代码动态设置网格列数 */
}

/* 小屏幕 - 1列布局 */
QWidget[screenSize="small"] #cardsContainer {
    /* 通过Python代码动态设置网格列数 */
}

/* =============================================================================
   动画和过渡效果 (Animation and Transition Effects)
   ============================================================================= */

/* Qt样式表不直接支持动画，通过Python代码实现 */

/* =============================================================================
   工具提示样式 (Tooltip Styles)
   ============================================================================= */

QToolTip {
    background-color: #333333;
    border: 1px solid #555555;
    border-radius: 6px;
    color: #ffffff;
    font-size: 12px;
    padding: 8px 12px;
    opacity: 230;
}
