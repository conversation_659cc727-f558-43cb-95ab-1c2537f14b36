"""
简化的网格参数编辑对话框模块

此模块定义了简化版的网格参数编辑对话框类，专门为模态分析设计：
1. 只保留模态分析所需的基本参数（名称、尺寸、单元类型）
2. 移除复杂的质量设置和高级配置
3. 保持与网格无关性界面一致的外观风格
4. 确保与 modal.py 脚本的兼容性

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import logging
from typing import Optional, Dict, Any
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import QDialog, QMessageBox

from core.mesh_manager import MeshParameter, ElementType
from ui.mesh_parameter_dialog_simplified_ui import Ui_MeshParameterDialog

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MeshParameterDialogSimplified(QDialog):
    """简化的网格参数编辑对话框类
    
    专门为模态分析设计的简化版本，只包含基本的网格参数：
    - 网格名称
    - 网格尺寸（modal.py 的核心参数）
    - 单元类型
    """
    
    # 自定义信号
    parameter_accepted = Signal(object)  # 参数确认信号，传递MeshParameter对象
    
    def __init__(self, parent=None, mesh_parameter: Optional[MeshParameter] = None):
        """初始化简化对话框
        
        Args:
            parent: 父窗口
            mesh_parameter: 要编辑的网格参数，如果为None则创建新参数
        """
        super().__init__(parent)
        
        # 设置UI
        self.ui = Ui_MeshParameterDialog()
        self.ui.setupUi(self)
        
        # 设置对话框属性
        self.setModal(True)
        self.setWindowTitle("网格参数设置 - 模态分析专用")
        
        # 存储原始参数（用于编辑模式）
        self._original_parameter = mesh_parameter
        self._is_edit_mode = mesh_parameter is not None
        
        # 设置输入验证器
        self._setup_validators()
        
        # 连接信号槽
        self._connect_signals()
        
        # 初始化界面数据
        self._initialize_ui()
        
        # 如果是编辑模式，加载现有参数
        if self._is_edit_mode:
            self._load_parameter_data(mesh_parameter)
        else:
            self._load_default_values()
        
        logger.debug(f"简化网格参数对话框初始化完成，模式: {'编辑' if self._is_edit_mode else '新建'}")
    
    def _setup_validators(self):
        """设置输入验证器"""
        # 网格尺寸范围设置 (0.1 - 1000.0)
        self.ui.doubleSpinBox_mesh_size.setRange(0.1, 1000.0)
        self.ui.doubleSpinBox_mesh_size.setDecimals(2)
        self.ui.doubleSpinBox_mesh_size.setSingleStep(0.1)
        self.ui.doubleSpinBox_mesh_size.setSuffix(" mm")
        
        logger.debug("简化版输入验证器设置完成")
    
    def _connect_signals(self):
        """连接信号槽"""
        # 按钮信号
        self.ui.btn_ok.clicked.connect(self._on_ok_clicked)
        self.ui.btn_reset.clicked.connect(self._on_reset_clicked)
        # btn_cancel 已在UI文件中连接到 reject()
        
        # 输入变化信号（用于实时验证）
        self.ui.lineEdit_mesh_name.textChanged.connect(self._validate_input)
        self.ui.doubleSpinBox_mesh_size.valueChanged.connect(self._validate_input)
        self.ui.comboBox_element_type.currentTextChanged.connect(self._validate_input)
        
        logger.debug("简化版信号槽连接完成")
    
    def _initialize_ui(self):
        """初始化界面数据"""
        # 设置单元类型选项
        self.ui.comboBox_element_type.clear()
        element_types = [
            ("四面体", ElementType.TETRAHEDRON),
            ("六面体", ElementType.HEXAHEDRON), 
            ("混合", ElementType.MIXED)
        ]
        
        for display_name, element_type in element_types:
            self.ui.comboBox_element_type.addItem(display_name, element_type)
        
        # 设置默认选择
        self.ui.comboBox_element_type.setCurrentIndex(0)  # 默认选择四面体
        
        logger.debug("简化版界面数据初始化完成")
    
    def _load_default_values(self):
        """加载默认值"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()
            default_settings = config_manager.get_mesh_default_settings()

            # 生成唯一的默认名称
            default_name = self._generate_unique_mesh_name("新网格")
            self.ui.lineEdit_mesh_name.setText(default_name)
            self.ui.doubleSpinBox_mesh_size.setValue(10.0)

            # 设置默认单元类型
            default_element_type = default_settings.get("element_type", "四面体")
            for i in range(self.ui.comboBox_element_type.count()):
                if self.ui.comboBox_element_type.itemText(i) == default_element_type:
                    self.ui.comboBox_element_type.setCurrentIndex(i)
                    break

        except Exception as e:
            logger.warning(f"加载默认配置失败，使用硬编码默认值: {str(e)}")
            # 使用硬编码默认值
            default_name = self._generate_unique_mesh_name("新网格")
            self.ui.lineEdit_mesh_name.setText(default_name)
            self.ui.doubleSpinBox_mesh_size.setValue(10.0)
            self.ui.comboBox_element_type.setCurrentIndex(0)  # 四面体

        logger.debug("简化版默认值加载完成")

    def _generate_unique_mesh_name(self, base_name: str) -> str:
        """生成唯一的网格名称

        Args:
            base_name: 基础名称

        Returns:
            str: 唯一的网格名称
        """
        try:
            from core.mesh_manager import MeshManager
            mesh_manager = MeshManager()

            # 如果基础名称不存在，直接返回
            if not mesh_manager.get_mesh_by_name(base_name):
                return base_name

            # 生成带数字后缀的唯一名称
            counter = 1
            while True:
                new_name = f"{base_name}_{counter}"
                if not mesh_manager.get_mesh_by_name(new_name):
                    return new_name
                counter += 1

                # 防止无限循环
                if counter > 1000:
                    import time
                    return f"{base_name}_{int(time.time())}"

        except Exception as e:
            logger.warning(f"生成唯一名称失败: {str(e)}")
            # 如果失败，使用时间戳
            import time
            return f"{base_name}_{int(time.time())}"
    
    def _load_parameter_data(self, mesh_parameter: MeshParameter):
        """加载网格参数数据到界面
        
        Args:
            mesh_parameter: 网格参数对象
        """
        try:
            # 基本参数
            self.ui.lineEdit_mesh_name.setText(mesh_parameter.name)
            self.ui.doubleSpinBox_mesh_size.setValue(mesh_parameter.size)
            
            # 单元类型
            element_type_text = mesh_parameter.element_type.value if hasattr(mesh_parameter.element_type, 'value') else str(mesh_parameter.element_type)
            for i in range(self.ui.comboBox_element_type.count()):
                if self.ui.comboBox_element_type.itemData(i) == mesh_parameter.element_type:
                    self.ui.comboBox_element_type.setCurrentIndex(i)
                    break
            
            logger.debug(f"简化版网格参数数据加载完成: {mesh_parameter.name}")
            
        except Exception as e:
            logger.error(f"加载网格参数数据失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "警告", f"加载网格参数数据失败: {str(e)}")
    
    def _validate_input(self):
        """验证输入数据"""
        try:
            # 创建临时参数对象进行验证
            temp_parameter = self._create_parameter_from_ui()
            errors = temp_parameter.validate()

            # 检查名称重复（仅在新建模式下）
            if not self._is_edit_mode:
                try:
                    from core.mesh_manager import MeshManager
                    mesh_manager = MeshManager()
                    if mesh_manager.get_mesh_by_name(temp_parameter.name):
                        errors.append(f"网格名称 '{temp_parameter.name}' 已存在")
                except Exception:
                    pass  # 如果无法获取mesh_manager，跳过重复检查

            # 更新确定按钮状态
            self.ui.btn_ok.setEnabled(len(errors) == 0)

            # 如果有错误，可以在这里显示提示（可选）
            if errors:
                logger.debug(f"简化版输入验证失败: {'; '.join(errors)}")

        except Exception as e:
            logger.debug(f"简化版输入验证异常: {str(e)}")
            self.ui.btn_ok.setEnabled(False)
    
    def _create_parameter_from_ui(self) -> MeshParameter:
        """从UI界面创建网格参数对象
        
        Returns:
            MeshParameter: 网格参数对象
        """
        # 创建网格参数对象
        if self._is_edit_mode:
            mesh_param = MeshParameter()
            mesh_param.id = self._original_parameter.id  # 保持原有ID
        else:
            mesh_param = MeshParameter()
        
        # 基本参数
        mesh_param.name = self.ui.lineEdit_mesh_name.text().strip()
        mesh_param.size = self.ui.doubleSpinBox_mesh_size.value()
        mesh_param.element_type = self.ui.comboBox_element_type.currentData()
        
        # 简化版不设置质量参数，使用默认值
        # 这样可以确保与 modal.py 脚本的兼容性
        
        return mesh_param
    
    def _on_ok_clicked(self):
        """确定按钮点击处理"""
        try:
            # 创建网格参数对象
            mesh_parameter = self._create_parameter_from_ui()

            # 验证参数
            errors = mesh_parameter.validate()

            # 检查名称重复（仅在新建模式下）
            if not self._is_edit_mode:
                try:
                    from core.mesh_manager import MeshManager
                    mesh_manager = MeshManager()
                    if mesh_manager.get_mesh_by_name(mesh_parameter.name):
                        errors.append(f"网格名称 '{mesh_parameter.name}' 已存在，请使用其他名称")
                except Exception:
                    pass  # 如果无法获取mesh_manager，跳过重复检查

            if errors:
                error_msg = "参数验证失败:\n" + "\n".join(errors)
                QMessageBox.warning(self, "参数错误", error_msg)
                return

            # 发出信号并关闭对话框
            self.parameter_accepted.emit(mesh_parameter)
            self.accept()

            logger.info(f"简化版网格参数确认: {mesh_parameter.name}")

        except Exception as e:
            logger.error(f"确认网格参数失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"确认网格参数失败: {str(e)}")
    
    def _on_reset_clicked(self):
        """重置按钮点击处理"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置所有参数吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if self._is_edit_mode:
                self._load_parameter_data(self._original_parameter)
            else:
                self._load_default_values()
            
            logger.debug("简化版参数已重置")
    
    def get_mesh_parameter(self) -> Optional[MeshParameter]:
        """获取网格参数对象（用于外部调用）
        
        Returns:
            Optional[MeshParameter]: 网格参数对象，如果验证失败则返回None
        """
        try:
            mesh_parameter = self._create_parameter_from_ui()
            errors = mesh_parameter.validate()
            if errors:
                return None
            return mesh_parameter
        except Exception:
            return None
