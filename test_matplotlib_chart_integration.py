"""
测试matplotlib图表集成功能

此脚本验证matplotlib图表组件与模态分析结果对比界面的集成：
1. matplotlib图表组件正常加载
2. 三种图表类型正确绘制
3. 图表保存和导出功能
4. 与现有界面的完整集成

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_matplotlib_availability():
    """测试matplotlib可用性"""
    print("🧪 测试matplotlib可用性...")

    try:
        import matplotlib
        print(f"✅ matplotlib版本: {matplotlib.__version__}")

        # 测试后端配置
        try:
            matplotlib.use('Qt5Agg')
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg
            print("✅ Qt5Agg后端可用")
            backend_available = True
        except ImportError:
            try:
                matplotlib.use('TkAgg')
                from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
                print("⚠️ 使用TkAgg后端（Qt5Agg不可用）")
                backend_available = True
            except ImportError:
                matplotlib.use('Agg')
                print("⚠️ 使用Agg后端（无GUI后端）")
                backend_available = True

        print(f"✅ 当前后端: {matplotlib.get_backend()}")

        # 测试基本绘图功能
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title("Test Chart")  # 使用英文避免字体问题
        plt.close(fig)

        print("✅ matplotlib基本功能正常")
        return True

    except ImportError as e:
        print(f"❌ matplotlib不可用: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ matplotlib测试失败: {str(e)}")
        return False

def test_chart_widget_creation():
    """测试图表组件创建"""
    print("\n🧪 测试图表组件创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        print("✅ 图表组件创建成功")
        
        # 检查组件属性
        if hasattr(chart_widget, 'current_chart_type'):
            print(f"✅ 默认图表类型: {chart_widget.current_chart_type}")
        
        if hasattr(chart_widget, 'chart_options'):
            print(f"✅ 图表选项: {chart_widget.chart_options}")
        
        # 检查信号
        if hasattr(chart_widget, 'chart_updated'):
            print("✅ 图表更新信号存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表组件创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_types():
    """测试三种图表类型"""
    print("\n🧪 测试三种图表类型...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 准备测试数据
        test_data = [
            {
                'name': '细网格',
                'size': 2.0,
                'frequencies': [45.2, 78.5, 112.3, 156.8, 203.4, 251.7],
                'node_count': 15000,
                'element_count': 12000
            },
            {
                'name': '中等网格',
                'size': 5.0,
                'frequencies': [44.8, 77.9, 111.2, 155.1, 201.8, 249.3],
                'node_count': 8000,
                'element_count': 6500
            },
            {
                'name': '粗网格',
                'size': 10.0,
                'frequencies': [43.5, 76.2, 108.9, 152.3, 198.7, 245.1],
                'node_count': 3200,
                'element_count': 2800
            }
        ]
        
        # 测试频率对比图
        print("  📊 测试频率对比图...")
        chart_widget.update_chart("frequency_comparison", test_data)
        print("  ✅ 频率对比图绘制成功")
        
        # 测试模态分布图
        print("  📈 测试模态分布图...")
        chart_widget.update_chart("mode_distribution", test_data)
        print("  ✅ 模态分布图绘制成功")
        
        # 测试网格收敛性分析
        print("  🔍 测试网格收敛性分析...")
        chart_widget.update_chart("mesh_convergence", test_data)
        print("  ✅ 网格收敛性分析绘制成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表类型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_options():
    """测试图表选项功能"""
    print("\n🧪 测试图表选项功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 准备测试数据
        test_data = [
            {
                'name': '测试网格',
                'size': 5.0,
                'frequencies': [50.0, 100.0, 150.0],
                'node_count': 5000,
                'element_count': 4000
            }
        ]
        
        # 测试不同的显示选项
        options_tests = [
            {'show_frequencies': True, 'show_mode_shapes': True, 'show_mesh_info': True},
            {'show_frequencies': False, 'show_mode_shapes': True, 'show_mesh_info': False},
            {'show_frequencies': True, 'show_mode_shapes': False, 'show_mesh_info': True}
        ]
        
        for i, options in enumerate(options_tests):
            print(f"  🔧 测试选项组合 {i+1}: {options}")
            chart_widget.update_chart("frequency_comparison", test_data, options)
            print(f"  ✅ 选项组合 {i+1} 测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表选项测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_save_functionality():
    """测试图表保存功能"""
    print("\n🧪 测试图表保存功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        import tempfile
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 准备测试数据
        test_data = [
            {
                'name': '保存测试网格',
                'size': 3.0,
                'frequencies': [60.0, 120.0, 180.0],
                'node_count': 6000,
                'element_count': 5000
            }
        ]
        
        # 更新图表
        chart_widget.update_chart("frequency_comparison", test_data)
        
        # 测试保存到临时文件
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            chart_widget.save_chart(temp_path)
            
            # 检查文件是否存在
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                print(f"✅ 图表保存成功: {temp_path}")
                print(f"✅ 文件大小: {file_size} 字节")
                
                # 清理临时文件
                os.unlink(temp_path)
                return True
            else:
                print("❌ 保存的文件不存在")
                return False
                
        except Exception as e:
            print(f"❌ 图表保存失败: {str(e)}")
            return False
        
    except Exception as e:
        print(f"❌ 图表保存功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_with_mesh_window():
    """测试与网格窗口的集成"""
    print("\n🧪 测试与网格窗口的集成...")

    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow

        app = QApplication.instance() or QApplication([])

        # 创建模拟的窗口管理器
        class MockWindowManager:
            def register_observer(self, observer):
                pass
            def unregister_observer(self, observer):
                pass

        # 创建网格窗口
        window = MeshWindow(MockWindowManager())
        
        # 检查图表组件是否正确集成
        if hasattr(window, 'modal_chart_widget'):
            if window.modal_chart_widget:
                print("✅ 图表组件已集成到网格窗口")
                print(f"✅ 图表组件类型: {type(window.modal_chart_widget).__name__}")
            else:
                print("⚠️ 图表组件为None（可能是matplotlib不可用）")
        else:
            print("❌ 网格窗口缺少图表组件属性")
            return False
        
        # 检查UI组件是否存在
        required_ui_components = [
            'widget_chart_container',
            'btn_update_chart',
            'btn_save_chart',
            'checkBox_show_frequencies',
            'checkBox_show_mode_shapes',
            'checkBox_show_mesh_info'
        ]
        
        missing_components = []
        for component in required_ui_components:
            if not hasattr(window.ui, component):
                missing_components.append(component)
        
        if not missing_components:
            print("✅ 所有必需的UI组件都存在")
        else:
            print(f"❌ 缺少UI组件: {missing_components}")
            return False
        
        # 检查信号连接
        if hasattr(window, '_on_update_chart') and hasattr(window, '_on_save_chart'):
            print("✅ 图表相关信号处理函数存在")
        else:
            print("❌ 缺少图表信号处理函数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 网格窗口集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 matplotlib图表集成功能验证")
    print("=" * 70)
    print("\n集成目标：")
    print("✨ 将matplotlib图表组件集成到模态分析结果对比界面")
    print("✨ 实现三种专业图表类型的真实绘制")
    print("✨ 提供图表保存和导出功能")
    print("✨ 确保与现有界面的完整集成")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    success_count = 0
    total_tests = 6
    
    # 测试matplotlib可用性
    if test_matplotlib_availability():
        success_count += 1
    
    # 测试图表组件创建
    if test_chart_widget_creation():
        success_count += 1
    
    # 测试图表类型
    if test_chart_types():
        success_count += 1
    
    # 测试图表选项
    if test_chart_options():
        success_count += 1
    
    # 测试图表保存功能
    if test_chart_save_functionality():
        success_count += 1
    
    # 测试与网格窗口的集成
    if test_integration_with_mesh_window():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！")
        print("✅ matplotlib图表组件集成成功")
        print("✅ 三种图表类型绘制正常")
        print("✅ 图表保存和导出功能完善")
        print("✅ 与现有界面完整集成")
        print("\n🎯 集成效果：")
        print("• 提供了真正的数据可视化功能")
        print("• 支持频率对比、模态分布、收敛性分析三种图表")
        print("• 具备完整的图表保存和导出能力")
        print("• 与模态分析数据完美配合")
        print("• 支持中文字体和交互功能")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
        print("请检查matplotlib图表集成的实现")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
