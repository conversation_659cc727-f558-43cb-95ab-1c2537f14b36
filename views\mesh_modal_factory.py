"""
网格与模态分析窗口工厂

此模块提供创建简化版网格与模态分析窗口的工厂函数，
用于在窗口管理器中注册和创建窗口实例。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import logging
from typing import Optional, Any
from PySide6.QtWidgets import QWidget

from views.mesh_modal_simplified_window import MeshModalSimplifiedWindow


def create_mesh_modal_simplified_window(parent: Optional[QWidget] = None, **kwargs) -> MeshModalSimplifiedWindow:
    """创建简化的网格与模态分析窗口
    
    Args:
        parent: 父窗口
        **kwargs: 其他参数
        
    Returns:
        MeshModalSimplifiedWindow: 简化的网格与模态分析窗口实例
    """
    try:
        window = MeshModalSimplifiedWindow(parent)
        logging.info("简化的网格与模态分析窗口创建成功")
        return window
    except Exception as e:
        logging.error(f"创建简化的网格与模态分析窗口失败: {e}")
        raise


def register_mesh_modal_simplified_factory(window_manager):
    """在窗口管理器中注册简化的网格与模态分析窗口工厂
    
    Args:
        window_manager: 窗口管理器实例
    """
    try:
        # 注册窗口工厂函数
        window_manager.register_window_factory(
            "mesh_modal_simplified",
            create_mesh_modal_simplified_window
        )
        logging.info("简化的网格与模态分析窗口工厂已注册")
    except Exception as e:
        logging.error(f"注册简化的网格与模态分析窗口工厂失败: {e}")


# 为了向后兼容，也可以提供一个替换原有网格窗口的函数
def replace_mesh_window_with_simplified(window_manager):
    """用简化版本替换原有的网格窗口
    
    Args:
        window_manager: 窗口管理器实例
    """
    try:
        # 如果存在原有的网格窗口类型，替换它
        if hasattr(window_manager, 'window_factories'):
            # 替换原有的网格窗口工厂
            window_manager.window_factories['mesh'] = create_mesh_modal_simplified_window
            logging.info("已用简化版本替换原有的网格窗口")
        else:
            # 如果没有工厂系统，直接注册
            register_mesh_modal_simplified_factory(window_manager)
    except Exception as e:
        logging.error(f"替换网格窗口失败: {e}")


# 导出主要函数
__all__ = [
    'create_mesh_modal_simplified_window',
    'register_mesh_modal_simplified_factory', 
    'replace_mesh_window_with_simplified'
]
