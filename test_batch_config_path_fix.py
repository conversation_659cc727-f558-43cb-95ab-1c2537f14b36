#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批处理配置文件路径修复验证测试

此脚本用于验证批处理模态分析配置文件路径修复是否正确，确保：
1. 批处理模态分析使用batch_mesh_config.json
2. 单网格配置使用mesh_config_last.json
3. 路径替换功能能正确区分不同的配置类型
4. 配置文件格式与脚本期望匹配

作者: AI Assistant
日期: 2025-08-02
"""

import sys
import os
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_type_differentiation():
    """测试配置类型区分功能"""
    try:
        logger.info("开始测试配置类型区分功能")
        
        from core.mesh_config_generator import replace_hardcoded_paths
        
        # 模拟脚本内容
        test_script_content = '''
# 测试脚本
cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"

def run():
    with open(cfg_path, 'r') as f:
        config = json.load(f)
    return config
'''
        
        work_dir = os.getcwd()
        
        # 测试单网格配置类型
        single_result = replace_hardcoded_paths(test_script_content, work_dir, config_type="single")
        
        # 测试批处理配置类型
        batch_result = replace_hardcoded_paths(test_script_content, work_dir, config_type="batch")
        
        # 验证结果
        if "mesh_config_last.json" not in single_result:
            logger.error("单网格配置类型未正确替换为mesh_config_last.json")
            return False
        
        if "batch_mesh_config.json" not in batch_result:
            logger.error("批处理配置类型未正确替换为batch_mesh_config.json")
            return False
        
        if "mesh_config_last.json" in batch_result:
            logger.error("批处理配置类型错误地使用了mesh_config_last.json")
            return False
        
        if "batch_mesh_config.json" in single_result:
            logger.error("单网格配置类型错误地使用了batch_mesh_config.json")
            return False
        
        logger.info("✅ 配置类型区分功能测试通过")
        logger.info(f"  - 单网格配置: mesh_config_last.json")
        logger.info(f"  - 批处理配置: batch_mesh_config.json")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置类型区分功能测试失败: {str(e)}")
        return False

def test_batch_config_format_compatibility():
    """测试批处理配置文件格式兼容性"""
    try:
        logger.info("开始测试批处理配置文件格式兼容性")
        
        # 读取实际的batch_mesh_config.json文件
        batch_config_path = "temp/batch_mesh_config.json"
        if not os.path.exists(batch_config_path):
            logger.warning(f"批处理配置文件不存在: {batch_config_path}")
            return True  # 如果文件不存在，跳过测试
        
        with open(batch_config_path, 'r', encoding='utf-8') as f:
            batch_config = json.load(f)
        
        # 验证批处理配置文件格式
        required_fields = ['element_size', 'output_directory', 'batch_mode', 'mesh_names']
        for field in required_fields:
            if field not in batch_config:
                logger.error(f"批处理配置文件缺少必需字段: {field}")
                return False
        
        # 验证element_size是数组
        element_size = batch_config['element_size']
        if not isinstance(element_size, list):
            logger.error(f"批处理配置的element_size应该是数组，实际类型: {type(element_size)}")
            return False
        
        if len(element_size) == 0:
            logger.error("批处理配置的element_size数组为空")
            return False
        
        # 验证batch_mode为True
        if not batch_config.get('batch_mode', False):
            logger.error("批处理配置的batch_mode应该为True")
            return False
        
        # 验证mesh_names是数组
        mesh_names = batch_config['mesh_names']
        if not isinstance(mesh_names, list):
            logger.error(f"批处理配置的mesh_names应该是数组，实际类型: {type(mesh_names)}")
            return False
        
        logger.info("✅ 批处理配置文件格式兼容性测试通过")
        logger.info(f"  - element_size数组长度: {len(element_size)}")
        logger.info(f"  - mesh_names数组长度: {len(mesh_names)}")
        logger.info(f"  - batch_mode: {batch_config['batch_mode']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 批处理配置文件格式兼容性测试失败: {str(e)}")
        return False

def test_single_config_format_compatibility():
    """测试单网格配置文件格式兼容性"""
    try:
        logger.info("开始测试单网格配置文件格式兼容性")
        
        # 读取实际的mesh_config_last.json文件
        single_config_path = "temp/mesh_config_last.json"
        if not os.path.exists(single_config_path):
            logger.warning(f"单网格配置文件不存在: {single_config_path}")
            return True  # 如果文件不存在，跳过测试
        
        with open(single_config_path, 'r', encoding='utf-8') as f:
            single_config = json.load(f)
        
        # 验证单网格配置文件格式
        required_fields = ['element_size']
        for field in required_fields:
            if field not in single_config:
                logger.error(f"单网格配置文件缺少必需字段: {field}")
                return False
        
        # 验证element_size是单个数值
        element_size = single_config['element_size']
        if not isinstance(element_size, (int, float)):
            logger.error(f"单网格配置的element_size应该是数值，实际类型: {type(element_size)}")
            return False
        
        if element_size <= 0:
            logger.error(f"单网格配置的element_size应该大于0，实际值: {element_size}")
            return False
        
        logger.info("✅ 单网格配置文件格式兼容性测试通过")
        logger.info(f"  - element_size: {element_size}")
        logger.info(f"  - 配置来源: {single_config.get('source', 'unknown')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单网格配置文件格式兼容性测试失败: {str(e)}")
        return False

def test_script_expectation_matching():
    """测试脚本期望与配置文件的匹配性"""
    try:
        logger.info("开始测试脚本期望与配置文件的匹配性")
        
        # 模拟batch_modal_analysis.py脚本的逻辑
        batch_config_path = "temp/batch_mesh_config.json"
        single_config_path = "temp/mesh_config_last.json"
        
        # 测试批处理配置是否满足脚本期望
        if os.path.exists(batch_config_path):
            with open(batch_config_path, 'r', encoding='utf-8') as f:
                batch_config = json.load(f)
            
            # 模拟脚本逻辑：sizes_to_test = config.get("element_size", [])
            sizes_to_test = batch_config.get("element_size", [])
            
            if not isinstance(sizes_to_test, list):
                logger.error("批处理配置的element_size不是数组，不满足脚本期望")
                return False
            
            if len(sizes_to_test) == 0:
                logger.error("批处理配置的element_size数组为空，不满足脚本期望")
                return False
            
            logger.info(f"✅ 批处理配置满足脚本期望，包含 {len(sizes_to_test)} 个网格尺寸")
        
        # 测试单网格配置不应该用于批处理脚本
        if os.path.exists(single_config_path):
            with open(single_config_path, 'r', encoding='utf-8') as f:
                single_config = json.load(f)
            
            # 模拟如果错误地使用单网格配置
            sizes_to_test = single_config.get("element_size", [])
            
            if isinstance(sizes_to_test, (int, float)):
                logger.info("✅ 单网格配置包含单个element_size值，不适用于批处理脚本")
            elif isinstance(sizes_to_test, list):
                logger.warning("单网格配置意外地包含数组格式的element_size")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 脚本期望与配置文件匹配性测试失败: {str(e)}")
        return False

def test_path_replacement_in_context():
    """测试在实际上下文中的路径替换"""
    try:
        logger.info("开始测试在实际上下文中的路径替换")
        
        from core.mesh_config_generator import replace_hardcoded_paths
        
        # 模拟batch_modal_analysis.py的关键部分
        batch_script_content = '''
def run(self):
    """
    Main controller for running simulations with multiple mesh sizes.
    """
    app.clear_previous_setup()
    # 1. --- Load configuration ---
    cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
    try:
        with open(cfg_path, "r") as f:
            config = json.load(f)
    except Exception as e:
        self.logger.error("Unable to load mesh config file %s: %s", cfg_path, e)
        return
    
    sizes_to_test = config.get("element_size", [])
    if not sizes_to_test:
        self.logger.error("'element_size' list not found in config file.")
        return
'''
        
        # 应用批处理配置类型的路径替换
        work_dir = os.getcwd()
        updated_script = replace_hardcoded_paths(batch_script_content, work_dir, config_type="batch")
        
        # 验证替换结果
        if "batch_mesh_config.json" not in updated_script:
            logger.error("批处理脚本未正确替换为batch_mesh_config.json路径")
            return False
        
        if "D:/data/all-XM/autoworkbench/csdaima/mesh_config.json" in updated_script:
            logger.error("批处理脚本仍包含硬编码路径")
            return False
        
        logger.info("✅ 实际上下文中的路径替换测试通过")
        logger.info("  - 批处理脚本正确使用batch_mesh_config.json")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 实际上下文中的路径替换测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始批处理配置文件路径修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    # 运行测试
    tests = [
        ("配置类型区分功能测试", test_config_type_differentiation),
        ("批处理配置文件格式兼容性测试", test_batch_config_format_compatibility),
        ("单网格配置文件格式兼容性测试", test_single_config_format_compatibility),
        ("脚本期望与配置文件匹配性测试", test_script_expectation_matching),
        ("实际上下文中的路径替换测试", test_path_replacement_in_context)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}")
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！批处理配置文件路径修复成功")
        logger.info("\n📋 修复验证结果:")
        logger.info("• ✅ 批处理模态分析使用batch_mesh_config.json")
        logger.info("• ✅ 单网格配置使用mesh_config_last.json")
        logger.info("• ✅ 路径替换功能正确区分配置类型")
        logger.info("• ✅ 配置文件格式与脚本期望匹配")
        logger.info("\n🔧 修复内容:")
        logger.info("• 修改replace_hardcoded_paths函数支持config_type参数")
        logger.info("• 批处理模态分析脚本生成时指定config_type='batch'")
        logger.info("• 确保批处理使用包含element_size数组的配置文件")
        logger.info("• 保持单网格配置使用单个element_size值")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
        logger.error("请检查配置文件路径和格式设置")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
