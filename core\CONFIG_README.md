# 配置管理模块

本模块实现了统一的配置管理功能，提供配置的读取、保存、验证和版本管理。

## 主要功能

1. **统一配置管理**：集中管理应用程序的所有配置
2. **配置验证**：验证配置数据的完整性和有效性
3. **版本管理**：支持配置格式的版本升级
4. **默认配置**：提供默认配置，确保应用程序正常运行
5. **信号机制**：通过信号通知配置变更和错误

## 使用方法

### 1. 获取配置管理器实例

ConfigManager 使用单例模式，可以在应用程序的任何地方获取同一个实例：

```python
from core import ConfigManager

# 获取配置管理器实例
config_manager = ConfigManager()
```

### 2. 读取配置

使用 `get` 方法读取配置，支持点号分隔的路径：

```python
# 读取配置
ansys_start_file = config_manager.get("ansys.start_file")
time_step = config_manager.get("analysis.time_step", 0.001)  # 提供默认值
```

### 3. 修改配置

使用 `set` 方法修改配置：

```python
# 修改配置
config_manager.set("ansys.start_file", "D:\\path\\to\\RunWB2.exe")
config_manager.set("analysis.time_step", 0.002)
```

### 4. 保存配置

修改配置后，使用 `save_config` 方法保存到文件：

```python
# 保存配置
success = config_manager.save_config()
if not success:
    print("保存配置失败")
```

### 5. 监听配置变更

通过信号机制监听配置变更：

```python
# 连接信号
config_manager.signals.config_changed.connect(on_config_changed)
config_manager.signals.config_error.connect(on_config_error)

def on_config_changed(config):
    print("配置已更新")
    
def on_config_error(error_msg):
    print(f"配置错误: {error_msg}")
```

### 6. 注册配置验证器

可以注册自定义验证器函数来验证配置：

```python
def validate_analysis_config(config):
    errors = []
    if "analysis" in config:
        analysis = config["analysis"]
        if "time_step" in analysis and analysis["time_step"] <= 0:
            errors.append("时间步长必须大于0")
    return errors

# 注册验证器
config_manager.register_validator(validate_analysis_config)
```

## 配置结构

默认配置结构如下：

```json
{
    "version": "1.0.0",
    "ansys": {
        "start_file": "D:\\path\\to\\RunWB2.exe",
        "work_dir": "D:\\work\\dir",
        "project_file": "D:\\path\\to\\project.wbpj"
    },
    "analysis": {
        "time_step": 0.001,
        "end_time": 0.1,
        "stiffness_coefficient": 0.0,
        "mass_coefficient": 0.0
    },
    "constrain": {
        "force_output_folder": "",
        "rotation_speed": 0.0
    },
    "ui": {
        "theme": "default",
        "language": "zh_CN",
        "window_state": {}
    }
}
```

## 版本升级

当配置文件版本低于当前版本时，ConfigManager 会自动升级配置：

```python
def _upgrade_config(self, config):
    version = config.get("version", "0.0.0")
    
    # 如果版本已经是最新的，直接返回
    if version == self.CURRENT_VERSION:
        return config
    
    # 创建配置副本
    new_config = copy.deepcopy(config)
    
    # 版本升级逻辑
    if version < "0.2.0":
        # 升级到 0.2.0
        if "old_key" in new_config:
            new_config["new_key"] = new_config.pop("old_key")
    
    # 设置新版本
    new_config["version"] = self.CURRENT_VERSION
    
    return new_config
``` 