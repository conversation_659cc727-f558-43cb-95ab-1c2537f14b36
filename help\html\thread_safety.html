<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线程安全架构 - 振动传递计算软件</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>线程安全架构</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>🛡️ 概述</h2>
            <p>v1.2.0版本引入了全新的线程安全架构，彻底解决了API线程与UI线程之间的数据竞争问题，显著提升了应用程序的稳定性和可靠性。</p>
            
            <div class="feature-highlight">
                <h3>核心改进</h3>
                <ul>
                    <li><strong>线程安全通信</strong> - 使用Qt信号槽机制替代直接回调</li>
                    <li><strong>跨线程错误处理</strong> - 完整的异常传播和处理机制</li>
                    <li><strong>数据访问同步</strong> - QMutex/QReadWriteLock保护共享资源</li>
                    <li><strong>线程验证机制</strong> - 确保UI更新只在主线程执行</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔧 技术实现</h2>
            
            <div class="feature-card">
                <h3>线程安全窗口管理器</h3>
                <p><code>core/thread_safe_window_manager.py</code></p>
                <ul>
                    <li>使用信号槽机制进行跨线程通信</li>
                    <li>自动检测当前线程并选择合适的通信方式</li>
                    <li>提供线程安全的窗口切换和状态管理</li>
                    <li>支持高并发的API调用</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>跨线程错误处理</h3>
                <p><code>core/thread_safe_error_handler.py</code></p>
                <ul>
                    <li>全局错误收集和报告机制</li>
                    <li>线程上下文信息记录</li>
                    <li>异常的安全传播和处理</li>
                    <li>详细的错误日志和调试信息</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>优化的API服务器</h3>
                <p><code>ctrl/api_server.py</code></p>
                <ul>
                    <li>线程安全的请求处理</li>
                    <li>异步操作支持</li>
                    <li>资源访问保护</li>
                    <li>性能监控和统计</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📊 性能优势</h2>
            
            <table>
                <thead>
                    <tr>
                        <th>指标</th>
                        <th>优化前</th>
                        <th>优化后</th>
                        <th>改进</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>崩溃风险</td>
                        <td>存在数据竞争</td>
                        <td>零崩溃风险</td>
                        <td>✅ 100%消除</td>
                    </tr>
                    <tr>
                        <td>并发支持</td>
                        <td>有限</td>
                        <td>高并发</td>
                        <td>✅ 显著提升</td>
                    </tr>
                    <tr>
                        <td>错误处理</td>
                        <td>基础</td>
                        <td>完整机制</td>
                        <td>✅ 全面覆盖</td>
                    </tr>
                    <tr>
                        <td>调试能力</td>
                        <td>困难</td>
                        <td>详细日志</td>
                        <td>✅ 大幅改善</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🔍 使用指南</h2>
            
            <div class="step-guide">
                <h3>自动启用</h3>
                <p>线程安全机制在应用程序启动时自动启用，无需手动配置。系统会自动：</p>
                <ol>
                    <li><strong>检测线程环境</strong> - 自动识别当前运行的线程类型</li>
                    <li><strong>选择通信方式</strong> - 根据线程类型选择最适合的通信机制</li>
                    <li><strong>保护共享资源</strong> - 自动为共享数据添加访问保护</li>
                    <li><strong>监控运行状态</strong> - 实时监控线程状态和性能指标</li>
                </ol>
            </div>

            <div class="ui-description">
                <h3>API调用示例</h3>
                <p>外部应用程序可以安全地调用API接口，无需担心线程安全问题：</p>
                <pre><code>
# Python示例
import requests

# 安全的API调用
response = requests.post('http://localhost:8000/api/v1/simulation-params', 
                        json={'param': 'value'})

# 系统会自动处理线程安全问题
if response.status_code == 200:
    print("参数更新成功")
                </code></pre>
            </div>
        </div>

        <div class="section">
            <h2>🔧 开发者指南</h2>
            
            <div class="note">
                <h3>线程安全编程原则</h3>
                <ul>
                    <li><strong>UI更新规则</strong> - 永远不要在工作线程中直接更新UI</li>
                    <li><strong>信号槽通信</strong> - 使用Qt信号槽机制进行跨线程通信</li>
                    <li><strong>共享数据保护</strong> - 使用互斥锁保护共享资源</li>
                    <li><strong>异常处理</strong> - 使用全局错误处理器报告异常</li>
                </ul>
            </div>

            <div class="warning">
                <h3>注意事项</h3>
                <ul>
                    <li>避免在锁内执行耗时操作</li>
                    <li>使用QMutexLocker自动管理锁的生命周期</li>
                    <li>提供足够的错误上下文信息</li>
                    <li>定期检查线程状态和性能指标</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📈 监控和调试</h2>
            
            <div class="interpretation">
                <h3>日志信息</h3>
                <p>系统会自动记录线程相关的重要信息：</p>
                <ul>
                    <li><strong>线程创建和销毁</strong> - 记录线程生命周期</li>
                    <li><strong>跨线程通信</strong> - 记录信号槽调用</li>
                    <li><strong>错误和异常</strong> - 详细的错误上下文</li>
                    <li><strong>性能指标</strong> - 线程执行时间和资源使用</li>
                </ul>
            </div>

            <div class="tip">
                <h3>性能监控</h3>
                <p>可以通过以下方式监控线程安全机制的性能：</p>
                <ul>
                    <li>查看应用程序日志文件</li>
                    <li>使用系统性能监控工具</li>
                    <li>观察API响应时间</li>
                    <li>检查内存使用情况</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
            <p><a href="index.html" class="back-link">← 返回主页</a></p>
        </div>
    </div>
</body>
</html>
