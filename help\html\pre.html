<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前处理界面 - ANSYS四合一自动化脚本</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-purple-600 to-blue-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-purple-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🔧 前处理界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-purple-100">
                    ANSYS四合一自动化脚本核心功能 | 智能化前处理操作平台
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-purple-500 bg-opacity-20 text-purple-100 text-sm font-semibold px-4 py-2 rounded-full border border-purple-400">
                        🧹 清理 | 🔧 创建 | 📝 规范 | 📤 导出
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">

        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                前处理界面是ANSYS Workbench自动化处理的核心模块，提供了强大的四合一自动化脚本功能。在这个界面中，您可以执行智能化的ANSYS前处理操作，包括命名选择管理、几何体处理和面选择导出等功能。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">ANSYS配置区</h3>
                    </div>
                    <p class="text-sm text-blue-600">位于界面上方，用于配置ANSYS Workbench路径和项目文件</p>
                </div>

                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10h.01M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">脚本执行区</h3>
                    </div>
                    <p class="text-sm text-green-600">位于界面中央，包含四合一自动化脚本的执行按钮</p>
                </div>

                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">进度监控区</h3>
                    </div>
                    <p class="text-sm text-purple-600">实时显示脚本执行进度和状态信息</p>
                </div>

                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">日志显示区</h3>
                    </div>
                    <p class="text-sm text-orange-600">显示详细的执行日志和中文输出信息</p>
                </div>

                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-red-800">结果查看区</h3>
                    </div>
                    <p class="text-sm text-red-600">显示生成的JSON文件和处理结果</p>
                </div>
            </div>
        </section>

        <!-- Four-in-One Automation Script -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🚀 ANSYS四合一自动化脚本</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">集成四大核心功能的智能化ANSYS前处理系统</p>
            </div>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🧹</span>任务1: 清理数字命名选择
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">核心功能</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 自动扫描并识别数字命名的选择</li>
                                    <li>• 智能清理不需要的数字命名模式</li>
                                    <li>• 保护重要的命名选择不被误删</li>
                                    <li>• 提供清理前的安全检查机制</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">技术特点</h4>
                                <p class="text-sm text-blue-600">使用智能模式识别算法，确保只清理真正不需要的数字命名选择，避免误删重要数据。</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>任务2: 创建/更新命名选择
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">核心功能</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 根据几何体名称自动创建命名选择</li>
                                    <li>• 支持ROTOR等关键组件的智能识别</li>
                                    <li>• 自动处理重复命名和版本更新</li>
                                    <li>• 智能匹配几何体和命名选择关系</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">智能识别</h4>
                                <p class="text-sm text-green-600">自动识别ROTOR、STATOR等关键组件，建立准确的几何体与命名选择映射关系。</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📝</span>任务3: 统一命名规范
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">核心功能</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 将所有命名选择统一为小写格式</li>
                                    <li>• 智能处理命名冲突和重复问题</li>
                                    <li>• 保持命名的一致性和规范性</li>
                                    <li>• 支持批量重命名操作</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">规范化处理</h4>
                                <p class="text-sm text-purple-600">确保所有命名选择遵循统一的命名规范，提高模型的可维护性和一致性。</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📤</span>任务4: 导出面命名选择
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">核心功能</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 自动识别并导出指定的面命名选择</li>
                                    <li>• 生成标准JSON格式的面选择数据文件</li>
                                    <li>• 支持多种面类型的批量导出</li>
                                    <li>• 包含完整的面ID和属性信息</li>
                                </ul>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">数据格式</h4>
                                <p class="text-sm text-orange-600">生成结构化的JSON数据文件，包含面ID、属性、时间戳等完整信息，便于后续处理。</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <div class="section">
            <h2>📊 实时进度监控系统</h2>

            <div class="feature-highlight">
                <h3>7步骤进度跟踪</h3>
                <p>系统提供详细的7步骤进度监控，实时显示脚本执行状态：</p>

                <div class="progress-steps">
                    <ol>
                        <li><strong>⏳ 脚本开始执行</strong> - 初始化ANSYS环境和脚本参数</li>
                        <li><strong>🧹 任务1: 清理数字命名选择</strong> - 扫描和清理不需要的命名选择</li>
                        <li><strong>🔧 任务2: 创建/更新命名选择</strong> - 智能创建和更新几何体命名选择</li>
                        <li><strong>📝 任务3: 统一命名规范</strong> - 将命名选择统一为小写格式</li>
                        <li><strong>📤 任务4: 导出面命名选择</strong> - 生成JSON格式的面选择数据</li>
                        <li><strong>🔄 UI树已刷新</strong> - 更新ANSYS界面显示</li>
                        <li><strong>✅ 所有任务执行完毕</strong> - 脚本执行完成</li>
                    </ol>
                </div>
            </div>

            <div class="feature-card">
                <h3>🌐 多编码日志监控</h3>
                <ul>
                    <li><strong>智能编码检测</strong> - 自动检测UTF-8、GBK、CP936编码格式</li>
                    <li><strong>中文完美显示</strong> - 确保中文日志内容正确显示</li>
                    <li><strong>实时关键词匹配</strong> - 智能匹配进度关键词，实时更新状态</li>
                    <li><strong>错误自动恢复</strong> - 编码错误自动修复机制</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>🧵 线程安全架构</h3>
                <ul>
                    <li><strong>LogMonitorThread</strong> - 后台日志监控线程</li>
                    <li><strong>ANSYS日志监控</strong> - 专用ANSYS输出监控线程</li>
                    <li><strong>进程输出捕获</strong> - 实时捕获进程输出线程</li>
                    <li><strong>UI线程安全</strong> - 确保界面更新的线程安全性</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📁 结果文件管理</h3>
                <ul>
                    <li><strong>JSON文件生成</strong> - 自动生成面选择数据文件</li>
                    <li><strong>时间戳命名</strong> - 使用时间戳避免文件冲突</li>
                    <li><strong>自动清理机制</strong> - 智能清理旧版本文件</li>
                    <li><strong>结果验证</strong> - 自动验证生成文件的完整性</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔧 四合一自动化脚本操作指南</h2>

            <div class="step-guide">
                <h3>完整操作流程</h3>
                <ol>
                    <li>
                        <strong>🔧 配置ANSYS环境</strong>
                        <p>在主界面设置中配置ANSYS Workbench的启动文件路径。确保路径指向正确的RunWB2.exe文件，通常位于：<br>
                        <code>C:\Program Files\ANSYS Inc\v232\Framework\bin\Win64\RunWB2.exe</code></p>
                    </li>
                    <li>
                        <strong>📁 加载项目文件</strong>
                        <p>确保ANSYS Workbench项目文件（.wbpj）已正确加载，并且包含需要处理的几何模型。验证项目文件路径配置正确。</p>
                    </li>
                    <li>
                        <strong>🚀 启动四合一脚本</strong>
                        <p>点击"执行脚本"按钮启动四合一自动化脚本。系统将自动打开进度监控对话框，显示实时执行状态。</p>
                    </li>
                    <li>
                        <strong>📊 监控执行进度</strong>
                        <p>在进度对话框中实时查看7步骤执行状态：
                        <ul>
                            <li>观察进度条的实时更新</li>
                            <li>查看详细的中文日志输出</li>
                            <li>确认每个任务的完成状态</li>
                        </ul></p>
                    </li>
                    <li>
                        <strong>✅ 验证执行结果</strong>
                        <p>脚本执行完成后，验证以下结果：
                        <ul>
                            <li>检查生成的JSON面选择文件</li>
                            <li>验证ANSYS模型中的命名选择</li>
                            <li>确认所有命名选择已统一为小写</li>
                            <li>查看详细的执行日志</li>
                        </ul></p>
                    </li>
                    <li>
                        <strong>📁 查看输出文件</strong>
                        <p>在output目录中查看生成的文件：
                        <ul>
                            <li><code>face_YYYYMMDD_HHMMSS.json</code> - 面选择数据文件</li>
                            <li><code>ansys_automation_log.log</code> - 详细执行日志</li>
                            <li><code>pre_process_YYYYMMDD_HHMMSS.log</code> - 进程日志</li>
                        </ul></p>
                    </li>
                </ol>
            </div>

            <div class="tip">
                <h3>💡 操作技巧</h3>
                <ul>
                    <li><strong>编码显示</strong> - 如果看到中文乱码，系统会自动处理编码转换，无需手动干预</li>
                    <li><strong>进度监控</strong> - 进度条停滞时，查看日志确认脚本是否正在执行</li>
                    <li><strong>错误处理</strong> - 如果执行失败，查看错误日志并检查ANSYS配置</li>
                    <li><strong>文件管理</strong> - 系统会自动清理旧版本文件，保持目录整洁</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>模型处理技巧</h2>
            
            <div class="tip">
                <h3>几何模型优化</h3>
                <ul>
                    <li><strong>简化复杂特征</strong> - 移除对分析不重要的小特征，如小圆角、小孔等，可以简化网格划分并提高计算效率</li>
                    <li><strong>修复几何错误</strong> - 使用修复工具处理模型中的拓扑错误，确保模型的完整性</li>
                    <li><strong>分割复杂模型</strong> - 将复杂模型分割成多个简单部件，便于网格划分和分析</li>
                </ul>
            </div>
            
            <div class="tip">
                <h3>面处理技巧</h3>
                <ul>
                    <li><strong>批量选择</strong> - 使用过滤器或框选工具批量选择相似的面，提高工作效率</li>
                    <li><strong>使用命名约定</strong> - 为重要的面和部件使用清晰的命名，便于后续识别和处理</li>
                    <li><strong>保存中间结果</strong> - 在处理复杂模型时，定期保存中间结果，避免意外情况导致工作丢失</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>常见问题</h2>
            
            <div class="faq">
                <h3>Q: 为什么导入的模型显示不完整？</h3>
                <p>A: 这可能是由于模型文件格式不兼容或模型存在拓扑错误导致的。尝试使用不同的文件格式保存模型，或使用CAD软件修复模型后再导入。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 为什么面处理配置需要较长时间？</h3>
                <p>A: 面处理配置涉及复杂的几何计算，处理时间与模型的复杂度直接相关。对于大型或复杂的模型，可能需要更长的处理时间。可以考虑简化模型或分批处理。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 如何处理面处理失败的情况？</h3>
                <p>A: 首先检查几何模型是否有错误，然后确认所有参数设置正确。如果问题仍然存在，可以尝试简化模型、调整参数设置或联系技术支持。</p>
            </div>
            
            <div class="faq">
                <h3>Q: 是否可以导入部分装配体？</h3>
                <p>A: 是的，软件支持导入完整装配体或部分装配体。您可以在CAD软件中选择需要的部件，然后导出为支持的格式导入到本软件中。</p>
            </div>
        </div>

        <div class="warning">
            <h3>注意事项</h3>
            <ul>
                <li>确保几何模型没有拓扑错误，如缺失面、重叠边等</li>
                <li>面处理配置过程中请耐心等待，不要中断操作</li>
                <li>如果出现错误提示，请检查模型和配置参数</li>
                <li>建议定期保存配置信息，避免数据丢失</li>
                <li>对于大型复杂模型，考虑适当简化以提高处理效率</li>
            </ul>
        </div>

        <a href="index.html" class="back-link">返回主页</a>
    </div>

    <div class="footer">
        <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
    </div>

    <div class="section">
        <h2>几何模型导入详解</h2>
        
        <div class="interpretation">
            <h3>支持的文件格式</h3>
            <p>软件支持多种常见的CAD文件格式导入：</p>
            <ul>
                <li><strong>STEP (.stp, .step)</strong> - 标准交换格式，保留完整的几何和拓扑信息</li>
                <li><strong>IGES (.igs, .iges)</strong> - 通用交换格式，广泛支持但可能丢失部分信息</li>
                <li><strong>STL (.stl)</strong> - 三角面片格式，适用于简单模型，但不包含曲面信息</li>
                <li><strong>Parasolid (.x_t, .x_b)</strong> - 高精度格式，保留完整的参数化信息</li>
                <li><strong>ACIS (.sat)</strong> - 适用于从多种CAD系统导入的模型</li>
            </ul>
        </div>
        
        <div class="interpretation">
            <h3>导入选项</h3>
            <p>导入模型时可以设置以下选项：</p>
            <ul>
                <li><strong>单位转换</strong> - 确保导入模型的单位与系统单位一致</li>
                <li><strong>修复选项</strong> - 自动修复常见的几何问题</li>
                <li><strong>简化选项</strong> - 控制小特征的处理方式</li>
                <li><strong>装配体选项</strong> - 控制部件之间的关系保留方式</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>面处理详细说明</h2>
        
        <div class="feature-card">
            <h3>面选择方法</h3>
            <ul>
                <li>单击选择 - 选择单个面</li>
                <li>框选 - 同时选择多个面</li>
                <li>特征选择 - 基于特征类型选择（如圆柱面、平面等）</li>
                <li>高级过滤 - 基于尺寸、位置等条件选择面</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h3>面属性类型</h3>
            <ul>
                <li>接触面 - 定义与其他部件接触的面</li>
                <li>约束面 - 定义固定或施加约束的面</li>
                <li>载荷面 - 定义施加力或压力的面</li>
                <li>对称面 - 定义模型的对称面</li>
                <li>热边界面 - 定义热传导边界条件的面</li>
            </ul>
        </div>
        
        <div class="feature-card">
            <h3>面处理操作</h3>
            <ul>
                <li>合并面 - 将多个相邻面合并为一个面</li>
                <li>分割面 - 将一个面分割成多个面</li>
                <li>延展面 - 扩展面的边界</li>
                <li>修复面 - 修复面的几何缺陷</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>材料属性设置</h2>
        
        <div class="ui-description">
            <h3>材料库</h3>
            <p>软件提供了丰富的材料库，包括：</p>
            <ul>
                <li>常见金属材料（钢、铝、铜等）</li>
                <li>工程塑料</li>
                <li>复合材料</li>
                <li>橡胶和弹性体</li>
            </ul>
        </div>
        
        <div class="ui-description">
            <h3>自定义材料</h3>
            <p>您可以创建自定义材料并设置以下属性：</p>
            <ul>
                <li>基本物理属性（密度、泊松比等）</li>
                <li>弹性属性（杨氏模量、剪切模量等）</li>
                <li>热学属性（热导率、比热容等）</li>
                <li>阻尼特性（结构阻尼、材料阻尼等）</li>
            </ul>
        </div>
        
        <div class="tip">
            <h3>材料分配技巧</h3>
            <ul>
                <li>使用"应用到所有"功能快速为整个模型分配相同材料</li>
                <li>为不同部件分配不同材料时，先选择部件再选择材料</li>
                <li>使用材料组功能管理复杂模型的材料分配</li>
                <li>检查材料分配完整性，确保所有部件都已分配材料</li>
            </ul>
        </div>
    </div>
</body>
</html> 