"""
网格窗口模块

此模块定义了应用程序的多网格管理窗口类，负责：
1. 显示多网格管理界面
2. 处理网格参数的增删改查操作
3. 管理网格生成和模态分析流程
4. 提供网格无关性验证功能

作者: [作者名]
日期: [日期]
"""

import logging
import json
from datetime import datetime
from typing import Optional, List
from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtGui import QDoubleValidator, QStandardItemModel, QStandardItem, QColor
from PySide6.QtWidgets import (QMessageBox, QTableWidgetItem, QHeaderView,
                               QPushButton, QHBoxLayout, QWidget, QProgressBar,
                               QFileDialog, QListWidgetItem, QAbstractItemView,
                               QMenu, QApplication)

from ui import ui_mesh_simple
from .base_window import BaseWindow
from .mesh_parameter_dialog import MeshParameterDialog
from core.mesh_manager import MeshManager, MeshParameter, MeshStatus, ElementType

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MeshWindow(BaseWindow):
    """多网格管理窗口类"""

    def __init__(self, window_manager):
        super().__init__(window_manager)

        # 设置简化的UI
        self.ui = ui_mesh_simple.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("网格无关性验证系统")

        # 初始化网格管理器
        self.mesh_manager = MeshManager()

        # 设置窗口样式
        self._setup_window_style()

        # 初始化UI组件
        self._setup_ui_components()

        # 应用按钮动画效果（必须在信号槽连接之前）
        self.setup_animated_buttons()

        # 连接信号槽（必须在按钮替换之后）
        self._connect_signals()

        # 加载配置数据
        self._load_configuration()

        logger.info("多网格管理窗口初始化完成")

    def _setup_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet(self.styleSheet() + """
            QMainWindow::title {
                font-weight: bold;
                font-size: 14px;
            }

            /* 表格样式增强 */
            QTableWidget {
                border: 1px solid #dcdfe6;
                background-color: white;
                gridline-color: #e9eaec;
                selection-background-color: #3498db;
                selection-color: white;
                alternate-background-color: #f5f7fa;
            }

            QTableWidget::item {
                padding: 8px;
                border: none;
            }

            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }

            /* 按钮样式增强 */
            QPushButton {
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 8px 16px;
                background-color: white;
                color: #34495e;
                font-weight: 500;
            }

            QPushButton:hover {
                background-color: #ecf0f1;
                border-color: #3498db;
            }

            QPushButton:pressed {
                background-color: #d5dbdb;
            }
        """)

    def _setup_ui_components(self):
        """初始化UI组件"""
        # 设置网格参数表格
        self._setup_mesh_table()

        # 设置网格预览下拉框
        self._setup_mesh_preview()

        logger.debug("UI组件初始化完成")

    def _setup_mesh_table(self):
        """设置网格参数表格"""
        table = self.ui.tableWidget_mesh_params

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setSortingEnabled(True)

        # 设置列宽模式
        header = table.horizontalHeader()
        header.setStretchLastSection(False)  # 不让最后一列自动拉伸

        # 设置各列的调整模式
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive)  # 网格名称 - 可交互调整
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 尺寸 - 自适应内容
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 状态 - 自适应内容
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 节点数 - 自适应内容
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # 单元数 - 自适应内容
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Fixed)  # 操作列 - 固定宽度

        # 设置初始列宽
        table.setColumnWidth(0, 150)  # 网格名称列
        table.setColumnWidth(5, 140)  # 操作列，确保两个按钮能完整显示

        # 设置行高，确保按钮能完整显示
        table.verticalHeader().setDefaultSectionSize(45)
        table.verticalHeader().setVisible(False)

        # 启用右键菜单
        table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        table.customContextMenuRequested.connect(self._show_table_context_menu)

        logger.debug("网格参数表格设置完成")

    def _setup_mesh_preview(self):
        """设置网格预览下拉框"""
        combo = self.ui.comboBox_mesh_select
        combo.clear()
        combo.addItem("请选择网格", None)

        logger.debug("网格预览下拉框设置完成")

    def _setup_batch_operation_list(self):
        """设置批量操作列表"""
        list_widget = self.ui.listWidget_selected_meshes
        list_widget.clear()
        list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)

        logger.debug("批量操作列表设置完成")

    def _setup_comparison_table(self):
        """设置网格对比表格"""
        table = self.ui.tableWidget_mesh_comparison

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        # 设置列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)

        logger.debug("网格对比表格设置完成")

    def _setup_result_comparison_list(self):
        """设置结果对比列表"""
        list_widget = self.ui.listWidget_comparison_meshes
        list_widget.clear()
        list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)

        logger.debug("结果对比列表设置完成")

    def _connect_signals(self):
        """连接信号槽"""
        # 网格管理标签页信号
        self.ui.btn_add_mesh.clicked.connect(self._on_add_mesh)
        self.ui.btn_import_mesh.clicked.connect(self._on_import_mesh)
        self.ui.btn_export_mesh.clicked.connect(self._on_export_mesh)

        # 表格信号
        self.ui.tableWidget_mesh_params.itemSelectionChanged.connect(self._on_mesh_selection_changed)
        self.ui.tableWidget_mesh_params.cellDoubleClicked.connect(self._on_mesh_double_clicked)

        # 网格预览信号
        self.ui.comboBox_mesh_select.currentIndexChanged.connect(self._on_preview_mesh_changed)

        # 简化的操作按钮信号
        self.ui.btn_generate_mesh.clicked.connect(self._on_generate_mesh)
        self.ui.btn_modal_analysis.clicked.connect(self._on_modal_analysis)
        self.ui.btn_export_results.clicked.connect(self._on_export_results)

        # 导航按钮信号
        self.ui.btn_main_menu.clicked.connect(self._on_main_menu)
        self.ui.push_constrainui.clicked.connect(self._on_previous)
        self.ui.push_resultui.clicked.connect(self._on_next)

        # 网格管理器信号
        self.mesh_manager.signals.mesh_added.connect(self._on_mesh_added)
        self.mesh_manager.signals.mesh_removed.connect(self._on_mesh_removed)
        self.mesh_manager.signals.mesh_updated.connect(self._on_mesh_updated)
        self.mesh_manager.signals.status_changed.connect(self._on_mesh_status_changed)
        self.mesh_manager.signals.current_mesh_changed.connect(self._on_current_mesh_changed)
        self.mesh_manager.signals.error_occurred.connect(self._on_mesh_error)

        logger.debug("信号槽连接完成")

    def _load_configuration(self):
        """加载配置数据"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 加载网格参数
            mesh_data = config_manager.get_mesh_parameters()
            if mesh_data:
                success = self.mesh_manager.from_dict({"mesh_parameters": mesh_data})
                if success:
                    logger.info(f"成功加载 {len(mesh_data)} 个网格参数")
                else:
                    logger.warning("加载网格参数失败")

            # 设置当前网格
            current_mesh_id = config_manager.get_current_mesh_id()
            if current_mesh_id:
                self.mesh_manager.set_current_mesh(current_mesh_id)

            # 刷新UI显示
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"加载配置数据失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "警告", f"加载配置数据失败: {str(e)}")

    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.btn_add_mesh,
            self.ui.btn_import_mesh,
            self.ui.btn_export_mesh,
            self.ui.btn_generate_mesh,
            self.ui.btn_modal_analysis,
            self.ui.btn_export_results,
            self.ui.btn_main_menu,
            self.ui.push_constrainui,
            self.ui.push_resultui
        ]

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        logger.debug("按钮动画效果设置完成")

    # ==================== 网格管理事件处理 ====================

    def _on_add_mesh(self):
        """添加网格按钮点击处理"""
        try:
            dialog = MeshParameterDialog(self)
            dialog.parameter_accepted.connect(self._add_new_mesh)
            dialog.exec()
        except Exception as e:
            logger.error(f"打开添加网格对话框失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"打开添加网格对话框失败: {str(e)}")

    def _add_new_mesh(self, mesh_parameter: MeshParameter):
        """添加新网格参数"""
        try:
            success = self.mesh_manager.add_mesh(mesh_parameter)
            if success:
                # 保存配置
                self._save_configuration()

                # 强制刷新所有UI组件（确保信号处理完成后的额外刷新）
                self._refresh_all_ui()

                # 处理事件循环，确保UI更新
                QApplication.processEvents()

                # 显示成功消息
                mesh_count = self.mesh_manager.mesh_count
                QMessageBox.information(
                    self, "添加成功",
                    f"成功添加网格: {mesh_parameter.name}\n当前共 {mesh_count} 个网格"
                )

                logger.info(f"成功添加网格: {mesh_parameter.name}, 总网格数: {mesh_count}")

                # 显示状态消息
                self.show_status_message(f"成功添加网格 '{mesh_parameter.name}'")

            else:
                QMessageBox.warning(self, "添加失败", "添加网格失败，请检查参数设置")

        except Exception as e:
            logger.error(f"添加网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"添加网格失败: {str(e)}")

    def _on_import_mesh(self):
        """导入网格配置按钮点击处理"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入网格配置", "", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 记录导入前的网格数量
                initial_count = self.mesh_manager.mesh_count

                success = self.mesh_manager.import_from_json(file_path)
                if success:
                    # 立即刷新所有UI组件
                    self._refresh_all_ui()

                    # 保存配置
                    self._save_configuration()

                    # 显示导入结果
                    final_count = self.mesh_manager.mesh_count
                    imported_count = final_count - initial_count if final_count > initial_count else final_count

                    QMessageBox.information(
                        self, "导入成功",
                        f"成功导入 {imported_count} 个网格配置\n总网格数量: {final_count}"
                    )

                    logger.info(f"成功导入网格配置: {file_path}, 导入数量: {imported_count}")

                    # 显示状态消息
                    self.show_status_message(f"成功导入 {imported_count} 个网格配置")

                else:
                    QMessageBox.warning(self, "导入失败", "网格配置导入失败，请检查文件格式")

        except Exception as e:
            logger.error(f"导入网格配置失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导入网格配置失败: {str(e)}")

    def _on_export_mesh(self):
        """导出网格配置按钮点击处理"""
        try:
            if self.mesh_manager.mesh_count == 0:
                QMessageBox.information(self, "提示", "没有网格参数可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出网格配置", "mesh_config.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                success = self.mesh_manager.export_to_json(file_path)
                if success:
                    QMessageBox.information(self, "成功", "网格配置导出成功")
                    logger.info(f"成功导出网格配置: {file_path}")
                else:
                    QMessageBox.warning(self, "失败", "网格配置导出失败")
        except Exception as e:
            logger.error(f"导出网格配置失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出网格配置失败: {str(e)}")

    def _on_mesh_selection_changed(self):
        """网格表格选择变化处理"""
        try:
            selected_items = self.ui.tableWidget_mesh_params.selectedItems()
            if selected_items:
                row = selected_items[0].row()
                mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if mesh_id:
                    self.mesh_manager.set_current_mesh(mesh_id)
                    self._update_mesh_preview()
                    self._update_mesh_statistics()
                    logger.debug(f"选中网格: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格选择变化失败: {str(e)}", exc_info=True)

    def _on_mesh_double_clicked(self, row: int, column: int):
        """网格表格双击处理"""
        try:
            mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if mesh_id:
                mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh_param:
                    dialog = MeshParameterDialog(self, mesh_param)
                    dialog.parameter_accepted.connect(lambda p: self._update_existing_mesh(mesh_id, p))
                    dialog.exec()
        except Exception as e:
            logger.error(f"处理网格双击失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"打开编辑对话框失败: {str(e)}")

    def _update_existing_mesh(self, mesh_id: str, mesh_parameter: MeshParameter):
        """更新现有网格参数"""
        try:
            mesh_parameter.id = mesh_id  # 保持原有ID
            success = self.mesh_manager.update_mesh(mesh_parameter)
            if success:
                self._save_configuration()
                QMessageBox.information(self, "成功", f"成功更新网格: {mesh_parameter.name}")
                logger.info(f"成功更新网格: {mesh_parameter.name}")
            else:
                QMessageBox.warning(self, "失败", "更新网格失败，请检查参数设置")
        except Exception as e:
            logger.error(f"更新网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"更新网格失败: {str(e)}")

    # ==================== 批量操作事件处理 ====================

    def _on_select_all_toggled(self, checked: bool):
        """全选复选框切换处理"""
        try:
            list_widget = self.ui.listWidget_selected_meshes
            for i in range(list_widget.count()):
                item = list_widget.item(i)
                if checked:
                    item.setSelected(True)
                else:
                    item.setSelected(False)
            logger.debug(f"批量选择状态: {checked}")
        except Exception as e:
            logger.error(f"处理全选切换失败: {str(e)}", exc_info=True)

    def _on_batch_generate(self):
        """批量生成按钮点击处理"""
        try:
            selected_items = self.ui.listWidget_selected_meshes.selectedItems()
            if not selected_items:
                QMessageBox.information(self, "提示", "请先选择要生成的网格")
                return

            # 切换到网格生成标签页
            self.ui.tabWidget_main.setCurrentIndex(1)

            # 开始批量生成（这里应该调用实际的网格生成逻辑）
            self._start_batch_generation(selected_items)

        except Exception as e:
            logger.error(f"批量生成失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"批量生成失败: {str(e)}")

    def _start_batch_generation(self, selected_items):
        """开始批量生成"""
        # 这里应该实现实际的网格生成逻辑
        # 目前只是模拟进度更新
        self.ui.progressBar_generation.setValue(0)
        self.ui.label_progress_text.setText("开始批量生成...")

        # 模拟生成过程
        total_count = len(selected_items)
        for i, item in enumerate(selected_items):
            mesh_id = item.data(Qt.ItemDataRole.UserRole)
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                # 更新状态为生成中
                mesh_param.update_status(MeshStatus.GENERATING)

                # 更新进度
                progress = int((i + 1) / total_count * 100)
                self.ui.progressBar_generation.setValue(progress)
                self.ui.label_progress_text.setText(f"正在生成 {mesh_param.name}... ({i+1}/{total_count})")

                # 这里应该调用实际的网格生成API
                # 暂时模拟生成完成
                mesh_param.update_status(MeshStatus.GENERATED)
                mesh_param.statistics.node_count = 10000 + i * 1000
                mesh_param.statistics.element_count = 8000 + i * 800

        self.ui.label_progress_text.setText("批量生成完成")
        self._refresh_all_ui()
        logger.info(f"批量生成完成，共生成 {total_count} 个网格")

    def _on_stop_generation(self):
        """停止生成按钮点击处理"""
        try:
            # 这里应该实现停止生成的逻辑
            self.ui.label_progress_text.setText("生成已停止")
            logger.info("用户停止了网格生成")
        except Exception as e:
            logger.error(f"停止生成失败: {str(e)}", exc_info=True)

    # ==================== 模态分析事件处理 ====================

    def _on_single_modal(self):
        """单个模态计算按钮点击处理"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if not current_mesh:
                QMessageBox.information(self, "提示", "请先选择要计算的网格")
                return

            if current_mesh.status != MeshStatus.GENERATED:
                QMessageBox.warning(self, "警告", "只能对已生成的网格进行模态计算")
                return

            # 切换到模态分析标签页
            self.ui.tabWidget_main.setCurrentIndex(2)

            # 开始模态计算
            self._start_modal_calculation([current_mesh])

        except Exception as e:
            logger.error(f"单个模态计算失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"单个模态计算失败: {str(e)}")

    def _on_batch_modal(self):
        """批量模态计算按钮点击处理"""
        try:
            generated_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.GENERATED)
            if not generated_meshes:
                QMessageBox.information(self, "提示", "没有已生成的网格可以进行模态计算")
                return

            # 切换到模态分析标签页
            self.ui.tabWidget_main.setCurrentIndex(2)

            # 开始批量模态计算
            self._start_modal_calculation(generated_meshes)

        except Exception as e:
            logger.error(f"批量模态计算失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"批量模态计算失败: {str(e)}")

    def _start_modal_calculation(self, meshes: List[MeshParameter]):
        """开始模态计算"""
        # 这里应该实现实际的模态计算逻辑
        # 目前只是模拟计算过程
        for mesh in meshes:
            mesh.update_status(MeshStatus.CALCULATING)

            # 模拟计算结果
            mesh.modal_results.frequencies = [10.5 + i * 2.3 for i in range(10)]
            mesh.modal_results.calculation_time = 120.0

            mesh.update_status(MeshStatus.COMPLETED)

        self._refresh_all_ui()
        logger.info(f"模态计算完成，共计算 {len(meshes)} 个网格")

    def _on_freq_limit_toggled(self, checked: bool):
        """频率限制复选框状态改变处理"""
        try:
            # 启用或禁用频率范围输入框
            self.ui.doubleSpinBox_freq_min.setEnabled(checked)
            self.ui.doubleSpinBox_freq_max.setEnabled(checked)
            self.ui.label_freq_range.setEnabled(checked)

            # 更新界面样式
            if checked:
                self.ui.label_freq_range.setStyleSheet("")
                logger.debug("频率范围限制已启用")
            else:
                self.ui.label_freq_range.setStyleSheet("color: #bdc3c7;")
                logger.debug("频率范围限制已禁用")

        except Exception as e:
            logger.error(f"处理频率限制状态变化失败: {str(e)}", exc_info=True)

    def _on_modal_params_changed(self):
        """模态参数变化处理"""
        try:
            # 获取当前参数
            mode_count = self.ui.spinBox_modal_count.value()
            freq_min = self.ui.doubleSpinBox_freq_min.value()
            freq_max = self.ui.doubleSpinBox_freq_max.value()
            freq_limited = self.ui.checkBox_limit_freq.isChecked()

            # 验证参数有效性
            if freq_limited and freq_min >= freq_max:
                # 自动调整最大频率
                self.ui.doubleSpinBox_freq_max.setValue(freq_min + 100)
                logger.debug(f"自动调整频率范围: {freq_min} - {freq_min + 100} Hz")

            # 根据模态阶数调整建议频率范围
            if mode_count > 0:
                suggested_max_freq = min(100000, mode_count * 50)
                if not freq_limited:
                    self.ui.doubleSpinBox_freq_max.setValue(suggested_max_freq)

            logger.debug(f"模态参数更新: 阶数={mode_count}, 频率范围={'限制' if freq_limited else '不限制'}")

        except Exception as e:
            logger.error(f"处理模态参数变化失败: {str(e)}", exc_info=True)

    # ==================== 其他事件处理 ====================

    def _on_preview_mesh_changed(self, index: int):
        """网格预览选择变化处理"""
        try:
            combo = self.ui.comboBox_mesh_select
            mesh_id = combo.itemData(index)
            if mesh_id:
                self.mesh_manager.set_current_mesh(mesh_id)
                self._update_mesh_preview()
                self._update_mesh_statistics()
        except Exception as e:
            logger.error(f"处理预览网格变化失败: {str(e)}", exc_info=True)

    def _on_export_results(self):
        """导出结果按钮点击处理"""
        try:
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            if not completed_meshes:
                QMessageBox.information(self, "提示", "没有已完成的模态计算结果可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出模态结果", "modal_results.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 这里应该实现结果导出逻辑
                QMessageBox.information(self, "成功", "模态结果导出成功")
                logger.info(f"成功导出模态结果: {file_path}")

        except Exception as e:
            logger.error(f"导出结果失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出结果失败: {str(e)}")

    # ==================== 底部导航按钮处理 ====================

    def _on_generate_mesh(self):
        """生成网格按钮点击处理"""
        try:
            # 切换到网格生成标签页
            self.ui.tabWidget_main.setCurrentIndex(1)
        except Exception as e:
            logger.error(f"切换到网格生成标签页失败: {str(e)}", exc_info=True)

    def _on_view_results(self):
        """查看结果按钮点击处理"""
        try:
            # 切换到结果对比标签页
            self.ui.tabWidget_main.setCurrentIndex(3)
        except Exception as e:
            logger.error(f"切换到结果对比标签页失败: {str(e)}", exc_info=True)

    def _on_previous(self):
        """上一步按钮点击处理 - 跳转到约束设置"""
        try:
            from core.navigation_manager import navigate_to_previous_step
            from window_manager import WindowType
            success = navigate_to_previous_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到约束设置界面")
            else:
                logger.warning("跳转到约束设置失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到约束设置界面")
        except Exception as e:
            logger.error(f"跳转到约束设置失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到约束设置界面: {str(e)}")

    def _on_next(self):
        """下一步按钮点击处理 - 跳转到计算结果"""
        try:
            from core.navigation_manager import navigate_to_next_step
            from window_manager import WindowType
            success = navigate_to_next_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到计算结果界面")
            else:
                logger.warning("跳转到计算结果失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到计算结果界面")
        except Exception as e:
            logger.error(f"跳转到计算结果失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到计算结果界面: {str(e)}")

    def _on_main_menu(self):
        """主菜单按钮点击处理"""
        try:
            from ctrl.mesh_slot import to_main_slot
            to_main_slot(self.window_manager)
            logger.info("成功跳转到主菜单")
        except Exception as e:
            logger.error(f"跳转到主菜单失败: {str(e)}", exc_info=True)
            QMessageBox.warning(self, "跳转失败", f"无法跳转到主菜单: {str(e)}")

    # ==================== 标签页切换功能 ====================

    def switch_to_tab(self, tab_index: int):
        """切换到指定标签页

        Args:
            tab_index: 标签页索引 (0=网格管理, 1=网格生成, 2=模态分析, 3=结果对比)
        """
        try:
            if 0 <= tab_index < self.ui.tabWidget_main.count():
                self.ui.tabWidget_main.setCurrentIndex(tab_index)
                tab_names = ["网格管理", "网格生成", "模态分析", "结果对比"]
                logger.info(f"切换到标签页: {tab_names[tab_index]}")
            else:
                logger.warning(f"无效的标签页索引: {tab_index}")
        except Exception as e:
            logger.error(f"切换标签页失败: {str(e)}", exc_info=True)

    def get_current_tab_index(self) -> int:
        """获取当前标签页索引"""
        try:
            return self.ui.tabWidget_main.currentIndex()
        except Exception as e:
            logger.error(f"获取当前标签页索引失败: {str(e)}", exc_info=True)
            return 0

    # ==================== 网格管理器信号处理 ====================

    def _on_mesh_added(self, mesh_id: str):
        """网格添加信号处理"""
        try:
            # 刷新所有相关UI组件（与删除操作保持一致）
            self._refresh_all_ui()

            # 更新统计信息
            self._update_mesh_statistics()

            # 更新UI状态
            self.update_ui_state()

            # 自动调整表格列宽
            self.auto_resize_table_columns()

            # 高亮显示新添加的网格
            self.highlight_mesh_in_table(mesh_id)

            # 显示状态消息
            mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
            mesh_name = mesh.name if mesh else mesh_id
            mesh_count = self.mesh_manager.mesh_count
            self.show_status_message(f"成功添加网格 '{mesh_name}'，当前共 {mesh_count} 个网格")

            logger.debug(f"网格添加UI更新完成: {mesh_id}, 总网格数: {mesh_count}")

        except Exception as e:
            logger.error(f"处理网格添加信号失败: {str(e)}", exc_info=True)

    def _on_mesh_removed(self, mesh_id: str):
        """网格移除信号处理"""
        try:
            # 刷新所有相关UI组件
            self._refresh_all_ui()

            # 更新统计信息
            self._update_mesh_statistics()

            # 更新UI状态
            self.update_ui_state()

            # 自动调整表格列宽
            self.auto_resize_table_columns()

            # 显示状态消息
            mesh_count = self.mesh_manager.mesh_count
            self.show_status_message(f"成功删除网格，当前共 {mesh_count} 个网格")

            logger.debug(f"网格移除UI更新完成: {mesh_id}, 剩余网格数: {mesh_count}")

        except Exception as e:
            logger.error(f"处理网格移除信号失败: {str(e)}", exc_info=True)

    def _on_mesh_updated(self, mesh_id: str):
        """网格更新信号处理"""
        try:
            self._refresh_mesh_table()
            self._refresh_mesh_preview()
            self._refresh_comparison_table()
            self.update_ui_state()
            self.show_status_message(f"成功更新网格")
            logger.debug(f"网格更新UI更新完成: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格更新信号失败: {str(e)}", exc_info=True)

    def _on_mesh_status_changed(self, mesh_id: str, new_status: str):
        """网格状态变化信号处理"""
        try:
            # 刷新所有相关UI组件
            self._refresh_mesh_table()
            self._refresh_batch_list()  # 确保批量操作列表也更新
            self._refresh_comparison_table()
            self._refresh_comparison_list()

            # 更新UI状态
            self.update_ui_state()

            # 显示状态消息
            mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
            mesh_name = mesh.name if mesh else mesh_id
            self.show_status_message(f"网格 '{mesh_name}' 状态更新: {new_status}")

            logger.debug(f"网格状态变化UI更新完成: {mesh_id} -> {new_status}")
        except Exception as e:
            logger.error(f"处理网格状态变化信号失败: {str(e)}", exc_info=True)

    def _on_mesh_error(self, mesh_id: str, error_message: str):
        """网格错误信号处理"""
        try:
            mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
            mesh_name = mesh.name if mesh else mesh_id
            QMessageBox.warning(self, "网格操作错误", f"网格 '{mesh_name}' 发生错误:\n{error_message}")
            logger.warning(f"网格错误: {mesh_name} - {error_message}")
        except Exception as e:
            logger.error(f"处理网格错误信号失败: {str(e)}", exc_info=True)

    def _on_current_mesh_changed(self, mesh_id: str):
        """当前网格变更信号处理"""
        try:
            self._refresh_mesh_preview()
            self._update_mesh_statistics()
            self.show_status_message(f"当前网格已切换")
            logger.debug(f"当前网格变更: {mesh_id}")
        except Exception as e:
            logger.error(f"处理当前网格变更信号失败: {str(e)}", exc_info=True)

    def _on_batch_import_completed(self, imported_mesh_ids: list):
        """批量导入完成信号处理"""
        try:
            # 强制刷新所有UI组件
            self._refresh_all_ui()

            # 更新UI状态
            self.update_ui_state()

            # 自动调整表格列宽
            self.auto_resize_table_columns()

            # 如果有导入的网格，高亮显示第一个
            if imported_mesh_ids:
                first_mesh_id = imported_mesh_ids[0]
                self.highlight_mesh_in_table(first_mesh_id)

            logger.info(f"批量导入完成UI更新: {len(imported_mesh_ids)} 个网格")

        except Exception as e:
            logger.error(f"处理批量导入完成信号失败: {str(e)}", exc_info=True)

    # ==================== UI更新方法 ====================

    def _refresh_all_ui(self):
        """刷新所有UI组件"""
        try:
            self._refresh_mesh_table()
            self._refresh_mesh_preview()
            self._refresh_batch_list()
            self._refresh_comparison_table()
            self._refresh_comparison_list()
            self._update_mesh_statistics()
            logger.debug("所有UI组件刷新完成")
        except Exception as e:
            logger.error(f"刷新UI失败: {str(e)}", exc_info=True)

    def _refresh_mesh_table(self):
        """刷新网格参数表格"""
        try:
            table = self.ui.tableWidget_mesh_params
            table.setRowCount(0)

            meshes = self.mesh_manager.get_all_meshes()
            table.setRowCount(len(meshes))

            for row, mesh in enumerate(meshes):
                # 网格名称
                name_item = QTableWidgetItem(mesh.name)
                name_item.setData(Qt.ItemDataRole.UserRole, mesh.id)
                table.setItem(row, 0, name_item)

                # 网格尺寸
                size_item = QTableWidgetItem(f"{mesh.size:.2f}")
                table.setItem(row, 1, size_item)

                # 状态
                status_item = QTableWidgetItem(mesh.status.value)
                # 根据状态设置颜色
                if mesh.status == MeshStatus.COMPLETED:
                    status_item.setBackground(QColor(46, 204, 113, 50))  # 绿色
                elif mesh.status == MeshStatus.GENERATING or mesh.status == MeshStatus.CALCULATING:
                    status_item.setBackground(QColor(243, 156, 18, 50))  # 橙色
                elif mesh.status == MeshStatus.ERROR:
                    status_item.setBackground(QColor(231, 76, 60, 50))  # 红色
                table.setItem(row, 2, status_item)

                # 节点数
                nodes_item = QTableWidgetItem(str(mesh.statistics.node_count) if mesh.statistics.node_count > 0 else "--")
                table.setItem(row, 3, nodes_item)

                # 单元数
                elements_item = QTableWidgetItem(str(mesh.statistics.element_count) if mesh.statistics.element_count > 0 else "--")
                table.setItem(row, 4, elements_item)

                # 操作按钮
                self._add_table_action_buttons(table, row, mesh.id)

            logger.debug(f"网格表格刷新完成，共 {len(meshes)} 个网格")

        except Exception as e:
            logger.error(f"刷新网格表格失败: {str(e)}", exc_info=True)

    def _add_table_action_buttons(self, table, row: int, mesh_id: str):
        """为表格行添加操作按钮"""
        try:
            # 创建按钮容器
            button_widget = QWidget()
            button_widget.setMinimumHeight(40)  # 设置容器最小高度
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(8, 6, 8, 6)  # 增加边距
            button_layout.setSpacing(8)  # 增加按钮间距

            # 编辑按钮
            edit_btn = QPushButton("编辑")
            edit_btn.setMinimumSize(55, 30)  # 设置最小尺寸确保文字显示
            edit_btn.setMaximumSize(65, 35)  # 增大最大尺寸
            edit_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            edit_btn.clicked.connect(lambda: self._edit_mesh(mesh_id))
            button_layout.addWidget(edit_btn)

            # 删除按钮
            delete_btn = QPushButton("删除")
            delete_btn.setMinimumSize(55, 30)  # 设置最小尺寸确保文字显示
            delete_btn.setMaximumSize(65, 35)  # 增大最大尺寸
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            delete_btn.clicked.connect(lambda: self._delete_mesh(mesh_id))
            button_layout.addWidget(delete_btn)

            # 设置到表格
            table.setCellWidget(row, 5, button_widget)

        except Exception as e:
            logger.error(f"添加表格操作按钮失败: {str(e)}", exc_info=True)

    def _edit_mesh(self, mesh_id: str):
        """编辑网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                dialog = MeshParameterDialog(self, mesh_param)
                dialog.parameter_accepted.connect(lambda p: self._update_existing_mesh(mesh_id, p))
                dialog.exec()
        except Exception as e:
            logger.error(f"编辑网格失败: {str(e)}", exc_info=True)

    def _delete_mesh(self, mesh_id: str):
        """删除网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要删除网格 '{mesh_param.name}' 吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                    QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    mesh_name = mesh_param.name
                    success = self.mesh_manager.remove_mesh(mesh_id)
                    if success:
                        # 保存配置
                        self._save_configuration()

                        # 强制刷新所有UI组件（信号处理可能有延迟）
                        self._refresh_all_ui()

                        # 处理事件循环，确保UI更新
                        QApplication.processEvents()

                        # 显示成功消息
                        remaining_count = self.mesh_manager.mesh_count
                        QMessageBox.information(
                            self, "删除成功",
                            f"成功删除网格: {mesh_name}\n当前剩余 {remaining_count} 个网格"
                        )

                        logger.info(f"成功删除网格: {mesh_name}, 剩余网格数: {remaining_count}")
                    else:
                        QMessageBox.warning(self, "删除失败", f"删除网格 '{mesh_name}' 失败")

        except Exception as e:
            logger.error(f"删除网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"删除网格失败: {str(e)}")

    def _refresh_mesh_preview(self):
        """刷新网格预览下拉框"""
        try:
            combo = self.ui.comboBox_mesh_select
            old_count = combo.count()
            combo.clear()

            meshes = self.mesh_manager.get_all_meshes()

            # 只有在有网格时才添加默认项
            if meshes:
                combo.addItem("请选择网格", None)

                for mesh in meshes:
                    combo.addItem(f"{mesh.name} ({mesh.size}mm)", mesh.id)

                # 设置当前选中的网格
                current_mesh = self.mesh_manager.current_mesh
                if current_mesh:
                    for i in range(combo.count()):
                        if combo.itemData(i) == current_mesh.id:
                            combo.setCurrentIndex(i)
                            break

            new_count = combo.count()
            logger.debug(f"网格预览下拉框刷新完成: {old_count} -> {new_count} 项，网格数: {len(meshes)}")

        except Exception as e:
            logger.error(f"刷新网格预览下拉框失败: {str(e)}", exc_info=True)

    def _refresh_batch_list(self):
        """刷新批量操作列表"""
        try:
            list_widget = self.ui.listWidget_selected_meshes

            # 记录刷新前的状态
            old_count = list_widget.count()

            # 清空列表
            list_widget.clear()

            # 获取所有网格
            meshes = self.mesh_manager.get_all_meshes()

            # 添加网格项到列表
            for mesh in meshes:
                # 构建详细的显示文本
                display_text = f"{mesh.name} ({mesh.status.value})"

                # 如果有统计信息，添加到显示文本中
                if mesh.statistics.node_count > 0:
                    display_text += f" - 节点: {mesh.statistics.node_count}"
                if mesh.statistics.element_count > 0:
                    display_text += f", 单元: {mesh.statistics.element_count}"

                # 创建列表项
                item = QListWidgetItem(display_text)
                item.setData(Qt.ItemDataRole.UserRole, mesh.id)

                # 根据状态设置颜色
                if mesh.status == MeshStatus.COMPLETED:
                    item.setBackground(QColor(46, 204, 113, 50))  # 绿色
                elif mesh.status == MeshStatus.GENERATED:
                    item.setBackground(QColor(52, 152, 219, 50))  # 蓝色
                elif mesh.status in [MeshStatus.GENERATING, MeshStatus.CALCULATING]:
                    item.setBackground(QColor(243, 156, 18, 50))  # 橙色
                elif mesh.status == MeshStatus.ERROR:
                    item.setBackground(QColor(231, 76, 60, 50))  # 红色

                list_widget.addItem(item)

            # 强制更新列表显示
            list_widget.repaint()

            new_count = list_widget.count()
            logger.debug(f"批量操作列表刷新完成: {old_count} -> {new_count} 个网格")

        except Exception as e:
            logger.error(f"刷新批量操作列表失败: {str(e)}", exc_info=True)

    def _refresh_comparison_table(self):
        """刷新网格对比表格"""
        try:
            table = self.ui.tableWidget_mesh_comparison
            table.setRowCount(0)

            meshes = self.mesh_manager.get_all_meshes()
            table.setRowCount(len(meshes))

            for row, mesh in enumerate(meshes):
                # 网格名称
                table.setItem(row, 0, QTableWidgetItem(mesh.name))

                # 节点数
                nodes_text = str(mesh.statistics.node_count) if mesh.statistics.node_count > 0 else "--"
                table.setItem(row, 1, QTableWidgetItem(nodes_text))

                # 单元数
                elements_text = str(mesh.statistics.element_count) if mesh.statistics.element_count > 0 else "--"
                table.setItem(row, 2, QTableWidgetItem(elements_text))

                # 质量
                quality_text = f"{mesh.statistics.avg_quality:.3f}" if mesh.statistics.avg_quality > 0 else "--"
                table.setItem(row, 3, QTableWidgetItem(quality_text))

            logger.debug(f"网格对比表格刷新完成，共 {len(meshes)} 个网格")

        except Exception as e:
            logger.error(f"刷新网格对比表格失败: {str(e)}", exc_info=True)

    def _refresh_comparison_list(self):
        """刷新结果对比列表"""
        try:
            list_widget = self.ui.listWidget_comparison_meshes
            list_widget.clear()

            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            for mesh in completed_meshes:
                item = QListWidgetItem(f"{mesh.name} (已完成)")
                item.setData(Qt.ItemDataRole.UserRole, mesh.id)
                list_widget.addItem(item)

            logger.debug(f"结果对比列表刷新完成，共 {len(completed_meshes)} 个已完成网格")

        except Exception as e:
            logger.error(f"刷新结果对比列表失败: {str(e)}", exc_info=True)

    def _update_mesh_preview(self):
        """更新网格预览"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                # 这里应该更新网格预览图
                # 目前只是更新预览标签的文本
                preview_text = f"当前预览: {current_mesh.name}\n网格尺寸: {current_mesh.size}mm\n单元类型: {current_mesh.element_type.value}"
                self.ui.label_mesh_preview.setText(preview_text)
                logger.debug(f"网格预览更新: {current_mesh.name}")
            else:
                self.ui.label_mesh_preview.setText("网格预览区域\n(将集成matplotlib画布)")
        except Exception as e:
            logger.error(f"更新网格预览失败: {str(e)}", exc_info=True)

    def _update_mesh_statistics(self):
        """更新网格统计信息"""
        try:
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                # 更新统计信息显示
                self.ui.label_nodes_value.setText(str(current_mesh.statistics.node_count) if current_mesh.statistics.node_count > 0 else "--")
                self.ui.label_elements_value.setText(str(current_mesh.statistics.element_count) if current_mesh.statistics.element_count > 0 else "--")
                self.ui.label_quality_value.setText(f"{current_mesh.statistics.avg_quality:.3f}" if current_mesh.statistics.avg_quality > 0 else "--")
                logger.debug(f"网格统计信息更新: {current_mesh.name}")
            else:
                self.ui.label_nodes_value.setText("--")
                self.ui.label_elements_value.setText("--")
                self.ui.label_quality_value.setText("--")
        except Exception as e:
            logger.error(f"更新网格统计信息失败: {str(e)}", exc_info=True)

    def _save_configuration(self):
        """保存配置到配置管理器"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 保存网格参数
            mesh_data = self.mesh_manager.to_dict()
            config_manager.set_mesh_parameters(mesh_data.get("mesh_parameters", {}))
            config_manager.set_current_mesh_id(mesh_data.get("current_mesh_id"))

            # 保存配置文件
            config_manager.save_config()

            logger.debug("配置保存完成")

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}", exc_info=True)

    def _refresh_all_ui(self):
        """刷新所有UI组件"""
        try:
            # 刷新主要数据显示组件
            self._refresh_mesh_table()
            self._refresh_mesh_preview()
            self._refresh_batch_list()
            self._refresh_comparison_table()
            self._refresh_comparison_list()

            # 更新统计信息
            self._update_mesh_statistics()

            # 更新UI状态
            self.update_ui_state()

            # 自动调整表格列宽
            self.auto_resize_table_columns()

            logger.debug("所有UI组件刷新完成")

        except Exception as e:
            logger.error(f"刷新所有UI组件失败: {str(e)}", exc_info=True)

    def highlight_mesh_in_table(self, mesh_id: str):
        """在表格中高亮显示指定网格"""
        try:
            table = self.ui.tableWidget_mesh_params
            for row in range(table.rowCount()):
                item = table.item(row, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) == mesh_id:
                    table.selectRow(row)
                    table.scrollToItem(item)
                    break
        except Exception as e:
            logger.error(f"高亮网格失败: {str(e)}", exc_info=True)

    def auto_resize_table_columns(self):
        """自动调整表格列宽"""
        try:
            table = self.ui.tableWidget_mesh_params

            # 对可调整的列进行内容自适应
            for col in [1, 2, 3, 4]:  # 尺寸、状态、节点数、单元数列
                table.resizeColumnToContents(col)

            # 确保名称列有足够宽度
            if table.columnCount() > 0:
                current_width = table.columnWidth(0)
                table.setColumnWidth(100, max(300, current_width))

            # 确保操作列有足够宽度显示按钮
            if table.columnCount() > 5:
                table.setColumnWidth(5, 200)

            logger.debug("表格列宽自动调整完成")

        except Exception as e:
            logger.error(f"自动调整表格列宽失败: {str(e)}", exc_info=True)

    # ==================== 增强功能方法 ====================

    def get_selected_mesh_ids(self) -> List[str]:
        """获取当前选中的网格ID列表

        Returns:
            List[str]: 选中的网格ID列表
        """
        try:
            selected_ids = []
            selected_items = self.ui.tableWidget_mesh_params.selectedItems()

            # 获取选中行的网格ID
            selected_rows = set()
            for item in selected_items:
                selected_rows.add(item.row())

            for row in selected_rows:
                mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if mesh_id:
                    selected_ids.append(mesh_id)

            return selected_ids
        except Exception as e:
            logger.error(f"获取选中网格ID失败: {str(e)}", exc_info=True)
            return []

    def select_mesh_by_id(self, mesh_id: str) -> bool:
        """根据ID选中指定网格

        Args:
            mesh_id: 网格ID

        Returns:
            bool: 选中成功返回True
        """
        try:
            table = self.ui.tableWidget_mesh_params
            for row in range(table.rowCount()):
                item = table.item(row, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) == mesh_id:
                    table.selectRow(row)
                    return True
            return False
        except Exception as e:
            logger.error(f"选中网格失败: {str(e)}", exc_info=True)
            return False

    def get_mesh_count_by_status(self, status: MeshStatus) -> int:
        """获取指定状态的网格数量

        Args:
            status: 网格状态

        Returns:
            int: 网格数量
        """
        try:
            return len(self.mesh_manager.get_meshes_by_status(status))
        except Exception as e:
            logger.error(f"获取网格数量失败: {str(e)}", exc_info=True)
            return 0

    def validate_mesh_operations(self) -> bool:
        """验证是否可以进行网格操作

        Returns:
            bool: 可以操作返回True
        """
        try:
            if self.mesh_manager.mesh_count == 0:
                QMessageBox.information(self, "提示", "请先添加网格参数")
                return False
            return True
        except Exception as e:
            logger.error(f"验证网格操作失败: {str(e)}", exc_info=True)
            return False

    def show_mesh_summary(self):
        """显示网格摘要信息"""
        try:
            total_count = self.mesh_manager.mesh_count
            if total_count == 0:
                QMessageBox.information(self, "网格摘要", "当前没有网格参数")
                return

            # 统计各状态的网格数量
            status_counts = {}
            for status in MeshStatus:
                count = self.get_mesh_count_by_status(status)
                if count > 0:
                    status_counts[status.value] = count

            # 构建摘要信息
            summary_text = f"网格参数总数: {total_count}\n\n状态分布:\n"
            for status, count in status_counts.items():
                summary_text += f"  {status}: {count}个\n"

            # 显示当前选中的网格
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                summary_text += f"\n当前选中: {current_mesh.name}"

            QMessageBox.information(self, "网格摘要", summary_text)

        except Exception as e:
            logger.error(f"显示网格摘要失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"显示网格摘要失败: {str(e)}")

    def clear_all_meshes(self):
        """清空所有网格参数"""
        try:
            if self.mesh_manager.mesh_count == 0:
                QMessageBox.information(self, "提示", "当前没有网格参数需要清空")
                return

            reply = QMessageBox.question(
                self, "确认清空",
                f"确定要清空所有 {self.mesh_manager.mesh_count} 个网格参数吗？\n此操作不可撤销！",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.mesh_manager.clear_all_meshes()
                self._save_configuration()
                QMessageBox.information(self, "成功", "所有网格参数已清空")
                logger.info("用户清空了所有网格参数")

        except Exception as e:
            logger.error(f"清空网格参数失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"清空网格参数失败: {str(e)}")

    def duplicate_selected_mesh(self):
        """复制选中的网格参数"""
        try:
            selected_ids = self.get_selected_mesh_ids()
            if not selected_ids:
                QMessageBox.information(self, "提示", "请先选择要复制的网格")
                return

            for mesh_id in selected_ids:
                mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh_param:
                    cloned_mesh = mesh_param.clone()
                    success = self.mesh_manager.add_mesh(cloned_mesh)
                    if success:
                        logger.info(f"成功复制网格: {mesh_param.name} -> {cloned_mesh.name}")
                    else:
                        QMessageBox.warning(self, "复制失败", f"复制网格 '{mesh_param.name}' 失败")

            self._save_configuration()
            QMessageBox.information(self, "成功", f"成功复制 {len(selected_ids)} 个网格参数")

        except Exception as e:
            logger.error(f"复制网格参数失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"复制网格参数失败: {str(e)}")

    def export_selected_meshes(self):
        """导出选中的网格参数"""
        try:
            selected_ids = self.get_selected_mesh_ids()
            if not selected_ids:
                QMessageBox.information(self, "提示", "请先选择要导出的网格")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出选中网格", f"selected_meshes_{len(selected_ids)}.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 构建导出数据
                export_data = {
                    "mesh_parameters": {},
                    "export_info": {
                        "count": len(selected_ids),
                        "timestamp": datetime.now().isoformat(),
                        "source": "多网格管理系统"
                    }
                }

                for mesh_id in selected_ids:
                    mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                    if mesh_param:
                        export_data["mesh_parameters"][mesh_id] = mesh_param.to_dict()

                # 保存文件
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"成功导出 {len(selected_ids)} 个网格参数到:\n{file_path}")
                logger.info(f"成功导出选中网格到: {file_path}")

        except Exception as e:
            logger.error(f"导出选中网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出选中网格失败: {str(e)}")

    # ==================== 右键菜单功能 ====================

    def _show_table_context_menu(self, position):
        """显示表格右键菜单"""
        try:
            table = self.ui.tableWidget_mesh_params
            item = table.itemAt(position)

            # 创建右键菜单
            context_menu = QMenu(self)

            if item:  # 点击在有效项目上
                # 获取选中的网格数量
                selected_count = len(self.get_selected_mesh_ids())

                if selected_count == 1:
                    # 单个网格的操作
                    edit_action = context_menu.addAction("编辑网格参数")
                    edit_action.triggered.connect(lambda: self._edit_mesh_from_menu(item.row()))

                    duplicate_action = context_menu.addAction("复制网格")
                    duplicate_action.triggered.connect(self.duplicate_selected_mesh)

                    context_menu.addSeparator()

                    delete_action = context_menu.addAction("删除网格")
                    delete_action.triggered.connect(lambda: self._delete_mesh_from_menu(item.row()))
                    delete_action.setStyleSheet("QAction { color: #e74c3c; }")

                elif selected_count > 1:
                    # 多个网格的操作
                    duplicate_action = context_menu.addAction(f"复制选中的 {selected_count} 个网格")
                    duplicate_action.triggered.connect(self.duplicate_selected_mesh)

                    export_action = context_menu.addAction(f"导出选中的 {selected_count} 个网格")
                    export_action.triggered.connect(self.export_selected_meshes)

                    context_menu.addSeparator()

                    delete_action = context_menu.addAction(f"删除选中的 {selected_count} 个网格")
                    delete_action.triggered.connect(self._delete_selected_meshes)
                    delete_action.setStyleSheet("QAction { color: #e74c3c; }")

                context_menu.addSeparator()

            # 通用操作
            add_action = context_menu.addAction("添加新网格")
            add_action.triggered.connect(self._on_add_mesh)

            if self.mesh_manager.mesh_count > 0:
                context_menu.addSeparator()

                summary_action = context_menu.addAction("显示网格摘要")
                summary_action.triggered.connect(self.show_mesh_summary)

                refresh_action = context_menu.addAction("刷新表格")
                refresh_action.triggered.connect(self._refresh_mesh_table)

                context_menu.addSeparator()

                clear_action = context_menu.addAction("清空所有网格")
                clear_action.triggered.connect(self.clear_all_meshes)
                clear_action.setStyleSheet("QAction { color: #e74c3c; }")

            # 显示菜单
            context_menu.exec(table.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示右键菜单失败: {str(e)}", exc_info=True)

    def _edit_mesh_from_menu(self, row: int):
        """从右键菜单编辑网格"""
        try:
            mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if mesh_id:
                self._edit_mesh(mesh_id)
        except Exception as e:
            logger.error(f"从菜单编辑网格失败: {str(e)}", exc_info=True)

    def _delete_mesh_from_menu(self, row: int):
        """从右键菜单删除网格"""
        try:
            mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if mesh_id:
                self._delete_mesh(mesh_id)
        except Exception as e:
            logger.error(f"从菜单删除网格失败: {str(e)}", exc_info=True)

    def _delete_selected_meshes(self):
        """删除选中的多个网格"""
        try:
            selected_ids = self.get_selected_mesh_ids()
            if not selected_ids:
                return

            # 获取网格名称用于确认对话框
            mesh_names = []
            for mesh_id in selected_ids:
                mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh:
                    mesh_names.append(mesh.name)

            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除以下 {len(selected_ids)} 个网格吗？\n\n" + "\n".join(mesh_names),
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                deleted_count = 0
                for mesh_id in selected_ids:
                    if self.mesh_manager.remove_mesh(mesh_id):
                        deleted_count += 1

                # 保存配置
                self._save_configuration()

                # 强制刷新所有UI组件
                self._refresh_all_ui()

                # 处理事件循环，确保UI更新
                QApplication.processEvents()

                # 显示结果
                remaining_count = self.mesh_manager.mesh_count
                QMessageBox.information(
                    self, "批量删除完成",
                    f"成功删除 {deleted_count} 个网格\n当前剩余 {remaining_count} 个网格"
                )

                logger.info(f"批量删除了 {deleted_count} 个网格，剩余 {remaining_count} 个")

        except Exception as e:
            logger.error(f"批量删除网格失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"批量删除网格失败: {str(e)}")

    # ==================== 键盘快捷键支持 ====================

    def keyPressEvent(self, event):
        """处理键盘事件"""
        try:
            # Ctrl+A: 全选
            if event.key() == Qt.Key.Key_A and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.ui.tableWidget_mesh_params.selectAll()
                event.accept()
                return

            # Delete: 删除选中项
            if event.key() == Qt.Key.Key_Delete:
                selected_ids = self.get_selected_mesh_ids()
                if selected_ids:
                    if len(selected_ids) == 1:
                        self._delete_mesh(selected_ids[0])
                    else:
                        self._delete_selected_meshes()
                event.accept()
                return

            # F5: 刷新
            if event.key() == Qt.Key.Key_F5:
                self._refresh_all_ui()
                event.accept()
                return

            # Ctrl+D: 复制
            if event.key() == Qt.Key.Key_D and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self.duplicate_selected_mesh()
                event.accept()
                return

            # Ctrl+N: 新建
            if event.key() == Qt.Key.Key_N and event.modifiers() == Qt.KeyboardModifier.ControlModifier:
                self._on_add_mesh()
                event.accept()
                return

            # 调用父类处理
            super().keyPressEvent(event)

        except Exception as e:
            logger.error(f"处理键盘事件失败: {str(e)}", exc_info=True)
            super().keyPressEvent(event)

    # ==================== 状态管理和UI增强 ====================

    def update_ui_state(self):
        """更新UI状态"""
        try:
            mesh_count = self.mesh_manager.mesh_count
            has_meshes = mesh_count > 0
            has_selection = len(self.get_selected_mesh_ids()) > 0

            # 更新按钮状态
            self.ui.btn_export_mesh.setEnabled(has_meshes)

            # 更新标签页标题显示网格数量
            self.ui.tabWidget_main.setTabText(0, f"网格管理 ({mesh_count})")

            # 更新生成标签页的网格列表
            generated_count = self.get_mesh_count_by_status(MeshStatus.GENERATED)
            self.ui.tabWidget_main.setTabText(1, f"网格生成 ({generated_count})")

            # 更新模态分析标签页
            completed_count = self.get_mesh_count_by_status(MeshStatus.COMPLETED)
            self.ui.tabWidget_main.setTabText(2, f"模态分析 ({completed_count})")

            # 更新结果对比标签页
            self.ui.tabWidget_main.setTabText(3, f"结果对比 ({completed_count})")

            # 更新批量操作按钮状态
            self.ui.btn_batch_generate.setEnabled(has_meshes)
            self.ui.btn_single_modal.setEnabled(has_selection)
            self.ui.btn_start_modal_calculation.setEnabled(generated_count > 0)
            self.ui.btn_export_results.setEnabled(completed_count > 0)

            logger.debug(f"UI状态更新完成: 网格数={mesh_count}, 选中={has_selection}")

        except Exception as e:
            logger.error(f"更新UI状态失败: {str(e)}", exc_info=True)

    def show_status_message(self, message: str, timeout: int = 3000):
        """在状态栏显示消息

        Args:
            message: 消息内容
            timeout: 显示时间(毫秒)
        """
        try:
            # 如果有状态栏，显示消息
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage(message, timeout)
            logger.info(f"状态消息: {message}")
        except Exception as e:
            logger.error(f"显示状态消息失败: {str(e)}", exc_info=True)

    def highlight_mesh_in_table(self, mesh_id: str):
        """在表格中高亮显示指定网格

        Args:
            mesh_id: 网格ID
        """
        try:
            table = self.ui.tableWidget_mesh_params
            for row in range(table.rowCount()):
                item = table.item(row, 0)
                if item and item.data(Qt.ItemDataRole.UserRole) == mesh_id:
                    # 选中并滚动到该行
                    table.selectRow(row)
                    table.scrollToItem(item)

                    # 设置高亮样式
                    for col in range(table.columnCount()):
                        cell_item = table.item(row, col)
                        if cell_item:
                            cell_item.setBackground(QColor(52, 152, 219, 100))  # 蓝色高亮

                    logger.debug(f"高亮显示网格: {mesh_id}")
                    return True
            return False
        except Exception as e:
            logger.error(f"高亮显示网格失败: {str(e)}", exc_info=True)
            return False

    def clear_table_highlights(self):
        """清除表格中的所有高亮"""
        try:
            table = self.ui.tableWidget_mesh_params
            for row in range(table.rowCount()):
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(QColor())  # 清除背景色
            logger.debug("清除表格高亮完成")
        except Exception as e:
            logger.error(f"清除表格高亮失败: {str(e)}", exc_info=True)



    def get_mesh_statistics_summary(self) -> dict:
        """获取网格统计摘要

        Returns:
            dict: 统计摘要信息
        """
        try:
            summary = {
                "total_count": self.mesh_manager.mesh_count,
                "status_distribution": {},
                "size_range": {"min": None, "max": None, "avg": None},
                "total_nodes": 0,
                "total_elements": 0
            }

            if summary["total_count"] == 0:
                return summary

            # 统计状态分布
            for status in MeshStatus:
                count = self.get_mesh_count_by_status(status)
                if count > 0:
                    summary["status_distribution"][status.value] = count

            # 统计尺寸范围和节点/单元总数
            sizes = []
            for mesh in self.mesh_manager.get_all_meshes():
                sizes.append(mesh.size)
                summary["total_nodes"] += mesh.statistics.node_count
                summary["total_elements"] += mesh.statistics.element_count

            if sizes:
                summary["size_range"]["min"] = min(sizes)
                summary["size_range"]["max"] = max(sizes)
                summary["size_range"]["avg"] = sum(sizes) / len(sizes)

            return summary

        except Exception as e:
            logger.error(f"获取网格统计摘要失败: {str(e)}", exc_info=True)
            return {"total_count": 0, "status_distribution": {}, "size_range": {}, "total_nodes": 0, "total_elements": 0}

    def export_mesh_statistics(self):
        """导出网格统计信息"""
        try:
            if self.mesh_manager.mesh_count == 0:
                QMessageBox.information(self, "提示", "没有网格数据可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出网格统计", "mesh_statistics.json",
                "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 获取详细统计信息
                statistics = {
                    "summary": self.get_mesh_statistics_summary(),
                    "meshes": [],
                    "export_info": {
                        "timestamp": datetime.now().isoformat(),
                        "source": "多网格管理系统",
                        "version": "1.0.0"
                    }
                }

                # 添加每个网格的详细信息
                for mesh in self.mesh_manager.get_all_meshes():
                    mesh_info = {
                        "name": mesh.name,
                        "size": mesh.size,
                        "element_type": mesh.element_type.value,
                        "status": mesh.status.value,
                        "statistics": {
                            "node_count": mesh.statistics.node_count,
                            "element_count": mesh.statistics.element_count,
                            "avg_quality": mesh.statistics.avg_quality,
                            "generation_time": mesh.statistics.generation_time
                        },
                        "modal_results": {
                            "frequencies": mesh.modal_results.frequencies,
                            "calculation_time": mesh.modal_results.calculation_time
                        }
                    }
                    statistics["meshes"].append(mesh_info)

                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(statistics, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"网格统计信息已导出到:\n{file_path}")
                logger.info(f"成功导出网格统计信息到: {file_path}")

        except Exception as e:
            logger.error(f"导出网格统计信息失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"导出网格统计信息失败: {str(e)}")

    def validate_mesh_for_generation(self, mesh_id: str) -> bool:
        """验证网格是否可以进行生成操作

        Args:
            mesh_id: 网格ID

        Returns:
            bool: 可以生成返回True
        """
        try:
            mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
            if not mesh:
                return False

            # 检查网格状态
            if mesh.status in [MeshStatus.GENERATING, MeshStatus.CALCULATING]:
                QMessageBox.warning(self, "操作受限", f"网格 '{mesh.name}' 正在处理中，请等待完成")
                return False

            # 验证网格参数
            errors = mesh.validate()
            if errors:
                QMessageBox.warning(self, "参数错误", f"网格 '{mesh.name}' 参数验证失败:\n" + "\n".join(errors))
                return False

            return True

        except Exception as e:
            logger.error(f"验证网格生成条件失败: {str(e)}", exc_info=True)
            return False

    def _on_tab_changed(self, index: int):
        """标签页切换处理"""
        try:
            tab_names = ["网格管理", "网格生成", "模态分析", "结果对比"]
            if 0 <= index < len(tab_names):
                logger.debug(f"切换到标签页: {tab_names[index]}")

                # 根据标签页更新相关UI
                if index == 0:  # 网格管理
                    self._refresh_mesh_table()
                    self.update_ui_state()
                elif index == 1:  # 网格生成
                    self._refresh_batch_list()
                    self._update_generation_ui()
                elif index == 2:  # 模态分析
                    self._update_modal_analysis_ui()
                elif index == 3:  # 结果对比
                    self._refresh_comparison_list()
                    self._update_comparison_ui()

                self.show_status_message(f"切换到 {tab_names[index]} 标签页")

        except Exception as e:
            logger.error(f"处理标签页切换失败: {str(e)}", exc_info=True)

    def _update_generation_ui(self):
        """更新网格生成界面"""
        try:
            # 更新进度条状态
            self.ui.progressBar_generation.setValue(0)
            self.ui.label_progress_text.setText("等待开始...")

            # 更新按钮状态
            has_meshes = self.mesh_manager.mesh_count > 0
            self.ui.btn_batch_generate.setEnabled(has_meshes)
            self.ui.btn_stop_generation.setEnabled(False)

            logger.debug("网格生成界面更新完成")
        except Exception as e:
            logger.error(f"更新网格生成界面失败: {str(e)}", exc_info=True)

    def _update_modal_analysis_ui(self):
        """更新模态分析界面"""
        try:
            # 更新模态参数
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                # 根据网格尺寸建议模态阶数 (0-200范围)
                suggested_modes = max(0, min(200, int(50 / current_mesh.size)))
                self.ui.spinBox_modal_count.setValue(suggested_modes)

                # 建议频率范围 (最大100kHz)
                max_freq = min(100000, 1000 / current_mesh.size)
                if self.ui.checkBox_limit_freq.isChecked():
                    self.ui.doubleSpinBox_freq_max.setValue(max_freq)

            # 更新频率限制控件状态
            freq_limited = self.ui.checkBox_limit_freq.isChecked()
            self.ui.doubleSpinBox_freq_min.setEnabled(freq_limited)
            self.ui.doubleSpinBox_freq_max.setEnabled(freq_limited)
            self.ui.label_freq_range.setEnabled(freq_limited)

            if not freq_limited:
                self.ui.label_freq_range.setStyleSheet("color: #bdc3c7;")
            else:
                self.ui.label_freq_range.setStyleSheet("")

            # 更新按钮状态
            has_generated = self.get_mesh_count_by_status(MeshStatus.GENERATED) > 0
            self.ui.btn_start_modal_calculation.setEnabled(has_generated)

            has_selection = self.mesh_manager.current_mesh is not None
            self.ui.btn_single_modal.setEnabled(has_selection and
                                               self.mesh_manager.current_mesh.status == MeshStatus.GENERATED)

            logger.debug("模态分析界面更新完成")
        except Exception as e:
            logger.error(f"更新模态分析界面失败: {str(e)}", exc_info=True)

    def _update_comparison_ui(self):
        """更新结果对比界面"""
        try:
            # 更新显示选项状态
            has_completed = self.get_mesh_count_by_status(MeshStatus.COMPLETED) > 0
            self.ui.btn_export_results.setEnabled(has_completed)

            # 更新显示选项
            self.ui.checkBox_show_frequency.setEnabled(has_completed)
            self.ui.checkBox_show_convergence.setEnabled(has_completed)
            self.ui.checkBox_show_grid.setEnabled(has_completed)

            if not has_completed:
                # 显示提示信息
                self.ui.label_comparison_chart.setText(
                    "暂无已完成的模态计算结果\n请先完成网格生成和模态分析"
                )
            else:
                self.ui.label_comparison_chart.setText(
                    "多网格结果对比图表区域\n(将集成matplotlib画布)"
                )

            logger.debug("结果对比界面更新完成")
        except Exception as e:
            logger.error(f"更新结果对比界面失败: {str(e)}", exc_info=True)

    def show_help_dialog(self):
        """显示帮助对话框"""
        try:
            help_text = """
多网格管理系统使用帮助

快捷键：
• Ctrl+N: 添加新网格
• Ctrl+D: 复制选中网格
• Delete: 删除选中网格
• Ctrl+A: 全选网格
• F5: 刷新界面

操作流程：
1. 网格管理: 添加、编辑、删除网格参数
2. 网格生成: 批量生成网格
3. 模态分析: 进行模态计算
4. 结果对比: 查看网格无关性验证结果

右键菜单：
在网格表格中右键可以访问更多操作选项

注意事项：
• 网格尺寸建议在0.1-1000mm之间
• 建议按照从粗到细的顺序创建网格
• 模态分析需要先完成网格生成
            """

            QMessageBox.information(self, "使用帮助", help_text.strip())

        except Exception as e:
            logger.error(f"显示帮助对话框失败: {str(e)}", exc_info=True)