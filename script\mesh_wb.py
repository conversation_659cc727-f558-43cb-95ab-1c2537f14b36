
# encoding: utf-8
# 2024 R2
SetScriptVersion(Version="24.2.133")
Open(FilePath=r"D:\data\all-XM\autoworkbench\damox.wbpj")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("D:\\data\\all-XM\\autoworkbench\\qtauto\\qt-cs\\qtproject\\script\\meshpy_copy.py")')
model1.Exit()
Save(Overwrite=True)
