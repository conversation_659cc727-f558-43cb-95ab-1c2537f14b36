"""
日志系统模块

此模块配置应用程序的日志系统，实现:
1. 分级日志记录
2. 日志文件自动轮转
3. 统一的日志格式
4. 控制台和文件双重输出

作者: [作者名]
日期: [日期]
"""

import logging
import sys
from logging.handlers import TimedRotatingFileHandler
import os
from pathlib import Path

LOG_DIR = "logs"
LOG_FILENAME = "app.log"

def setup_logging():
    """配置全局日志系统"""
    # 确保日志目录存在
    log_dir_path = Path(LOG_DIR)
    log_dir_path.mkdir(exist_ok=True)

    log_file_path = log_dir_path / LOG_FILENAME
    
    # 1. 创建格式化器
    log_format = "%(asctime)s - %(levelname)s - [%(name)s:%(lineno)d] - %(message)s"
    formatter = logging.Formatter(log_format)
    
    # 2. 获取根 logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO) # 设置根日志级别
    
    # 防止重复添加 handler
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # 3. 创建并配置控制台 Handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 4. 创建并配置文件 Handler (按天轮转，保留7天)
    file_handler = TimedRotatingFileHandler(
        str(log_file_path),
        when="D",
        interval=1,
        backupCount=7,
        encoding='utf-8'
    )
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    logging.info("日志系统配置完成。")
    
    return root_logger 