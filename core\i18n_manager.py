"""
国际化管理器模块

此模块提供完整的国际化支持，包括：
1. QTranslator 集成和管理
2. 动态语言切换
3. 翻译文件加载和管理
4. 语言设置持久化
5. RTL语言支持

作者: [作者名]
日期: [日期]
"""

import os
import logging
from typing import Dict, List, Optional, Callable
from PySide6.QtCore import QObject, Signal, QTranslator, QCoreApplication, QLocale, QLibraryInfo
from PySide6.QtWidgets import QApplication
from .config_manager import ConfigManager

# 导入简单QM读取器
try:
    import sys
    import os
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'tools'))
    from simple_qm_compiler import SimpleQMReader
    HAS_SIMPLE_QM_READER = True
except ImportError:
    HAS_SIMPLE_QM_READER = False

logger = logging.getLogger(__name__)


class I18nManager(QObject):
    """国际化管理器
    
    负责管理应用程序的多语言支持，包括翻译文件加载、
    语言切换和界面更新。
    """
    
    # 信号定义
    language_changed = Signal(str)  # 语言改变信号
    translation_loaded = Signal(str, bool)  # 翻译加载信号 (language, success)
    
    # 支持的语言配置
    SUPPORTED_LANGUAGES = {
        'zh_CN': {
            'name': '简体中文',
            'native_name': '简体中文',
            'locale': 'zh_CN',
            'direction': 'ltr',
            'flag': '🇨🇳'
        },
        'en_US': {
            'name': 'English',
            'native_name': 'English',
            'locale': 'en_US',
            'direction': 'ltr',
            'flag': '🇺🇸'
        },
        'ja_JP': {
            'name': 'Japanese',
            'native_name': '日本語',
            'locale': 'ja_JP',
            'direction': 'ltr',
            'flag': '🇯🇵'
        }
    }
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        super().__init__()
        self._config_manager = config_manager
        self._current_language = 'zh_CN'  # 默认语言
        self._translators: Dict[str, QTranslator] = {}
        self._qt_translator: Optional[QTranslator] = None
        self._translation_dir = self._get_translation_directory()
        self._language_change_callbacks: List[Callable[[str], None]] = []
        
        # 确保翻译目录存在
        self._ensure_translation_directory()
        
        # 加载保存的语言设置
        self._load_saved_language()
        
        logger.info(f"I18nManager 初始化完成，当前语言: {self._current_language}")
    
    def _get_translation_directory(self) -> str:
        """获取翻译文件目录
        
        Returns:
            str: 翻译文件目录路径
        """
        # 获取应用程序根目录
        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        return os.path.join(app_dir, 'translations')
    
    def _ensure_translation_directory(self) -> None:
        """确保翻译目录存在"""
        try:
            os.makedirs(self._translation_dir, exist_ok=True)
            logger.debug(f"翻译目录已确保存在: {self._translation_dir}")
        except Exception as e:
            logger.error(f"创建翻译目录失败: {e}")
    
    def _load_saved_language(self) -> None:
        """加载保存的语言设置"""
        try:
            if self._config_manager:
                saved_language = self._config_manager.get('ui.language', 'zh_CN')
                if saved_language in self.SUPPORTED_LANGUAGES:
                    self._current_language = saved_language
                    logger.info(f"加载保存的语言设置: {saved_language}")
                else:
                    logger.warning(f"不支持的语言设置: {saved_language}，使用默认语言")
        except Exception as e:
            logger.error(f"加载语言设置失败: {e}")
    
    def get_current_language(self) -> str:
        """获取当前语言
        
        Returns:
            str: 当前语言代码
        """
        return self._current_language
    
    def get_supported_languages(self) -> Dict[str, Dict[str, str]]:
        """获取支持的语言列表
        
        Returns:
            Dict[str, Dict[str, str]]: 支持的语言信息
        """
        return self.SUPPORTED_LANGUAGES.copy()
    
    def get_language_info(self, language_code: str) -> Optional[Dict[str, str]]:
        """获取指定语言的信息
        
        Args:
            language_code: 语言代码
            
        Returns:
            Optional[Dict[str, str]]: 语言信息，如果不存在则返回None
        """
        return self.SUPPORTED_LANGUAGES.get(language_code)
    
    def is_rtl_language(self, language_code: Optional[str] = None) -> bool:
        """检查是否为从右到左的语言
        
        Args:
            language_code: 语言代码，如果为None则使用当前语言
            
        Returns:
            bool: 是否为RTL语言
        """
        lang = language_code or self._current_language
        lang_info = self.SUPPORTED_LANGUAGES.get(lang, {})
        return lang_info.get('direction', 'ltr') == 'rtl'
    
    def load_translation(self, language_code: str) -> bool:
        """加载指定语言的翻译文件
        
        Args:
            language_code: 语言代码
            
        Returns:
            bool: 是否加载成功
        """
        try:
            if language_code not in self.SUPPORTED_LANGUAGES:
                logger.error(f"不支持的语言: {language_code}")
                return False
            
            app = QApplication.instance()
            if not app:
                logger.error("没有QApplication实例")
                return False
            
            # 移除旧的翻译器
            self._remove_current_translators()
            
            # 加载应用程序翻译
            app_translator = QTranslator()
            app_translation_file = os.path.join(self._translation_dir, f"app_{language_code}.qm")
            
            if os.path.exists(app_translation_file):
                if app_translator.load(app_translation_file):
                    app.installTranslator(app_translator)
                    self._translators['app'] = app_translator
                    logger.info(f"应用程序翻译文件加载成功: {app_translation_file}")
                else:
                    logger.warning(f"应用程序翻译文件加载失败: {app_translation_file}")
            else:
                logger.warning(f"应用程序翻译文件不存在: {app_translation_file}")
            
            # 加载Qt系统翻译
            qt_translator = QTranslator()
            qt_translation_file = f"qt_{language_code}"
            
            # 尝试从Qt安装目录加载系统翻译
            qt_translations_path = QLibraryInfo.path(QLibraryInfo.TranslationsPath)
            if qt_translator.load(qt_translation_file, qt_translations_path):
                app.installTranslator(qt_translator)
                self._qt_translator = qt_translator
                logger.info(f"Qt系统翻译加载成功: {qt_translation_file}")
            else:
                logger.warning(f"Qt系统翻译加载失败: {qt_translation_file}")
            
            # 发射翻译加载信号
            self.translation_loaded.emit(language_code, True)
            return True
            
        except Exception as e:
            logger.error(f"加载翻译文件时发生错误: {e}")
            self.translation_loaded.emit(language_code, False)
            return False
    
    def _remove_current_translators(self) -> None:
        """移除当前的翻译器"""
        try:
            app = QApplication.instance()
            if not app:
                return
            
            # 移除应用程序翻译器
            for translator in self._translators.values():
                app.removeTranslator(translator)
            self._translators.clear()
            
            # 移除Qt系统翻译器
            if self._qt_translator:
                app.removeTranslator(self._qt_translator)
                self._qt_translator = None
                
            logger.debug("已移除当前翻译器")
            
        except Exception as e:
            logger.error(f"移除翻译器时发生错误: {e}")
    
    def change_language(self, language_code: str) -> bool:
        """切换语言
        
        Args:
            language_code: 目标语言代码
            
        Returns:
            bool: 是否切换成功
        """
        try:
            if language_code == self._current_language:
                logger.info(f"语言已经是 {language_code}，无需切换")
                return True
            
            if language_code not in self.SUPPORTED_LANGUAGES:
                logger.error(f"不支持的语言: {language_code}")
                return False
            
            # 加载新语言的翻译文件
            if self.load_translation(language_code):
                old_language = self._current_language
                self._current_language = language_code
                
                # 保存语言设置
                self._save_language_setting(language_code)
                
                # 设置应用程序的语言环境
                self._set_application_locale(language_code)
                
                # 调用语言变更回调
                self._call_language_change_callbacks(language_code)
                
                # 发射语言变更信号
                self.language_changed.emit(language_code)
                
                logger.info(f"语言切换成功: {old_language} -> {language_code}")
                return True
            else:
                logger.error(f"语言切换失败，无法加载翻译文件: {language_code}")
                return False
                
        except Exception as e:
            logger.error(f"切换语言时发生错误: {e}")
            return False
    
    def _save_language_setting(self, language_code: str) -> None:
        """保存语言设置
        
        Args:
            language_code: 语言代码
        """
        try:
            if self._config_manager:
                self._config_manager.set('ui.language', language_code)
                logger.debug(f"语言设置已保存: {language_code}")
        except Exception as e:
            logger.error(f"保存语言设置失败: {e}")
    
    def _set_application_locale(self, language_code: str) -> None:
        """设置应用程序的语言环境
        
        Args:
            language_code: 语言代码
        """
        try:
            locale = QLocale(language_code)
            QLocale.setDefault(locale)
            logger.debug(f"应用程序语言环境已设置: {language_code}")
        except Exception as e:
            logger.error(f"设置应用程序语言环境失败: {e}")
    
    def register_language_change_callback(self, callback: Callable[[str], None]) -> None:
        """注册语言变更回调函数
        
        Args:
            callback: 回调函数，接收新语言代码作为参数
        """
        if callback not in self._language_change_callbacks:
            self._language_change_callbacks.append(callback)
            logger.debug("语言变更回调函数已注册")
    
    def unregister_language_change_callback(self, callback: Callable[[str], None]) -> None:
        """注销语言变更回调函数
        
        Args:
            callback: 要注销的回调函数
        """
        if callback in self._language_change_callbacks:
            self._language_change_callbacks.remove(callback)
            logger.debug("语言变更回调函数已注销")
    
    def _call_language_change_callbacks(self, language_code: str) -> None:
        """调用所有语言变更回调函数
        
        Args:
            language_code: 新语言代码
        """
        for callback in self._language_change_callbacks:
            try:
                callback(language_code)
            except Exception as e:
                logger.error(f"调用语言变更回调函数时发生错误: {e}")
    
    def get_translation_file_path(self, language_code: str, file_type: str = 'app') -> str:
        """获取翻译文件路径
        
        Args:
            language_code: 语言代码
            file_type: 文件类型 ('app' 或 'qt')
            
        Returns:
            str: 翻译文件路径
        """
        if file_type == 'app':
            return os.path.join(self._translation_dir, f"app_{language_code}.qm")
        elif file_type == 'qt':
            return os.path.join(self._translation_dir, f"qt_{language_code}.qm")
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
    
    def initialize_default_language(self) -> bool:
        """初始化默认语言
        
        Returns:
            bool: 是否初始化成功
        """
        return self.load_translation(self._current_language)


# 全局国际化管理器实例
_global_i18n_manager: Optional[I18nManager] = None


def get_i18n_manager(config_manager: Optional[ConfigManager] = None) -> I18nManager:
    """获取全局国际化管理器实例
    
    Args:
        config_manager: 配置管理器实例
        
    Returns:
        I18nManager: 国际化管理器实例
    """
    global _global_i18n_manager
    if _global_i18n_manager is None:
        _global_i18n_manager = I18nManager(config_manager)
    return _global_i18n_manager


def tr(text: str, context: str = None) -> str:
    """翻译函数的便捷包装
    
    Args:
        text: 要翻译的文本
        context: 翻译上下文
        
    Returns:
        str: 翻译后的文本
    """
    if context:
        return QCoreApplication.translate(context, text)
    else:
        return QCoreApplication.translate("", text)
