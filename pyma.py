import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('QtAgg') # <-- 在import pyplot之前添加这一行！
import matplotlib.pyplot as plt
from scipy.fft import fft, fftfreq
import warnings

# --- 1. 数据加载和预处理 ---

def load_and_preprocess_data(filepath):
    """
    加载并预处理时域加速度数据。
    文件格式为制表符分隔，第一行为列名。
    单位从 mm/s^2 转换为 m/s^2。
    """
    try:
        # 使用pandas读取制表符分隔的文件，并跳过第一行（列名）
        # skiprows=1 如果第一行是规范的列名，可以去掉。这里假设第一行需要跳过。
        # header=0 表示第一行是列名
        # sep='\t' 表示分隔符是制表符
        # skipinitialspace=True 可以去除列名周围的空格
        df = pd.read_csv(
            filepath, 
            sep=r'\s+',  # 使用正则表达式匹配一个或多个空白字符（包括tab和空格）
            header=0,    # 第一行作为列名
            skiprows=[0] # 跳过文件的第一行描述性文字
        )
        
        # 为了更稳健地处理列名中的特殊字符和空格
        df.columns = [
            'Index', 'Time', 'Acc_X', 'Acc_Y', 'Acc_Z', 'Acc_Total'
        ]
        
        # 提取数据
        time = df['Time'].values
        # 将单位从 mm/s^2 转换为 m/s^2
        acc_x = df['Acc_X'].values / 1000.0
        acc_y = df['Acc_Y'].values / 1000.0
        acc_z = df['Acc_Z'].values / 1000.0
        
        print("数据加载和预处理成功。")
        return time, {'X': acc_x, 'Y': acc_y, 'Z': acc_z}
        
    except FileNotFoundError:
        print(f"错误：文件 '{filepath}' 未找到。")
        return None, None
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None, None

def load_motor_data(filepath):
    """
    加载电机数据文件。
    文件格式：Excel文件，包含时间(s)和Z方向加速度(m/s²)两列。

    Args:
        filepath: 电机数据文件路径

    Returns:
        tuple: (time_data, z_acceleration_data) 或 (None, None) 如果失败
    """
    try:
        # 尝试读取Excel文件
        if filepath.endswith('.xlsx') or filepath.endswith('.xls'):
            df = pd.read_excel(filepath)
        else:
            # 如果是文本文件，尝试用制表符分隔读取
            df = pd.read_csv(filepath, sep=r'\s+', header=0)

        # 检查列数，应该至少有2列
        if df.shape[1] < 2:
            print(f"错误：电机数据文件格式不正确，需要至少2列数据")
            return None, None

        # 获取前两列数据
        time_col = df.iloc[:, 0].values  # 第一列：时间
        z_acc_col = df.iloc[:, 1].values  # 第二列：Z方向加速度

        # 数据验证
        if len(time_col) == 0 or len(z_acc_col) == 0:
            print(f"错误：电机数据文件为空")
            return None, None

        print(f"电机数据加载成功，共 {len(time_col)} 个数据点")
        return time_col, z_acc_col

    except FileNotFoundError:
        print(f"错误：文件 '{filepath}' 未找到。")
        return None, None
    except Exception as e:
        print(f"读取电机数据文件时出错: {e}")
        return None, None

# --- 2. FFT变换函数 ---

def perform_fft(time_signal, acc_signal):
    """
    对时域信号执行FFT，返回频率和幅值谱。
    """
    N = len(time_signal)
    if N == 0:
        return np.array([]), np.array([])
        
    # 计算采样间隔
    dt = time_signal[1] - time_signal[0] if N > 1 else 1.0
    
    # 执行FFT
    # 对实数信号，FFT结果是共轭对称的，我们只需要前半部分
    yf = fft(acc_signal)
    xf = fftfreq(N, dt)[:N//2]
    
    # 计算幅值谱 (单边谱)
    # 幅值需要乘以 2/N，直流分量(第一个点)除以 N
    amp = 2.0/N * np.abs(yf[0:N//2])
    amp[0] = amp[0] / 2.0 # 处理直流分量

    return xf, amp

# --- 3. 1/3倍频程分析函数 ---

def analyze_third_octave(freq, amp, axis_name, frequency_range='extended'):
    """
    将频域信号转换为1/3倍频程带，并返回结果。

    Args:
        freq: 频率数组
        amp: 幅值数组
        axis_name: 轴名称 (X, Y, Z)
        frequency_range: 频率范围选择
            - 'standard': 标准频段 10-315 Hz (16个频段)
            - 'extended': 扩展频段 10-10000 Hz (31个频段)

    Returns:
        dict: 包含分析结果的字典
    """
    range_name = "标准频段 (10-315 Hz)" if frequency_range == 'standard' else "扩展频段 (10-10000 Hz)"
    print(f"\n--- 开始分析 {axis_name} 方向的1/3倍频程 [{range_name}] ---")

    # 根据频率范围选择中心频率
    if frequency_range == 'standard':
        # 标准频段：10-315 Hz (16个频段)
        octave_centers = np.array([
            10, 12.5, 16, 20, 25, 31.5, 40, 50, 63, 80, 100, 125, 160, 200, 250, 315
        ])
    else:
        # 扩展频段：10-10000 Hz (31个频段)
        octave_centers = np.array([
            10, 12.5, 16, 20, 25, 31.5, 40, 50, 63, 80,
            100, 125, 160, 200, 250, 315, 400, 500, 630, 800,
            1000, 1250, 1600, 2000, 2500, 3150, 4000, 5000, 6300, 8000, 10000
        ])
    
    # 带宽比率因子
    k = 2**(1/6)
    lower_limits = octave_centers / k
    upper_limits = octave_centers * k
    octave_bands = np.vstack((lower_limits, upper_limits)).T
    
    num_bands = len(octave_centers)
    octave_power = np.zeros(num_bands)

    # 计算每个频段的功率
    for i in range(num_bands):
        lower_limit = octave_bands[i, 0]
        upper_limit = octave_bands[i, 1]
        
        indices_mask = (freq >= lower_limit) & (freq < upper_limit)
        
        if not np.any(indices_mask):
            octave_power[i] = 0
            continue
            
        band_amp = amp[indices_mask]
        # 功率是均方根值的平方，对于FFT幅值谱，功率是幅值平方和的一半
        power = np.sum((band_amp / np.sqrt(2))**2) # 注意：这里是关键修正
        octave_power[i] = power

    # 计算振动加速度级 (dB)
    a_ref = 1e-6  # 参考加速度 1 µm/s²
    a_rms = np.sqrt(octave_power)
    
    with warnings.catch_warnings():
        warnings.simplefilter("ignore", RuntimeWarning)
        L_A = 20 * np.log10(a_rms / a_ref)
    L_A[np.isneginf(L_A)] = 0
    
    # 计算总振动加速度级
    total_power = np.sum(octave_power)
    total_a_rms = np.sqrt(total_power)
    total_L_A = 20 * np.log10(total_a_rms / a_ref) if total_a_rms > 0 else 0

    print(f"{axis_name} 方向总振动加速度级: {total_L_A:.2f} dB")
    
    # 准备结果
    results = {
        'octave_centers': octave_centers,
        'L_A': L_A,
        'total_L_A': total_L_A,
        'freq_spectrum': freq,
        'amp_spectrum': amp,
        'frequency_range': frequency_range,
        'range_name': range_name,
        'num_bands': len(octave_centers)
    }
    return results

def combine_vibration_levels(L_a1, L_a2):
    """
    使用对数加法公式组合两个振动加速度级。

    公式: L_a_total = 10*lg(10^(L_a1/10) + 10^(L_a2/10))

    Args:
        L_a1: 第一个振动加速度级 (dB)
        L_a2: 第二个振动加速度级 (dB)

    Returns:
        float: 组合后的振动加速度级 (dB)
    """
    if L_a1 <= 0 and L_a2 <= 0:
        return 0.0
    elif L_a1 <= 0:
        return L_a2
    elif L_a2 <= 0:
        return L_a1
    else:
        # 对数加法公式
        combined = 10 * np.log10(10**(L_a1/10) + 10**(L_a2/10))
        return combined

def calculate_total_vibration_level(L_x, L_y, L_z):
    """
    计算总振动加速度级。

    根据用户需求，总振级的计算公式为：
    总振级 = sqrt((X方向振级² + Y方向振级² + Z方向振级²)/3)

    但是由于电机只有Z方向数据，X和Y方向默认为0，
    所以在电机数据情况下，总振级与Z方向振级一致。

    Args:
        L_x: X方向振动加速度级 (dB)，如果没有数据则为0或None
        L_y: Y方向振动加速度级 (dB)，如果没有数据则为0或None
        L_z: Z方向振动加速度级 (dB)

    Returns:
        float: 总振动加速度级 (dB)
    """
    # 处理None值，将其设为0
    x_level = L_x if L_x is not None and L_x > 0 else 0.0
    y_level = L_y if L_y is not None and L_y > 0 else 0.0
    z_level = L_z if L_z is not None and L_z > 0 else 0.0

    # 如果所有方向都没有有效数据，返回0
    if x_level <= 0 and y_level <= 0 and z_level <= 0:
        return 0.0

    # 计算总振级：sqrt((X²+Y²+Z²)/3)
    if x_level ==0.0 and y_level ==0.0 and z_level >=0.0:
        return z_level

    # 计算均方根 
    total = np.sqrt((x_level**2 + y_level**2 + z_level**2) / 3)

    # 转换回dB值
    if total > 0:
        return total
    else:
        return 0.0

# --- 4. 绘图函数 ---

def plot_results(results, axis_name):
    """
    绘制频谱和1/3倍频程图。
    """
    # 图1：原始频谱
    plt.figure(figsize=(9, 5))
    plt.plot(results['freq_spectrum'], results['amp_spectrum'])
    plt.title(f'{axis_name} 方向 - 频谱', fontsize=14)
    plt.xlabel('频率 (Hz)')
    plt.ylabel(r'振幅 ($m/s^2$)')
    plt.xlim(0, 500) # 限制显示范围，使其更清晰
    plt.grid(True)
    plt.tight_layout()

    # 图2：1/3倍频程
    plt.figure(figsize=(10, 6))
    octave_centers = results['octave_centers']
    L_A = results['L_A']
    
    # 使用较小的相对宽度，以在对数坐标上获得良好视觉效果
    bar_width = octave_centers * 0.2
    plt.bar(octave_centers, L_A, width=bar_width, align='center', edgecolor='black')
    plt.gca().set_xscale('log')
    
    plt.title(f'{axis_name} 方向 - 1/3倍频程振动加速度级', fontsize=14)
    plt.xlabel('中心频率 (Hz)')
    plt.ylabel(r'振动加速度级 L_A (dB re 1μm/s²)')
    plt.xticks(octave_centers, labels=[str(f) for f in octave_centers], rotation=45, ha="right")
    plt.grid(True, which="both", ls="--")
    plt.tight_layout()

# --- 5. 主流程 ---

if __name__ == "__main__":
    # 设置matplotlib以支持中文
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    plt.rcParams['axes.unicode_minus'] = False

    # 文件路径
    filepath = 'csacc.txt'
    
    # 加载数据
    time_data, acc_data_dict = load_and_preprocess_data(filepath)
    
    if time_data is not None:
        # 使用ExcelWriter来将多个DataFrame写入同一个Excel文件的不同工作表
        with pd.ExcelWriter('csacc-结果.xlsx') as writer:
            all_results = {}
            # 循环处理每个方向
            for axis in ['X', 'Y', 'Z']:
                # a. 执行FFT
                freq, amp = perform_fft(time_data, acc_data_dict[axis])
                
                # b. 进行1/3倍频程分析
                axis_results = analyze_third_octave(freq, amp, axis)
                all_results[axis] = axis_results
                
                # c. 准备导出到Excel的数据
                results_df = pd.DataFrame({
                    '中心频率 (Hz)': axis_results['octave_centers'],
                    '振动加速度级 L_A (dB)': axis_results['L_A']
                })
                # 将总值添加到DataFrame的末尾以便查看
                total_row = pd.DataFrame([{'中心频率 (Hz)': '总计', '振动加速度级 L_A (dB)': axis_results['total_L_A']}])
                results_df = pd.concat([results_df, total_row], ignore_index=True)

                # d. 写入Excel工作表
                results_df.to_excel(writer, sheet_name=f'{axis} 方向结果', index=False)
                
            print("\n所有方向的结果已成功导出到 'csacc-结果.xlsx'")

            # e. 绘制所有图表
            for axis, results in all_results.items():
                plot_results(results, axis)
            
            # 显示所有图形
            plt.show()