"""
结果显示窗口模块

此模块定义了应用程序的结果显示窗口类，负责：
1. 显示计算结果界面
2. 处理结果显示相关操作

作者: [作者名]
日期: [日期]
"""

from ui import ui_result
from .base_window import BaseWindow


class ResultWindow(BaseWindow):
    """结果显示窗口类"""
    def __init__(self, window_manager):
        super().__init__(window_manager)
        self.ui = ui_result.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("计算设置")
        
        # 设置窗口样式
        self.setStyleSheet(self.styleSheet() + """
            QMainWindow::title {
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # 应用按钮动画效果
        self.setup_animated_buttons()
        
    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.push_mainui,
            self.ui.push_constrainui,
            self.ui.push_finish,
            self.ui.push_postui
        ]
        
        # 保存按钮的点击处理函数
        from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step
        from window_manager import WindowType
        # 注意：不在这里连接finish按钮，避免重复连接
        # finish按钮的连接在ctrl/result_slot.py中统一处理
        # 使用统一的导航管理器
        mainui_handler = lambda: navigate_to_main_menu(self.window_manager)
        constrainui_handler = lambda: navigate_to_previous_step(self.window_manager, WindowType.RESULT)  # 上一步(网格验证)
        postui_handler = lambda: navigate_to_next_step(self.window_manager, WindowType.RESULT)  # 下一步(后处理)

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        # 重新连接信号（不包括finish按钮）
        self.ui.push_mainui.clicked.connect(mainui_handler)
        self.ui.push_constrainui.clicked.connect(constrainui_handler)
        # self.ui.push_finish.clicked.connect(finish_handler)  # 移除重复连接
        self.ui.push_postui.clicked.connect(postui_handler)