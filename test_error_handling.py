#!/usr/bin/env python3
"""
测试改进的错误处理机制

此脚本用于验证新的错误处理机制是否能够：
1. 正确区分关键性错误和非关键性错误
2. 在非关键性错误时继续执行计算流程
3. 在关键性错误时正确停止计算流程
4. 通过结果文件检测计算成功与否

作者: AI Assistant
日期: 2025-01-02
"""

import os
import sys
import tempfile
import subprocess
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_bat_file(test_type: str) -> str:
    """创建测试用的批处理文件
    
    Args:
        test_type: 测试类型
            - "success": 成功执行
            - "warning": 有警告但成功
            - "critical_error": 致命错误
            - "timeout": 超时测试
    
    Returns:
        str: 批处理文件路径
    """
    with tempfile.NamedTemporaryFile(mode='w', suffix='.bat', delete=False) as f:
        if test_type == "success":
            # 成功执行的测试
            f.write("""@echo off
echo Starting ANSYS simulation...
echo INFO: Mesh generation completed successfully
echo WARNING: Element quality is below recommended threshold
echo INFO: Modal analysis started
echo INFO: Solution converged
echo INFO: Results written to output file
echo Analysis finished successfully
exit 0
""")
        elif test_type == "warning":
            # 有警告但成功的测试
            f.write("""@echo off
echo Starting ANSYS simulation...
echo WARNING: Deprecated feature used
echo WARNING: Mesh quality warning - some elements have poor aspect ratio
echo INFO: Analysis completed despite warnings
echo INFO: Results written successfully
exit 0
""")
        elif test_type == "critical_error":
            # 致命错误测试
            f.write("""@echo off
echo Starting ANSYS simulation...
echo INFO: Initialization complete
echo FATAL ERROR: License not available
echo ERROR: Cannot continue without valid license
exit 1
""")
        elif test_type == "timeout":
            # 超时测试（长时间运行）
            f.write("""@echo off
echo Starting long-running simulation...
timeout /t 60 /nobreak > nul
echo This should timeout before completion
exit 0
""")
        
        return f.name

def create_test_result_file(output_dir: str, element_size: float, success: bool = True) -> str:
    """创建测试用的结果文件
    
    Args:
        output_dir: 输出目录
        element_size: 网格尺寸（米）
        success: 是否创建成功的结果文件
    
    Returns:
        str: 结果文件路径
    """
    os.makedirs(output_dir, exist_ok=True)
    
    result_file = os.path.join(output_dir, f"modal_freq_{element_size}.json")
    
    if success:
        # 创建成功的结果文件
        result_data = {
            "frequencies_Hz": [45.2, 78.5, 123.7, 189.3, 245.8],
            "calculation_time_s": 125.6,
            "element_count": 15420,
            "node_count": 8765,
            "element_size_m": element_size
        }
    else:
        # 创建空的或无效的结果文件
        result_data = {}
    
    with open(result_file, 'w', encoding='utf-8') as f:
        json.dump(result_data, f, indent=2)
    
    return result_file

def test_error_classification():
    """测试错误分类功能"""
    print("=== 测试错误分类功能 ===")
    
    # 导入我们的错误分析函数
    try:
        from views.mesh_window_merged import _analyze_execution_output
    except ImportError as e:
        print(f"❌ 无法导入错误分析函数: {e}")
        return False
    
    # 创建模拟的网格参数对象
    class MockMeshParameter:
        def __init__(self, name: str, size: float):
            self.name = name
            self.size = size  # 毫米
    
    mesh_param = MockMeshParameter("test_mesh", 5.0)
    
    # 测试用例
    test_cases = [
        {
            "name": "成功案例",
            "output_info": {
                "stdout": "INFO: Analysis completed successfully\nINFO: Results written to file",
                "stderr": "",
                "return_code": 0
            },
            "create_result_file": True,
            "expected_success": True
        },
        {
            "name": "警告案例",
            "output_info": {
                "stdout": "WARNING: Element quality below threshold\nINFO: Analysis completed",
                "stderr": "WARNING: Deprecated feature used",
                "return_code": 0
            },
            "create_result_file": True,
            "expected_success": True
        },
        {
            "name": "致命错误案例",
            "output_info": {
                "stdout": "Starting analysis...",
                "stderr": "FATAL ERROR: License not available",
                "return_code": 1
            },
            "create_result_file": False,
            "expected_success": False
        },
        {
            "name": "无结果文件案例",
            "output_info": {
                "stdout": "INFO: Analysis completed",
                "stderr": "",
                "return_code": 0
            },
            "create_result_file": False,
            "expected_success": False
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases):
        print(f"\n测试 {i+1}: {test_case['name']}")
        
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 如果需要，创建结果文件
            if test_case['create_result_file']:
                create_test_result_file(temp_dir, mesh_param.size / 1000.0, True)
            
            # 执行错误分析
            try:
                success = _analyze_execution_output(
                    test_case['output_info'], 
                    mesh_param, 
                    temp_dir
                )
                
                if success == test_case['expected_success']:
                    print(f"  ✅ 通过: 预期 {test_case['expected_success']}, 实际 {success}")
                else:
                    print(f"  ❌ 失败: 预期 {test_case['expected_success']}, 实际 {success}")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ 异常: {e}")
                all_passed = False
    
    return all_passed

def test_bat_execution():
    """测试批处理文件执行功能"""
    print("\n=== 测试批处理文件执行功能 ===")
    
    # 导入我们的执行函数
    try:
        from views.mesh_window_merged import _execute_bat_with_error_tolerance
        from error_handler import ErrorHandler
    except ImportError as e:
        print(f"❌ 无法导入执行函数: {e}")
        return False
    
    # 创建模拟对象
    class MockMeshParameter:
        def __init__(self, name: str, size: float):
            self.name = name
            self.size = size
    
    class MockMeshWindow:
        def __init__(self):
            self.ui = None
    
    mesh_param = MockMeshParameter("test_mesh", 5.0)
    error_handler = ErrorHandler()
    mesh_window = MockMeshWindow()
    
    # 测试成功案例
    print("\n测试 1: 成功执行")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建结果文件
            create_test_result_file(temp_dir, mesh_param.size / 1000.0, True)
            
            # 创建成功的批处理文件
            bat_file = create_test_bat_file("success")
            
            try:
                success, output_info = _execute_bat_with_error_tolerance(
                    bat_file, mesh_param, temp_dir, error_handler, mesh_window
                )
                
                if success:
                    print("  ✅ 成功执行测试通过")
                else:
                    print(f"  ❌ 成功执行测试失败: {output_info.get('error_message', '未知错误')}")
                    
            finally:
                os.unlink(bat_file)
                
    except Exception as e:
        print(f"  ❌ 成功执行测试异常: {e}")
        return False
    
    # 测试警告案例
    print("\n测试 2: 有警告但成功")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建结果文件
            create_test_result_file(temp_dir, mesh_param.size / 1000.0, True)
            
            # 创建有警告的批处理文件
            bat_file = create_test_bat_file("warning")
            
            try:
                success, output_info = _execute_bat_with_error_tolerance(
                    bat_file, mesh_param, temp_dir, error_handler, mesh_window
                )
                
                if success and len(output_info.get('warnings', [])) > 0:
                    print("  ✅ 警告处理测试通过")
                else:
                    print(f"  ❌ 警告处理测试失败: success={success}, warnings={len(output_info.get('warnings', []))}")
                    
            finally:
                os.unlink(bat_file)
                
    except Exception as e:
        print(f"  ❌ 警告处理测试异常: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始测试改进的错误处理机制...")
    
    # 测试错误分类
    classification_passed = test_error_classification()
    
    # 测试批处理执行
    execution_passed = test_bat_execution()
    
    # 总结结果
    print("\n" + "="*50)
    print("测试结果总结:")
    print(f"错误分类测试: {'✅ 通过' if classification_passed else '❌ 失败'}")
    print(f"批处理执行测试: {'✅ 通过' if execution_passed else '❌ 失败'}")
    
    if classification_passed and execution_passed:
        print("\n🎉 所有测试通过！改进的错误处理机制工作正常。")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步检查和修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
