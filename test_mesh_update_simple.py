#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的网格更新功能测试

测试MeshManager.update_mesh()方法的正确调用方式
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_mesh_update():
    """测试网格更新功能"""
    try:
        print("开始测试网格更新功能...")
        
        # 导入必要的类
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        
        # 创建网格管理器
        mesh_manager = MeshManager()
        print("✅ 网格管理器创建成功")
        
        # 创建测试网格
        original_mesh = MeshParameter(
            name="test_mesh",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        # 添加网格
        success = mesh_manager.add_mesh(original_mesh)
        if not success:
            print("❌ 添加网格失败")
            return False
        
        print(f"✅ 原始网格添加成功: {original_mesh.name}, ID: {original_mesh.id}")
        
        # 创建更新后的网格参数
        updated_mesh = MeshParameter(
            name="updated_test_mesh",
            size=15.0,
            element_type=ElementType.HEXAHEDRON
        )
        
        # 设置正确的ID（这是关键）
        updated_mesh.id = original_mesh.id
        print(f"✅ 设置更新网格ID: {updated_mesh.id}")
        
        # 测试update_mesh方法 - 只传递一个参数
        print("正在调用 mesh_manager.update_mesh(updated_mesh)...")
        success = mesh_manager.update_mesh(updated_mesh)
        
        if not success:
            print("❌ 更新网格失败")
            return False
        
        print("✅ 网格更新成功")
        
        # 验证更新结果
        retrieved_mesh = mesh_manager.get_mesh_by_id(original_mesh.id)
        if retrieved_mesh is None:
            print("❌ 无法获取更新后的网格")
            return False
        
        print(f"✅ 验证更新结果:")
        print(f"  - 网格名称: {retrieved_mesh.name} (期望: updated_test_mesh)")
        print(f"  - 网格尺寸: {retrieved_mesh.size} (期望: 15.0)")
        print(f"  - 单元类型: {retrieved_mesh.element_type.value} (期望: HEXAHEDRON)")
        
        # 检查结果
        if (retrieved_mesh.name == "updated_test_mesh" and 
            retrieved_mesh.size == 15.0 and 
            retrieved_mesh.element_type == ElementType.HEXAHEDRON):
            print("🎉 所有验证通过！网格更新功能正常")
            return True
        else:
            print("❌ 验证失败，更新结果不正确")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_method_signature():
    """测试方法签名"""
    try:
        print("\n检查MeshManager.update_mesh()方法签名...")
        
        from core.mesh_manager import MeshManager
        import inspect
        
        # 获取方法签名
        signature = inspect.signature(MeshManager.update_mesh)
        print(f"✅ 方法签名: update_mesh{signature}")
        
        # 检查参数数量
        params = list(signature.parameters.keys())
        print(f"✅ 参数列表: {params}")
        
        # 除了self之外应该只有一个参数
        if len(params) == 2 and params[0] == 'self':  # self + mesh_param
            print("✅ 方法签名正确：只需要一个MeshParameter参数")
            return True
        else:
            print(f"❌ 方法签名不正确，参数数量: {len(params)}")
            return False
            
    except Exception as e:
        print(f"❌ 检查方法签名时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("网格更新功能修复验证测试")
    print("=" * 50)
    
    # 测试方法签名
    signature_ok = test_method_signature()
    
    # 测试网格更新
    update_ok = test_mesh_update()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"• 方法签名检查: {'✅ 通过' if signature_ok else '❌ 失败'}")
    print(f"• 网格更新测试: {'✅ 通过' if update_ok else '❌ 失败'}")
    
    if signature_ok and update_ok:
        print("\n🎉 所有测试通过！")
        print("\n修复总结:")
        print("• 原来的调用: mesh_manager.update_mesh(mesh_id, updated_param)")
        print("• 修复后调用: mesh_manager.update_mesh(updated_param)")
        print("• 关键点: 确保 updated_param.id = mesh_id")
        return True
    else:
        print("\n❌ 测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
