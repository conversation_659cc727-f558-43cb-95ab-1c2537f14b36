"""
测试数据验证脚本

验证所有测试数据文件的格式和内容是否正确，
并演示导入功能的使用方法。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import json
import csv
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def validate_json_files():
    """验证JSON格式文件"""
    print("🧪 验证JSON格式文件...")
    
    json_files = [
        "test_data/reference_models.json",
        "test_data/experimental_data.json", 
        "test_data/convergence_study.json"
    ]
    
    success_count = 0
    for file_path in json_files:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"  ✅ {os.path.basename(file_path)}: {len(data)} 条记录")
                
                # 验证数据结构
                for i, item in enumerate(data):
                    required_fields = ['name', 'size', 'frequencies', 'node_count', 'element_count']
                    missing_fields = [field for field in required_fields if field not in item]
                    if missing_fields:
                        print(f"    ⚠️ 记录 {i+1} 缺少字段: {missing_fields}")
                    else:
                        freq_count = len(item['frequencies'])
                        print(f"    - {item['name']}: {freq_count} 个频率")
                
                success_count += 1
            else:
                print(f"  ❌ {file_path}: 文件不存在")
                
        except Exception as e:
            print(f"  ❌ {file_path}: {str(e)}")
    
    return success_count, len(json_files)

def validate_csv_files():
    """验证CSV格式文件"""
    print("\n🧪 验证CSV格式文件...")
    
    csv_files = [
        "test_data/benchmark_models.csv",
        "test_data/literature_comparison.csv",
        "test_data/material_comparison.csv"
    ]
    
    success_count = 0
    for file_path in csv_files:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = list(reader)
                
                print(f"  ✅ {os.path.basename(file_path)}: {len(rows)} 条记录")
                
                # 验证数据结构
                for i, row in enumerate(rows):
                    required_fields = ['name', 'size', 'frequencies', 'node_count', 'element_count']
                    missing_fields = [field for field in required_fields if field not in row]
                    if missing_fields:
                        print(f"    ⚠️ 记录 {i+1} 缺少字段: {missing_fields}")
                    else:
                        # 解析频率字符串
                        freq_str = row['frequencies'].strip('[]')
                        freq_count = len(freq_str.split(','))
                        print(f"    - {row['name']}: {freq_count} 个频率")
                
                success_count += 1
            else:
                print(f"  ❌ {file_path}: 文件不存在")
                
        except Exception as e:
            print(f"  ❌ {file_path}: {str(e)}")
    
    return success_count, len(csv_files)

def validate_txt_files():
    """验证TXT格式文件"""
    print("\n🧪 验证TXT格式文件...")
    
    txt_files = [
        "test_data/software_comparison.txt",
        "test_data/boundary_conditions.txt"
    ]
    
    success_count = 0
    for file_path in txt_files:
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                if len(lines) < 2:
                    print(f"  ❌ {file_path}: 文件格式不正确")
                    continue
                
                # 解析标题行
                headers = lines[0].strip().split('\t')
                data_lines = [line.strip() for line in lines[1:] if line.strip()]
                
                print(f"  ✅ {os.path.basename(file_path)}: {len(data_lines)} 条记录")
                print(f"    字段: {', '.join(headers)}")
                
                # 验证数据行
                for i, line in enumerate(data_lines):
                    values = line.split('\t')
                    if len(values) == len(headers):
                        row_dict = dict(zip(headers, values))
                        print(f"    - {row_dict.get('name', f'记录{i+1}')}")
                    else:
                        print(f"    ⚠️ 记录 {i+1} 字段数量不匹配")
                
                success_count += 1
            else:
                print(f"  ❌ {file_path}: 文件不存在")
                
        except Exception as e:
            print(f"  ❌ {file_path}: {str(e)}")
    
    return success_count, len(txt_files)

def test_data_import():
    """测试数据导入功能"""
    print("\n🧪 测试数据导入功能...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建测试数据管理器
        data_manager = ModalDataManager("test_validation.pkl")
        
        # 测试导入不同格式的文件
        test_files = [
            "test_data/reference_models.json",
            "test_data/benchmark_models.csv",
            "test_data/software_comparison.txt"
        ]
        
        import_success = 0
        for file_path in test_files:
            if os.path.exists(file_path):
                success = data_manager.import_from_file(file_path)
                if success:
                    print(f"  ✅ 导入成功: {os.path.basename(file_path)}")
                    import_success += 1
                else:
                    print(f"  ❌ 导入失败: {os.path.basename(file_path)}")
            else:
                print(f"  ⚠️ 文件不存在: {file_path}")
        
        # 显示导入结果统计
        imported_results = data_manager.get_imported_results()
        print(f"\n  📊 导入结果统计:")
        print(f"    成功导入文件: {import_success}/{len(test_files)}")
        print(f"    总导入记录数: {len(imported_results)}")
        
        # 显示前几条记录
        for i, result in enumerate(imported_results[:5]):
            print(f"    {i+1}. {result.name} - {len(result.frequencies)} 个模态")
        
        if len(imported_results) > 5:
            print(f"    ... 还有 {len(imported_results) - 5} 条记录")
        
        return import_success == len(test_files)
        
    except Exception as e:
        print(f"  ❌ 导入功能测试失败: {str(e)}")
        return False

def create_usage_examples():
    """创建使用示例"""
    print("\n📋 创建使用示例...")
    
    examples = """
# 测试数据使用示例

## 快速开始

### 1. 基础功能测试
```
文件: test_data/reference_models.json
用途: 验证基本导入功能
步骤:
1. 打开网格管理界面
2. 切换到"模态结果对比"标签页
3. 点击"导入结果"按钮
4. 选择 reference_models.json
5. 查看导入的3个参考模型
6. 点击"更新图表"查看对比效果
```

### 2. 网格收敛性分析
```
文件: test_data/convergence_study.json
用途: 网格无关性分析
步骤:
1. 导入 convergence_study.json（包含6个不同网格密度）
2. 选择图表类型为"网格收敛性分析"
3. 观察频率随网格尺寸的收敛趋势
4. 确定最优网格密度
```

### 3. 实验验证对比
```
文件: test_data/experimental_data.json
用途: 仿真结果验证
步骤:
1. 先完成当前网格的模态计算
2. 导入 experimental_data.json
3. 选择图表类型为"频率对比图"
4. 对比仿真结果与实验结果的差异
5. 评估仿真精度
```

### 4. 软件交叉验证
```
文件: test_data/software_comparison.txt
用途: 不同软件结果对比
步骤:
1. 导入 software_comparison.txt
2. 查看ANSYS、ABAQUS、NASTRAN等软件结果
3. 对比不同求解器的差异
4. 验证结果一致性
```

### 5. 材料影响分析
```
文件: test_data/material_comparison.csv
用途: 材料选择分析
步骤:
1. 导入 material_comparison.csv
2. 选择图表类型为"模态分布图"
3. 对比不同材料的模态特性
4. 为设计选择合适的材料
```

## 批量导入示例

### 综合对比分析
```
同时导入多个文件进行综合分析:
- reference_models.json (参考模型)
- experimental_data.json (实验数据)
- benchmark_models.csv (基准测试)

这样可以在一个图表中同时看到:
- 当前计算结果
- 文献参考结果
- 实验测试结果
- 标准基准结果
```

## 高级使用技巧

### 1. 数据筛选
- 使用"管理导入结果"功能
- 重命名导入的结果以便识别
- 删除不需要的对比数据
- 查看详细的数据信息

### 2. 图表定制
- 切换不同的图表类型
- 调整显示选项（频率标签、网格信息等）
- 保存高质量图表用于报告

### 3. 结果导出
- 导出对比图表为PNG/PDF格式
- 导出数据为JSON/CSV格式
- 用于报告和文档编写

## 故障排除

### 常见问题
1. **导入失败**: 检查文件格式和编码
2. **数据显示异常**: 验证必需字段是否完整
3. **图表不更新**: 确保选择了对比数据
4. **内存不足**: 分批导入大量数据

### 数据格式要求
- JSON: 标准JSON数组格式
- CSV: 包含标题行，逗号分隔
- TXT: 制表符分隔，包含标题行
- 编码: UTF-8
- 必需字段: name, size, frequencies, node_count, element_count
"""
    
    try:
        with open("test_data/usage_examples.md", "w", encoding="utf-8") as f:
            f.write(examples)
        print("  ✅ 使用示例已保存: test_data/usage_examples.md")
        return True
    except Exception as e:
        print(f"  ❌ 示例创建失败: {str(e)}")
        return False

def main():
    """主验证函数"""
    print("=" * 70)
    print("🎯 测试数据验证和使用指南")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 验证各种格式的文件
    json_success, json_total = validate_json_files()
    csv_success, csv_total = validate_csv_files()
    txt_success, txt_total = validate_txt_files()
    
    # 测试导入功能
    import_ok = test_data_import()
    
    # 创建使用示例
    examples_ok = create_usage_examples()
    
    print("\n" + "=" * 70)
    print("📋 验证结果总结:")
    print(f"JSON文件验证: {json_success}/{json_total} 通过")
    print(f"CSV文件验证: {csv_success}/{csv_total} 通过")
    print(f"TXT文件验证: {txt_success}/{txt_total} 通过")
    print(f"导入功能测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"使用示例创建: {'✅ 完成' if examples_ok else '❌ 失败'}")
    
    total_files = json_total + csv_total + txt_total
    total_success = json_success + csv_success + txt_success
    
    if total_success == total_files and import_ok:
        print("\n🎉 所有测试数据验证通过！")
        print("\n✨ 可用的测试数据:")
        print("  📊 参考模型数据 (3个模型)")
        print("  🧪 实验测试数据 (3组实验)")
        print("  📏 基准测试数据 (5个基准)")
        print("  📚 文献对比数据 (5篇文献)")
        print("  💻 软件对比数据 (7个软件)")
        print("  🔍 收敛性研究 (6个网格)")
        print("  🔧 材料对比数据 (7种材料)")
        print("  ⚙️ 边界条件对比 (7种边界)")
        
        print("\n🎯 使用建议:")
        print("  1. 从 reference_models.json 开始测试基本功能")
        print("  2. 使用 convergence_study.json 测试网格收敛性")
        print("  3. 尝试批量导入多个文件进行综合对比")
        print("  4. 查看 test_data/README.md 获取详细说明")
        print("  5. 参考 test_data/usage_examples.md 学习使用技巧")
        
    else:
        print("\n⚠️ 部分测试数据验证失败")
        print("请检查文件格式和内容是否正确")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
