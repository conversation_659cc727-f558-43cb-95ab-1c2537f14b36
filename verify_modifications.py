"""
验证网格与模态分析界面简化修改

快速验证修改是否成功完成。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def verify_ui_modifications():
    """验证UI修改"""
    print("🔍 验证UI修改...")
    
    try:
        # 检查UI文件中是否移除了批量生成组件
        with open('ui/ui_mesh_merged.py', 'r', encoding='utf-8') as f:
            ui_content = f.read()
        
        removed_components = [
            'btn_batch_generate',
            'btn_stop_generation',
            'progressBar_generation',
            'label_generation_progress'
        ]
        
        found_components = []
        for component in removed_components:
            if component in ui_content:
                found_components.append(component)
        
        if not found_components:
            print("✅ UI组件移除成功 - 所有批量生成相关组件已移除")
        else:
            print(f"⚠️ 部分UI组件未移除: {found_components}")
        
        # 检查是否添加了新的说明标签
        if 'label_mesh_selection_info' in ui_content:
            print("✅ 新增说明标签已添加")
        else:
            print("⚠️ 新增说明标签未找到")
        
        return len(found_components) == 0
        
    except Exception as e:
        print(f"❌ UI验证失败: {e}")
        return False

def verify_window_modifications():
    """验证窗口类修改"""
    print("\n🔍 验证窗口类修改...")
    
    try:
        # 检查窗口文件中是否移除了批量生成方法
        with open('views/mesh_window_merged.py', 'r', encoding='utf-8') as f:
            window_content = f.read()
        
        removed_methods = [
            'def _on_batch_generate',
            'def _start_batch_generation',
            'def _process_next_generation',
            'def _finish_batch_generation',
            'def _on_stop_generation'
        ]
        
        found_methods = []
        for method in removed_methods:
            if method in window_content:
                found_methods.append(method)
        
        if not found_methods:
            print("✅ 批量生成方法移除成功")
        else:
            print(f"⚠️ 部分方法未移除: {found_methods}")
        
        # 检查是否添加了自动网格生成方法
        new_methods = [
            'def _auto_generate_mesh',
            'def _auto_generate_meshes_for_batch'
        ]
        
        found_new_methods = []
        for method in new_methods:
            if method in window_content:
                found_new_methods.append(method)
        
        if len(found_new_methods) == len(new_methods):
            print("✅ 自动网格生成方法添加成功")
        else:
            print(f"⚠️ 部分新方法未添加: {[m for m in new_methods if m not in found_new_methods]}")
        
        # 检查验证逻辑是否简化
        if '系统将自动处理网格生成和模态计算流程' in window_content:
            print("✅ 验证逻辑已简化")
        else:
            print("⚠️ 验证逻辑可能未完全简化")
        
        return len(found_methods) == 0 and len(found_new_methods) == len(new_methods)
        
    except Exception as e:
        print(f"❌ 窗口类验证失败: {e}")
        return False

def verify_signal_connections():
    """验证信号连接修改"""
    print("\n🔍 验证信号连接修改...")
    
    try:
        with open('views/mesh_window_merged.py', 'r', encoding='utf-8') as f:
            window_content = f.read()
        
        # 检查是否移除了批量生成信号连接
        removed_signals = [
            'btn_batch_generate.clicked.connect',
            'btn_stop_generation.clicked.connect'
        ]
        
        found_signals = []
        for signal in removed_signals:
            if signal in window_content:
                found_signals.append(signal)
        
        if not found_signals:
            print("✅ 批量生成信号连接已移除")
        else:
            print(f"⚠️ 部分信号连接未移除: {found_signals}")
        
        return len(found_signals) == 0
        
    except Exception as e:
        print(f"❌ 信号连接验证失败: {e}")
        return False

def main():
    """主验证函数"""
    print("=" * 60)
    print("🔍 网格与模态分析界面简化修改验证")
    print("=" * 60)
    
    success_count = 0
    total_checks = 3
    
    # 验证UI修改
    if verify_ui_modifications():
        success_count += 1
    
    # 验证窗口类修改
    if verify_window_modifications():
        success_count += 1
    
    # 验证信号连接修改
    if verify_signal_connections():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 验证完成！成功 {success_count}/{total_checks} 项检查")
    
    if success_count == total_checks:
        print("✅ 所有修改验证通过！")
        print("\n🎯 简化效果：")
        print("✅ 移除了批量网格生成相关UI组件")
        print("✅ 移除了批量网格生成相关处理函数")
        print("✅ 移除了批量网格生成信号连接")
        print("✅ 添加了自动网格生成功能")
        print("✅ 简化了模态计算前置条件")
        print("\n🚀 新的工作流程：")
        print("1. 用户设置网格参数")
        print("2. 直接点击模态计算按钮")
        print("3. 系统自动生成网格并进行模态计算")
        print("4. 状态直接从'待处理'跳转到'完成'")
    else:
        print(f"⚠️ 有 {total_checks - success_count} 项检查失败")
        print("请检查修改是否完整")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
