"""
测试模态分析结果对比界面重新设计

此脚本验证重新设计的结果对比界面是否正常工作：
1. 界面组件正确加载
2. 基于实际模态分析数据的显示功能
3. 新的图表类型选择功能
4. 模态分析选项控制功能

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_components():
    """测试UI组件加载"""
    print("🧪 测试UI组件加载...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格窗口
        window = MeshWindow()
        
        print("✅ 网格窗口创建成功")
        
        # 检查新的UI组件是否存在
        required_components = [
            # 模态分析选项
            'checkBox_show_frequencies',
            'checkBox_show_mode_shapes', 
            'checkBox_show_mesh_info',
            
            # 图表类型选择
            'radioButton_frequency_comparison',
            'radioButton_mode_distribution',
            'radioButton_mesh_convergence',
            
            # 操作按钮
            'btn_update_chart',
            'btn_save_chart',
            
            # 图表显示
            'label_chart_title',
            'widget_chart_container',
            'label_chart_placeholder'
        ]
        
        missing_components = []
        for component in required_components:
            if not hasattr(window.ui, component):
                missing_components.append(component)
        
        if not missing_components:
            print("✅ 所有新UI组件都存在")
        else:
            print(f"❌ 缺少UI组件: {missing_components}")
            return False
        
        # 检查移除的旧组件
        removed_components = [
            'checkBox_show_frequency',
            'checkBox_show_convergence', 
            'checkBox_show_grid',
            'groupBox_display_options'
        ]
        
        existing_old_components = []
        for component in removed_components:
            if hasattr(window.ui, component):
                existing_old_components.append(component)
        
        if not existing_old_components:
            print("✅ 旧的不相关组件已成功移除")
        else:
            print(f"⚠️ 仍存在旧组件: {existing_old_components}")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_modal_data_display():
    """测试模态数据显示功能"""
    print("\n🧪 测试模态数据显示功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow
        from core.mesh_manager import MeshManager, MeshParameter, MeshStatus
        from core.modal_results import ModalResults
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格窗口
        window = MeshWindow()
        
        # 创建测试模态数据
        mesh_manager = MeshManager()
        
        # 添加测试网格1
        mesh1 = MeshParameter()
        mesh1.name = "细网格"
        mesh1.size = 2.0
        mesh1.status = MeshStatus.COMPLETED
        mesh1.modal_results = ModalResults()
        mesh1.modal_results.frequencies = [45.2, 78.5, 112.3, 156.8, 203.4]
        mesh1.modal_results.calculation_time = 120.5
        mesh_manager.add_mesh(mesh1)
        
        # 添加测试网格2
        mesh2 = MeshParameter()
        mesh2.name = "中等网格"
        mesh2.size = 5.0
        mesh2.status = MeshStatus.COMPLETED
        mesh2.modal_results = ModalResults()
        mesh2.modal_results.frequencies = [44.8, 77.9, 111.2, 155.1, 201.8]
        mesh2.modal_results.calculation_time = 85.2
        mesh_manager.add_mesh(mesh2)
        
        # 添加测试网格3
        mesh3 = MeshParameter()
        mesh3.name = "粗网格"
        mesh3.size = 10.0
        mesh3.status = MeshStatus.COMPLETED
        mesh3.modal_results = ModalResults()
        mesh3.modal_results.frequencies = [43.5, 76.2, 108.9, 152.3, 198.7]
        mesh3.modal_results.calculation_time = 45.8
        mesh_manager.add_mesh(mesh3)
        
        print(f"✅ 创建了 {len(mesh_manager.get_all_meshes())} 个测试网格")
        
        # 测试图表生成功能
        mesh_data = [
            {
                'name': mesh1.name,
                'size': mesh1.size,
                'frequencies': mesh1.modal_results.frequencies,
                'node_count': 1000,
                'element_count': 800
            },
            {
                'name': mesh2.name,
                'size': mesh2.size,
                'frequencies': mesh2.modal_results.frequencies,
                'node_count': 500,
                'element_count': 400
            }
        ]
        
        # 测试频率对比图表生成
        window._generate_frequency_comparison_chart(mesh_data)
        print("✅ 频率对比图表生成成功")
        
        # 测试模态分布图表生成
        window._generate_mode_distribution_chart(mesh_data)
        print("✅ 模态分布图表生成成功")
        
        # 测试网格收敛性图表生成
        window._generate_mesh_convergence_chart(mesh_data)
        print("✅ 网格收敛性图表生成成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模态数据显示测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_chart_type_functionality():
    """测试图表类型功能"""
    print("\n🧪 测试图表类型功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_window_merged import MeshWindow
        
        app = QApplication.instance() or QApplication([])
        
        # 创建网格窗口
        window = MeshWindow()
        
        # 测试默认选择
        if window.ui.radioButton_frequency_comparison.isChecked():
            print("✅ 默认选择频率对比图")
        else:
            print("❌ 默认选择不正确")
            return False
        
        # 测试图表类型切换
        window.ui.radioButton_mode_distribution.setChecked(True)
        if window.ui.radioButton_mode_distribution.isChecked():
            print("✅ 模态分布图选择正常")
        else:
            print("❌ 模态分布图选择失败")
            return False
        
        window.ui.radioButton_mesh_convergence.setChecked(True)
        if window.ui.radioButton_mesh_convergence.isChecked():
            print("✅ 网格收敛性分析选择正常")
        else:
            print("❌ 网格收敛性分析选择失败")
            return False
        
        # 测试模态分析选项
        options = [
            'checkBox_show_frequencies',
            'checkBox_show_mode_shapes',
            'checkBox_show_mesh_info'
        ]
        
        for option in options:
            checkbox = getattr(window.ui, option)
            if checkbox.isChecked():
                print(f"✅ {option} 默认选中")
            else:
                print(f"⚠️ {option} 默认未选中")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表类型功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_data_compatibility():
    """测试与modal.py数据兼容性"""
    print("\n🧪 测试与modal.py数据兼容性...")
    
    try:
        # 模拟modal.py输出的数据格式
        modal_output_data = {
            "mesh_results": [
                {
                    "mesh_name": "网格_0.001m",
                    "mesh_size": 0.001,  # 单位：米
                    "node_count": 15000,
                    "element_count": 12000,
                    "modal_frequencies": [42.5, 75.8, 108.2, 145.6, 189.3, 234.7],
                    "calculation_time": 180.5
                },
                {
                    "mesh_name": "网格_0.002m", 
                    "mesh_size": 0.002,  # 单位：米
                    "node_count": 8000,
                    "element_count": 6500,
                    "modal_frequencies": [42.1, 75.2, 107.5, 144.8, 188.1, 233.2],
                    "calculation_time": 95.2
                },
                {
                    "mesh_name": "网格_0.005m",
                    "mesh_size": 0.005,  # 单位：米
                    "node_count": 3200,
                    "element_count": 2800,
                    "modal_frequencies": [41.3, 74.1, 106.2, 143.1, 186.4, 230.8],
                    "calculation_time": 42.8
                }
            ]
        }
        
        print(f"✅ 模拟modal.py输出数据: {len(modal_output_data['mesh_results'])} 个网格结果")
        
        # 验证数据格式兼容性
        for i, result in enumerate(modal_output_data['mesh_results']):
            required_fields = ['mesh_name', 'mesh_size', 'modal_frequencies']
            missing_fields = [field for field in required_fields if field not in result]
            
            if not missing_fields:
                print(f"✅ 网格 {i+1} 数据格式兼容")
                print(f"  - 名称: {result['mesh_name']}")
                print(f"  - 尺寸: {result['mesh_size']*1000:.1f}mm")
                print(f"  - 模态数: {len(result['modal_frequencies'])} 阶")
                print(f"  - 频率范围: {min(result['modal_frequencies']):.1f} - {max(result['modal_frequencies']):.1f} Hz")
            else:
                print(f"❌ 网格 {i+1} 缺少字段: {missing_fields}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据兼容性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 模态分析结果对比界面重新设计验证")
    print("=" * 70)
    print("\n重新设计目标：")
    print("✨ 基于模态分析实际可获取数据重新设计界面")
    print("✨ 移除不可获取的显示选项（收敛线、网格线）")
    print("✨ 添加适合模态分析的可视化元素")
    print("✨ 确保与modal.py脚本输出数据兼容")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    success_count = 0
    total_tests = 4
    
    # 测试UI组件
    if test_ui_components():
        success_count += 1
    
    # 测试模态数据显示
    if test_modal_data_display():
        success_count += 1
    
    # 测试图表类型功能
    if test_chart_type_functionality():
        success_count += 1
    
    # 测试数据兼容性
    if test_data_compatibility():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！")
        print("✅ 界面重新设计成功")
        print("✅ 模态分析数据显示正常")
        print("✅ 图表类型功能完善")
        print("✅ 与modal.py完全兼容")
        print("\n🎯 重新设计效果：")
        print("• 移除了不可获取的收敛线和网格线显示选项")
        print("• 添加了基于实际模态数据的图表类型")
        print("• 优化了界面布局，突出模态频率对比")
        print("• 确保了与modal.py脚本的数据兼容性")
        print("• 提供了频率对比、模态分布、收敛性分析三种图表")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
        print("请检查界面重新设计的实现")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
