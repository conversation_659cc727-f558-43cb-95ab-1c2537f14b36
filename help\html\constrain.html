<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>约束设置界面 - 振动传递计算软件帮助文档</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>约束设置界面使用说明</h1>
    </div>
    
    <div class="container">
        <div class="section">
            <h2>界面概述</h2>
            <p>约束设置界面是一个综合性的边界条件定义模块，不仅包含传统的力、位移、远程位移和旋转等约束设置，还集成了强大的监控点管理系统。该界面为用户提供了完整的约束定义和监控点管理解决方案。</p>
        </div>

        <div class="section">
            <h2>🎯 主要功能模块</h2>

            <div class="feature-card">
                <h3>🔒 传统约束设置</h3>
                <ul>
                    <li>力约束设置 - 定义力的时间历程和作用位置</li>
                    <li>位移约束设置 - 设置固定约束和位移边界条件</li>
                    <li>远程位移约束设置 - 配置远程位移参考点和约束面</li>
                    <li>旋转约束设置 - 定义旋转速度和旋转轴</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>📍 监控点管理系统</h3>
                <ul>
                    <li>监控点文件导入 - 支持JSON和TXT格式</li>
                    <li>数据格式验证 - 自动检查坐标数据完整性</li>
                    <li>实时数据预览 - 表格形式显示监控点信息</li>
                    <li>自动清理机制 - 智能管理监控点文件版本</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📍 监控点管理系统详解</h2>

            <div class="feature-highlight">
                <h3>🎯 系统特性</h3>
                <p>监控点管理系统是约束设置界面的核心功能之一，提供了完整的监控点数据处理能力：</p>

                <div class="feature-card">
                    <h4>📁 文件格式支持</h4>
                    <p><strong>JSON格式示例：</strong></p>
                    <pre><code>{
  "monitor_points": [
    {
      "name": "Point_1",
      "coordinates": [0.0, 0.0, 0.0],
      "id": 1,
      "created_time": "2025-06-23T00:00:00",
      "source": "user_input"
    }
  ],
  "monitor_points_count": 1,
  "monitor_points_source": "manual"
}</code></pre>

                    <p><strong>TXT格式示例：</strong></p>
                    <pre><code># 监控点坐标文件
# X, Y, Z
0.0, 0.0, 0.0
1.0, 1.0, 1.0
-0.5, 0.5, 2.0</code></pre>
                </div>

                <div class="feature-card">
                    <h4>✅ 数据验证机制</h4>
                    <ul>
                        <li><strong>格式验证</strong> - 检查文件格式和语法正确性</li>
                        <li><strong>坐标验证</strong> - 确保坐标数据为有效数值</li>
                        <li><strong>完整性检查</strong> - 验证必需字段的存在</li>
                        <li><strong>重复检测</strong> - 自动识别和处理重复的监控点</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h4>🔄 自动清理机制</h4>
                    <ul>
                        <li><strong>智能识别</strong> - 自动识别监控点文件模式</li>
                        <li><strong>时间排序</strong> - 按时间戳排序，保留最新文件</li>
                        <li><strong>数量控制</strong> - 支持自定义保留文件数量</li>
                        <li><strong>安全保护</strong> - 异常处理，确保清理过程安全</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>约束类型说明</h2>
            <h3>力约束</h3>
            <ul>
                <li><span class="menu-item">力文件选择</span> - 导入力的时间历程数据</li>
                <li><span class="menu-item">力的方向</span> - 定义力的作用方向</li>
                <li><span class="menu-item">作用位置</span> - 选择力的施加位置</li>
            </ul>

            <h3>位移约束</h3>
            <ul>
                <li><span class="menu-item">约束方向</span> - 选择限制的自由度</li>
                <li><span class="menu-item">约束值</span> - 设置位移量</li>
            </ul>

            <h3>远程位移</h3>
            <ul>
                <li><span class="menu-item">参考点</span> - 设置远程位移的参考点</li>
                <li><span class="menu-item">约束面</span> - 选择受约束的面</li>
                <li><span class="menu-item">位移量</span> - 设置位移大小</li>
            </ul>

            <h3>旋转约束</h3>
            <ul>
                <li><span class="menu-item">旋转速度</span> - 设置转速（r/min）</li>
                <li><span class="menu-item">旋转轴</span> - 定义旋转轴方向</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔧 监控点管理操作指南</h2>

            <div class="step-guide">
                <h3>完整操作流程</h3>
                <ol>
                    <li>
                        <strong>📂 访问监控点管理界面</strong>
                        <p>在主界面选择"约束设置"模块，切换到"监控点管理"选项卡（Tab 5）。界面包含文件导入区域、数据预览表格和操作按钮。</p>
                    </li>
                    <li>
                        <strong>📁 导入监控点文件</strong>
                        <p>点击"导入"按钮，选择监控点文件：
                        <ul>
                            <li><strong>JSON格式</strong> - 包含完整监控点信息的结构化数据</li>
                            <li><strong>TXT格式</strong> - 简单的坐标列表，每行一个监控点</li>
                        </ul>
                        系统会自动检测文件格式并进行相应处理。</p>
                    </li>
                    <li>
                        <strong>✅ 数据验证和预览</strong>
                        <p>文件导入后，系统会自动执行以下验证：
                        <ul>
                            <li>检查文件格式和语法正确性</li>
                            <li>验证坐标数据的有效性</li>
                            <li>确认必需字段的完整性</li>
                            <li>在表格中显示监控点预览</li>
                        </ul></p>
                    </li>
                    <li>
                        <strong>📊 查看监控点数据</strong>
                        <p>在数据预览表格中查看导入的监控点信息：
                        <ul>
                            <li><strong>名称</strong> - 监控点的标识名称</li>
                            <li><strong>X坐标</strong> - X方向坐标值</li>
                            <li><strong>Y坐标</strong> - Y方向坐标值</li>
                            <li><strong>Z坐标</strong> - Z方向坐标值</li>
                            <li><strong>ID</strong> - 监控点的唯一标识符</li>
                        </ul></p>
                    </li>
                    <li>
                        <strong>🔄 应用监控点设置</strong>
                        <p>确认数据无误后，点击"应用"按钮将监控点设置应用到当前项目。系统会：
                        <ul>
                            <li>保存监控点配置到项目文件</li>
                            <li>更新相关的约束设置</li>
                            <li>触发自动清理机制</li>
                        </ul></p>
                    </li>
                    <li>
                        <strong>🧹 自动文件清理</strong>
                        <p>系统会自动执行文件清理操作：
                        <ul>
                            <li>识别监控点文件模式</li>
                            <li>按时间戳排序文件</li>
                            <li>保留最新的文件版本</li>
                            <li>清理过期的临时文件</li>
                        </ul></p>
                    </li>
                </ol>
            </div>
        </div>

        <div class="section">
            <h2>🔒 传统约束设置操作</h2>

            <div class="step-guide">
                <h3>约束设置流程</h3>
                <ol>
                    <li><strong>力约束设置</strong>
                        <ul>
                            <li>点击"选择文件"导入力的时间历程数据</li>
                            <li>确认力的方向和大小</li>
                            <li>选择力的作用位置和面</li>
                        </ul>
                    </li>
                    <li><strong>位移约束设置</strong>
                        <ul>
                            <li>选择需要约束的自由度（X、Y、Z方向）</li>
                            <li>输入位移值或设置为固定约束</li>
                            <li>确认约束的作用位置</li>
                        </ul>
                    </li>
                    <li><strong>远程位移设置</strong>
                        <ul>
                            <li>定义远程位移的参考点位置</li>
                            <li>选择受约束的面或节点</li>
                            <li>设置位移参数和约束类型</li>
                        </ul>
                    </li>
                    <li><strong>旋转约束设置</strong>
                        <ul>
                            <li>输入旋转速度（r/min）</li>
                            <li>定义旋转轴的方向和位置</li>
                            <li>设置旋转中心点</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </div>

        <div class="section">
            <div class="note">
                <h3>注意事项</h3>
                <ul>
                    <li>力文件格式必须符合要求</li>
                    <li>确保约束不会相互冲突</li>
                    <li>检查约束的单位是否正确</li>
                    <li>旋转速度必须为正数</li>
                    <li>远程位移的参考点位置要合理</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>❓ 常见问题解答</h2>

            <div class="faq-section">
                <h3>📍 监控点管理相关问题</h3>

                <div class="faq-item">
                    <h4>Q: 支持哪些监控点文件格式？</h4>
                    <p>A: 系统支持两种主要格式：</p>
                    <ul>
                        <li><strong>JSON格式</strong> - 包含完整监控点信息的结构化数据，支持名称、坐标、ID、创建时间等字段</li>
                        <li><strong>TXT格式</strong> - 简单的坐标列表，每行格式为 "X, Y, Z"，支持注释行（以#开头）</li>
                    </ul>
                </div>

                <div class="faq-item">
                    <h4>Q: 监控点文件导入失败怎么办？</h4>
                    <p>A: 请检查以下几个方面：</p>
                    <ul>
                        <li>确保文件编码为UTF-8格式</li>
                        <li>验证JSON文件语法正确性</li>
                        <li>检查坐标数据是否为有效数值</li>
                        <li>确认文件路径和权限正确</li>
                    </ul>
                </div>

                <div class="faq-item">
                    <h4>Q: 自动清理机制如何工作？</h4>
                    <p>A: 系统会自动执行以下清理操作：</p>
                    <ul>
                        <li>识别监控点文件的命名模式</li>
                        <li>按时间戳对文件进行排序</li>
                        <li>保留最新的文件版本</li>
                        <li>清理超过保留数量的旧文件</li>
                    </ul>
                </div>

                <div class="faq-item">
                    <h4>Q: 如何创建正确的监控点文件？</h4>
                    <p>A: 建议按照以下步骤：</p>
                    <ul>
                        <li>参考项目中提供的示例文件格式</li>
                        <li>确保坐标数据的单位一致</li>
                        <li>为监控点提供有意义的名称</li>
                        <li>保存文件时选择UTF-8编码</li>
                    </ul>
                </div>
            </div>

            <div class="faq-section">
                <h3>🔒 传统约束设置问题</h3>

                <div class="faq-item">
                    <h4>Q: 力文件格式要求是什么？</h4>
                    <p>A: 力文件应为文本格式，包含时间和力值两列数据，用空格或逗号分隔。第一行应包含列标题。确保数据单位正确且时间步长一致。</p>
                </div>

                <div class="faq-item">
                    <h4>Q: 如何处理约束冲突？</h4>
                    <p>A: 检查是否在同一位置施加了相互矛盾的约束，确保约束之间的独立性。必要时可以调整约束位置或类型，避免过约束问题。</p>
                </div>

                <div class="faq-item">
                    <h4>Q: 远程位移参考点如何选择？</h4>
                    <p>A: 参考点通常选择在约束面的中心或特征点位置，应避免选择会导致不合理变形的位置。确保参考点与约束面之间有合理的距离。</p>
                </div>

                <div class="faq-item">
                    <h4>Q: 旋转约束设置有什么注意事项？</h4>
                    <p>A: 旋转速度必须为正数，旋转轴方向要明确定义，确保旋转中心点位置合理。注意单位换算，系统使用r/min作为旋转速度单位。</p>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="note">
                <h3>💡 使用技巧</h3>
                <ul>
                    <li><strong>文件管理</strong> - 定期检查监控点文件，确保数据的准确性和时效性</li>
                    <li><strong>数据备份</strong> - 重要的监控点配置建议进行备份</li>
                    <li><strong>格式统一</strong> - 团队协作时统一使用相同的文件格式和命名规范</li>
                    <li><strong>验证检查</strong> - 导入后务必检查预览数据的正确性</li>
                    <li><strong>版本控制</strong> - 利用自动清理机制，但重要版本可手动备份</li>
                </ul>
            </div>
        </div>

        <a href="index.html" class="back-link">返回目录</a>
    </div>
</body>
</html> 