#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复版打包脚本

解决了所有已知的依赖和路径问题：
1. logs目录问题
2. unittest模块问题
3. scipy/numpy依赖问题

使用方法:
    python package_final.py
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import time

def print_step(step_num, total_steps, message):
    """打印步骤信息"""
    print(f"[{step_num}/{total_steps}] {message}")

def ensure_logs_directory():
    """确保logs目录存在并包含必要的文件"""
    print_step(1, 6, "准备日志目录...")
    
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    # 创建必要的日志文件（空文件）
    log_files = [
        "app.log",
        "error.log", 
        "performance.log",
        "resource_manager.log",
        "startup_config.log",
        "exceptions.log"
    ]
    
    for log_file in log_files:
        log_path = logs_dir / log_file
        if not log_path.exists():
            log_path.touch()
    
    print(f"   ✅ 日志目录准备完成: {logs_dir}")

def check_dependencies():
    """检查关键依赖"""
    print_step(2, 6, "检查依赖包...")
    
    critical_packages = [
        'PySide6', 'numpy', 'scipy', 'pandas', 'matplotlib', 
        'fastapi', 'uvicorn', 'openpyxl'
    ]
    
    missing_packages = []
    for package in critical_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"   ⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("   💡 请运行: pip install -r requirements.txt")
        return False
    
    print("   ✅ 所有依赖包检查通过")
    return True

def clean_build_dirs():
    """清理构建目录"""
    print_step(3, 6, "清理旧的构建文件...")
    
    dirs_to_clean = ['dist', 'build']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"   🧹 清理目录: {dir_name}")
            shutil.rmtree(dir_name)
        else:
            print(f"   📁 目录不存在: {dir_name}")

def run_pyinstaller():
    """运行PyInstaller"""
    print_step(4, 6, "执行PyInstaller打包...")
    
    # 使用修复后的spec文件
    cmd = [
        sys.executable, "-m", "PyInstaller", 
        "--clean", "--noconfirm", 
        "qt_new_simple.spec"
    ]
    
    print(f"   🔧 执行命令: {' '.join(cmd)}")
    print("   ⏳ 打包中，这可能需要几分钟...")
    
    start_time = time.time()
    
    try:
        # 实时显示输出
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            text=True,
            universal_newlines=True
        )
        
        # 读取输出
        output_lines = []
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                output_lines.append(output.strip())
                # 只显示重要信息
                if any(keyword in output.lower() for keyword in ['info:', 'warning:', 'error:', 'building']):
                    print(f"   {output.strip()}")
        
        rc = process.poll()
        end_time = time.time()
        
        if rc == 0:
            print(f"   ✅ 打包成功! 耗时: {end_time - start_time:.1f}秒")
            return True
        else:
            print(f"   ❌ 打包失败! 返回码: {rc}")
            # 显示最后几行输出
            print("   最后的输出:")
            for line in output_lines[-10:]:
                print(f"     {line}")
            return False
            
    except Exception as e:
        print(f"   ❌ 打包过程发生异常: {e}")
        return False

def verify_package():
    """验证打包结果"""
    print_step(5, 6, "验证打包结果...")
    
    dist_dir = Path("dist/vibration_transfer")
    if not dist_dir.exists():
        print(f"   ❌ 输出目录不存在: {dist_dir}")
        return False
    
    exe_file = dist_dir / "振动传递计算软件.exe"
    if not exe_file.exists():
        print(f"   ❌ 可执行文件不存在: {exe_file}")
        return False
    
    # 检查文件大小
    size_mb = exe_file.stat().st_size / (1024 * 1024)
    print(f"   📊 可执行文件大小: {size_mb:.2f} MB")
    
    # 检查关键目录
    required_dirs = [
        "assets", "help", "config", "translations", 
        "logs", "originscript", "ui", "core", "views", "ctrl"
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = dist_dir / dir_name
        if dir_path.exists():
            print(f"   ✅ {dir_name}")
        else:
            missing_dirs.append(dir_name)
            print(f"   ⚠️ 缺失: {dir_name}")
    
    # 统计总大小
    total_size = sum(f.stat().st_size for f in dist_dir.rglob('*') if f.is_file())
    total_size_mb = total_size / (1024 * 1024)
    print(f"   📊 总包大小: {total_size_mb:.2f} MB")
    
    if missing_dirs:
        print(f"   ⚠️ 缺失目录: {', '.join(missing_dirs)}")
    
    print("   ✅ 打包结果验证完成")
    return True

def generate_summary():
    """生成打包总结"""
    print_step(6, 6, "生成打包总结...")
    
    dist_dir = Path("dist/vibration_transfer")
    exe_file = dist_dir / "振动传递计算软件.exe"
    
    print("\n" + "=" * 60)
    print("🎉 打包完成总结")
    print("=" * 60)
    
    if exe_file.exists():
        print(f"✅ 可执行文件: {exe_file}")
        print(f"📁 输出目录: {dist_dir}")
        print(f"📊 文件大小: {exe_file.stat().st_size / (1024 * 1024):.2f} MB")
        
        print("\n📋 使用说明:")
        print("1. 将整个 vibration_transfer 目录复制到目标机器")
        print("2. 双击 '振动传递计算软件.exe' 运行程序")
        print("3. 确保目标机器安装了 Visual C++ Redistributable")
        
        print("\n💡 测试建议:")
        print("1. 在当前机器上测试程序启动")
        print("2. 测试各个功能模块")
        print("3. 在干净的Windows环境中测试")
        
        return True
    else:
        print("❌ 打包失败，未找到可执行文件")
        return False

def main():
    """主函数"""
    print("🚀 振动传递计算软件 - 最终修复版打包工具")
    print("=" * 60)
    print("解决问题: logs目录、unittest模块、scipy/numpy依赖")
    print("=" * 60)
    
    try:
        # 1. 准备日志目录
        ensure_logs_directory()
        
        # 2. 检查依赖
        if not check_dependencies():
            print("\n❌ 依赖检查失败，请安装缺失的包后重试")
            sys.exit(1)
        
        # 3. 清理旧文件
        clean_build_dirs()
        
        # 4. 执行打包
        if not run_pyinstaller():
            print("\n❌ 打包失败!")
            sys.exit(1)
        
        # 5. 验证结果
        if not verify_package():
            print("\n❌ 打包验证失败!")
            sys.exit(1)
        
        # 6. 生成总结
        if not generate_summary():
            sys.exit(1)
        
        print("\n🎉 所有步骤完成!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 打包过程发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
