#!/usr/bin/env python3
"""
帮助文档模块 - 浏览器启动版本

此模块提供帮助文档的浏览器启动功能，替代传统的Qt对话框界面。
使用系统默认浏览器打开帮助文档，提供更好的用户体验。

特性:
- 使用系统默认浏览器打开帮助文档
- 保持现代化HTML文件优先显示
- 完整的国际化支持和错误处理
- 智能备用文件机制
- 简化的用户界面和操作流程

作者: [作者名]
日期: [日期]
版本: v2.0.0 - 浏览器启动版本
"""

import os
import logging
from typing import Optional

from PySide6.QtWidgets import QMessageBox, QApplication
from PySide6.QtCore import QObject

# 导入新的帮助文档启动器
from .help_browser_launcher import HelpBrowserLauncher
from .help_section_dialog import HelpSectionDialog

# 导入国际化支持
from core.i18n_manager import tr

logger = logging.getLogger(__name__)


class HelpDialog(QObject):
    """帮助文档启动器

    使用系统默认浏览器打开帮助文档，替代传统的Qt对话框界面
    
    特性:
    - 使用系统默认浏览器打开帮助文档
    - 保持现代化HTML文件优先显示
    - 完整的国际化支持和错误处理
    - 智能备用文件机制
    - 简化的用户界面和操作流程
    """

    def __init__(self, help_file_path: str, parent=None):
        """初始化帮助文档启动器
        
        Args:
            help_file_path: 帮助文档主文件路径（通常是index.html）
            parent: 父对象
        """
        super().__init__(parent)

        # 初始化成员变量
        self.help_file_path = help_file_path
        self.base_dir = os.path.dirname(help_file_path)
        self.parent_widget = parent

        # 创建浏览器启动器
        try:
            self.browser_launcher = HelpBrowserLauncher(self.base_dir, parent)
            logger.info("HelpDialog 初始化完成 - 浏览器启动模式")
        except Exception as e:
            logger.error(f"初始化帮助文档启动器失败: {e}")
            raise

    def show(self) -> None:
        """显示帮助文档选择对话框并在浏览器中打开"""
        try:
            # 获取可用的帮助章节
            available_sections = self.browser_launcher.get_available_help_sections()
            
            if not available_sections:
                self._show_error_message(
                    tr("帮助文档不可用", "HelpDialog"),
                    tr("没有找到可用的帮助文档文件。\n\n请检查帮助文档是否已正确安装。", "HelpDialog")
                )
                return
            
            # 显示章节选择对话框
            selected_section = HelpSectionDialog.select_help_section(available_sections, self.parent_widget)
            
            if selected_section:
                # 在浏览器中打开选中的章节
                success = self.browser_launcher.open_help_section(selected_section)
                if not success:
                    logger.warning(f"打开帮助章节失败: {selected_section}")
            else:
                logger.debug("用户取消了帮助文档选择")
                
        except Exception as e:
            logger.error(f"显示帮助文档失败: {e}", exc_info=True)
            self._show_error_message(
                tr("帮助文档错误", "HelpDialog"),
                tr("显示帮助文档时发生错误：{}", "HelpDialog").format(str(e))
            )

    def exec(self) -> None:
        """执行帮助文档显示（兼容旧接口）"""
        self.show()

    def open_help(self, file_name: str = "index.html") -> bool:
        """直接打开指定的帮助文档文件
        
        Args:
            file_name: 帮助文件名
            
        Returns:
            bool: 是否成功打开
        """
        return self.browser_launcher.open_help(file_name)

    def open_help_section(self, section: str) -> bool:
        """打开特定的帮助文档章节
        
        Args:
            section: 章节名称
            
        Returns:
            bool: 是否成功打开
        """
        return self.browser_launcher.open_help_section(section)

    def _show_error_message(self, title: str, message: str) -> None:
        """显示错误消息对话框
        
        Args:
            title: 对话框标题
            message: 错误消息
        """
        try:
            msg_box = QMessageBox(self.parent_widget)
            msg_box.setIcon(QMessageBox.Icon.Warning)
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
            msg_box.exec()
        except Exception as e:
            logger.error(f"显示错误消息失败: {e}")


# 为了向后兼容，保留一些静态方法
def show_help_dialog(help_file_path: str, parent=None) -> None:
    """显示帮助文档（静态方法，向后兼容）
    
    Args:
        help_file_path: 帮助文档主文件路径
        parent: 父窗口
    """
    try:
        help_dialog = HelpDialog(help_file_path, parent)
        help_dialog.show()
    except Exception as e:
        logger.error(f"显示帮助文档失败: {e}")
        QMessageBox.critical(
            parent,
            tr("帮助文档错误", "HelpDialog"),
            tr("无法显示帮助文档：{}", "HelpDialog").format(str(e))
        )


def open_help_in_browser(help_base_dir: str, file_name: str = "index.html", parent=None) -> bool:
    """在浏览器中打开帮助文档（静态方法）
    
    Args:
        help_base_dir: 帮助文档基础目录
        file_name: 文件名
        parent: 父窗口
        
    Returns:
        bool: 是否成功打开
    """
    try:
        launcher = HelpBrowserLauncher(help_base_dir, parent)
        return launcher.open_help(file_name)
    except Exception as e:
        logger.error(f"在浏览器中打开帮助文档失败: {e}")
        return False


def open_help_section_in_browser(help_base_dir: str, section: str, parent=None) -> bool:
    """在浏览器中打开特定帮助章节（静态方法）
    
    Args:
        help_base_dir: 帮助文档基础目录
        section: 章节名称
        parent: 父窗口
        
    Returns:
        bool: 是否成功打开
    """
    try:
        launcher = HelpBrowserLauncher(help_base_dir, parent)
        return launcher.open_help_section(section)
    except Exception as e:
        logger.error(f"在浏览器中打开帮助章节失败: {e}")
        return False


def show_help_section_selector(help_base_dir: str, parent=None) -> Optional[str]:
    """显示帮助章节选择器（静态方法）
    
    Args:
        help_base_dir: 帮助文档基础目录
        parent: 父窗口
        
    Returns:
        str: 选中的章节，如果取消则返回None
    """
    try:
        launcher = HelpBrowserLauncher(help_base_dir, parent)
        available_sections = launcher.get_available_help_sections()
        
        if not available_sections:
            QMessageBox.warning(
                parent,
                tr("帮助文档不可用", "HelpDialog"),
                tr("没有找到可用的帮助文档文件。", "HelpDialog")
            )
            return None
        
        return HelpSectionDialog.select_help_section(available_sections, parent)
        
    except Exception as e:
        logger.error(f"显示帮助章节选择器失败: {e}")
        return None


# 快捷方法
def quick_help(help_base_dir: str, parent=None) -> None:
    """快速显示帮助文档（直接打开概述页面）
    
    Args:
        help_base_dir: 帮助文档基础目录
        parent: 父窗口
    """
    open_help_in_browser(help_base_dir, "index.html", parent)


def quick_help_section(help_base_dir: str, section: str, parent=None) -> None:
    """快速打开特定帮助章节
    
    Args:
        help_base_dir: 帮助文档基础目录
        section: 章节名称
        parent: 父窗口
    """
    open_help_section_in_browser(help_base_dir, section, parent)
