<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>振动传递计算软件帮助文档 - 增强动画</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6; /* blue-500 */
            --secondary-color: #1e3a8a; /* blue-800 */
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        #hero-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
            background: linear-gradient(180deg, #111827 0%, #1f2937 100%);
        }
        .header-content {
            position: relative;
            z-index: 1;
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.2);
        }
        .quick-start-step {
            display: flex;
            align-items: center;
            background-color: #f9fafb;
            padding: 1rem;
            border-radius: 0.75rem;
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        .quick-start-step:hover {
            border-color: var(--primary-color);
            background-color: #eff6ff;
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* New Header Text Animation */
        @keyframes text-fade-in-up {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .animate-title {
            animation: text-fade-in-up 0.8s ease-out forwards;
        }
        .animate-subtitle {
            opacity: 0;
            animation: text-fade-in-up 0.8s ease-out 0.3s forwards;
        }

    </style>
</head>
<body class="antialiased">

    <!-- Header & Animated Background -->
    <header class="relative overflow-hidden text-white py-20 md:py-32">
        <canvas id="hero-canvas"></canvas>
        <div class="header-content container mx-auto px-6 text-center">
            <h1 class="text-4xl md:text-6xl font-extrabold tracking-tighter leading-tight animate-title">
                振动传递计算软件
            </h1>
            <p class="mt-4 text-lg md:text-xl max-w-3xl mx-auto text-gray-300 animate-subtitle">
                一个强大、直观的帮助中心，助您探索软件的全部潜力。
            </p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">

        <!-- Software Interface Navigation -->
        <section id="interfaces" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">软件界面导航</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
                 <!-- Interface items will be injected by JS -->
            </div>
        </section>

        <!-- Quick Start Guide -->
        <section id="quick-start" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">九步快速入门</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                 <!-- Quick start steps will be injected by JS -->
            </div>
        </section>

        <!-- New Features v0.2.1 -->
        <section id="new-features" class="mb-12 scroll-reveal">
             <div class="text-center mb-8">
                <span class="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full">v0.2.1 版本</span>
                <h2 class="text-3xl font-bold text-gray-800 mt-2">核心功能亮点</h2>
                <p class="text-gray-600 mt-2 max-w-2xl mx-auto">探索旨在简化工作流程并提高精度的最新增强功能。</p>
            </div>
            <div class="space-y-4">
                <!-- Accordion for features will be injected by JS -->
            </div>
        </section>
        
        <!-- Notes & Warnings -->
        <section id="notes" class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6">
                <h3 class="font-bold text-xl mb-4 text-gray-800">重要提示</h3>
                <ul class="space-y-2 list-disc list-inside text-gray-600">
                    <li>使用软件前请确保已正确安装所有必要的组件。</li>
                    <li>定期保存项目文件，避免数据丢失。</li>
                    <li>参数设置时注意单位的一致性。</li>
                    <li>语言切换功能需要重新生成翻译文件后才能使用。</li>
                    <li>线程安全机制自动启用，无需手动配置。</li>
                </ul>
            </div>
            <div class="section-card p-6 border-l-4 border-yellow-400">
                 <h3 class="font-bold text-xl mb-4 text-yellow-800">常见问题与警告</h3>
                 <ul class="space-y-2 list-disc list-inside text-gray-600">
                    <li>工作目录不支持中文路径，请使用英文路径。</li>
                    <li>确保ANSYS Workbench已正确安装并配置。</li>
                    <li>程序运行前需完成ANSYS路径配置。</li>
                    <li>API服务器默认使用8000端口，确保该端口未被占用。</li>
                 </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-400 py-8">
        <div class="container mx-auto px-6 text-center">
             <p>&copy; 2025 振动传递计算软件团队. All Rights Reserved.</p>
             <p class="text-sm mt-2">技术支持: <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300"><EMAIL></a></p>
        </div>
    </footer>

    <script>
    document.addEventListener('DOMContentLoaded', () => {

        // --- Data Definitions (same as before) ---
        const interfaces = [ { name: '主界面', desc: '主要操作界面', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6z" /></svg>`}, { name: '网格划分', desc: '模型网格参数', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" /></svg>`}, { name: '前处理', desc: '导入几何模型', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" /></svg>`}, { name: '连接设置', desc: '设置连接属性', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>`}, { name: '分析设置', desc: '配置计算参数', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>`}, { name: '约束设置', desc: '定义边界条件', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0v4M5 9h14l1 12H4L5 9z" /></svg>`}, { name: '结果显示', desc: '查看分析结果', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" /></svg>`}, { name: '振动分析', desc: '高级数据分析', icon: `<svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" /></svg>`},];
        const quickStartSteps = [ "创建/打开项目", "设置网格参数", "导入几何模型", "设置连接参数", "配置分析参数", "定义约束条件", "运行分析", "查看结果", "导出报告" ];
        const newFeatures = [ { title: "ANSYS四合一自动化脚本", icon: "🔧", content: "全新的智能化ANSYS Workbench自动化处理系统，涵盖清理、创建、规范化命名选择及导出功能，并提供7步骤实时进度监控。" }, { title: "多编码处理系统", icon: "🌐", content: "完整的编码兼容性和中文支持，可自动检测并转换UTF-8、GBK、CP936编码，智能恢复错误并完美显示中文日志。" }, { title: "监控点管理系统", icon: "📍", content: "支持JSON和TXT格式，提供自动数据验证、格式检查、文件清理及数据实时预览，实现版本化文件管理。" }, { title: "线程安全架构", icon: "🛡️", content: "增强的多线程处理和安全机制，通过多个监控线程和安全的UI更新机制，确保系统稳定运行，消除数据竞争。" } ];

        // --- Dynamic Content Injection (same as before) ---
        const interfacesContainer = document.querySelector('#interfaces .grid');
        interfaces.forEach(item => { const div = document.createElement('a'); div.href = "#"; div.className = 'text-center p-4 bg-gray-50 hover:bg-white rounded-lg transition duration-300 ease-in-out hover:shadow-md'; div.innerHTML = `<div class="flex items-center justify-center h-16 w-16 mx-auto bg-blue-100 text-blue-600 rounded-full mb-3">${item.icon}</div><h4 class="font-semibold text-gray-700">${item.name}</h4><p class="text-sm text-gray-500">${item.desc}</p>`; interfacesContainer.appendChild(div); });
        const quickStartContainer = document.querySelector('#quick-start .grid');
        quickStartSteps.forEach((step, i) => { const div = document.createElement('div'); div.className = 'quick-start-step'; div.innerHTML = `<div class="flex-shrink-0 w-10 h-10 flex items-center justify-center bg-blue-500 text-white rounded-full font-bold text-lg mr-4">${i + 1}</div><span class="text-gray-700 font-medium">${step}</span>`; quickStartContainer.appendChild(div); });
        const featuresContainer = document.querySelector('#new-features .space-y-4');
        newFeatures.forEach(feature => { const details = document.createElement('details'); details.className = 'feature-accordion section-card overflow-hidden'; details.innerHTML = `<summary class="font-bold text-lg text-gray-800"><span class="flex items-center"><span class="text-2xl mr-4">${feature.icon}</span>${feature.title}</span></summary><div class="feature-accordion-content text-gray-600"><p>${feature.content}</p></div>`; featuresContainer.appendChild(details); });


        // --- Three.js Animated & Optimized Background ---
        let scene, camera, renderer, particles;
        let mouse = new THREE.Vector2(-100, -100); 
        let targetMouse = new THREE.Vector2(-100, -100);
        let originalPositions;
        const clock = new THREE.Clock();

        function initThree() {
            const canvas = document.getElementById('hero-canvas');
            const container = canvas.parentElement;

            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(50, container.clientWidth / container.clientHeight, 1, 1000);
            camera.position.z = 100;

            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            renderer.setSize(container.clientWidth, container.clientHeight);

            const geometry = new THREE.PlaneGeometry(300, 300, 50, 50); 
            originalPositions = Float32Array.from(geometry.attributes.position.array);

            const material = new THREE.PointsMaterial({
                color: 0x60a5fa,
                size: 1.0, // **修改**: 将球体半径从 1.5 减小到 1.0
                sizeAttenuation: true,
                transparent: true,
                opacity: 0.8,
                blending: THREE.AdditiveBlending
            });

            particles = new THREE.Points(geometry, material);
            scene.add(particles);

            window.addEventListener('resize', onWindowResize, false);
            container.addEventListener('mousemove', onMouseMove, false);
            container.addEventListener('mouseleave', onMouseLeave, false);
            animate();
        }

        function onWindowResize() {
            const container = document.getElementById('hero-canvas').parentElement;
            camera.aspect = container.clientWidth / container.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(container.clientWidth, container.clientHeight);
        }

        function onMouseMove(event) {
            const rect = renderer.domElement.getBoundingClientRect();
            targetMouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
            targetMouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
        }

        function onMouseLeave(event) {
            targetMouse.x = -100;
            targetMouse.y = -100;
        }

        function animate() {
            requestAnimationFrame(animate);
            const elapsedTime = clock.getElapsedTime();
            const positions = particles.geometry.attributes.position.array;

            mouse.x += (targetMouse.x - mouse.x) * 0.05;
            mouse.y += (targetMouse.y - mouse.y) * 0.05;

            const vFOV = camera.fov * Math.PI / 180;
            const height = 2 * Math.tan(vFOV / 2) * camera.position.z;
            const width = height * camera.aspect;
            const mouseWorld = new THREE.Vector3(mouse.x * width / 2, mouse.y * height / 2, 0);

            for (let i = 0; i < positions.length; i += 3) {
                const x = originalPositions[i];
                const y = originalPositions[i + 1];

                const vec = new THREE.Vector3(x, y, 0);
                const dist = vec.distanceTo(mouseWorld);
                
                const maxDisplacement = 10;
                const effectRadius = 60;
                const displacement = Math.max(0, maxDisplacement - (dist / effectRadius) * maxDisplacement);
                
                const ripple = Math.sin(dist * 0.4 - elapsedTime * 3.0) * displacement;
                const bgWave = Math.sin(x * 0.1 + elapsedTime * 0.5) * 0.5 + Math.cos(y * 0.1 + elapsedTime * 0.5) * 0.5;

                positions[i + 2] = ripple + bgWave;
            }

            particles.geometry.attributes.position.needsUpdate = true;
            renderer.render(scene, camera);
        }

        initThree();

        // --- Scroll Reveal Animation (same as before) ---
        const scrollElements = document.querySelectorAll('.scroll-reveal');
        const elementInView = (el, dividend = 1) => { const elementTop = el.getBoundingClientRect().top; return (elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend); };
        const handleScrollAnimation = () => { scrollElements.forEach((el) => { if (elementInView(el, 1.1)) el.classList.add('visible'); }) }
        window.addEventListener('scroll', handleScrollAnimation);
        handleScrollAnimation();
    });
    </script>
</body>
</html>
