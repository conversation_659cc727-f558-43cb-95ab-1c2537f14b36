"""
启动画面资源生成器

此模块用于生成启动画面所需的资源文件，包括：
1. 背景图片生成
2. 高DPI图片支持
3. 动画资源准备

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import json
from typing import Dict, Any, Optional
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QLinearGradient, QBrush, QPen, QConicalGradient
from PySide6.QtWidgets import QApplication


class SplashResourceGenerator:
    """启动画面资源生成器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化资源生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.assets_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "assets")
        self.splash_dir = os.path.join(self.assets_dir, "splash")
        
        # 确保目录存在
        os.makedirs(self.splash_dir, exist_ok=True)
    
    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 配置字典
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                     "config", "splash_config.json")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 返回默认配置
            return {
                "splash_screen": {
                    "layout": {"width": 480, "height": 320},
                    "colors": {
                        "primary": "#3498db",
                        "secondary": "#2ecc71",
                        "text": "#34495e"
                    }
                }
            }
    
    def generate_background_image(self, scale_factor: float = 1.0) -> QPixmap:
        """生成背景图片
        
        Args:
            scale_factor: 缩放因子，用于高DPI支持
            
        Returns:
            QPixmap: 生成的背景图片
        """
        config = self.config["splash_screen"]
        layout = config["layout"]
        colors = config["colors"]
        
        # 计算实际尺寸
        width = int(layout["width"] * scale_factor)
        height = int(layout["height"] * scale_factor)
        
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建主背景渐变
        gradient = QLinearGradient(0, 0, width, height)
        primary_color = QColor(colors["primary"])
        secondary_color = QColor(colors["secondary"])
        
        gradient.setColorAt(0, QColor(primary_color.red(), primary_color.green(), primary_color.blue(), 240))
        gradient.setColorAt(0.5, QColor(255, 255, 255, 250))
        gradient.setColorAt(1, QColor(secondary_color.red(), secondary_color.green(), secondary_color.blue(), 240))
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 15 * scale_factor, 15 * scale_factor)
        
        # 添加装饰性元素
        self._add_decorative_elements(painter, width, height, scale_factor)
        
        painter.end()
        
        # 设置设备像素比
        pixmap.setDevicePixelRatio(scale_factor)
        
        return pixmap
    
    def _add_decorative_elements(self, painter: QPainter, width: int, height: int, scale_factor: float):
        """添加装饰性元素
        
        Args:
            painter: 绘制器
            width: 宽度
            height: 高度
            scale_factor: 缩放因子
        """
        # 添加一些几何装饰
        painter.setPen(QPen(QColor(255, 255, 255, 100), 2 * scale_factor))
        painter.setBrush(Qt.NoBrush)
        
        # 绘制装饰圆圈
        circle_size = int(100 * scale_factor)
        painter.drawEllipse(width - circle_size - 20, -20, circle_size, circle_size)
        painter.drawEllipse(-50, height - circle_size + 20, circle_size, circle_size)
        
        # 绘制装饰线条
        painter.setPen(QPen(QColor(255, 255, 255, 80), 1 * scale_factor))
        for i in range(5):
            y = int((height / 6) * (i + 1))
            painter.drawLine(0, y, int(width * 0.3), y)
            painter.drawLine(int(width * 0.7), y, width, y)
    
    def generate_all_backgrounds(self):
        """生成所有分辨率的背景图片"""
        scale_factors = [1.0, 1.25, 1.5, 2.0]  # 支持不同DPI
        
        for scale in scale_factors:
            pixmap = self.generate_background_image(scale)
            filename = f"splash_background_{int(scale*100)}.png"
            filepath = os.path.join(self.splash_dir, filename)
            pixmap.save(filepath, "PNG")
            print(f"生成背景图片: {filename}")
    
    def create_app_icon_variants(self):
        """创建应用图标的不同尺寸变体"""
        icon_path = os.path.join(self.assets_dir, "icons", "vibration_transfer_icon_alt.ico")
        
        if not os.path.exists(icon_path):
            print(f"警告: 找不到应用图标文件 {icon_path}")
            return
        
        original_pixmap = QPixmap(icon_path)
        if original_pixmap.isNull():
            print("警告: 无法加载应用图标")
            return
        
        # 生成不同尺寸的图标
        sizes = [32, 48, 64, 96, 128]
        
        for size in sizes:
            scaled_pixmap = original_pixmap.scaled(
                size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation
            )
            filename = f"app_icon_{size}.png"
            filepath = os.path.join(self.splash_dir, filename)
            scaled_pixmap.save(filepath, "PNG")
            print(f"生成图标变体: {filename}")
    
    def generate_loading_animation_frames(self, frame_count: int = 12):
        """生成加载动画帧
        
        Args:
            frame_count: 动画帧数
        """
        size = 32
        
        for i in range(frame_count):
            pixmap = QPixmap(size, size)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 创建旋转渐变
            gradient = QConicalGradient(size/2, size/2, i * (360 / frame_count))
            gradient.setColorAt(0, QColor(52, 152, 219, 255))
            gradient.setColorAt(0.5, QColor(52, 152, 219, 100))
            gradient.setColorAt(1, QColor(52, 152, 219, 0))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawEllipse(2, 2, size-4, size-4)
            
            painter.end()
            
            filename = f"loading_frame_{i:02d}.png"
            filepath = os.path.join(self.splash_dir, filename)
            pixmap.save(filepath, "PNG")
        
        print(f"生成加载动画帧: {frame_count} 帧")
    
    def generate_all_resources(self):
        """生成所有启动画面资源"""
        print("开始生成启动画面资源...")
        
        # 生成背景图片
        self.generate_all_backgrounds()
        
        # 生成图标变体
        self.create_app_icon_variants()
        
        # 生成动画帧
        self.generate_loading_animation_frames()
        
        print(f"所有资源已生成到: {self.splash_dir}")


def generate_splash_resources():
    """生成启动画面资源的便捷函数"""
    # 确保有QApplication实例
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    generator = SplashResourceGenerator()
    generator.generate_all_resources()


if __name__ == "__main__":
    generate_splash_resources()
