"""
模态计算确认对话框

此模块定义了模态计算确认对话框，用于：
1. 显示将要进行模态计算的网格列表
2. 显示计算参数设置
3. 提供确认和取消选项
4. 显示计算预估时间

作者: AI Assistant
日期: 2025-07-25
"""

import logging
from typing import List, Dict, Any
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QTextEdit, QHeaderView, QMessageBox, QWidget)
from PySide6.QtGui import QColor
from PySide6.QtGui import QFont

from core.mesh_manager import MeshParameter

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class ModalCalculationDialog(QDialog):
    """模态计算确认对话框"""
    
    # 信号定义
    calculation_confirmed = Signal(list, dict)  # 确认计算信号：(网格列表, 计算参数)
    
    def __init__(self, parent=None, selected_meshes: List[MeshParameter] = None, 
                 calculation_params: Dict[str, Any] = None):
        super().__init__(parent)
        
        self.selected_meshes = selected_meshes or []
        self.calculation_params = calculation_params or {}
        
        self._setup_ui()
        self._setup_connections()
        self._populate_data()
        
        logger.debug(f"模态计算确认对话框初始化完成，网格数量: {len(self.selected_meshes)}")
    
    def _setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("模态计算确认")
        self.setModal(True)
        self.resize(1200, 900)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)

        # 标题区域
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)

        # 图标
        icon_label = QLabel("🔬")
        icon_label.setStyleSheet("font-size: 24px; margin-right: 10px;")
        title_layout.addWidget(icon_label)

        # 标题
        title_label = QLabel("模态计算确认")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 0;")
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 状态指示器
        self.status_label = QLabel("准备开始计算")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                color: #2196f3;
                border: 1px solid #2196f3;
                border-radius: 12px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        title_layout.addWidget(self.status_label)

        layout.addWidget(title_widget)
        
        # 网格列表组
        mesh_group = QGroupBox("将要计算的网格")
        mesh_group.setMinimumSize(1000, 500)
        mesh_layout = QVBoxLayout(mesh_group)
        
        # 网格信息表格
        self.mesh_table = QTableWidget()
        self.mesh_table.setColumnCount(6)
        self.mesh_table.setHorizontalHeaderLabels([
            "网格名称", "尺寸(mm)", "单元类型", "当前状态", "预估时间", "计算状态"
        ])

        # 设置表格属性
        self.mesh_table.setAlternatingRowColors(True)
        self.mesh_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.mesh_table.verticalHeader().setVisible(False)
        self.mesh_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: white;
                gridline-color: #f0f0f0;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                border-bottom: 2px solid #ddd;
                font-weight: bold;
            }
        """)

        # 设置列宽
        header = self.mesh_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        mesh_layout.addWidget(self.mesh_table)
        layout.addWidget(mesh_group)
        
        # 计算参数组
        params_group = QGroupBox("计算参数")
        params_layout = QVBoxLayout(params_group)
        
        self.params_text = QTextEdit()
        self.params_text.setMaximumHeight(120)
        self.params_text.setReadOnly(True)
        params_layout.addWidget(self.params_text)
        
        layout.addWidget(params_group)
        
        # 计算摘要组
        summary_group = QGroupBox("计算摘要")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_label = QLabel()
        self.summary_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                border: 1px solid #b0c4de;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        summary_layout.addWidget(self.summary_label)
        
        layout.addWidget(summary_group)

        # 进度指示器区域（初始隐藏）
        self.progress_widget = QWidget()
        progress_layout = QVBoxLayout(self.progress_widget)
        progress_layout.setContentsMargins(0, 10, 0, 10)

        # 进度标签
        self.progress_label = QLabel("计算进度: 准备中...")
        self.progress_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        progress_layout.addWidget(self.progress_label)

        # 进度条
        from PySide6.QtWidgets import QProgressBar
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        self.progress_bar.setValue(0)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 4px;
            }
        """)
        progress_layout.addWidget(self.progress_bar)

        # 详细状态标签
        self.detail_status_label = QLabel("")
        self.detail_status_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                margin-top: 5px;
            }
        """)
        progress_layout.addWidget(self.detail_status_label)

        self.progress_widget.setVisible(False)  # 初始隐藏
        layout.addWidget(self.progress_widget)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumSize(100, 35)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
                border: 1px solid #34495e;
            }
            QPushButton:pressed {
                background-color: #34495e;
            }
        """)
        button_layout.addWidget(self.cancel_button)

        self.confirm_button = QPushButton("🚀 确认计算")
        self.confirm_button.setMinimumSize(120, 40)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 1px solid #1e8449;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #1e8449;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        button_layout.addWidget(self.confirm_button)
        
        layout.addLayout(button_layout)
    
    def _setup_connections(self):
        """设置信号连接"""
        self.cancel_button.clicked.connect(self.reject)
        self.confirm_button.clicked.connect(self._on_confirm)
    
    def _populate_data(self):
        """填充数据"""
        try:
            # 填充网格表格
            self.mesh_table.setRowCount(len(self.selected_meshes))
            
            total_estimated_time = 0
            for row, mesh in enumerate(self.selected_meshes):
                # 网格名称
                name_item = QTableWidgetItem(mesh.name)
                self.mesh_table.setItem(row, 0, name_item)
                
                # 尺寸
                size_item = QTableWidgetItem(f"{mesh.size:.2f}")
                self.mesh_table.setItem(row, 1, size_item)
                
                # 单元类型
                type_item = QTableWidgetItem(mesh.element_type.value)
                self.mesh_table.setItem(row, 2, type_item)
                
                # 当前状态
                status_item = QTableWidgetItem(mesh.status.value)
                self.mesh_table.setItem(row, 3, status_item)
                
                # 预估时间（基于网格尺寸的简单估算）
                estimated_time = max(30, int(100 / mesh.size))  # 简单的时间估算
                time_item = QTableWidgetItem(f"{estimated_time}秒")
                self.mesh_table.setItem(row, 4, time_item)

                # 计算状态（初始为等待）
                calc_status_item = QTableWidgetItem("等待中...")
                calc_status_item.setBackground(QColor("#f8f9fa"))
                calc_status_item.setForeground(QColor("#6c757d"))
                self.mesh_table.setItem(row, 5, calc_status_item)

                total_estimated_time += estimated_time
            
            # 填充计算参数
            params_text = []
            modal_count = self.calculation_params.get('modal_count', 10)
            params_text.append(f"模态阶数: {modal_count}")
            
            if self.calculation_params.get('limit_freq', True):
                freq_min = self.calculation_params.get('freq_min', 0.0)
                freq_max = self.calculation_params.get('freq_max', 1000.0)
                params_text.append(f"频率范围: {freq_min:.1f} ~ {freq_max:.1f} Hz")
            else:
                params_text.append("频率范围: 无限制")
            
            self.params_text.setPlainText("\n".join(params_text))
            
            # 填充计算摘要
            mesh_count = len(self.selected_meshes)
            total_time_min = total_estimated_time // 60
            total_time_sec = total_estimated_time % 60
            
            summary_text = f"""
计算摘要:
• 网格数量: {mesh_count} 个
• 总模态数: {mesh_count * modal_count} 个
• 预估总时间: {total_time_min}分{total_time_sec}秒
• 计算类型: 批量模态分析

注意事项:
• 计算过程中请勿关闭程序
• 可以随时暂停或停止计算
• 计算完成后可在结果对比页面查看
            """.strip()
            
            self.summary_label.setText(summary_text)
            
        except Exception as e:
            logger.error(f"填充对话框数据失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"填充对话框数据失败: {str(e)}")
    
    def _on_confirm(self):
        """确认按钮点击处理"""
        try:
            if not self.selected_meshes:
                QMessageBox.warning(self, "警告", "没有选择要计算的网格")
                return
            
            # 发射确认信号
            self.calculation_confirmed.emit(self.selected_meshes, self.calculation_params)
            
            # 关闭对话框
            self.accept()
            
            logger.info(f"用户确认进行模态计算，网格数量: {len(self.selected_meshes)}")
            
        except Exception as e:
            logger.error(f"确认计算失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"确认计算失败: {str(e)}")
    
    def get_selected_meshes(self) -> List[MeshParameter]:
        """获取选中的网格列表"""
        return self.selected_meshes
    
    def get_calculation_params(self) -> Dict[str, Any]:
        """获取计算参数"""
        return self.calculation_params

    def update_progress(self, current: int, total: int, current_mesh_name: str = "", status: str = ""):
        """更新计算进度

        Args:
            current: 当前完成的网格数
            total: 总网格数
            current_mesh_name: 当前处理的网格名称
            status: 当前状态描述
        """
        try:
            # 显示进度区域
            self.progress_widget.setVisible(True)

            # 更新进度条
            progress_percent = int((current / total) * 100) if total > 0 else 0
            self.progress_bar.setValue(progress_percent)

            # 更新进度标签
            self.progress_label.setText(f"计算进度: {current}/{total} ({progress_percent}%)")

            # 更新详细状态
            if current_mesh_name:
                if status:
                    detail_text = f"正在处理: {current_mesh_name} - {status}"
                else:
                    detail_text = f"正在处理: {current_mesh_name}"
            else:
                detail_text = status if status else "准备中..."

            self.detail_status_label.setText(detail_text)

            # 更新状态指示器
            if current >= total:
                self.status_label.setText("计算完成")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                        border-radius: 12px;
                        padding: 6px 12px;
                        font-weight: bold;
                        font-size: 11px;
                    }
                """)
            else:
                self.status_label.setText("计算中...")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #fff3cd;
                        color: #856404;
                        border: 1px solid #ffeaa7;
                        border-radius: 12px;
                        padding: 6px 12px;
                        font-weight: bold;
                        font-size: 11px;
                    }
                """)

            # 更新表格中的计算状态
            if current_mesh_name:
                for row in range(self.mesh_table.rowCount()):
                    mesh_name = self.mesh_table.item(row, 0).text()
                    if mesh_name == current_mesh_name:
                        status_item = QTableWidgetItem("计算中...")
                        status_item.setBackground(QColor("#fff3cd"))
                        status_item.setForeground(QColor("#856404"))
                        self.mesh_table.setItem(row, 5, status_item)
                        break

            logger.debug(f"更新计算进度: {current}/{total}, 当前网格: {current_mesh_name}")

        except Exception as e:
            logger.error(f"更新计算进度失败: {str(e)}", exc_info=True)

    def mark_mesh_completed(self, mesh_name: str, success: bool = True):
        """标记网格计算完成

        Args:
            mesh_name: 网格名称
            success: 是否成功完成
        """
        try:
            for row in range(self.mesh_table.rowCount()):
                if self.mesh_table.item(row, 0).text() == mesh_name:
                    if success:
                        status_item = QTableWidgetItem("✅ 完成")
                        status_item.setBackground(QColor("#d4edda"))
                        status_item.setForeground(QColor("#155724"))
                    else:
                        status_item = QTableWidgetItem("❌ 失败")
                        status_item.setBackground(QColor("#f8d7da"))
                        status_item.setForeground(QColor("#721c24"))

                    self.mesh_table.setItem(row, 5, status_item)
                    break

            logger.debug(f"标记网格 {mesh_name} 计算{'成功' if success else '失败'}")

        except Exception as e:
            logger.error(f"标记网格完成状态失败: {str(e)}", exc_info=True)
