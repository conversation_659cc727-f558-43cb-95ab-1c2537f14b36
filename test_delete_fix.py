"""
测试修复后的删除功能

验证CustomMessageBox API修复后删除功能是否正常工作

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_custom_message_box():
    """测试CustomMessageBox API"""
    print("🧪 测试CustomMessageBox API...")
    
    try:
        from views.custom_message_box import CustomMessageBox
        
        # 检查常量是否存在
        has_yes = hasattr(CustomMessageBox, 'Yes')
        has_no = hasattr(CustomMessageBox, 'No')
        has_question = hasattr(CustomMessageBox, 'question')
        
        print(f"  Yes常量: {'✅ 存在' if has_yes else '❌ 缺失'}")
        print(f"  No常量: {'✅ 存在' if has_no else '❌ 缺失'}")
        print(f"  question方法: {'✅ 存在' if has_question else '❌ 缺失'}")
        
        if has_yes and has_no:
            print(f"  Yes值: {CustomMessageBox.Yes}")
            print(f"  No值: {CustomMessageBox.No}")
        
        return has_yes and has_no and has_question
        
    except Exception as e:
        print(f"  ❌ CustomMessageBox测试失败: {str(e)}")
        return False

def test_delete_method_syntax():
    """测试删除方法语法"""
    print("\n🧪 测试删除方法语法...")
    
    try:
        from views.mesh_window_merged import MeshWindow
        from views.custom_message_box import CustomMessageBox
        
        # 检查方法是否存在
        has_delete_method = hasattr(MeshWindow, '_delete_imported_result')
        has_context_menu = hasattr(MeshWindow, '_show_comparison_context_menu')
        
        print(f"  删除方法: {'✅ 存在' if has_delete_method else '❌ 缺失'}")
        print(f"  右键菜单: {'✅ 存在' if has_context_menu else '❌ 缺失'}")
        
        # 模拟删除方法中的关键代码片段
        try:
            # 这些应该不会抛出语法错误
            yes_value = CustomMessageBox.Yes
            no_value = CustomMessageBox.No
            
            print(f"  常量访问: ✅ 正常 (Yes={yes_value}, No={no_value})")
            
            # 模拟比较操作
            reply = yes_value
            if reply == CustomMessageBox.Yes:
                print("  比较操作: ✅ 正常")
            
            return True
            
        except Exception as e:
            print(f"  ❌ 语法测试失败: {str(e)}")
            return False
        
    except Exception as e:
        print(f"  ❌ 删除方法测试失败: {str(e)}")
        return False

def test_data_manager_delete():
    """测试数据管理器删除功能"""
    print("\n🧪 测试数据管理器删除功能...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        
        # 创建测试数据管理器
        data_manager = ModalDataManager("test_delete_fix.pkl")
        
        # 导入一些测试数据
        if os.path.exists("test_data/reference_models.json"):
            success = data_manager.import_from_file("test_data/reference_models.json")
            print(f"  导入测试数据: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                # 获取导入的结果
                imported_results = data_manager.get_imported_results()
                print(f"  导入数据数量: {len(imported_results)}")
                
                if len(imported_results) > 0:
                    # 测试删除功能
                    original_count = len(imported_results)
                    delete_success = data_manager.remove_imported_result(0)
                    
                    print(f"  删除操作: {'✅ 成功' if delete_success else '❌ 失败'}")
                    
                    if delete_success:
                        remaining_results = data_manager.get_imported_results()
                        print(f"  删除后数量: {len(remaining_results)}")
                        print(f"  数量变化: {original_count} → {len(remaining_results)}")
                        
                        return len(remaining_results) == original_count - 1
                    
        return False
        
    except Exception as e:
        print(f"  ❌ 数据管理器删除测试失败: {str(e)}")
        return False

def simulate_delete_workflow():
    """模拟完整的删除工作流程"""
    print("\n🧪 模拟完整删除工作流程...")
    
    try:
        from ui.components.modal_data_manager import ModalDataManager
        from views.custom_message_box import CustomMessageBox
        
        # 步骤1: 创建数据管理器并导入数据
        print("  步骤1: 创建数据管理器并导入数据")
        data_manager = ModalDataManager("test_workflow_fix.pkl")
        
        if os.path.exists("test_data/benchmark_models.csv"):
            success = data_manager.import_from_file("test_data/benchmark_models.csv")
            print(f"    导入结果: {'✅ 成功' if success else '❌ 失败'}")
            
            if success:
                imported_results = data_manager.get_imported_results()
                print(f"    导入数量: {len(imported_results)}")
                
                # 步骤2: 模拟右键菜单触发
                print("  步骤2: 模拟右键菜单触发")
                if len(imported_results) > 0:
                    result_to_delete = imported_results[0]
                    print(f"    选中删除: {result_to_delete.name}")
                    
                    # 步骤3: 模拟确认对话框
                    print("  步骤3: 模拟确认对话框")
                    confirm_message = f"""确定要删除导入的结果吗？

名称: {result_to_delete.name}
网格尺寸: {result_to_delete.size:.2f}mm
模态数: {len(result_to_delete.frequencies)}

删除后将无法恢复。"""
                    
                    print(f"    确认信息: {confirm_message.replace(chr(10), ' | ')}")
                    
                    # 模拟用户选择"是"
                    user_choice = CustomMessageBox.Yes
                    print(f"    用户选择: {'是' if user_choice == CustomMessageBox.Yes else '否'}")
                    
                    # 步骤4: 执行删除
                    print("  步骤4: 执行删除")
                    if user_choice == CustomMessageBox.Yes:
                        delete_success = data_manager.remove_imported_result(0)
                        print(f"    删除结果: {'✅ 成功' if delete_success else '❌ 失败'}")
                        
                        if delete_success:
                            remaining_results = data_manager.get_imported_results()
                            print(f"    剩余数量: {len(remaining_results)}")
                            
                            # 步骤5: 模拟成功消息
                            print("  步骤5: 显示成功消息")
                            success_message = f"""已成功删除导入结果 '{result_to_delete.name}'

当前还有 {len(remaining_results)} 个导入结果可用于对比。

如果当前图表包含被删除的数据，请重新选择数据并更新图表。"""
                            
                            print(f"    成功消息: {success_message.replace(chr(10), ' | ')}")
                            
                            return True
        
        return False
        
    except Exception as e:
        print(f"  ❌ 工作流程模拟失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🎯 删除功能修复验证测试")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)
    
    # 测试各个组件
    message_box_ok = test_custom_message_box()
    syntax_ok = test_delete_method_syntax()
    data_manager_ok = test_data_manager_delete()
    workflow_ok = simulate_delete_workflow()
    
    print("\n" + "=" * 70)
    print("📋 修复验证结果:")
    print(f"CustomMessageBox API: {'✅ 正常' if message_box_ok else '❌ 异常'}")
    print(f"删除方法语法: {'✅ 正常' if syntax_ok else '❌ 异常'}")
    print(f"数据管理器删除: {'✅ 正常' if data_manager_ok else '❌ 异常'}")
    print(f"完整工作流程: {'✅ 正常' if workflow_ok else '❌ 异常'}")
    
    if all([message_box_ok, syntax_ok, data_manager_ok, workflow_ok]):
        print("\n🎉 删除功能修复成功！")
        print("\n✨ 修复内容:")
        print("  ✅ 移除了不存在的 StandardButton 引用")
        print("  ✅ 使用正确的 CustomMessageBox.Yes 常量")
        print("  ✅ 确认对话框API调用正确")
        print("  ✅ 删除操作流程完整")
        
        print("\n🎯 现在可以正常使用:")
        print("  • 右键点击导入结果项")
        print("  • 选择'🗑️ 删除导入结果'")
        print("  • 在确认对话框中点击'是'")
        print("  • 成功删除并刷新列表")
        
    else:
        print("\n⚠️ 部分功能仍有问题，请检查错误信息")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
