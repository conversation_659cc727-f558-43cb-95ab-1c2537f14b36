# UTF-8
#
# For more details about fixed file info 'ffi' see:
# http://msdn.microsoft.com/en-us/library/ms646997.aspx
VSVersionInfo(
  ffi=FixedFileInfo(
    # filevers and prodvers should be always a tuple with four items: (1, 2, 3, 4)
    # Set not needed items to zero 0.
    filevers=(1, 2, 0, 0),
    prodvers=(1, 2, 0, 0),
    # Contains a bitmask that specifies the valid bits 'flags'r
    mask=0x3f,
    # Contains a bitmask that specifies the Boolean attributes of the file.
    flags=0x0,
    # The operating system for which this file was designed.
    # 0x4 - NT and there is no need to change it.
    OS=0x4,
    # The general type of file.
    # 0x1 - the file is an application.
    fileType=0x1,
    # The function of the file.
    # 0x0 - the function is not defined for this fileType
    subtype=0x0,
    # Creation date and time stamp.
    date=(0, 0)
    ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'080404B0',
        [StringStruct(u'CompanyName', u'振动传递计算软件开发团队'),
        StringStruct(u'FileDescription', u'振动传递计算软件 - 基于Qt的工程计算应用'),
        StringStruct(u'FileVersion', u'*******'),
        StringStruct(u'InternalName', u'vibration_transfer'),
        StringStruct(u'LegalCopyright', u'Copyright © 2024-2025 振动传递计算软件开发团队'),
        StringStruct(u'OriginalFilename', u'振动传递计算软件.exe'),
        StringStruct(u'ProductName', u'振动传递计算软件'),
        StringStruct(u'ProductVersion', u'*******'),
        StringStruct(u'Comments', u'专业的振动传递分析计算软件，集成ANSYS自动化功能'),
        StringStruct(u'LegalTrademarks', u''),
        StringStruct(u'PrivateBuild', u''),
        StringStruct(u'SpecialBuild', u'优化版本 - 包含启动性能优化和字体管理功能')])
      ]),
    VarFileInfo([VarStruct(u'Translation', [2052, 1200])])
  ]
)
