<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1400</width>
    <height>900</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Microsoft YaHei UI</family>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>多网格管理系统</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <property name="spacing">
     <number>10</number>
    </property>
    <property name="leftMargin">
     <number>15</number>
    </property>
    <property name="topMargin">
     <number>15</number>
    </property>
    <property name="rightMargin">
     <number>15</number>
    </property>
    <property name="bottomMargin">
     <number>15</number>
    </property>
    <item>
     <widget class="QLabel" name="label_title">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>60</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei UI</family>
        <pointsize>24</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>网格无关性验证系统</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
      <property name="styleSheet">
       <string>QLabel {
    color: #34495e;
    background-color: transparent;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget_main">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <property name="styleSheet">
       <string>QTabWidget::pane {
    border: 1px solid #e9eaec;
    border-radius: 0 6px 6px 6px;
    background: white;
    top: -1px;
}

QTabBar::tab {
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    min-width: 120px;
    padding: 12px 20px;
    font-weight: 500;
    font-size: 11px;
}

QTabBar::tab:selected {
    background: white;
    border-bottom-color: white;
    color: #3498db;
}

QTabBar::tab:hover:!selected {
    background: #ecf0f1;
}</string>
      </property>
      <widget class="QWidget" name="tab_mesh_management">
       <attribute name="title">
        <string>网格管理</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_mesh_mgmt">
        <property name="spacing">
         <number>15</number>
        </property>
        <item>
         <widget class="QWidget" name="widget_left_panel" native="true">
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>650</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_left">
           <item>
            <widget class="QGroupBox" name="groupBox_mesh_params">
             <property name="title">
              <string>网格参数管理</string>
             </property>
             <property name="styleSheet">
              <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_params">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_toolbar">
                <item>
                 <widget class="QPushButton" name="btn_add_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>添加网格</string>
                  </property>
                  <property name="icon">
                   <iconset>
                    <normaloff>../assets/icons/add.png</normaloff>../assets/icons/add.png</iconset>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_import_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>导入配置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_export_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>导出配置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_toolbar">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QTableWidget" name="tableWidget_mesh_params">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>300</height>
                 </size>
                </property>
                <property name="alternatingRowColors">
                 <bool>true</bool>
                </property>
                <property name="selectionBehavior">
                 <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
                </property>
                <property name="gridStyle">
                 <enum>Qt::PenStyle::SolidLine</enum>
                </property>
                <property name="sortingEnabled">
                 <bool>true</bool>
                </property>
                <property name="styleSheet">
                 <string>QTableWidget {
    border: 1px solid #dcdfe6;
    background-color: white;
    gridline-color: #e9eaec;
    selection-background-color: #3498db;
    selection-color: white;
    alternate-background-color: #f5f7fa;
}

QHeaderView::section {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    padding: 8px;
    font-weight: bold;
    color: #34495e;
}</string>
                </property>
                <column>
                 <property name="text">
                  <string>网格名称</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>尺寸(mm)</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>状态</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>节点数</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>单元数</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>操作</string>
                 </property>
                </column>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_right_panel" native="true">
          <layout class="QVBoxLayout" name="verticalLayout_right">
           <item>
            <widget class="QGroupBox" name="groupBox_mesh_preview">
             <property name="title">
              <string>网格预览与详情</string>
             </property>
             <property name="styleSheet">
              <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_preview">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_preview_ctrl">
                <item>
                 <widget class="QLabel" name="label_select_mesh">
                  <property name="text">
                   <string>选择网格:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBox_mesh_select">
                  <property name="minimumSize">
                   <size>
                    <width>200</width>
                    <height>30</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_preview">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QLabel" name="label_mesh_preview">
                <property name="minimumSize">
                 <size>
                  <width>400</width>
                  <height>300</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                  <pointsize>16</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>网格预览区域
(将集成matplotlib画布)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
                <property name="styleSheet">
                 <string>QLabel {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background-color: #f9f9f9;
    color: #7f8c8d;
}</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QGroupBox" name="groupBox_mesh_stats">
                <property name="title">
                 <string>网格统计信息</string>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>120</height>
                 </size>
                </property>
                <layout class="QGridLayout" name="gridLayout_stats">
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_nodes">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>节点数:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_nodes_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #3498db;</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="0">
                  <widget class="QLabel" name="label_elements">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>单元数:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QLabel" name="label_elements_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #3498db;</string>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="0">
                  <widget class="QLabel" name="label_quality">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>网格质量:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="1">
                  <widget class="QLabel" name="label_quality_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #2ecc71;</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <item>
       <widget class="QPushButton" name="push_generatemesh">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>生成网格</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_result">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>查看结果</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_preui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>上一步(前处理)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_connectionui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>下一步(连接设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_mainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>宋体</family>
          <pointsize>20</pointsize>
         </font>
        </property>
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
