"""
日志查看器模块

此模块提供了一个图形化的日志查看界面，功能包括：
1. 按日志级别过滤（信息、警告、错误、调试、严重错误）
2. 文本搜索功能
3. 日志文件加载和刷新
4. 日志条目的详细查看

作者: [作者名]
日期: [日期]
"""

from PySide6.QtCore import Qt, QSortFilterProxyModel, QAbstractTableModel, QModelIndex
from PySide6.QtWidgets import (QVBoxLayout, QHBoxLayout, QPushButton,
                              QTableView, QHeaderView, QLabel, QLineEdit,
                              QDialog, QTextEdit, QWidget)
from PySide6.QtGui import QColor, QIcon, QFont

import os
import logging
import re
import sys

from .base_window import BaseWindow
from window_manager import WindowType

logger = logging.getLogger(__name__)

# 图标资源辅助函数
def get_icon(icon_name):
    """获取图标，如果不存在则使用系统标准图标
    
    Args:
        icon_name: 图标名称，如 'log.png'
        
    Returns:
        QIcon: 图标对象
    """
    from PySide6.QtWidgets import QStyle, QApplication
    
    icon_path = os.path.join("assets", "icons", icon_name)
    if os.path.exists(icon_path):
        return QIcon(icon_path)
    else:
        # 使用系统标准图标作为备用
        style = QApplication.style()
        if icon_name == "log.png":
            return QIcon(style.standardIcon(QStyle.SP_FileDialogDetailedView))
        elif icon_name == "refresh.png":
            return QIcon(style.standardIcon(QStyle.SP_BrowserReload))
        elif icon_name == "folder.png":
            return QIcon(style.standardIcon(QStyle.SP_DirOpenIcon))
        else:
            return QIcon()

class LogTableModel(QAbstractTableModel):
    """日志数据模型，用于显示日志条目"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.log_entries = []
        self.headers = ["时间", "级别", "模块", "消息"]
        
        # 现代化日志级别对应的颜色
        self.level_colors = {
            "DEBUG": QColor(149, 165, 166),    # 灰色 - 调试信息
            "INFO": QColor(52, 152, 219),      # 蓝色 - 一般信息
            "WARNING": QColor(243, 156, 18),   # 橙色 - 警告
            "ERROR": QColor(231, 76, 60),      # 红色 - 错误
            "CRITICAL": QColor(142, 68, 173)   # 紫色 - 严重错误
        }
        
    def rowCount(self, parent=QModelIndex()):
        return len(self.log_entries)
        
    def columnCount(self, parent=QModelIndex()):
        return len(self.headers)
        
    def data(self, index, role=Qt.DisplayRole):
        if not index.isValid() or index.row() >= len(self.log_entries):
            return None
            
        entry = self.log_entries[index.row()]
        column = index.column()
        
        if role == Qt.DisplayRole:
            return [entry["time"], entry["level"], entry["module"], entry["message"]][column]
            
        elif role == Qt.ForegroundRole:
            return self.level_colors.get(entry["level"], QColor(0, 0, 0))
            
        elif role == Qt.TextAlignmentRole:
            if column < 3:
                return Qt.AlignCenter
            return Qt.AlignLeft | Qt.AlignVCenter
            
        return None
        
    def headerData(self, section, orientation, role=Qt.DisplayRole):
        if orientation == Qt.Horizontal and role == Qt.DisplayRole:
            return self.headers[section]
        return None
        
    def loadLogFile(self, file_path):
        """加载日志文件内容
        
        Args:
            file_path: 日志文件路径
        """
        self.beginResetModel()
        self.log_entries = []
        
        # 日志格式解析正则表达式
        pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) - (\w+) - \[([^:]+):(\d+)\] - (.+)"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    match = re.match(pattern, line.strip())
                    if match:
                        time, level, module_name, line_num, message = match.groups()
                        self.log_entries.append({
                            "time": time,
                            "level": level,
                            "module": f"{module_name}:{line_num}",
                            "message": message,
                            "raw": line.strip()
                        })
                    else:
                        # 处理可能的多行日志（如异常堆栈）
                        if self.log_entries:
                            self.log_entries[-1]["raw"] += "\n" + line.strip()
                            
            logger.info(f"成功加载日志文件: {file_path}，共 {len(self.log_entries)} 条记录")
        except Exception as e:
            logger.error(f"加载日志文件失败: {e}")
            
        self.endResetModel()

class LogFilterProxyModel(QSortFilterProxyModel):
    """日志过滤代理模型，用于实现日志级别和文本的过滤"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.level_filter = set()
        self.search_text = ""
        
    def filterAcceptsRow(self, source_row, source_parent):
        model = self.sourceModel()
        level_idx = model.index(source_row, 1, source_parent)
        message_idx = model.index(source_row, 3, source_parent)
        module_idx = model.index(source_row, 2, source_parent)
        
        level = model.data(level_idx, Qt.DisplayRole)
        
        # 如果设置了级别过滤，检查当前行是否符合
        if self.level_filter and level not in self.level_filter:
            return False
            
        # 如果有搜索文本，检查消息和模块中是否包含
        if self.search_text:
            message = model.data(message_idx, Qt.DisplayRole)
            module = model.data(module_idx, Qt.DisplayRole)
            return (self.search_text.lower() in message.lower() or 
                   self.search_text.lower() in module.lower())
                   
        return True
        
    def setLevelFilter(self, levels):
        """设置日志级别过滤
        
        Args:
            levels: 要显示的日志级别列表
        """
        self.level_filter = set(levels)
        self.invalidateFilter()
        
    def setSearchText(self, text):
        """设置搜索文本
        
        Args:
            text: 要搜索的文本
        """
        self.search_text = text
        self.invalidateFilter()

class LogDetailDialog(QDialog):
    """现代化日志详情对话框，用于显示日志的完整内容"""

    def __init__(self, log_entry, parent=None):
        super().__init__(parent)
        self.setWindowTitle("📋 日志详情 - ANSYS Workbench 振动传递计算")
        self.resize(900, 600)

        # 移除关闭按钮
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowCloseButtonHint)

        # 设置现代化样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border-radius: 10px;
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题区域
        title_widget = QWidget()
        title_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2);
                border-radius: 8px;
                padding: 15px;
            }
        """)

        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(15, 10, 15, 10)

        title_label = QLabel("📋 日志详细信息")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: 600;
                color: white;
                background: transparent;
                border: none;
            }
        """)

        # 日志级别标签
        level = log_entry.get("level", "UNKNOWN")
        level_colors = {
            "DEBUG": "#95a5a6", "INFO": "#3498db", "WARNING": "#f39c12",
            "ERROR": "#e74c3c", "CRITICAL": "#8e44ad"
        }
        level_color = level_colors.get(level, "#95a5a6")

        level_label = QLabel(f"🏷️ {level}")
        level_label.setStyleSheet(f"""
            QLabel {{
                background: {level_color};
                color: white;
                border-radius: 6px;
                padding: 5px 12px;
                font-weight: 500;
                font-size: 12px;
                border: none;
            }}
        """)

        title_layout.addWidget(title_label)
        title_layout.addStretch()
        title_layout.addWidget(level_label)

        # 日志详情文本编辑器
        self.text_edit = QTextEdit()
        self.text_edit.setReadOnly(True)

        # 设置现代化等宽字体
        detail_font = QFont("JetBrains Mono", 11)
        if not detail_font.exactMatch():
            detail_font = QFont("Consolas", 11)
        if not detail_font.exactMatch():
            detail_font = QFont("Courier New", 11)

        self.text_edit.setFont(detail_font)
        self.text_edit.setText(log_entry.get("raw", "无详细信息"))
        self.text_edit.setStyleSheet("""
            QTextEdit {
                background: white;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 15px;
                color: #2c3e50;
                selection-background-color: #667eea;
                selection-color: white;
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #bdc3c7;
                min-height: 20px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: #95a5a6;
            }
        """)

        # 现代化按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 复制按钮
        copy_button = QPushButton("📋 复制到剪贴板")
        copy_button.clicked.connect(self.copyToClipboard)
        copy_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2, stop: 1 #3498db);
            }
            QPushButton:pressed {
                background: #2471a3;
            }
        """)

        # 关闭按钮
        close_button = QPushButton("✖️ 关闭")
        close_button.clicked.connect(self.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #95a5a6, stop: 1 #7f8c8d);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: 500;
                font-size: 14px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #b2bec3, stop: 1 #95a5a6);
            }
            QPushButton:pressed {
                background: #636e72;
            }
        """)

        button_layout.addStretch()
        button_layout.addWidget(copy_button)
        button_layout.addWidget(close_button)

        layout.addWidget(title_widget)
        layout.addWidget(self.text_edit)
        layout.addWidget(button_widget)
        
    def copyToClipboard(self):
        """复制日志内容到剪贴板"""
        self.text_edit.selectAll()
        self.text_edit.copy()
        self.text_edit.deselect()
        logger.debug("日志内容已复制到剪贴板")

class LogViewer(BaseWindow):
    """日志查看器主界面"""

    def __init__(self, window_manager):
        # 显式调用父类的初始化方法，传递window_manager参数
        # 注意不要再次初始化QMainWindow，因为BaseWindow已经初始化过了
        BaseWindow.__init__(self, window_manager)

        self.setWindowTitle("ANSYS Workbench 振动传递计算 - 日志查看器")
        self.resize(1200, 800)
        self.setWindowIcon(get_icon("log.png"))

        # 移除窗口关闭按钮
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowCloseButtonHint)

        # 创建中央部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 设置现代化字体
        self.log_font = QFont("JetBrains Mono", 10)
        if not self.log_font.exactMatch():
            self.log_font = QFont("Consolas", 10)
        if not self.log_font.exactMatch():
            self.log_font = QFont("Courier New", 10)

        self.setupUI()
        self.loadLatestLog()

        logger.info("日志查看器已初始化")
        
    def setupUI(self):
        """设置用户界面"""
        layout = QVBoxLayout(self.central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 设置现代化窗口样式
        self.central_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                font-family: "Microsoft YaHei UI", "Segoe UI", "Helvetica Neue", Arial, sans-serif;
                color: #2c3e50;
            }
        """)

        # 现代化标题区域
        title_layout = QHBoxLayout()
        title_widget = QWidget()
        title_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #667eea, stop: 1 #764ba2);
                border-radius: 12px;
                padding: 15px;
                margin: 0px;
            }
        """)

        title_inner_layout = QHBoxLayout(title_widget)
        title_inner_layout.setContentsMargins(20, 15, 20, 15)

        # 主标题
        title_label = QLabel("🔍 日志查看器")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: 600;
                color: white;
                background: transparent;
                border: none;
            }
        """)

        # 副标题
        subtitle_label = QLabel("ANSYS Workbench 振动传递计算系统")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: 400;
                color: rgba(255, 255, 255, 0.8);
                background: transparent;
                border: none;
                margin-top: 5px;
            }
        """)

        title_text_layout = QVBoxLayout()
        title_text_layout.setSpacing(5)
        title_text_layout.addWidget(title_label)
        title_text_layout.addWidget(subtitle_label)

        title_inner_layout.addLayout(title_text_layout)
        title_inner_layout.addStretch()

        # 返回主界面按钮
        return_btn = QPushButton("🏠 返回主界面")
        return_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(255, 255, 255, 0.3);
                border: 2px solid rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background: rgba(255, 255, 255, 0.1);
            }
        """)
        return_btn.clicked.connect(self.return_to_main)
        title_inner_layout.addWidget(return_btn)

        title_layout.addWidget(title_widget)
        layout.addLayout(title_layout)
        
        # 现代化过滤器控制面板
        filter_widget = QWidget()
        filter_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 10px;
                border: 1px solid #e1e8ed;
                padding: 15px;
            }
        """)

        filter_main_layout = QVBoxLayout(filter_widget)
        filter_main_layout.setSpacing(15)

        # 过滤器标题
        filter_title = QLabel("📊 日志过滤器")
        filter_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
                background: transparent;
                border: none;
                margin-bottom: 10px;
            }
        """)
        filter_main_layout.addWidget(filter_title)

        # 级别过滤器区域
        level_layout = QHBoxLayout()

        # 级别标签
        level_label = QLabel("日志级别:")
        level_label.setStyleSheet("""
            QLabel {
                font-weight: 600;
                color: #34495e;
                background: transparent;
                border: none;
                min-width: 80px;
            }
        """)
        level_layout.addWidget(level_label)

        # 日志级别过滤按钮
        self.btn_debug = QPushButton("🔧 DEBUG")
        self.btn_info = QPushButton("ℹ️ INFO")
        self.btn_warning = QPushButton("⚠️ WARNING")
        self.btn_error = QPushButton("❌ ERROR")
        self.btn_critical = QPushButton("🚨 CRITICAL")

        # 设置为可选中的按钮
        buttons_config = [
            (self.btn_debug, "#95a5a6", "#7f8c8d"),
            (self.btn_info, "#3498db", "#2980b9"),
            (self.btn_warning, "#f39c12", "#e67e22"),
            (self.btn_error, "#e74c3c", "#c0392b"),
            (self.btn_critical, "#8e44ad", "#732d91")
        ]

        for btn, color, hover_color in buttons_config:
            btn.setCheckable(True)
            btn.setChecked(True)
            btn.clicked.connect(self.updateLevelFilter)
            btn.setStyleSheet(f"""
                QPushButton {{
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-weight: 500;
                    font-size: 12px;
                    color: {color};
                    background: white;
                    min-width: 80px;
                }}
                QPushButton:checked {{
                    background: {color};
                    color: white;
                    border: 2px solid {color};
                }}
                QPushButton:hover {{
                    border: 2px solid {hover_color};
                    background: {hover_color if 'checked' in btn.styleSheet() else 'rgba(52, 152, 219, 0.1)'};
                }}
                QPushButton:pressed {{
                    background: {hover_color};
                    color: white;
                }}
            """)
            level_layout.addWidget(btn)

        level_layout.addStretch()
        filter_main_layout.addLayout(level_layout)
        
        # 搜索和操作区域
        search_action_layout = QHBoxLayout()

        # 搜索区域
        search_label = QLabel("🔍 搜索:")
        search_label.setStyleSheet("""
            QLabel {
                font-weight: 600;
                color: #34495e;
                background: transparent;
                border: none;
                min-width: 60px;
            }
        """)
        search_action_layout.addWidget(search_label)

        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("输入关键词搜索日志内容...")
        self.search_box.textChanged.connect(self.onSearchTextChanged)
        self.search_box.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px 15px;
                font-size: 14px;
                background: white;
                min-width: 300px;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
                background: #f8f9fa;
            }
            QLineEdit:hover {
                border: 2px solid #95a5a6;
            }
        """)
        search_action_layout.addWidget(self.search_box)

        search_action_layout.addStretch()

        # 操作按钮区域
        action_buttons_layout = QHBoxLayout()
        action_buttons_layout.setSpacing(10)

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新日志")
        self.refresh_btn.setIcon(get_icon("refresh.png"))
        self.refresh_btn.clicked.connect(self.loadLatestLog)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2, stop: 1 #3498db);
            }
            QPushButton:pressed {
                background: #2471a3;
            }
        """)

        # 清除过滤器按钮
        self.clear_filter_btn = QPushButton("🗑️ 清除过滤")
        self.clear_filter_btn.clicked.connect(self.clearFilters)
        self.clear_filter_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #95a5a6, stop: 1 #7f8c8d);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #b2bec3, stop: 1 #95a5a6);
            }
            QPushButton:pressed {
                background: #636e72;
            }
        """)

        # 日志目录按钮
        self.log_dir_btn = QPushButton("📁 打开日志目录")
        self.log_dir_btn.setIcon(get_icon("folder.png"))
        self.log_dir_btn.clicked.connect(self.openLogDirectory)
        self.log_dir_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #2ecc71, stop: 1 #27ae60);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: 500;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #58d68d, stop: 1 #2ecc71);
            }
            QPushButton:pressed {
                background: #1e8449;
            }
        """)

        action_buttons_layout.addWidget(self.refresh_btn)
        action_buttons_layout.addWidget(self.clear_filter_btn)
        action_buttons_layout.addWidget(self.log_dir_btn)

        search_action_layout.addLayout(action_buttons_layout)
        filter_main_layout.addLayout(search_action_layout)

        layout.addWidget(filter_widget)
        
        # 现代化日志表格视图
        table_container = QWidget()
        table_container.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 10px;
                border: 1px solid #e1e8ed;
            }
        """)

        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(15, 15, 15, 15)

        # 表格标题
        table_title = QLabel("📋 日志记录")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: 600;
                color: #2c3e50;
                background: transparent;
                border: none;
                margin-bottom: 10px;
            }
        """)
        table_layout.addWidget(table_title)

        self.log_view = QTableView()
        self.log_view.setFont(self.log_font)
        self.log_view.setAlternatingRowColors(True)
        self.log_view.setSortingEnabled(True)
        self.log_view.setSelectionBehavior(QTableView.SelectRows)
        self.log_view.setContextMenuPolicy(Qt.CustomContextMenu)
        self.log_view.customContextMenuRequested.connect(self.showContextMenu)
        self.log_view.setStyleSheet("""
            QTableView {
                border: none;
                background-color: white;
                gridline-color: #f1f3f4;
                selection-background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #667eea, stop: 1 #764ba2);
                selection-color: white;
                alternate-background-color: #f8f9fa;
                border-radius: 8px;
                font-size: 12px;
            }
            QTableView::item {
                padding: 8px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableView::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #667eea, stop: 1 #764ba2);
                color: white;
            }
            QTableView::item:hover {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #dee2e6;
                border-bottom: 2px solid #667eea;
                font-weight: 600;
                color: #495057;
                font-size: 13px;
            }
            QHeaderView::section:first {
                border-top-left-radius: 8px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 8px;
                border-right: none;
            }
            QScrollBar:vertical {
                border: none;
                background: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #bdc3c7, stop: 1 #95a5a6);
                min-height: 20px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #95a5a6, stop: 1 #7f8c8d);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                border: none;
                background: #f8f9fa;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #bdc3c7, stop: 1 #95a5a6);
                min-width: 20px;
                border-radius: 6px;
                margin: 2px;
            }
            QScrollBar::handle:horizontal:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #95a5a6, stop: 1 #7f8c8d);
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
        """)
        
        # 设置表头
        header = self.log_view.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 时间列
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 级别列
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 模块列
        header.setSectionResizeMode(3, QHeaderView.Stretch)           # 消息列

        # 双击显示详情
        self.log_view.doubleClicked.connect(self.showLogDetail)

        # 创建和设置模型
        self.log_model = LogTableModel()
        self.proxy_model = LogFilterProxyModel()
        self.proxy_model.setSourceModel(self.log_model)
        self.log_view.setModel(self.proxy_model)

        table_layout.addWidget(self.log_view)

        # 现代化状态栏
        status_widget = QWidget()
        status_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border-radius: 8px;
                border: 1px solid #dee2e6;
                padding: 10px;
            }
        """)

        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(15, 10, 15, 10)

        # 状态信息
        self.status_label = QLabel("📄 日志文件: 无")
        self.status_label.setStyleSheet("""
            QLabel {
                font-weight: 500;
                color: #495057;
                background: transparent;
                border: none;
            }
        """)

        self.count_label = QLabel("📊 条目数: 0")
        self.count_label.setStyleSheet("""
            QLabel {
                font-weight: 500;
                color: #495057;
                background: transparent;
                border: none;
            }
        """)

        # 实时状态指示器
        self.status_indicator = QLabel("🟢 就绪")
        self.status_indicator.setStyleSheet("""
            QLabel {
                font-weight: 500;
                color: #28a745;
                background: transparent;
                border: none;
            }
        """)

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.count_label)
        status_layout.addWidget(self.status_indicator)

        # 添加到主布局
        layout.addWidget(table_container)
        layout.addWidget(status_widget)

    def return_to_main(self):
        """返回主界面"""
        try:
            if hasattr(self, 'window_manager') and self.window_manager:
                self.window_manager.switch_to(WindowType.MAIN)
                logger.info("从日志查看器返回主界面")
        except Exception as e:
            logger.error(f"返回主界面失败: {e}")
    
    def loadLatestLog(self):
        """加载最新的日志文件"""
        try:
            # 更新状态指示器
            self.status_indicator.setText("🔄 加载中...")
            self.status_indicator.setStyleSheet("""
                QLabel {
                    font-weight: 500;
                    color: #f39c12;
                    background: transparent;
                    border: none;
                }
            """)

            log_dir = "logs"
            log_file = "app.log"
            log_path = os.path.join(log_dir, log_file)

            if os.path.exists(log_path):
                self.log_model.loadLogFile(log_path)
                self.status_label.setText(f"📄 日志文件: {log_path}")
                self.count_label.setText(f"📊 条目数: {self.log_model.rowCount()}")

                # 默认按时间倒序排序（最新的在前面）
                self.log_view.sortByColumn(0, Qt.DescendingOrder)

                # 更新状态指示器为成功
                self.status_indicator.setText("✅ 加载完成")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        font-weight: 500;
                        color: #27ae60;
                        background: transparent;
                        border: none;
                    }
                """)

                logger.debug(f"已加载日志文件 {log_path}, 共 {self.log_model.rowCount()} 条记录")
            else:
                self.status_label.setText(f"📄 日志文件不存在: {log_path}")
                self.status_indicator.setText("❌ 文件不存在")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        font-weight: 500;
                        color: #e74c3c;
                        background: transparent;
                        border: none;
                    }
                """)
                logger.warning(f"日志文件不存在: {log_path}")

        except Exception as e:
            self.status_indicator.setText("❌ 加载失败")
            self.status_indicator.setStyleSheet("""
                QLabel {
                    font-weight: 500;
                    color: #e74c3c;
                    background: transparent;
                    border: none;
                }
            """)
            logger.error(f"加载日志文件失败: {e}")
            
    def updateLevelFilter(self):
        """更新日志级别过滤"""
        levels = []
        if self.btn_debug.isChecked():
            levels.append("DEBUG")
        if self.btn_info.isChecked():
            levels.append("INFO")
        if self.btn_warning.isChecked():
            levels.append("WARNING")
        if self.btn_error.isChecked():
            levels.append("ERROR")
        if self.btn_critical.isChecked():
            levels.append("CRITICAL")
            
        self.proxy_model.setLevelFilter(levels)
        logger.debug(f"已更新日志级别过滤: {levels}")
        
    def onSearchTextChanged(self, text):
        """搜索框文本变化时更新过滤器"""
        self.proxy_model.setSearchText(text)
        
    def openLogDirectory(self):
        """打开日志文件所在目录"""
        try:
            import subprocess
            log_dir = os.path.abspath("logs")
            if os.path.exists(log_dir):
                # 使用系统默认程序打开目录
                if os.name == 'nt':  # Windows
                    os.startfile(log_dir)
                elif os.name == 'posix':  # macOS 和 Linux
                    if sys.platform == 'darwin':
                        subprocess.Popen(['open', log_dir])
                    else:
                        subprocess.Popen(['xdg-open', log_dir])
                logger.info(f"已打开日志目录: {log_dir}")
            else:
                logger.warning(f"日志目录不存在: {log_dir}")
        except Exception as e:
            logger.error(f"打开日志目录失败: {e}")
    
    def showLogDetail(self, index):
        """显示日志详情对话框
        
        Args:
            index: 表格视图中的索引
        """
        # 获取原始模型中的行索引
        source_index = self.proxy_model.mapToSource(index)
        row = source_index.row()
        
        if 0 <= row < len(self.log_model.log_entries):
            log_entry = self.log_model.log_entries[row]
            dialog = LogDetailDialog(log_entry, self)
            dialog.exec()

    def showContextMenu(self, position):
        """显示右键菜单
        
        Args:
            position: 鼠标位置
        """
        # 获取当前选中的行
        indexes = self.log_view.selectedIndexes()
        if not indexes:
            return
            
        # 创建右键菜单
        from PySide6.QtWidgets import QMenu
        context_menu = QMenu(self)
        
        # 添加菜单项
        view_action = context_menu.addAction("查看详情")
        view_action.triggered.connect(lambda: self.showLogDetail(indexes[0]))
        
        copy_action = context_menu.addAction("复制")
        copy_action.triggered.connect(self.copySelectedLog)
        
        # 添加分割线
        context_menu.addSeparator()
        
        # 添加清除过滤器菜单项
        clear_filter_action = context_menu.addAction("清除过滤器")
        clear_filter_action.triggered.connect(self.clearFilters)
        
        # 显示菜单
        context_menu.exec(self.log_view.viewport().mapToGlobal(position))
        
    def copySelectedLog(self):
        """复制选中的日志条目到剪贴板"""
        indexes = self.log_view.selectedIndexes()
        if not indexes:
            return
            
        # 获取选中行的源模型索引
        source_row = self.proxy_model.mapToSource(indexes[0]).row()
        
        # 获取日志条目
        if 0 <= source_row < len(self.log_model.log_entries):
            log_entry = self.log_model.log_entries[source_row]
            
            # 创建剪贴板
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            
            # 复制到剪贴板
            clipboard.setText(log_entry.get("raw", ""))
            logger.debug("日志内容已复制到剪贴板")
            
    def clearFilters(self):
        """清除所有过滤器"""
        # 重置级别过滤器
        for btn in [self.btn_info, self.btn_warning, self.btn_error, self.btn_debug, self.btn_critical]:
            btn.setChecked(True)
            
        # 清空搜索框
        self.search_box.clear()
        
        # 更新过滤器
        self.updateLevelFilter()
        logger.debug("已清除所有过滤器") 