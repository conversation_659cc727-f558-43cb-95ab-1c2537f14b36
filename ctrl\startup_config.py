"""
启动配置模块

此模块负责处理启动时从上游Electron应用程序接收的配置文件，主要功能包括：
1. 检查并加载启动配置文件
2. 验证配置数据
3. 处理配置文件的清理
4. 与ConfigManager集成

作者: [作者名]
日期: [日期]
"""

import os
import json
import logging
from typing import Dict, Any, Optional
import sys

# 添加项目根目录到路径，以便导入core模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from core import ConfigManager
    HAS_CONFIG_MANAGER = True
except ImportError:
    HAS_CONFIG_MANAGER = False
    print("警告: 未能导入ConfigManager，将使用传统配置方式")

# 设置日志
logger = logging.getLogger("startup_config")
logger.setLevel(logging.INFO)

# 确保日志目录存在
from pathlib import Path
log_dir = Path('logs')
log_dir.mkdir(exist_ok=True)

log_file = log_dir / 'startup_config.log'
handler = logging.FileHandler(str(log_file))
handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)

# 启动时配置文件的路径
def get_startup_config_path():
    """获取启动配置文件路径，兼容开发环境和打包环境"""
    if getattr(sys, 'frozen', False):
        # 打包环境：使用可执行文件所在目录
        app_dir = os.path.dirname(sys.executable)
    else:
        # 开发环境：使用项目根目录
        app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    return os.path.join(app_dir, "temp", "temp_startup_config.json")

STARTUP_CONFIG_PATH = get_startup_config_path()

def load_initial_data() -> Optional[Dict[str, Any]]:
    """检查并加载启动时的配置文件
    
    Returns:
        Dict[str, Any] or None: 配置数据，如果文件不存在或读取失败则返回None
    """
    # 首先尝试从临时启动配置文件加载
    startup_data = _load_startup_config()
    
    # 如果启用了ConfigManager，将启动数据与已有配置合并
    if HAS_CONFIG_MANAGER and startup_data:
        try:
            config_manager = ConfigManager()
            
            # 将启动数据转换为ConfigManager格式
            if 'timeStep' in startup_data:
                config_manager.set("analysis.time_step", startup_data['timeStep'])
            if 'endTime' in startup_data:
                config_manager.set("analysis.end_time", startup_data['endTime'])
            if 'stiffnessCoefficient' in startup_data:
                config_manager.set("analysis.stiffness_coefficient", startup_data['stiffnessCoefficient'])
            if 'massCoefficient' in startup_data:
                config_manager.set("analysis.mass_coefficient", startup_data['massCoefficient'])
            if 'forceOutputFolder' in startup_data:
                config_manager.set("constrain.force_output_folder", startup_data['forceOutputFolder'])
            if 'rotationSpeed' in startup_data:
                config_manager.set("constrain.rotation_speed", startup_data['rotationSpeed'])
                
            # 保存配置
            config_manager.save_config()
            logger.info("已将启动配置合并到ConfigManager")
        except Exception as e:
            logger.error(f"合并配置到ConfigManager失败: {str(e)}")
    
    return startup_data

def _load_startup_config() -> Optional[Dict[str, Any]]:
    """从临时文件加载启动配置
    
    Returns:
        Dict[str, Any] or None: 配置数据，如果文件不存在或读取失败则返回None
    """
    if os.path.exists(STARTUP_CONFIG_PATH):
        try:
            with open(STARTUP_CONFIG_PATH, 'r', encoding='utf-8') as f:
                data = json.load(f)
            logger.info(f"成功读取启动配置文件: {STARTUP_CONFIG_PATH}")
            
            # 读取后立即删除
            try:
                os.remove(STARTUP_CONFIG_PATH)
                logger.info(f"已删除启动配置文件: {STARTUP_CONFIG_PATH}")
            except Exception as e:
                logger.warning(f"删除启动配置文件失败: {str(e)}")
                
            return data
        except (json.JSONDecodeError, IOError) as e:
            logger.error(f"读取启动配置文件失败: {str(e)}")
            # 如果文件有问题，也尝试删除它
            try:
                os.remove(STARTUP_CONFIG_PATH)
                logger.info(f"已删除损坏的启动配置文件: {STARTUP_CONFIG_PATH}")
            except Exception as e:
                logger.warning(f"删除损坏的启动配置文件失败: {str(e)}")
    else:
        logger.info(f"启动配置文件不存在: {STARTUP_CONFIG_PATH}")
    
    return None 