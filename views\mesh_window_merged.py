"""
合并的网格窗口模块

此模块定义了合并后的网格管理窗口类，负责：
1. 网格管理功能
2. 批量网格生成与模态分析
3. 结果对比功能
4. 功能联动机制

作者: AI Assistant
日期: 2025-07-25
"""
import glob
import shutil
import subprocess
import re
import time
import threading
import json
from window_manager import WindowManager, WindowType
from resource_manager import ResourceManager
from error_handler import (
    ErrorHandler, AppError, AnsysError, ValidationError,
    FileOperationError, ConfigurationError, ErrorSeverity
)
import logging
import json
import os
from datetime import datetime
from typing import Optional, List, Dict, Any
from PySide6.QtCore import Qt, QTimer, QObject, Signal, QFileSystemWatcher
from PySide6.QtGui import QColor
from PySide6.QtWidgets import (QMessageBox, QTableWidgetItem, QHeaderView,
                               QPushButton, QHBoxLayout, QWidget,
                               QFileDialog, QAbstractItemView,
                               QApplication, QListWidgetItem, QMenu)
from PySide6.QtGui import QColor, QAction

from ui import ui_mesh_merged
from .base_window import BaseWindow
from .mesh_parameter_dialog_simplified import MeshParameterDialogSimplified as MeshParameterDialog
# from .modal_calculation_dialog import ModalCalculationDialog  # 不再使用对话框
from .result_selection_dialog import ResultSelectionDialog
from .custom_message_box import CustomMessageBox
from ui.components.modal_chart_widget import ModalChartWidget
from core.mesh_manager import MeshManager, MeshParameter, MeshStatus, ElementType

# 获取模块日志记录器
logger = logging.getLogger(__name__)



# 简化的模态结果跟踪器
class ModalResultTracker:
    """模态分析结果跟踪器"""

    def __init__(self):
        self.current_result = None

    def update_result(self, result_info):
        """更新结果信息"""
        self.current_result = result_info

    def clear_result(self):
        """清除结果信息"""
        self.current_result = None


class ModalResultInfo:
    """模态分析结果信息"""

    def __init__(self, output_dir, timestamp, mesh_name, element_size, config_files):
        self.output_dir = output_dir
        self.timestamp = timestamp
        self.mesh_name = mesh_name
        self.element_size = element_size
        self.config_files = config_files


def cleanup_modal_temp_files(resource_manager, error_handler, mesh_window, exclude_paths=None):
    """清理模态分析临时文件

    Args:
        resource_manager: 资源管理器实例
        error_handler: 错误处理器
        mesh_window: 网格窗口实例
        exclude_paths: 需要排除的文件路径列表
    """
    try:
        if hasattr(resource_manager, 'cleanup_temp_files'):
            resource_manager.cleanup_temp_files(exclude_paths or [])
        logger.info("模态分析临时文件清理完成")
    except Exception as e:
        logger.warning(f"清理模态分析临时文件失败: {str(e)}")
        if error_handler and mesh_window:
            error_handler.handle_error(
                AppError(f"清理临时文件失败: {str(e)}", ErrorSeverity.WARNING),
                mesh_window
            )


def execute_single_modal_calculation(window_manager: WindowManager, mesh_parameter, calc_params: Dict[str, Any]) -> str:
    """执行单个模态计算的完整流程

    此函数按顺序执行以下步骤：
    1. 初始化资源管理器
    2. 创建临时输出目录
    3. 验证和获取配置文件路径
    4. 准备模态分析脚本
    5. 修改脚本内容
    6. 创建新版本的脚本
    7. 创建Workbench控制脚本
    8. 创建批处理文件
    9. 执行批处理文件

    Args:
        window_manager: 窗口管理器实例
        mesh_parameter: 网格参数对象
        calc_params: 计算参数字典
    """
    logger.info("=== execute_single_modal_calculation 开始执行 ===")
    print("DEBUG: execute_single_modal_calculation 开始执行")
    print(f"DEBUG: mesh_parameter.name = {mesh_parameter.name}")
    print(f"DEBUG: calc_params = {calc_params}")

    # 获取窗口实例和错误处理器
    try:
        mesh_window = window_manager.get_window(WindowType.MESH)
        main_window = window_manager.get_window(WindowType.MAIN)
        error_handler = ErrorHandler()
        modal_tracker = ModalResultTracker()

        logger.info(f"窗口实例获取成功: mesh_window={mesh_window is not None}, main_window={main_window is not None}")
        print(f"DEBUG: 窗口实例获取成功: mesh_window={mesh_window is not None}, main_window={main_window is not None}")

    except Exception as e:
        logger.error(f"获取窗口实例失败: {str(e)}")
        print(f"DEBUG: 获取窗口实例失败: {str(e)}")
        raise

    if not mesh_window or not main_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    # 用于存储需要保护的文件路径
    active_files = []

    try:
        # 1. 初始化资源管理器
        resource_manager = ResourceManager()
        try:
            resource_manager.initialize(main_window.ANSYS_Work_Dir)

            # 首先清理旧的临时文件
            cleanup_modal_temp_files(resource_manager, error_handler, mesh_window)

        except Exception as e:
            raise ConfigurationError(
                "初始化资源管理器失败",
                details={'work_dir': main_window.ANSYS_Work_Dir}
            ) from e

        # 2. 创建临时输出目录
        try:
            temp_dir_name = resource_manager.create_temp_file(prefix="modal_output_")
            active_files.append(temp_dir_name)
            os.remove(temp_dir_name)  # 删除生成的文件
            temp_output_dir = temp_dir_name  # 使用这个名字作为目录名
            os.makedirs(temp_output_dir, exist_ok=True)

            # 创建新的模态结果跟踪记录
            modal_tracker.update_result(ModalResultInfo(
                output_dir=temp_output_dir,
                timestamp=datetime.now(),
                mesh_name=mesh_parameter.name,
                element_size=mesh_parameter.size,
                config_files=[]
            ))

        except Exception as e:
            raise FileOperationError(
                "创建临时输出目录失败",
                details={'error': str(e)}
            ) from e

         # 3.生成模态分析配置
        try:
            modal_config = _generate_modal_config(mesh_parameter, resource_manager, calc_params, temp_output_dir)
            active_files.append(modal_config['config_path'])
            logger.info(f"模态分析配置生成成功: {modal_config['config_path']}")
        except Exception as e:
            raise ConfigurationError(
                "生成模态分析配置失败",
                details={'mesh': mesh_parameter.name}
            ) from e

        # 4. 获取配置文件路径
        try:
            # 修正文件名匹配问题：使用实际生成的文件名
            ansys_result_path = os.path.join(resource_manager.json_dir, "analysis_config_latest.json")
            constrain_result_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            connection_result_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            cfg_path = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")

            # 添加调试信息
            logger.info(f"配置文件路径检查:")
            logger.info(f"  json_dir: {resource_manager.json_dir}")
            logger.info(f"  analysis_config: {ansys_result_path}")
            logger.info(f"  constrain_config: {constrain_result_path}")
            logger.info(f"  connection_config: {connection_result_path}")
            logger.info(f"  mesh_config: {cfg_path}")

            # 检查json_dir是否存在
            if not os.path.exists(resource_manager.json_dir):
                logger.warning(f"JSON目录不存在: {resource_manager.json_dir}")
                # 尝试使用工作目录下的json目录
                work_json_dir = os.path.join(resource_manager.work_dir, "json")
                if os.path.exists(work_json_dir):
                    logger.info(f"使用工作目录下的JSON目录: {work_json_dir}")
                    ansys_result_path = os.path.join(work_json_dir, "analysis_config_latest.json")
                    constrain_result_path = os.path.join(work_json_dir, "constrain_config_latest.json")
                    connection_result_path = os.path.join(work_json_dir, "connection_config_latest.json")
                    cfg_path = os.path.join(work_json_dir, "mesh_config_latest.json")

            # 增强的文件存在性检查
            config_files = [
                ("分析配置", ansys_result_path),
                ("约束配置", constrain_result_path),
                ("连接配置", connection_result_path),
                ("网格配置", cfg_path)
            ]

            missing_files = []
            found_files = []

            for config_name, path in config_files:
                logger.info(f"检查{config_name}文件: {path}")
                if os.path.exists(path):
                    try:
                        # 验证文件可读性和JSON格式
                        with open(path, "r", encoding="utf-8") as f:
                            content = f.read()
                            if content.strip():  # 检查文件不为空
                                # 尝试解析JSON以验证格式
                                import json
                                json.loads(content)
                                found_files.append((config_name, path))
                                logger.info(f"✅ {config_name}文件验证通过: {path}")

                                # 在UI中显示验证结果
                                if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                                    current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                                    mesh_window.ui.textEdit_calc_stats.setPlainText(
                                        current_text + f"\n✅ {config_name}文件验证通过: {os.path.basename(path)}"
                                    )
                            else:
                                missing_files.append((config_name, path, "文件为空"))
                                logger.warning(f"⚠️ {config_name}文件为空: {path}")
                    except json.JSONDecodeError as e:
                        missing_files.append((config_name, path, f"JSON格式错误: {str(e)}"))
                        logger.error(f"❌ {config_name}文件JSON格式错误: {path}, 错误: {str(e)}")
                    except Exception as e:
                        missing_files.append((config_name, path, f"读取失败: {str(e)}"))
                        logger.error(f"❌ {config_name}文件读取失败: {path}, 错误: {str(e)}")
                else:
                    missing_files.append((config_name, path, "文件不存在"))
                    logger.error(f"❌ {config_name}文件不存在: {path}")

            # 如果有缺失的文件，提供详细的错误信息
            if missing_files:
                error_details = {
                    'missing_count': len(missing_files),
                    'found_count': len(found_files),
                    'missing_files': missing_files,
                    'found_files': found_files,
                    'json_dir': resource_manager.json_dir,
                    'work_dir': resource_manager.work_dir
                }

                # 构建详细的错误消息
                error_msg = f"配置文件检查失败，缺失 {len(missing_files)} 个文件:\n"
                for config_name, path, reason in missing_files:
                    error_msg += f"  • {config_name}: {reason}\n    路径: {path}\n"

                if found_files:
                    error_msg += f"\n已找到的文件 ({len(found_files)} 个):\n"
                    for config_name, path in found_files:
                        error_msg += f"  ✅ {config_name}: {path}\n"

                error_msg += f"\n检查目录:\n  JSON目录: {resource_manager.json_dir}\n  工作目录: {resource_manager.work_dir}"

                raise FileOperationError(
                    "配置文件验证失败",
                    details=error_details
                )

            logger.info(f"✅ 所有配置文件验证通过，共 {len(found_files)} 个文件")

        except Exception as e:
            raise FileOperationError(
                "获取配置文件路径失败",
                details={'error': str(e)}
            ) from e



        # 5. 准备源脚本
        try:
            source_script_path = os.path.join(resource_manager.base_dir, "originscript", "modal.py")
            with open(source_script_path, "r", encoding="utf-8") as f:
                script_content = f.read()

        except Exception as e:
            raise FileOperationError(
                "读取源脚本文件失败",
                details={'source_file': source_script_path}
            ) from e

        # 6. 修改脚本内容
        target_dir = main_window.ANSYS_Work_Dir.replace("\\", "/")
        old_target_dir = r'target_directory = r"D:/data/all-XM/autoworkbench/csdaima"'
        new_target_dir = f'target_directory = r"{target_dir}"'
        script_content = script_content.replace(old_target_dir, new_target_dir)

        # 使用动态路径替换功能处理mesh_config路径 - 单模态计算使用single配置
        from core.mesh_config_generator import replace_hardcoded_paths
        script_content = replace_hardcoded_paths(script_content, main_window.ANSYS_Work_Dir, config_type="single")

        # 替换其他配置文件路径
        replacements = {
            r'ansys_result_path = r"D:/data/all-XM/autoworkbench/csdaima/analysis_config_latest.json"':
                f'ansys_result_path = r"{ansys_result_path}"',
            r'constrain_result_path = r"D:/data/all-XM/autoworkbench/csdaima/2.json"':
                f'constrain_result_path = r"{constrain_result_path}"',
            r'connection_result_path = r"D:/data/all-XM/autoworkbench/csdaima/connection_result.json"':
                f'connection_result_path = r"{connection_result_path}"'
        }

        for old, new in replacements.items():
            script_content = script_content.replace(old, new)

        # 7. 创建新版本的脚本
        try:
            script_file = resource_manager.create_script_version("modal_analysis.py", script_content)
            active_files.append(script_file)

        except Exception as e:
            raise FileOperationError(
                "创建脚本文件失败",
                details={'script_name': "modal_analysis.py"}
            ) from e

        # 8. 清理旧版本
        resource_manager.clean_old_versions("modal_analysis.py")

        # 9. 创建Workbench控制脚本
        wb_script_content = f'''
# encoding: utf-8
Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
'''

        # 10. 获取最新的脚本路径
        script_file = script_file.replace("\\", "/")

        # 11. 添加执行命令
        wb_script_content += f'''
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{script_file}")')
model1.Exit()
Save(Overwrite=True)
'''

        # 12. 创建Workbench脚本文件
        try:
            wb_script_file = resource_manager.create_temp_file(prefix="modal_wb", suffix=".py")
            active_files.append(wb_script_file)
            with open(wb_script_file, "w", encoding="utf-8") as f:
                f.write(wb_script_content)

        except Exception as e:
            raise FileOperationError(
                "创建Workbench脚本文件失败",
                details={'script_file': wb_script_file}
            ) from e

        # 13. 创建批处理文件
        try:
            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            wb_script_file = wb_script_file.replace("\\", "/")
            bat_content = (
                f'"{ansys_path}" '
                f'-B -R "{wb_script_file}"'
            )
            bat_file = resource_manager.create_temp_file(prefix="modal", suffix=".bat")
            active_files.append(bat_file)
            with open(bat_file, "w", encoding="utf-8") as f:
                f.write(bat_content)

        except Exception as e:
            raise FileOperationError(
                "创建批处理文件失败",
                details={'bat_file': bat_file}
            ) from e

        # 14. 执行批处理文件 - 改进的错误处理机制
        try:
            # 更新界面状态
            if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                mesh_window.ui.textEdit_calc_stats.setPlainText(
                    current_text + f"\n🚀 开始执行ANSYS模态分析..."
                )

            # 使用改进的执行方法，支持非关键性错误处理
            success, output_info = _execute_bat_with_error_tolerance(
                bat_file,
                mesh_parameter,
                modal_config['output_directory'],
                error_handler,
                mesh_window
            )

            if success:
                # 显示成功消息
                error_handler.handle_error(
                    AppError(
                        f"模态计算完成: {mesh_parameter.name}",
                        ErrorSeverity.INFO
                    ),
                    mesh_window
                )

                # 更新界面状态
                if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                    current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                    mesh_window.ui.textEdit_calc_stats.setPlainText(
                        current_text + f"\n✅ 模态计算成功完成!"
                    )

                logger.info(f"模态计算成功完成: {mesh_parameter.name}")
            else:
                # 计算失败，但已经在_execute_bat_with_error_tolerance中处理了错误
                modal_tracker.clear_result()
                raise AnsysError(
                    f"ANSYS执行失败: {output_info.get('error_message', '未知错误')}",
                    details=output_info
                )

        except AnsysError:
            # 重新抛出ANSYS错误
            raise
        except Exception as e:
            # 处理其他异常
            modal_tracker.clear_result()
            raise AnsysError(
                f"执行批处理文件时发生异常: {str(e)}",
                details={'exception': str(e)}
            ) from e

    except AppError as e:
        # 处理应用程序异常
        modal_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_error(e, mesh_window)
    except Exception as e:
        # 处理其他未预期的异常
        modal_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_exception(e, mesh_window)
    finally:
        # 清理临时文件，但排除当前正在使用的文件
        cleanup_modal_temp_files(
            resource_manager,
            error_handler,
            mesh_window,
            exclude_paths=active_files
        )

        # 返回输出目录路径
        if 'modal_config' in locals():
            return modal_config['output_directory']
        else:
            # 如果modal_config未定义，返回默认路径
            return os.path.join(temp_output_dir, f"modal_result_{mesh_parameter.name}").replace("\\", "/")


def _generate_modal_config(mesh_parameter, resource_manager: ResourceManager,calc_params: Dict[str, Any], temp_output_dir: str) -> Dict[str, Any]:
    """生成模态分析网格配置文件

    参考 ctrl/result_slot.py 中的配置文件生成模式，
    生成与 originscript/modal.py 兼容的网格配置文件。

    Args:
        mesh_parameter: 网格参数对象，包含以下属性：
            - name: 网格名称 (str)
            - size: 网格尺寸，单位毫米 (float)
        calc_params: 计算参数字典
        temp_output_dir: 临时输出目录路径 (str)

    Returns:
        Dict[str, Any]: 包含以下键值的配置字典：
            - config_path: 配置文件路径 (str)
            - output_directory: 输出目录路径 (str)
            - element_size: 网格尺寸，单位米 (float)

    Raises:
        ValueError: 当网格参数无效时
        FileOperationError: 当文件操作失败时
    """
    try:
        # 1. 参数验证
        if not hasattr(mesh_parameter, 'name') or not hasattr(mesh_parameter, 'size'):
            raise ValueError("网格参数对象缺少必需的属性 (name, size)")

        mesh_name = str(mesh_parameter.name)
        mesh_size_mm = float(mesh_parameter.size)

        if mesh_size_mm <= 0:
            raise ValueError(f"网格尺寸必须大于0，当前值: {mesh_size_mm} mm")

        if not temp_output_dir or not isinstance(temp_output_dir, str):
            raise ValueError("临时输出目录路径无效")

        # 2. 单位转换：毫米转米
        element_size_m = mesh_size_mm / 1000.0

        # 3. 构建输出目录路径
        output_directory = os.path.join(temp_output_dir, f"modal_result_{mesh_name}").replace("\\", "/")

        # 4. 创建输出目录
        try:
            os.makedirs(output_directory, exist_ok=True)
            logger.info(f"输出目录创建成功: {output_directory}")
        except Exception as e:
            raise FileOperationError(
                "创建输出目录失败",
                details={'directory': output_directory, 'error': str(e)}
            ) from e

        # 5. 构建网格配置数据（与 originscript/modal.py 兼容）
        modal_config = {
            "element_size": [element_size_m],  # 数组格式，单位：米
            "output_directory": output_directory
        }

        # 6. 保存网格配置文件
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)

            # 清理旧的配置文件，只保留最新的5个
            pattern = os.path.join(resource_manager.json_dir, "mesh_config_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            for old_file in json_files[5:]:  # 保留最新的5个文件
                try:
                    os.remove(old_file)
                    print(f"清理旧配置文件: {old_file}")
                except Exception as e:
                    print(f"警告: 无法删除旧的配置文件 {old_file}: {str(e)}")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"mesh_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)

            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(modal_config, f, indent=4, ensure_ascii=False)

            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")

            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
        except Exception as e:
            raise FileOperationError(
                "保存网格配置文件失败",
                details={'file': config_file, 'error': str(e)}
            ) from e



        # 7. 生成分析设置配置文件
        try:
            _generate_analysis_config(resource_manager, calc_params)
        except Exception as e:
            logger.error(f"生成分析设置配置失败: {str(e)}")
            # 不抛出异常，因为网格配置已经成功生成

        # 8. 记录成功日志
        logger.info(f"网格配置已保存: {config_file}")
        logger.info(f"网格参数: 名称={mesh_name}, 尺寸={mesh_size_mm}mm ({element_size_m}m)")
        logger.info(f"输出目录: {output_directory}")

        # 9. 返回配置信息
        return {
            'config_path': config_file,
            'output_directory': output_directory,
            'element_size': element_size_m
        }

    except (ValueError, TypeError) as e:
        logger.error(f"网格配置参数验证失败: {str(e)}")
        raise ValueError(f"网格配置参数验证失败: {str(e)}") from e
    except Exception as e:
        logger.error(f"生成网格配置失败: {str(e)}")
        raise FileOperationError(
            "生成网格配置失败",
            details={'error': str(e)}
        ) from e


def _generate_analysis_config(resource_manager: ResourceManager, calc_params: Dict[str, Any]) -> None:
    """生成分析设置配置文件

    参考 ctrl/analysis_slot.py 中的 get_analysis_json() 函数实现模式，
    生成与 originscript/modal.py 兼容的分析配置文件。

    Args:
        calc_params: 计算参数字典，包含以下键值：
            - modal_count: 模态数量 (int)
            - limit_freq: 是否限制频率范围 (bool)
            - freq_min: 最小频率 (float)
            - freq_max: 最大频率 (float)

    Raises:
        ValueError: 当参数类型或值不正确时
        FileOperationError: 当文件写入失败时
    """
    try:
        # 1. 参数验证和类型转换
        modal_count = int(calc_params.get('modal_count', 12))
        limit_freq = bool(calc_params.get('limit_freq', False))
        freq_min = float(calc_params.get('freq_min', 0.0))
        freq_max = float(calc_params.get('freq_max', 1000.0))

        # 验证参数有效性
        if modal_count <= 0:
            raise ValueError(f"模态数量必须大于0，当前值: {modal_count}")
        if freq_min < 0:
            raise ValueError(f"最小频率不能为负数，当前值: {freq_min}")
        if freq_max <= freq_min:
            raise ValueError(f"最大频率({freq_max})必须大于最小频率({freq_min})")

        # 2. 构建配置数据结构（与 originscript/modal.py 兼容）
        analysis_config = {
            "analysis_settings": {
                "MaximumModesToFind": modal_count,
                "LimitSearchToRange": limit_freq,
                "MinimumFrequency": freq_min,
                "MaximumFrequency": freq_max
            }
        }

        # 3. 保存分析配置文件
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)

            # 清理旧的配置文件，只保留最新的5个
            pattern = os.path.join(resource_manager.json_dir, "analysis_config_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            for old_file in json_files[5:]:  # 保留最新的5个文件
                try:
                    os.remove(old_file)
                    print(f"清理旧配置文件: {old_file}")
                except Exception as e:
                    print(f"警告: 无法删除旧的配置文件 {old_file}: {str(e)}")

            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"analysis_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)

            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(analysis_config, f, indent=4, ensure_ascii=False)

            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "analysis_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")

            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
        except Exception as e:
            raise FileOperationError(
                "保存分析配置文件失败",
                details={'file': config_file, 'error': str(e)}
            ) from e


        # 4. 记录成功日志
        logger.info(f"分析配置已保存: {config_file}")
        logger.info(f"配置参数: 模态数={modal_count}, 频率限制={limit_freq}, 频率范围=[{freq_min}, {freq_max}]")

    except (ValueError, TypeError) as e:
        logger.error(f"分析配置参数验证失败: {str(e)}")
        raise ValueError(f"分析配置参数验证失败: {str(e)}") from e
    except Exception as e:
        logger.error(f"生成分析配置失败: {str(e)}")
        raise FileOperationError(
            "生成分析配置失败",
            details={'error': str(e)}
        ) from e

def read_modal_calculation_results(output_directory: str, mesh_size: float) -> Dict[str, Any]:
    """读取模态计算结果文件

    Args:
        output_directory: 输出目录路径
        mesh_size: 网格尺寸（用于构建文件名）

    Returns:
        Dict: 包含模态分析结果的字典，格式为：
        {
            'success': bool,
            'frequencies': List[float],
            'element_count': int,
            'node_count': int,
            'calculation_time': float,
            'element_size': float,
            'error_message': str
        }
    """
    try:
        # 构建文件路径
        element_size_str = str(mesh_size / 1000.0)  # 转换为米
        modal_freq_file = os.path.join(output_directory, f"modal_freq_{element_size_str}.json")
        mesh_info_file = os.path.join(output_directory, f"mesh_info_{element_size_str}.json")

        logger.info(f"尝试读取模态结果文件: {modal_freq_file}")
        logger.info(f"尝试读取网格信息文件: {mesh_info_file}")

        # 检查文件是否存在
        if not os.path.exists(modal_freq_file):
            logger.warning(f"模态结果文件不存在: {modal_freq_file}")
            return {
                'success': False,
                'error_message': f"模态结果文件不存在: {os.path.basename(modal_freq_file)}"
            }

        # 读取模态分析结果
        modal_data = {}
        try:
            with open(modal_freq_file, 'r', encoding='utf-8') as f:
                modal_data = json.load(f)
            logger.info(f"成功读取模态结果文件，包含 {len(modal_data.get('frequencies_Hz', []))} 个频率")
        except Exception as e:
            logger.error(f"读取模态结果文件失败: {str(e)}")
            return {
                'success': False,
                'error_message': f"读取模态结果文件失败: {str(e)}"
            }

        # 读取网格信息（可选）
        mesh_data = {}
        if os.path.exists(mesh_info_file):
            try:
                with open(mesh_info_file, 'r', encoding='utf-8') as f:
                    mesh_data = json.load(f)
                logger.info(f"成功读取网格信息文件")
            except Exception as e:
                logger.warning(f"读取网格信息文件失败: {str(e)}")

        # 整合数据
        result = {
            'success': True,
            'frequencies': modal_data.get('frequencies_Hz', []),
            'element_count': modal_data.get('element_count', mesh_data.get('Elements', 0)),
            'node_count': modal_data.get('node_count', mesh_data.get('Nodes', 0)),
            'calculation_time': modal_data.get('calculation_time_s', 0.0),
            'element_size': modal_data.get('element_size_m', mesh_data.get('ElementSize', mesh_size / 1000.0)),
            'error_message': ''
        }

        logger.info(f"模态结果读取成功: {len(result['frequencies'])} 个频率, "
                   f"{result['element_count']} 个单元, {result['node_count']} 个节点")

        return result

    except Exception as e:
        logger.error(f"读取模态计算结果失败: {str(e)}")
        return {
            'success': False,
            'error_message': f"读取模态计算结果失败: {str(e)}"
        }


def _execute_bat_with_error_tolerance(bat_file: str, mesh_parameter, output_directory: str,
                                    error_handler: ErrorHandler, mesh_window) -> tuple[bool, Dict[str, Any]]:
    """执行批处理文件，支持非关键性错误容忍

    此函数实现了改进的错误处理机制：
    1. 使用非阻塞的进程执行
    2. 智能错误分类，区分关键性和非关键性错误
    3. 通过结果文件检测计算成功与否
    4. 支持超时和进程监控

    Args:
        bat_file: 批处理文件路径
        mesh_parameter: 网格参数对象
        output_directory: 输出目录路径
        error_handler: 错误处理器
        mesh_window: 网格窗口实例

    Returns:
        tuple: (success: bool, output_info: Dict[str, Any])
            success: 计算是否成功
            output_info: 输出信息字典，包含错误信息、输出内容等
    """

    logger.info(f"开始执行批处理文件: {bat_file}")

    # 初始化返回信息
    output_info = {
        'stdout': '',
        'stderr': '',
        'return_code': None,
        'execution_time': 0,
        'error_message': '',
        'warnings': [],
        'critical_errors': [],
        'process_completed': False
    }

    start_time = time.time()
    max_execution_time = 1800  # 30分钟超时

    try:
        # 启动ANSYS进程（非阻塞）
        logger.info("启动ANSYS进程...")
        process = subprocess.Popen(
            f'"{bat_file}"',
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=os.path.dirname(bat_file)
        )

        # 用于收集输出的线程安全容器
        stdout_lines = []
        stderr_lines = []
        process_finished = threading.Event()

        def collect_stdout():
            """收集标准输出"""
            try:
                for line in iter(process.stdout.readline, ''):
                    if not line:
                        break
                    stdout_lines.append(line.strip())
                    logger.debug(f"STDOUT: {line.strip()}")
            except Exception as e:
                logger.warning(f"收集stdout失败: {str(e)}")
            finally:
                process_finished.set()

        def collect_stderr():
            """收集标准错误输出"""
            try:
                for line in iter(process.stderr.readline, ''):
                    if not line:
                        break
                    stderr_lines.append(line.strip())
                    logger.debug(f"STDERR: {line.strip()}")
            except Exception as e:
                logger.warning(f"收集stderr失败: {str(e)}")

        # 启动输出收集线程
        stdout_thread = threading.Thread(target=collect_stdout, daemon=True)
        stderr_thread = threading.Thread(target=collect_stderr, daemon=True)

        stdout_thread.start()
        stderr_thread.start()

        # 等待进程完成或超时
        logger.info(f"等待ANSYS进程完成，最大等待时间: {max_execution_time}秒")

        while True:
            # 检查进程是否完成
            return_code = process.poll()
            if return_code is not None:
                output_info['return_code'] = return_code
                output_info['process_completed'] = True
                logger.info(f"ANSYS进程完成，返回码: {return_code}")
                break

            # 检查超时
            elapsed_time = time.time() - start_time
            if elapsed_time > max_execution_time:
                logger.warning(f"ANSYS进程执行超时 ({elapsed_time:.1f}s)")
                process.terminate()
                time.sleep(5)
                if process.poll() is None:
                    logger.warning("强制终止ANSYS进程")
                    process.kill()

                output_info['error_message'] = f"执行超时 ({elapsed_time:.1f}s)"
                break

            # 更新界面状态（每10秒更新一次）
            if int(elapsed_time) % 10 == 0:
                if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                    current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                    mesh_window.ui.textEdit_calc_stats.setPlainText(
                        current_text + f"\n⏱️ 计算进行中... ({elapsed_time:.0f}s)"
                    )

            time.sleep(1)  # 每秒检查一次

        # 等待输出收集线程完成
        stdout_thread.join(timeout=5)
        stderr_thread.join(timeout=5)

        # 记录执行时间
        output_info['execution_time'] = time.time() - start_time
        output_info['stdout'] = '\n'.join(stdout_lines)
        output_info['stderr'] = '\n'.join(stderr_lines)

        logger.info(f"批处理执行完成，耗时: {output_info['execution_time']:.1f}秒")

    except Exception as e:
        logger.error(f"执行批处理文件时发生异常: {str(e)}")
        output_info['error_message'] = f"执行异常: {str(e)}"
        output_info['critical_errors'].append(str(e))
        return False, output_info

    # 分析输出，进行智能错误分类
    success = _analyze_execution_output(output_info, mesh_parameter, output_directory)

    return success, output_info


def _analyze_execution_output(output_info: Dict[str, Any], mesh_parameter, output_directory: str) -> bool:
    """分析执行输出，进行智能错误分类

    此函数实现智能错误分类机制：
    1. 区分致命错误、警告和信息性消息
    2. 通过结果文件检测计算是否真正成功
    3. 只对致命错误返回失败状态

    Args:
        output_info: 输出信息字典
        mesh_parameter: 网格参数对象
        output_directory: 输出目录路径

    Returns:
        bool: 计算是否成功
    """
    logger.info("开始分析执行输出...")

    # 定义错误模式分类
    CRITICAL_ERROR_PATTERNS = [
        # 致命错误模式 - 这些错误会导致计算完全失败
        r'fatal error',
        r'critical error',
        r'access violation',
        r'segmentation fault',
        r'out of memory',
        r'license.*not.*available',
        r'license.*expired',
        r'cannot.*open.*file.*for.*writing',
        r'disk.*full',
        r'permission.*denied.*writing',
        r'ansys.*crashed',
        r'solver.*failed.*to.*start',
        r'mesh.*generation.*failed',
        r'geometry.*import.*failed'
    ]

    WARNING_PATTERNS = [
        # 警告模式 - 这些不影响计算结果
        r'warning',
        r'deprecated',
        r'obsolete',
        r'recommendation',
        r'suggestion',
        r'note:',
        r'info:',
        r'convergence.*slow',
        r'element.*quality.*poor',
        r'mesh.*quality.*warning'
    ]

    INFO_PATTERNS = [
        # 信息性消息模式
        r'information',
        r'completed.*successfully',
        r'analysis.*finished',
        r'results.*written',
        r'solution.*converged',
        r'calculation.*complete'
    ]

    # 合并所有输出文本
    all_output = (output_info.get('stdout', '') + '\n' + output_info.get('stderr', '')).lower()

    # 分析错误模式
    import re

    critical_errors = []
    warnings = []
    info_messages = []

    # 检查致命错误
    for pattern in CRITICAL_ERROR_PATTERNS:
        matches = re.findall(pattern, all_output, re.IGNORECASE)
        if matches:
            critical_errors.extend(matches)
            logger.error(f"发现致命错误模式: {pattern} -> {matches}")

    # 检查警告
    for pattern in WARNING_PATTERNS:
        matches = re.findall(pattern, all_output, re.IGNORECASE)
        if matches:
            warnings.extend(matches)
            logger.warning(f"发现警告模式: {pattern} -> {matches}")

    # 检查信息性消息
    for pattern in INFO_PATTERNS:
        matches = re.findall(pattern, all_output, re.IGNORECASE)
        if matches:
            info_messages.extend(matches)
            logger.info(f"发现信息模式: {pattern} -> {matches}")

    # 更新输出信息
    output_info['critical_errors'] = critical_errors
    output_info['warnings'] = warnings
    output_info['info_messages'] = info_messages

    # 如果有致命错误，直接返回失败
    if critical_errors:
        output_info['error_message'] = f"发现致命错误: {'; '.join(critical_errors[:3])}"
        logger.error(f"由于致命错误，计算失败: {output_info['error_message']}")
        return False

    # 检查结果文件是否生成 - 这是判断成功的关键指标
    success = _check_result_files_exist(mesh_parameter, output_directory)

    if success:
        logger.info(f"计算成功完成，网格: {mesh_parameter.name}")
        if warnings:
            logger.warning(f"计算完成但有警告 ({len(warnings)}个): {'; '.join(warnings[:3])}")
    else:
        # 没有致命错误但结果文件不存在，可能是其他问题
        output_info['error_message'] = "计算完成但未生成预期的结果文件"
        logger.warning(f"计算可能失败: {output_info['error_message']}")

    return success


def _check_result_files_exist(mesh_parameter, output_directory: str) -> bool:
    """检查结果文件是否存在

    Args:
        mesh_parameter: 网格参数对象
        output_directory: 输出目录路径

    Returns:
        bool: 结果文件是否存在
    """
    try:
        # 构建期望的结果文件路径
        element_size_str = str(mesh_parameter.size / 1000.0)  # 转换为米
        modal_freq_file = os.path.join(output_directory, f"modal_freq_{element_size_str}.json")

        logger.info(f"检查结果文件: {modal_freq_file}")

        # 检查文件是否存在且不为空
        if os.path.exists(modal_freq_file):
            file_size = os.path.getsize(modal_freq_file)
            if file_size > 0:
                logger.info(f"✅ 结果文件存在且有效: {modal_freq_file} ({file_size} bytes)")

                # 尝试验证JSON格式
                try:
                    with open(modal_freq_file, 'r', encoding='utf-8') as f:
                        import json
                        data = json.load(f)
                        if 'frequencies_Hz' in data and len(data['frequencies_Hz']) > 0:
                            logger.info(f"✅ 结果文件包含有效的频率数据: {len(data['frequencies_Hz'])} 个频率")
                            return True
                        else:
                            logger.warning("⚠️ 结果文件格式不正确或无频率数据")
                            return False
                except Exception as e:
                    logger.warning(f"⚠️ 结果文件JSON格式验证失败: {str(e)}")
                    return False
            else:
                logger.warning(f"⚠️ 结果文件为空: {modal_freq_file}")
                return False
        else:
            logger.warning(f"⚠️ 结果文件不存在: {modal_freq_file}")
            return False

    except Exception as e:
        logger.error(f"检查结果文件时发生错误: {str(e)}")
        return False


def update_mesh_data_from_results(mesh: 'MeshParameter', result_data: Dict[str, Any]) -> bool:
    """根据计算结果更新网格数据

    Args:
        mesh: 要更新的网格参数对象
        result_data: 从read_modal_calculation_results返回的结果数据

    Returns:
        bool: 更新是否成功
    """
    try:
        if not result_data.get('success', False):
            logger.error(f"无法更新网格数据，结果数据无效: {result_data.get('error_message', '未知错误')}")
            return False

        # 更新模态分析结果
        mesh.modal_results.frequencies = result_data['frequencies']
        mesh.modal_results.calculation_time = result_data['calculation_time']

        # 更新网格统计信息
        mesh.statistics.element_count = result_data['element_count']
        mesh.statistics.node_count = result_data['node_count']

        # 更新网格状态
        mesh.update_status(MeshStatus.COMPLETED)

        logger.info(f"网格数据更新成功: {mesh.name}, "
                   f"频率数量: {len(mesh.modal_results.frequencies)}, "
                   f"单元数: {mesh.statistics.element_count}, "
                   f"节点数: {mesh.statistics.node_count}")

        return True

    except Exception as e:
        logger.error(f"更新网格数据失败: {str(e)}")
        return False





class BatchModalCalculationManager(QObject):
    """批量模态计算管理器

    负责管理真实的ANSYS批量模态计算过程，包括：
    1. 文件系统监控 - 检测新生成的结果文件
    2. 进程监控 - 监控ANSYS进程状态
    3. 进度更新 - 实时更新计算进度
    4. 结果处理 - 读取和解析计算结果
    """

    # 信号定义
    progress_updated = Signal(int, int, str, str)  # current, total, mesh_name, status
    mesh_completed = Signal(str, bool)             # mesh_name, success
    calculation_completed = Signal(list)           # results
    calculation_failed = Signal(str)               # error_message

    def __init__(self, window_manager: WindowManager, parent=None):
        """初始化批量计算管理器

        Args:
            window_manager: 窗口管理器实例
            parent: 父对象
        """
        super().__init__(parent)

        # 核心属性
        self.window_manager = window_manager
        self.batch_meshes = []                      # 批量计算的网格列表
        self.calc_params = {}                       # 计算参数
        self.output_directory = ""                  # 输出目录

        # 监控组件
        self.file_watcher = QFileSystemWatcher()    # 文件系统监控
        self.process_monitor = QTimer()             # 进程监控定时器
        self.backup_scanner = QTimer()              # 备用扫描定时器

        # 状态跟踪
        self.expected_files = {}                    # 期望的输出文件 {file_path: mesh_info}
        self.completed_meshes = []                  # 已完成的网格
        self.failed_meshes = []                     # 失败的网格
        self.processed_files = set()                # 已处理的文件集合

        # ANSYS进程相关
        self.ansys_process = None                   # ANSYS进程引用
        self.calculation_start_time = None          # 计算开始时间
        self.max_calculation_time = 3600            # 最大计算时间（秒）

        # 设置信号连接
        self._setup_connections()

        logger.info("BatchModalCalculationManager 初始化完成")

    def _setup_connections(self):
        """设置信号连接"""
        try:
            # 文件系统监控信号
            self.file_watcher.directoryChanged.connect(self._on_directory_changed)

            # 进程监控信号
            self.process_monitor.timeout.connect(self._check_ansys_process)

            # 备用扫描信号
            self.backup_scanner.timeout.connect(self._scan_output_directory)

            logger.debug("BatchModalCalculationManager 信号连接设置完成")

        except Exception as e:
            logger.error(f"设置信号连接失败: {str(e)}")

    def start_calculation(self, meshes: List[MeshParameter], calc_params: Dict[str, Any]) -> bool:
        """启动批量模态计算

        Args:
            meshes: 要计算的网格列表
            calc_params: 计算参数

        Returns:
            bool: 启动是否成功
        """
        try:
            logger.info(f"开始启动批量模态计算，网格数量: {len(meshes)}")

            # 保存参数
            self.batch_meshes = meshes
            self.calc_params = calc_params

            # 重置状态
            self._reset_calculation_state()
            
            # 创建输出目录
            self.output_directory = self._create_output_directory()

            # 准备期望的文件列表
            self._prepare_expected_files()

            # 设置文件监控
            self._setup_file_monitoring()

            # 生成批量配置文件
            config_path = self._create_batch_mesh_config()

            # 启动ANSYS批量计算
            success = self._execute_ansys_batch(config_path)

            if success:
                # 启动进程监控
                self._setup_process_monitoring()
                logger.info("批量模态计算启动成功")
                return True
            else:
                logger.error("批量模态计算启动失败")
                self.calculation_failed.emit("ANSYS批量计算启动失败")
                return False

        except Exception as e:
            logger.error(f"启动批量模态计算失败: {str(e)}")
            self.calculation_failed.emit(f"启动失败: {str(e)}")
            return False

    def _reset_calculation_state(self):
        """重置计算状态"""
        try:
            # 停止现有的监控
            self._stop_monitoring()

            # 清理状态
            self.completed_meshes.clear()
            self.failed_meshes.clear()
            self.processed_files.clear()
            self.expected_files.clear()

            # 重置时间
            self.calculation_start_time = time.time()
            self.ansys_process = None

            logger.debug("计算状态重置完成")

        except Exception as e:
            logger.error(f"重置计算状态失败: {str(e)}")

    def _stop_monitoring(self):
        """停止所有监控"""
        try:
            # 停止定时器
            if self.process_monitor.isActive():
                self.process_monitor.stop()

            if self.backup_scanner.isActive():
                self.backup_scanner.stop()

            # 清理文件监控
            if self.file_watcher.directories():
                self.file_watcher.removePaths(self.file_watcher.directories())

            logger.debug("所有监控已停止")

        except Exception as e:
            logger.error(f"停止监控失败: {str(e)}")

    def cleanup(self):
        """清理资源"""
        try:
            logger.info("开始清理BatchModalCalculationManager资源")

            # 停止监控
            self._stop_monitoring()

            # 终止ANSYS进程（如果还在运行）
            if self.ansys_process and self.ansys_process.poll() is None:
                logger.warning("强制终止ANSYS进程")
                self.ansys_process.terminate()

                # 等待进程结束
                try:
                    self.ansys_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("ANSYS进程未能正常终止，强制杀死")
                    self.ansys_process.kill()

            logger.info("BatchModalCalculationManager资源清理完成")

        except Exception as e:
            logger.error(f"清理资源失败: {str(e)}")

    # ==================== 文件监控相关方法 ====================

    def _create_output_directory(self) -> str:
        """创建输出目录 - 使用包含所有网格名称的单个目录"""
        try:
            # 获取工作目录
            main_window = self.window_manager.get_window(WindowType.MAIN)
            if main_window and hasattr(main_window, 'ANSYS_Work_Dir'):
                work_dir = main_window.ANSYS_Work_Dir
            else:
                work_dir = os.getcwd()

            # 生成时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 构建包含所有网格名称的目录名
            mesh_names = "_".join([mesh.name for mesh in self.batch_meshes])

            # 创建单个输出目录，格式：batch_<mesh1>_<mesh2>_<mesh3>_modal_output__<timestamp>
            output_dir_name = f"batch_{mesh_names}_modal_output__{timestamp}"
            output_dir = os.path.join(work_dir, "temp", output_dir_name)

            # 转换为正斜杠格式
            output_dir = output_dir.replace("\\", "/")

            # 创建目录
            os.makedirs(output_dir, exist_ok=True)

            logger.info(f"创建批量计算输出目录: {output_dir}")
            logger.info(f"包含网格: {mesh_names}")
            return output_dir

        except Exception as e:
            logger.error(f"创建输出目录失败: {str(e)}")
            raise FileOperationError(f"创建输出目录失败: {str(e)}")

    def _prepare_expected_files(self):
        """准备期望的输出文件列表"""
        try:
            self.expected_files.clear()

            for mesh in self.batch_meshes:
                # 计算期望的文件路径
                element_size_str = str(mesh.size / 1000.0)  # 转换为米

                modal_freq_file = os.path.join(
                    self.output_directory,
                    f"modal_freq_{element_size_str}.json"
                )
                mesh_info_file = os.path.join(
                    self.output_directory,
                    f"mesh_info_{element_size_str}.json"
                )

                # 保存文件到网格的映射
                self.expected_files[modal_freq_file] = {
                    'mesh': mesh,
                    'type': 'modal_freq',
                    'element_size': element_size_str
                }
                self.expected_files[mesh_info_file] = {
                    'mesh': mesh,
                    'type': 'mesh_info',
                    'element_size': element_size_str
                }

            logger.info(f"准备期望文件列表完成，共 {len(self.expected_files)} 个文件")

        except Exception as e:
            logger.error(f"准备期望文件列表失败: {str(e)}")

    def _setup_file_monitoring(self):
        """设置文件系统监控"""
        try:
            # 添加输出目录到监控
            if os.path.exists(self.output_directory):
                self.file_watcher.addPath(self.output_directory)
                logger.info(f"开始监控输出目录: {self.output_directory}")

            # 启动备用扫描定时器（每30秒扫描一次）
            self.backup_scanner.start(30000)
            logger.debug("备用扫描定时器已启动")

        except Exception as e:
            logger.error(f"设置文件监控失败: {str(e)}")

    def _on_directory_changed(self, path: str):
        """文件系统监控回调 - 目录内容发生变化"""
        try:
            logger.debug(f"检测到目录变化: {path}")
            self._scan_output_directory()

        except Exception as e:
            logger.error(f"处理目录变化失败: {str(e)}")

    def _scan_output_directory(self):
        """扫描输出目录，检测新生成的文件 - 增强版检测逻辑"""
        try:
            if not os.path.exists(self.output_directory):
                logger.warning(f"输出目录不存在: {self.output_directory}")
                return

            # 扫描JSON文件，特别关注modal_freq_*.json文件
            json_pattern = os.path.join(self.output_directory, "*.json")
            current_files = set(glob.glob(json_pattern))

            logger.debug(f"扫描输出目录: {self.output_directory}")
            logger.debug(f"找到JSON文件数量: {len(current_files)}")

            # 特别扫描modal_freq_*.json文件
            modal_freq_pattern = os.path.join(self.output_directory, "modal_freq_*.json")
            modal_freq_files = set(glob.glob(modal_freq_pattern))

            logger.info(f"找到模态频率文件数量: {len(modal_freq_files)}")
            for file_path in modal_freq_files:
                logger.info(f"  - {os.path.basename(file_path)}")

            # 检测新文件
            new_files = current_files - self.processed_files

            if new_files:
                logger.info(f"检测到新文件数量: {len(new_files)}")
                for file_path in new_files:
                    logger.info(f"  - 新文件: {os.path.basename(file_path)}")

            for file_path in new_files:
                if self._is_file_ready(file_path):
                    logger.info(f"处理新文件: {file_path}")
                    self._process_completed_file(file_path)
                    self.processed_files.add(file_path)
                else:
                    logger.debug(f"文件尚未准备好: {file_path}")

        except Exception as e:
            # 避免递归错误，不使用exc_info=True
            logger.error(f"扫描输出目录失败: {str(e)}")
            print(f"ERROR: 扫描输出目录失败: {str(e)}")  # 备用错误输出

    def _is_file_ready(self, file_path: str) -> bool:
        """检查文件是否已完全写入"""
        try:
            # 检查文件大小稳定性
            if not os.path.exists(file_path):
                return False

            size1 = os.path.getsize(file_path)
            time.sleep(0.1)  # 等待100ms
            size2 = os.path.getsize(file_path)

            # 如果文件大小稳定且不为空，认为文件已准备好
            return size1 == size2 and size1 > 0

        except Exception as e:
            logger.warning(f"检查文件状态失败: {file_path}, 错误: {str(e)}")
            return False

    def _process_completed_file(self, file_path: str):
        """处理新完成的结果文件"""
        try:
            logger.info(f"处理完成的文件: {file_path}")

            # 从文件名解析网格尺寸
            mesh_size = self._extract_mesh_size_from_filename(file_path)
            if mesh_size is None:
                logger.warning(f"无法从文件名解析网格尺寸: {file_path}")
                return

            # 找到对应的网格对象
            mesh = self._find_mesh_by_size(mesh_size)
            if mesh is None:
                logger.warning(f"找不到对应的网格对象，尺寸: {mesh_size}")
                return

            # 检查是否是模态频率文件
            if "modal_freq_" in os.path.basename(file_path):
                self._process_modal_result_file(mesh, file_path)

        except Exception as e:
            logger.error(f"处理完成文件失败: {file_path}, 错误: {str(e)}")

    def _process_modal_result_file(self, mesh: MeshParameter, file_path: str):
        """处理模态结果文件 - 直接处理JSON格式"""
        try:
            logger.info(f"开始处理模态结果文件: {file_path}")

            # 直接读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            logger.info(f"JSON文件内容: {json_data}")

            # 检查必要的字段
            if 'frequencies_Hz' in json_data and 'calculation_time_s' in json_data:
                # 创建模态结果对象
                from core.mesh_manager import ModalResults
                modal_results = ModalResults()
                modal_results.frequencies = json_data['frequencies_Hz']
                modal_results.calculation_time = json_data['calculation_time_s']

                # 更新网格数据
                mesh.modal_results = modal_results
                mesh.update_status(MeshStatus.COMPLETED)

                # 添加到已完成列表
                if mesh not in self.completed_meshes:
                    self.completed_meshes.append(mesh)

                # 发射进度更新信号
                self.progress_updated.emit(
                    len(self.completed_meshes),
                    len(self.batch_meshes),
                    mesh.name,
                    "计算完成"
                )

                # 发射网格完成信号
                self.mesh_completed.emit(mesh.name, True)

                logger.info(f"网格 {mesh.name} 计算完成，频率数量: {len(modal_results.frequencies)}")

                # 检查是否所有网格都已完成
                self._check_calculation_completion()

            else:
                # JSON文件格式不正确
                error_msg = f"JSON文件缺少必要字段: {list(json_data.keys())}"
                logger.error(error_msg)
                self._handle_mesh_failure(mesh, error_msg)

        except Exception as e:
            logger.error(f"处理模态结果文件失败: {str(e)}")
            self._handle_mesh_failure(mesh, f"处理结果文件时发生错误: {str(e)}")

    # ==================== 文件解析和网格匹配方法 ====================

    def _extract_mesh_size_from_filename(self, file_path: str) -> Optional[float]:
        """从文件名提取网格尺寸"""
        try:
            pattern = r'modal_freq_(\d+\.?\d*)\.json'
            match = re.search(pattern, os.path.basename(file_path))
            if match:
                element_size_m = float(match.group(1))
                mesh_size_mm = element_size_m * 1000.0  # 转换为毫米
                return mesh_size_mm
            return None

        except Exception as e:
            logger.error(f"解析文件名失败: {file_path}, 错误: {str(e)}")
            return None

    def _find_mesh_by_size(self, mesh_size: float) -> Optional[MeshParameter]:
        """根据尺寸查找对应的网格对象"""
        try:
            for mesh in self.batch_meshes:
                if abs(mesh.size - mesh_size) < 0.001:  # 浮点数比较容差
                    return mesh
            return None

        except Exception as e:
            logger.error(f"查找网格失败，尺寸: {mesh_size}, 错误: {str(e)}")
            return None

    # ==================== 进程监控相关方法 ====================

    def _setup_process_monitoring(self):
        """设置ANSYS进程监控"""
        try:
            # 启动进程监控定时器（每2秒检查一次）
            self.process_monitor.start(2000)
            logger.info("ANSYS进程监控已启动")

        except Exception as e:
            logger.error(f"设置进程监控失败: {str(e)}")

    def _check_ansys_process(self):
        """检查ANSYS进程状态"""
        try:
            # 检查进程是否还在运行
            if self.ansys_process and self.ansys_process.poll() is not None:
                # 进程已结束
                self.process_monitor.stop()

                if self.ansys_process.returncode == 0:
                    logger.info("ANSYS进程正常结束")
                    # 等待一段时间让文件系统完成写入
                    QTimer.singleShot(3000, self._finalize_calculation)
                else:
                    logger.error(f"ANSYS进程异常结束，返回码: {self.ansys_process.returncode}")
                    self._handle_ansys_failure()
                return

            # 检查超时
            if self.calculation_start_time:
                elapsed_time = time.time() - self.calculation_start_time
                if elapsed_time > self.max_calculation_time:
                    logger.warning(f"计算超时 ({elapsed_time:.1f}s > {self.max_calculation_time}s)")
                    self._handle_calculation_timeout()
                    return

            # 检查是否有新的文件生成（心跳检测）
            self._scan_output_directory()

        except Exception as e:
            logger.error(f"检查ANSYS进程失败: {str(e)}")

    def _handle_ansys_failure(self):
        """处理ANSYS进程失败"""
        try:
            logger.error("ANSYS进程执行失败")

            # 停止监控
            self._stop_monitoring()

            # 检查已完成的网格
            completed_count = len(self.completed_meshes)
            total_count = len(self.batch_meshes)

            if completed_count > 0:
                # 部分成功
                error_msg = f"ANSYS进程异常结束，已完成 {completed_count}/{total_count} 个网格的计算"
                logger.warning(error_msg)
                self._finalize_calculation()
            else:
                # 完全失败
                error_msg = "ANSYS进程执行失败，没有网格计算完成"
                logger.error(error_msg)
                self.calculation_failed.emit(error_msg)

        except Exception as e:
            logger.error(f"处理ANSYS失败时发生错误: {str(e)}")

    def _handle_calculation_timeout(self):
        """处理计算超时"""
        try:
            logger.warning("批量计算超时，强制终止")

            # 终止ANSYS进程
            if self.ansys_process and self.ansys_process.poll() is None:
                self.ansys_process.terminate()

                # 等待进程结束
                try:
                    self.ansys_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning("强制杀死ANSYS进程")
                    self.ansys_process.kill()

            # 停止监控
            self._stop_monitoring()

            # 检查已完成的网格
            completed_count = len(self.completed_meshes)
            total_count = len(self.batch_meshes)

            if completed_count > 0:
                error_msg = f"计算超时，已完成 {completed_count}/{total_count} 个网格的计算"
                logger.warning(error_msg)
                self._finalize_calculation()
            else:
                error_msg = "计算超时，没有网格计算完成"
                self.calculation_failed.emit(error_msg)

        except Exception as e:
            logger.error(f"处理计算超时时发生错误: {str(e)}")

    def _handle_mesh_failure(self, mesh: MeshParameter, error_message: str):
        """处理单个网格计算失败"""
        try:
            logger.warning(f"网格 {mesh.name} 计算失败: {error_message}")

            # 添加到失败列表
            if mesh not in self.failed_meshes:
                self.failed_meshes.append(mesh)

            # 更新网格状态
            mesh.update_status(MeshStatus.ERROR)

            # 发射网格完成信号（失败）
            self.mesh_completed.emit(mesh.name, False)

            # 检查是否所有网格都已处理完成
            self._check_calculation_completion()

        except Exception as e:
            logger.error(f"处理网格失败时发生错误: {str(e)}")

    def _check_calculation_completion(self):
        """检查计算是否完成"""
        try:
            total_processed = len(self.completed_meshes) + len(self.failed_meshes)
            total_meshes = len(self.batch_meshes)

            logger.debug(f"计算进度检查: {total_processed}/{total_meshes}")

            if total_processed >= total_meshes:
                logger.info("所有网格计算已完成")
                self._finalize_calculation()

        except Exception as e:
            logger.error(f"检查计算完成状态失败: {str(e)}")

    def _finalize_calculation(self):
        """完成计算，清理资源并发射完成信号 - 强化结果收集逻辑"""
        try:
            logger.info("开始完成批量计算")

            # 停止监控
            self._stop_monitoring()

            # 强制执行手动结果处理，确保所有生成的文件都被正确识别
            logger.info("执行强制结果扫描和处理...")
            self._manual_result_processing()

            # 再次检查是否有遗漏的结果
            if len(self.completed_meshes) == 0 and len(self.failed_meshes) == 0:
                logger.warning("仍然没有找到任何结果，执行深度扫描...")
                self._deep_result_scan()

            # 构建结果列表
            results = []

            # 添加成功的结果
            for mesh in self.completed_meshes:
                result_data = {
                    'mesh': mesh,
                    'success': True,
                    'frequencies': getattr(mesh.modal_results, 'frequencies', []),
                    'calculation_time': getattr(mesh.modal_results, 'calculation_time', 0.0)
                }
                results.append(result_data)
                logger.debug(f"添加成功结果: {mesh.name}, 频率数: {len(result_data['frequencies'])}")

            # 添加失败的结果
            for mesh in self.failed_meshes:
                result_data = {
                    'mesh': mesh,
                    'success': False,
                    'error': 'Calculation failed'
                }
                results.append(result_data)
                logger.debug(f"添加失败结果: {mesh.name}")

            # 验证结果的完整性 - 检查是否有网格被遗漏
            processed_mesh_ids = set()
            for result in results:
                processed_mesh_ids.add(result['mesh'].id)

            # 为未处理的网格创建结果
            for mesh in self.batch_meshes:
                if mesh.id not in processed_mesh_ids:
                    # 检查是否有对应的结果文件存在
                    has_result_file = self._check_mesh_result_file_exists(mesh)
                    if has_result_file:
                        logger.warning(f"网格 {mesh.name} 有结果文件但未被处理，标记为成功")
                        # 尝试最后一次处理
                        try:
                            self._force_process_mesh_result(mesh)
                            if mesh in self.completed_meshes:
                                results.append({
                                    'mesh': mesh,
                                    'success': True,
                                    'frequencies': getattr(mesh.modal_results, 'frequencies', []),
                                    'calculation_time': getattr(mesh.modal_results, 'calculation_time', 0.0)
                                })
                            else:
                                results.append({
                                    'mesh': mesh,
                                    'success': False,
                                    'error': 'Result file exists but processing failed'
                                })
                        except Exception as e:
                            logger.error(f"强制处理网格 {mesh.name} 失败: {str(e)}")
                            results.append({
                                'mesh': mesh,
                                'success': False,
                                'error': f'Force processing failed: {str(e)}'
                            })
                    else:
                        logger.warning(f"网格 {mesh.name} 没有找到结果文件")
                        results.append({
                            'mesh': mesh,
                            'success': False,
                            'error': 'No result files found'
                        })

            # 发射完成信号
            logger.info(f"发射批量计算完成信号，结果数量: {len(results)}")
            self.calculation_completed.emit(results)

            logger.info(f"批量计算完成，成功: {len(self.completed_meshes)}, 失败: {len(self.failed_meshes)}, 总结果: {len(results)}")

        except Exception as e:
            # 避免递归错误，不使用exc_info=True
            logger.error(f"完成计算时发生错误: {str(e)}")
            print(f"ERROR: 完成计算时发生错误: {str(e)}")  # 备用错误输出
            # 即使出错也要发射信号，避免UI卡住
            try:
                error_results = []
                for mesh in self.batch_meshes:
                    error_results.append({
                        'mesh': mesh,
                        'success': False,
                        'error': f'Finalization error: {str(e)}'
                    })
                self.calculation_completed.emit(error_results)
            except Exception as emit_error:
                # 避免递归错误，不使用exc_info=True
                logger.error(f"发射错误结果信号失败: {str(emit_error)}")
                print(f"ERROR: 发射错误结果信号失败: {str(emit_error)}")  # 备用错误输出

    def _manual_result_processing(self):
        """手动处理结果文件 - 当自动监控没有找到结果时使用"""
        try:
            logger.info("开始手动处理批量计算结果")

            if not self.output_directory or not os.path.exists(self.output_directory):
                logger.warning(f"输出目录不存在: {self.output_directory}")
                return

            # 扫描输出目录中的所有结果文件
            result_files = []
            for root, _, files in os.walk(self.output_directory):
                for file in files:
                    if file.endswith('.json') and ('modal_freq' in file or 'modal_result' in file):
                        result_files.append(os.path.join(root, file))

            logger.info(f"找到 {len(result_files)} 个可能的结果文件")
            for file_path in result_files:
                logger.info(f"  - {os.path.basename(file_path)}")

            # 处理每个结果文件
            processed_count = 0
            for file_path in result_files:
                try:
                    logger.info(f"处理结果文件: {file_path}")

                    # 从文件名或路径提取网格尺寸
                    mesh_size = self._extract_mesh_size_from_filename(file_path)
                    if mesh_size is None:
                        logger.warning(f"无法从文件名提取网格尺寸: {file_path}")
                        continue

                    logger.info(f"从文件名提取的网格尺寸: {mesh_size}mm")

                    # 查找对应的网格
                    mesh = self._find_mesh_by_size(mesh_size)
                    if mesh is None:
                        logger.warning(f"找不到尺寸为 {mesh_size}mm 的网格")
                        # 显示所有可用网格尺寸
                        available_sizes = [mesh.size for mesh in self.batch_meshes]
                        logger.info(f"可用网格尺寸: {available_sizes}")
                        continue

                    logger.info(f"找到对应网格: {mesh.name} (尺寸: {mesh.size}mm)")

                    # 检查是否已经处理过
                    if mesh in self.completed_meshes or mesh in self.failed_meshes:
                        logger.debug(f"网格 {mesh.name} 已经处理过，跳过")
                        continue

                    # 处理结果文件
                    self._process_modal_result_file(mesh, file_path)
                    processed_count += 1

                except Exception as e:
                    logger.error(f"处理结果文件 {file_path} 失败: {str(e)}")
                    continue

            logger.info(f"手动结果处理完成，处理文件数: {processed_count}, 成功: {len(self.completed_meshes)}, 失败: {len(self.failed_meshes)}")

        except Exception as e:
            logger.error(f"手动结果处理失败: {str(e)}")

    def _deep_result_scan(self):
        """深度扫描结果文件 - 最后的保障机制"""
        try:
            logger.info("开始深度结果扫描")

            if not self.output_directory or not os.path.exists(self.output_directory):
                logger.warning(f"输出目录不存在，无法进行深度扫描: {self.output_directory}")
                return

            # 扫描所有可能的结果文件
            import glob
            all_files = []
            for pattern in ["*.json", "*.csv", "*.txt"]:
                files = glob.glob(os.path.join(self.output_directory, pattern))
                all_files.extend(files)

            logger.info(f"深度扫描找到 {len(all_files)} 个文件")
            for file_path in all_files:
                logger.info(f"  - {os.path.basename(file_path)}")

            # 特别关注modal_freq_*.json文件
            modal_freq_files = [f for f in all_files if 'modal_freq' in os.path.basename(f) and f.endswith('.json')]
            logger.info(f"找到 {len(modal_freq_files)} 个模态频率文件")

            # 强制处理每个模态频率文件
            for file_path in modal_freq_files:
                try:
                    logger.info(f"强制处理文件: {file_path}")

                    # 从文件名提取网格尺寸
                    mesh_size = self._extract_mesh_size_from_filename(file_path)
                    if mesh_size is None:
                        logger.warning(f"无法从文件名提取网格尺寸: {file_path}")
                        continue

                    # 查找对应的网格
                    mesh = self._find_mesh_by_size(mesh_size)
                    if mesh is None:
                        logger.warning(f"找不到尺寸为 {mesh_size}mm 的网格")
                        continue

                    # 检查是否已经处理过
                    if mesh in self.completed_meshes:
                        logger.info(f"网格 {mesh.name} 已经在完成列表中")
                        continue

                    if mesh in self.failed_meshes:
                        logger.info(f"网格 {mesh.name} 在失败列表中，尝试重新处理")
                        self.failed_meshes.remove(mesh)

                    # 强制处理结果文件
                    self._process_modal_result_file(mesh, file_path)

                except Exception as e:
                    logger.error(f"深度扫描处理文件 {file_path} 失败: {str(e)}")
                    continue

            logger.info(f"深度扫描完成，成功: {len(self.completed_meshes)}, 失败: {len(self.failed_meshes)}")

        except Exception as e:
            logger.error(f"深度结果扫描失败: {str(e)}")

    def _check_mesh_result_file_exists(self, mesh: MeshParameter) -> bool:
        """检查网格的结果文件是否存在"""
        try:
            if not self.output_directory or not os.path.exists(self.output_directory):
                return False

            # 根据网格尺寸构建期望的文件名
            element_size_m = mesh.size / 1000.0  # 转换为米
            expected_filename = f"modal_freq_{element_size_m:.3f}.json"
            expected_path = os.path.join(self.output_directory, expected_filename)

            exists = os.path.exists(expected_path)
            logger.info(f"检查网格 {mesh.name} 结果文件: {expected_filename} -> {'存在' if exists else '不存在'}")

            return exists

        except Exception as e:
            logger.error(f"检查网格结果文件失败: {str(e)}")
            return False

    def _force_process_mesh_result(self, mesh: MeshParameter):
        """强制处理网格结果"""
        try:
            if not self.output_directory or not os.path.exists(self.output_directory):
                logger.warning("输出目录不存在，无法强制处理")
                return

            # 根据网格尺寸构建文件路径
            element_size_m = mesh.size / 1000.0  # 转换为米
            result_filename = f"modal_freq_{element_size_m:.3f}.json"
            result_path = os.path.join(self.output_directory, result_filename)

            if not os.path.exists(result_path):
                logger.warning(f"结果文件不存在: {result_path}")
                return

            logger.info(f"强制处理网格 {mesh.name} 的结果文件: {result_filename}")

            # 直接处理结果文件
            self._process_modal_result_file(mesh, result_path)

        except Exception as e:
            logger.error(f"强制处理网格结果失败: {str(e)}")

    # ==================== 配置文件和ANSYS执行方法 ====================

    def _create_batch_mesh_config(self) -> str:
        """为批量计算创建网格配置文件 - 使用单个输出目录"""
        try:
            # 构建批量配置 - 使用单个输出目录，不创建子目录
            config = {
                "element_size": [mesh.size / 1000.0 for mesh in self.batch_meshes],  # 转换为米
                "output_directory": self.output_directory,  # 单个输出目录
                "batch_mode": True,
                "mesh_names": [mesh.name for mesh in self.batch_meshes],
                "calculation_params": self.calc_params
            }

            # 保存配置文件
            config_path = os.path.join("temp", "batch_mesh_config.json")
            os.makedirs(os.path.dirname(config_path), exist_ok=True)

            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            logger.info(f"批量网格配置文件已创建: {config_path}")
            logger.info(f"输出目录: {self.output_directory}")
            logger.info(f"网格列表: {[mesh.name for mesh in self.batch_meshes]}")

            return config_path

        except Exception as e:
            logger.error(f"创建批量配置文件失败: {str(e)}")
            raise ConfigurationError(f"创建批量配置文件失败: {str(e)}")

    def _execute_ansys_batch(self, config_path: str) -> bool:
        """执行ANSYS批量计算 - 参考execute_single_modal_calculation的实现"""
        try:
            logger.info("开始执行ANSYS批量计算")

            # 导入必要的模块
            from resource_manager import ResourceManager
            from error_handler import ErrorHandler
            from ctrl.modal_slot import cleanup_modal_temp_files

            # 获取窗口实例
            mesh_window = self.window_manager.get_window(WindowType.MESH)
            main_window = self.window_manager.get_window(WindowType.MAIN)

            if not mesh_window or not main_window:
                logger.error("无法获取窗口实例")
                return False

            # 用于存储需要保护的文件路径
            active_files = []

            # 1. 初始化资源管理器
            resource_manager = ResourceManager()
            resource_manager.initialize(main_window.ANSYS_Work_Dir)
            error_handler = ErrorHandler()

            # 首先清理旧的临时文件
            cleanup_modal_temp_files(resource_manager, error_handler, mesh_window)

            # 2. 创建批量配置文件并复制到标准位置
            self._prepare_batch_config_files(resource_manager, config_path)

            # 3. 获取配置文件路径
            ansys_result_path = os.path.join(resource_manager.json_dir, "analysis_modal_config_latest.json")
            constrain_result_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            connection_result_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            cfg_path = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")

            # 检查配置文件是否存在
            for path in [ansys_result_path, constrain_result_path, connection_result_path, cfg_path]:
                if not os.path.exists(path):
                    logger.error(f"配置文件不存在: {path}")
                    return False
                else:
                    logger.info(f"✅ 配置文件验证通过: {os.path.basename(path)}")

            # 4. 创建批量模态分析脚本
            bat_file = self._create_batch_modal_script(resource_manager, main_window, active_files)
            if not bat_file:
                logger.error("创建批量模态分析脚本失败")
                return False

            # 5. 异步启动ANSYS进程
            self._start_ansys_process(bat_file)

            logger.info("ANSYS批量计算启动成功")
            return True

        except Exception as e:
            logger.error(f"执行ANSYS批量计算失败: {str(e)}")
            return False

    def _prepare_batch_config_files(self, resource_manager: ResourceManager, config_path: str):
        """准备批量计算的配置文件"""
        try:
            logger.info("开始准备批量计算配置文件")

            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)

            # 读取批量配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                batch_config = json.load(f)

            # 创建mesh_config_latest.json文件（批量格式） - 使用单个输出目录
            mesh_config = {
                "element_size": batch_config["element_size"],  # 保持列表格式
                "output_directory": batch_config["output_directory"].replace("\\", "/")  # 确保使用正斜杠
            }

            mesh_config_path = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")
            with open(mesh_config_path, 'w', encoding='utf-8') as f:
                json.dump(mesh_config, f, indent=4, ensure_ascii=False)

            logger.info(f"批量网格配置文件已创建: {mesh_config_path}")
            logger.info(f"网格尺寸列表: {mesh_config['element_size']}")
            logger.info(f"输出目录: {mesh_config['output_directory']}")
            logger.info(f"网格列表: {batch_config.get('mesh_names', [])}")

            # 生成分析配置文件（如果不存在）
            self._ensure_analysis_config_files(resource_manager)

        except Exception as e:
            logger.error(f"准备批量配置文件失败: {str(e)}")
            raise

    def _ensure_analysis_config_files(self, resource_manager: ResourceManager):
        """确保分析配置文件存在"""
        try:
            # 分析配置文件
            analysis_config_path = os.path.join(resource_manager.json_dir, "analysis_modal_config_latest.json")
            if not os.path.exists(analysis_config_path):
                # 创建默认的分析配置
                analysis_config = {
                    "modal_count": self.calc_params.get('modal_count', 5),
                    "limit_freq": self.calc_params.get('limit_freq', True),
                    "freq_min": self.calc_params.get('freq_min', 0.0),
                    "freq_max": self.calc_params.get('freq_max', 1000.0)
                }
                with open(analysis_config_path, 'w', encoding='utf-8') as f:
                    json.dump(analysis_config, f, indent=4, ensure_ascii=False)
                logger.info(f"创建默认分析配置: {analysis_config_path}")

            # 约束配置文件
            constrain_config_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            if not os.path.exists(constrain_config_path):
                constrain_config = {"constraints": []}
                with open(constrain_config_path, 'w', encoding='utf-8') as f:
                    json.dump(constrain_config, f, indent=4, ensure_ascii=False)
                logger.info(f"创建默认约束配置: {constrain_config_path}")

            # 连接配置文件
            connection_config_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            if not os.path.exists(connection_config_path):
                connection_config = {"connections": []}
                with open(connection_config_path, 'w', encoding='utf-8') as f:
                    json.dump(connection_config, f, indent=4, ensure_ascii=False)
                logger.info(f"创建默认连接配置: {connection_config_path}")

        except Exception as e:
            logger.error(f"确保分析配置文件失败: {str(e)}")
            raise

    def _create_batch_modal_script(self, resource_manager: ResourceManager, main_window, active_files: list) -> str:
        """创建批量模态分析脚本 - 参考execute_single_modal_calculation的实现"""
        try:
            logger.info("开始创建批量模态分析脚本")

            # 1. 准备源脚本
            source_script_path = os.path.join(resource_manager.base_dir, "originscript", "modal.py")
            with open(source_script_path, "r", encoding="utf-8") as f:
                script_content = f.read()

            # 2. 修改脚本内容 - 替换路径配置
            target_dir = main_window.ANSYS_Work_Dir.replace("\\", "/")
            old_target_dir = r'target_directory = r"D:/data/all-XM/autoworkbench/csdaima"'
            new_target_dir = f'target_directory = r"{target_dir}"'
            script_content = script_content.replace(old_target_dir, new_target_dir)

            # 获取配置文件路径
            ansys_result_path = os.path.join(resource_manager.json_dir, "analysis_modal_config_latest.json")
            constrain_result_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            connection_result_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            cfg_path = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")

            # 使用动态路径替换功能处理mesh_config路径 - 批处理模态分析使用batch配置
            from core.mesh_config_generator import replace_hardcoded_paths
            script_content = replace_hardcoded_paths(script_content, main_window.ANSYS_Work_Dir, config_type="batch")

            # 替换其他配置文件路径
            replacements = {
                r'cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"':
                    f'cfg_path = r"{cfg_path}"',
                r'ansys_result_path = r"D:/data/all-XM/autoworkbench/csdaima/analysis_config_latest.json"':
                    f'ansys_result_path = r"{ansys_result_path}"',
                r'constrain_result_path = r"D:/data/all-XM/autoworkbench/csdaima/2.json"':
                    f'constrain_result_path = r"{constrain_result_path}"',
                r'connection_result_path = r"D:/data/all-XM/autoworkbench/csdaima/connection_result.json"':
                    f'connection_result_path = r"{connection_result_path}"'
            }

            for old, new in replacements.items():
                script_content = script_content.replace(old, new)

            # 3. 创建新版本的脚本
            script_file = resource_manager.create_script_version("batch_modal_analysis.py", script_content)
            active_files.append(script_file)

            # 4. 清理旧版本
            resource_manager.clean_old_versions("batch_modal_analysis.py")

            # 5. 创建Workbench控制脚本
            wb_script_content = f'''
# encoding: utf-8
Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
'''

            # 6. 获取最新的脚本路径
            script_file = script_file.replace("\\", "/")

            # 7. 添加执行命令
            wb_script_content += f'''
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{script_file}")')
model1.Exit()
Save(Overwrite=True)
'''

            # 8. 创建Workbench脚本文件
            wb_script_file = resource_manager.create_temp_file(prefix="batch_modal_wb", suffix=".py")
            active_files.append(wb_script_file)
            with open(wb_script_file, "w", encoding="utf-8") as f:
                f.write(wb_script_content)

            # 9. 创建批处理文件
            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            wb_script_file = wb_script_file.replace("\\", "/")
            bat_content = (
                f'"{ansys_path}" '
                f'-B -R "{wb_script_file}"'
            )
            bat_file = resource_manager.create_temp_file(prefix="batch_modal", suffix=".bat")
            active_files.append(bat_file)
            with open(bat_file, "w", encoding="utf-8") as f:
                f.write(bat_content)

            logger.info(f"批量模态分析脚本创建成功: {bat_file}")
            logger.info(f"  - 模态脚本: {script_file}")
            logger.info(f"  - Workbench脚本: {wb_script_file}")
            logger.info(f"  - 批处理文件: {bat_file}")

            return bat_file

        except Exception as e:
            logger.error(f"创建批量模态分析脚本失败: {str(e)}")
            raise

    def _create_batch_script(self, resource_manager: ResourceManager, config_path: str) -> str:
        """创建批量计算的批处理脚本"""
        try:
            logger.info("开始创建批量计算脚本")

            # 创建模态分析脚本路径
            script_path = os.path.join(resource_manager.script_dir, "modal.py")

            # 确保脚本存在
            if not os.path.exists(script_path):
                # 复制原始脚本
                original_script = os.path.join("originscript", "modal.py")
                if os.path.exists(original_script):
                    shutil.copy2(original_script, script_path)
                    logger.info(f"复制模态分析脚本: {original_script} -> {script_path}")
                else:
                    raise FileNotFoundError(f"找不到原始模态分析脚本: {original_script}")

            # 复制配置文件到标准位置
            standard_config_path = os.path.join("config", "mesh_config.json")
            os.makedirs(os.path.dirname(standard_config_path), exist_ok=True)
            shutil.copy2(config_path, standard_config_path)
            logger.info(f"复制配置文件: {config_path} -> {standard_config_path}")

            # 创建批处理文件
            bat_file = self._create_batch_file(resource_manager, script_path)

            logger.info(f"批量计算脚本创建成功: {bat_file}")
            return bat_file

        except Exception as e:
            logger.error(f"创建批量脚本失败: {str(e)}")
            return None


    def _create_batch_file(self, resource_manager: ResourceManager, script_path: str) -> str:
        """创建批处理文件"""
        try:
            # 生成批处理文件路径
            bat_file = resource_manager.create_temp_file(suffix=".bat")

            # 获取ANSYS安装路径
            main_window = self.window_manager.get_window(WindowType.MAIN)
            if not main_window or not hasattr(main_window, 'ANSYS_Start_File'):
                raise FileNotFoundError("未找到ANSYS安装路径配置")

            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            if not ansys_path or not os.path.exists(ansys_path):
                raise FileNotFoundError(f"ANSYS安装路径不存在: {ansys_path}")

            # 验证脚本路径
            if not script_path or not os.path.exists(script_path):
                raise FileNotFoundError(f"模态分析脚本不存在: {script_path}")

            # 创建批处理内容
            bat_content = f'''@echo off
echo Starting ANSYS Workbench Batch Modal Calculation...
echo Script: {script_path}
echo Output: {self.output_directory}

cd /d "{os.path.dirname(ansys_path)}"
"{ansys_path}" -B -R "{script_path}"

echo ANSYS Workbench execution completed.
pause
'''

            # 写入批处理文件
            with open(bat_file, 'w', encoding='utf-8') as f:
                f.write(bat_content)

            logger.info(f"批处理文件创建成功: {bat_file}")
            return bat_file

        except Exception as e:
            logger.error(f"创建批处理文件失败: {str(e)}")
            raise

    def _start_ansys_process(self, bat_file: str):
        """启动ANSYS进程"""
        try:
            logger.info(f"启动ANSYS批量计算进程: {bat_file}")

            # 异步启动ANSYS进程
            self.ansys_process = subprocess.Popen(
                f'"{bat_file}"',
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=os.path.dirname(bat_file)
            )

            logger.info(f"ANSYS进程已启动，PID: {self.ansys_process.pid}")

        except Exception as e:
            logger.error(f"启动ANSYS进程失败: {str(e)}")
            raise


class MeshWindow(BaseWindow):
    """合并的网格管理窗口类"""

    def __init__(self, window_manager):
        super().__init__(window_manager)

        # 设置合并的UI
        self.ui = ui_mesh_merged.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("网格无关性验证系统")

        # 初始化网格管理器
        self.mesh_manager = MeshManager()

        # 界面状态变量
        self.selected_meshes_for_generation = []  # 选中用于生成的网格
        self.selected_meshes_for_modal = []       # 选中用于模态计算的网格
        self.is_generating = False                # 是否正在生成
        self.is_calculating = False               # 是否正在计算

        # 设置窗口样式
        self._setup_window_style()

        # 初始化UI组件
        self._setup_ui_components()

        # 初始化matplotlib图表组件
        self._setup_chart_widget()

        # 应用按钮动画效果
        self.setup_animated_buttons()

        # 连接信号槽
        self._connect_signals()

        # 加载配置数据
        self._load_configuration()

        # 重置所有网格状态为未生成（确保重新启动时需要重新生成）
        reset_count = self.mesh_manager.reset_all_mesh_states()
        if reset_count > 0:
            logger.info(f"应用启动时重置了 {reset_count} 个网格状态")

        logger.info("合并网格管理窗口初始化完成")

    def _setup_window_style(self):
        """设置窗口样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            QPushButton:disabled {
                background-color: #f5f5f5;
                color: #999999;
            }
        """)

    def _setup_ui_components(self):
        """初始化UI组件"""
        # 设置网格参数表格
        self._setup_mesh_table()

        # 设置网格状态列表
        self._setup_mesh_status_list()

        # 设置对比网格列表
        self._setup_comparison_list()

        # 设置标签页
        self._setup_tab_widget()

        logger.debug("UI组件初始化完成")

    def _setup_chart_widget(self):
        """设置matplotlib图表组件"""
        try:
            # 导入数据管理器
            from ui.components.modal_data_manager import ModalDataManager

            # 创建数据管理器
            self.modal_data_manager = ModalDataManager("mesh_modal_data.pkl")

            # 创建图表组件（传入数据管理器）
            self.modal_chart_widget = ModalChartWidget(data_manager=self.modal_data_manager)

            # 将图表组件添加到图表容器中
            chart_container = self.ui.widget_chart_container
            if chart_container.layout():
                # 清空现有布局
                layout = chart_container.layout()
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                # 创建新布局
                from PySide6.QtWidgets import QVBoxLayout
                layout = QVBoxLayout(chart_container)
                layout.setContentsMargins(0, 0, 0, 0)

            # 添加图表组件到布局
            layout = chart_container.layout()
            layout.addWidget(self.modal_chart_widget)

            # 连接图表信号
            self.modal_chart_widget.chart_updated.connect(self._on_chart_updated)

            logger.debug("matplotlib图表组件设置完成")

        except Exception as e:
            logger.error(f"图表组件设置失败: {str(e)}")
            # 如果图表组件设置失败，保持原有的占位符
            self.modal_chart_widget = None
            self.modal_data_manager = None

    def _setup_mesh_table(self):
        """设置网格参数表格"""
        table = self.ui.tableWidget_mesh_params

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        table.setSortingEnabled(True)

        # 设置列宽模式
        header = table.horizontalHeader()
        header.setStretchLastSection(False)

        # 设置各列的宽度策略
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # 名称列
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # 尺寸列
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # 单元类型列
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # 状态列
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)          # 创建时间列
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # 操作列

        # 设置行高
        table.verticalHeader().setDefaultSectionSize(35)
        table.verticalHeader().setVisible(False)

        logger.debug("网格参数表格设置完成")

    def _setup_mesh_status_list(self):
        """设置网格状态列表"""
        try:
            # 设置列表属性
            list_widget = self.ui.listWidget_mesh_status
            list_widget.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
            list_widget.clear()

            # 设置样式 - 优化选中状态的文字可见性
            list_widget.setStyleSheet("""
                QListWidget {
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    background-color: white;
                }
                QListWidget::item {
                    border-bottom: 1px solid #eeeeee;
                    padding: 8px;
                    margin: 1px;
                    color: #333333;
                }
                QListWidget::item:selected {
                    background-color: #2196f3;
                    border: 1px solid #1976d2;
                    color: white;
                    font-weight: bold;
                }
                QListWidget::item:hover {
                    background-color: #f5f5f5;
                    color: #333333;
                }
                QListWidget::item:selected:hover {
                    background-color: #1976d2;
                    color: white;
                }
            """)

            logger.debug("网格状态列表设置完成")
        except Exception as e:
            logger.error(f"设置网格状态列表失败: {str(e)}")

    def _setup_comparison_list(self):
        """设置对比网格列表"""
        try:
            # 设置对比网格列表
            comparison_list = self.ui.listWidget_comparison_meshes
            comparison_list.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)
            comparison_list.clear()

            # 初始化模态分析显示选项
            self.ui.checkBox_show_frequencies.setChecked(True)
            self.ui.checkBox_show_mode_shapes.setChecked(True)
            self.ui.checkBox_show_mesh_info.setChecked(True)

            # 初始化图表类型选择
            self.ui.radioButton_frequency_comparison.setChecked(True)

            logger.debug("对比网格列表设置完成")
        except Exception as e:
            logger.error(f"设置对比网格列表失败: {str(e)}")

    def _setup_tab_widget(self):
        """设置标签页"""
        try:
            # 设置默认标签页
            self.ui.tabWidget_main.setCurrentIndex(0)

            # 初始化模态参数
            self.ui.spinBox_modal_count.setValue(10)
            self.ui.doubleSpinBox_freq_min.setValue(0.0)
            self.ui.doubleSpinBox_freq_max.setValue(1000.0)
            self.ui.checkBox_limit_freq.setChecked(True)

            # 初始化状态标签（安全检查）
            if hasattr(self.ui, 'label_selection_count'):
                self.ui.label_selection_count.setText("已选择: 0 个网格")
            if hasattr(self.ui, 'label_selected_info'):
                self.ui.label_selected_info.setText("请选择要进行模态计算的网格")
            if hasattr(self.ui, 'label_generation_progress'):
                self.ui.label_generation_progress.setText("生成进度: 准备就绪")
            if hasattr(self.ui, 'label_current_calc'):
                self.ui.label_current_calc.setText("当前状态: 等待开始")

            logger.debug("标签页设置完成")
        except Exception as e:
            logger.error(f"设置标签页失败: {str(e)}")
    def cs(self):
        print("DEBUG: CS")

    def _connect_signals(self):
        """连接信号槽"""
        # 网格管理标签页信号
        self.ui.btn_add_mesh.clicked.connect(self._on_add_mesh)
        self.ui.btn_import_mesh.clicked.connect(self._on_import_mesh)
        self.ui.btn_export_mesh.clicked.connect(self._on_export_mesh)

        # 表格信号
        self.ui.tableWidget_mesh_params.itemSelectionChanged.connect(self._on_mesh_selection_changed)
        self.ui.tableWidget_mesh_params.cellDoubleClicked.connect(self._on_mesh_double_clicked)

        # 批量网格操作信号
        self.ui.btn_select_all_meshes.clicked.connect(self._on_select_all_meshes)
        self.ui.btn_select_none_meshes.clicked.connect(self._on_select_none_meshes)
        self.ui.btn_select_inverse_meshes.clicked.connect(self._on_select_inverse_meshes)
        self.ui.listWidget_mesh_status.itemSelectionChanged.connect(self._on_mesh_status_selection_changed)

        # 批量生成信号已移除 - 现在模态计算会自动处理网格生成

        # 模态计算信号
        self.ui.btn_single_modal.clicked.connect(self._on_single_modal)

        self.ui.btn_batch_modal.clicked.connect(self._on_batch_modal)
        self.ui.btn_pause_calculation.clicked.connect(self._on_pause_calculation)
        self.ui.btn_stop_calculation.clicked.connect(self._on_stop_calculation)
        self.ui.btn_stop_calculation.clicked.connect(self.cs)

        # 模态参数信号
        self.ui.checkBox_limit_freq.toggled.connect(self._on_freq_limit_toggled)
        self.ui.spinBox_modal_count.valueChanged.connect(self._on_modal_params_changed)
        self.ui.doubleSpinBox_freq_min.valueChanged.connect(self._on_modal_params_changed)
        self.ui.doubleSpinBox_freq_max.valueChanged.connect(self._on_modal_params_changed)

        # 结果选择信号
        self.ui.btn_select_results.clicked.connect(self._on_select_results)

        # 结果对比信号
        self.ui.btn_update_chart.clicked.connect(self._on_update_chart)
        self.ui.btn_import_results.clicked.connect(self._on_import_results)
        self.ui.btn_export_results.clicked.connect(self._on_export_results)
        self.ui.btn_save_chart.clicked.connect(self._on_save_chart)

        # 对比列表右键菜单和多选模式
        self.ui.listWidget_comparison_meshes.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.ui.listWidget_comparison_meshes.customContextMenuRequested.connect(self._show_comparison_context_menu)
        # 启用多选模式，支持Ctrl+点击和Shift+点击
        self.ui.listWidget_comparison_meshes.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)

        # 模态分析选项信号
        self.ui.checkBox_show_frequencies.toggled.connect(self._on_modal_option_changed)
        self.ui.checkBox_show_mode_shapes.toggled.connect(self._on_modal_option_changed)
        self.ui.checkBox_show_mesh_info.toggled.connect(self._on_modal_option_changed)

        # 图表类型选择信号
        self.ui.radioButton_frequency_comparison.toggled.connect(self._on_chart_type_changed)
        self.ui.radioButton_mode_distribution.toggled.connect(self._on_chart_type_changed)
        self.ui.radioButton_mesh_convergence.toggled.connect(self._on_chart_type_changed)

        # 底部导航按钮信号
        self.ui.btn_generate_mesh.clicked.connect(self._on_generate_mesh_nav)
        self.ui.btn_view_results.clicked.connect(self._on_view_results)
        self.ui.btn_previous.clicked.connect(self._on_previous)
        self.ui.btn_next.clicked.connect(self._on_next)
        self.ui.btn_main_menu.clicked.connect(self._on_main_menu)

        # 标签页切换信号
        self.ui.tabWidget_main.currentChanged.connect(self._on_tab_changed)

        # 网格管理器信号
        self.mesh_manager.signals.mesh_added.connect(self._on_mesh_added)
        self.mesh_manager.signals.mesh_removed.connect(self._on_mesh_removed)
        self.mesh_manager.signals.mesh_updated.connect(self._on_mesh_updated)
        self.mesh_manager.signals.status_changed.connect(self._on_mesh_status_changed)
        self.mesh_manager.signals.current_mesh_changed.connect(self._on_current_mesh_changed)
        self.mesh_manager.signals.error_occurred.connect(self._on_mesh_error)

        logger.debug("信号槽连接完成")

    def _load_configuration(self):
        """加载配置数据"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 加载网格参数
            mesh_data = config_manager.get_mesh_parameters()
            if mesh_data:
                success = self.mesh_manager.from_dict({"mesh_parameters": mesh_data})
                if success:
                    logger.info(f"成功加载 {len(mesh_data)} 个网格参数")
                else:
                    logger.warning("加载网格参数失败")

            # 设置当前网格
            current_mesh_id = config_manager.get_current_mesh_id()
            if current_mesh_id:
                self.mesh_manager.set_current_mesh(current_mesh_id)

            # 刷新UI显示
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"加载配置数据失败: {str(e)}")
            QMessageBox.warning(self, "警告", f"加载配置数据失败: {str(e)}")

    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            # 网格管理按钮
            self.ui.btn_add_mesh,
            self.ui.btn_import_mesh,
            self.ui.btn_export_mesh,

            # 批量操作按钮
            self.ui.btn_select_all_meshes,
            self.ui.btn_select_none_meshes,
            self.ui.btn_select_inverse_meshes,

            # 模态分析按钮
            self.ui.btn_single_modal,
            self.ui.btn_batch_modal,
            self.ui.btn_pause_calculation,
            self.ui.btn_stop_calculation,

            # 结果选择按钮
            self.ui.btn_select_results,
            self.ui.btn_import_results,
            self.ui.btn_export_results,

            # 底部导航按钮
            self.ui.btn_generate_mesh,
            self.ui.btn_view_results,
            self.ui.btn_previous,
            self.ui.btn_next,
            self.ui.btn_main_menu
        ]

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        logger.debug("按钮动画效果设置完成")

    # ==================== 网格管理事件处理 ====================

    def _on_add_mesh(self):
        """添加网格按钮点击处理"""
        try:
            dialog = MeshParameterDialog(self)
            dialog.parameter_accepted.connect(self._add_new_mesh)
            dialog.exec()

        except Exception as e:
            logger.error(f"添加网格对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加网格对话框失败: {str(e)}")

    def _add_new_mesh(self, mesh_parameter: MeshParameter):
        """添加新网格参数"""
        try:
            success = self.mesh_manager.add_mesh(mesh_parameter)
            if success:
                # 保存配置
                self._save_configuration()

                # 强制刷新所有UI组件
                self._refresh_all_ui()

                # 处理事件循环，确保UI更新
                QApplication.processEvents()

                # 显示成功消息
                mesh_count = self.mesh_manager.mesh_count
                CustomMessageBox.information(
                    self, "添加成功",
                    f"成功添加网格: {mesh_parameter.name}\n当前共 {mesh_count} 个网格"
                )

                logger.info(f"成功添加网格: {mesh_parameter.name}, 总网格数: {mesh_count}")

                # 显示状态消息
                self.show_status_message(f"成功添加网格 '{mesh_parameter.name}'")

            else:
                QMessageBox.warning(self, "添加失败", "添加网格失败，请检查参数设置")

        except Exception as e:
            logger.error(f"添加网格失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"添加网格失败: {str(e)}")

    def _on_import_mesh(self):
        """导入网格配置按钮点击处理"""
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "导入网格配置", "", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 记录导入前的网格数量
                initial_count = self.mesh_manager.mesh_count

                success = self.mesh_manager.import_from_json(file_path)
                if success:
                    # 立即刷新所有UI组件
                    self._refresh_all_ui()

                    # 保存配置
                    self._save_configuration()

                    # 显示导入结果
                    final_count = self.mesh_manager.mesh_count
                    imported_count = final_count - initial_count if final_count > initial_count else final_count

                    CustomMessageBox.information(
                        self, "导入成功",
                        f"成功导入 {imported_count} 个网格配置\n总网格数量: {final_count}"
                    )

                    logger.info(f"成功导入网格配置: {file_path}, 导入数量: {imported_count}")

                    # 显示状态消息
                    self.show_status_message(f"成功导入 {imported_count} 个网格配置")

                else:
                    QMessageBox.warning(self, "导入失败", "网格配置导入失败，请检查文件格式")

        except Exception as e:
            logger.error(f"导入网格配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导入网格配置失败: {str(e)}")

    def _on_export_mesh(self):
        """导出网格配置按钮点击处理"""
        try:
            if self.mesh_manager.mesh_count == 0:
                CustomMessageBox.information(self, "提示", "没有网格参数可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出网格配置", "mesh_config.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                success = self.mesh_manager.export_to_json(file_path)
                if success:
                    CustomMessageBox.information(self, "成功", "网格配置导出成功")
                    logger.info(f"成功导出网格配置: {file_path}")
                else:
                    QMessageBox.warning(self, "失败", "网格配置导出失败")
        except Exception as e:
            logger.error(f"导出网格配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出网格配置失败: {str(e)}")

    def _on_mesh_selection_changed(self):
        """网格表格选择变化处理"""
        try:
            selected_items = self.ui.tableWidget_mesh_params.selectedItems()
            if selected_items:
                row = selected_items[0].row()
                mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
                if mesh_id:
                    self.mesh_manager.set_current_mesh(mesh_id)
                    logger.debug(f"选择网格: {mesh_id}")

        except Exception as e:
            logger.error(f"处理网格选择变化失败: {str(e)}")

    def _on_mesh_double_clicked(self, row: int, column: int):
        """网格表格双击处理"""
        try:
            mesh_id = self.ui.tableWidget_mesh_params.item(row, 0).data(Qt.ItemDataRole.UserRole)
            if mesh_id:
                mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
                if mesh_param:
                    # 打开编辑对话框
                    dialog = MeshParameterDialog(self, mesh_param)
                    dialog.parameter_accepted.connect(lambda updated_param: self._update_mesh(mesh_id, updated_param))
                    dialog.exec()

        except Exception as e:
            logger.error(f"处理网格双击失败: {str(e)}")

    def _update_mesh(self, mesh_id: str, updated_param: MeshParameter):
        """更新网格参数"""
        try:
            # 确保updated_param有正确的ID
            updated_param.id = mesh_id

            # MeshManager.update_mesh()只需要一个MeshParameter参数
            success = self.mesh_manager.update_mesh(updated_param)
            if success:
                self._save_configuration()
                CustomMessageBox.information(self, "更新成功", f"网格 '{updated_param.name}' 更新成功")
                logger.info(f"网格更新成功: {updated_param.name}")
            else:
                QMessageBox.warning(self, "更新失败", "网格更新失败")

        except Exception as e:
            logger.error(f"更新网格失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"更新网格失败: {str(e)}")

    # ==================== 批量网格操作事件处理 ====================

    def _on_select_all_meshes(self):
        """全选网格按钮点击处理"""
        try:
            self.ui.listWidget_mesh_status.selectAll()
            logger.debug("全选网格")
        except Exception as e:
            logger.error(f"全选网格失败: {str(e)}")

    def _on_select_none_meshes(self):
        """全不选网格按钮点击处理"""
        try:
            self.ui.listWidget_mesh_status.clearSelection()
            logger.debug("全不选网格")
        except Exception as e:
            logger.error(f"全不选网格失败: {str(e)}")

    def _on_select_inverse_meshes(self):
        """反选网格按钮点击处理"""
        try:
            list_widget = self.ui.listWidget_mesh_status
            for i in range(list_widget.count()):
                item = list_widget.item(i)
                item.setSelected(not item.isSelected())
            logger.debug("反选网格")
        except Exception as e:
            logger.error(f"反选网格失败: {str(e)}")

    def _on_mesh_status_selection_changed(self):
        """网格状态列表选择变化处理"""
        try:
            # 如果正在进行批量生成，不要修改选择列表
            if self.is_generating:
                logger.debug("批量生成进行中，跳过选择变化处理")
                return

            selected_items = self.ui.listWidget_mesh_status.selectedItems()
            self.selected_meshes_for_generation = []

            for item in selected_items:
                mesh_id = item.data(Qt.ItemDataRole.UserRole)
                if mesh_id:
                    mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
                    if mesh:
                        self.selected_meshes_for_generation.append(mesh)

            # 更新选择计数
            count = len(self.selected_meshes_for_generation)
            self.ui.label_selection_count.setText(f"已选择: {count} 个网格")

            # 更新模态计算信息
            self._update_modal_selection_info()

            # 更新按钮状态
            self._update_ui_state()

            logger.debug(f"选择了 {count} 个网格用于批量操作")

        except Exception as e:
            logger.error(f"处理网格状态选择变化失败: {str(e)}")



    # ==================== 模态计算验证方法 ====================

    def _validate_mesh_for_modal_calculation(self, mesh: MeshParameter) -> Dict[str, Any]:
        """验证单个网格是否可以进行模态计算

        Args:
            mesh: 要验证的网格

        Returns:
            Dict: 包含验证结果的字典
        """
        try:
            # 简化的验证逻辑 - 只检查基本参数，不检查网格状态
            # 系统将在模态计算过程中自动处理网格生成

            # 检查网格基本参数
            if not mesh.name or mesh.name.strip() == "":
                return {
                    'valid': False,
                    'message': f"❌ 网格参数验证失败\n\n"
                              f"网格名称不能为空。\n\n"
                              f"📋 解决方案:\n"
                              f"1. 为网格设置有效的名称\n"
                              f"2. 重新尝试模态计算"
                }

            if mesh.size <= 0:
                return {
                    'valid': False,
                    'message': f"❌ 网格参数验证失败\n\n"
                              f"网格 '{mesh.name}' 的尺寸参数无效 ({mesh.size})。\n\n"
                              f"📋 解决方案:\n"
                              f"1. 设置有效的网格尺寸 (> 0)\n"
                              f"2. 重新尝试模态计算"
                }

            # 检查是否正在计算中
            if mesh.status == MeshStatus.CALCULATING:
                return {
                    'valid': False,
                    'message': f"⏳ 网格正在计算中\n\n"
                              f"网格 '{mesh.name}' 正在进行模态计算，请等待计算完成。\n\n"
                              f"📋 当前状态: {mesh.status.value}\n\n"
                              f"💡 提示: 请等待当前计算完成后再进行新的计算。"
                }

            # 验证通过 - 简化的验证逻辑
            # 系统将在模态计算过程中自动处理网格生成和验证
            return {
                'valid': True,
                'message': f"✅ 网格验证通过\n\n"
                          f"网格 '{mesh.name}' 可以进行模态计算:\n"
                          f"• 网格尺寸: {mesh.size} mm\n"
                          f"• 单元类型: {mesh.element_type}\n"
                          f"• 当前状态: {mesh.status.value}\n\n"
                          f"💡 系统将自动处理网格生成和模态计算流程。"
            }

        except Exception as e:
            logger.error(f"验证网格失败: {str(e)}")
            return {
                'valid': False,
                'message': f"❌ 验证过程出错\n\n"
                          f"验证网格 '{mesh.name}' 时发生错误: {str(e)}\n\n"
                          f"请检查网格数据完整性或联系技术支持。"
            }

    def _validate_meshes_for_batch_modal_calculation(self) -> Dict[str, Any]:
        """验证选中的网格是否可以进行批量模态计算

        Returns:
            Dict: 包含验证结果的字典
        """
        try:
            selected_meshes = self.selected_meshes_for_generation

            if not selected_meshes:
                return {
                    'valid': False,
                    'message': "⚠️ 没有选择任何网格\n\n"
                              "请在网格状态列表中选择要进行模态计算的网格。\n\n"
                              "📋 操作步骤:\n"
                              "1. 在网格状态列表中选择一个或多个网格\n"
                              "2. 点击'批量模态计算'按钮\n"
                              "3. 系统将自动处理网格生成和模态计算"
                }

            # 简化的验证逻辑 - 只检查是否有正在计算的网格
            calculating_meshes = []
            valid_meshes = []

            for mesh in selected_meshes:
                if mesh.status == MeshStatus.CALCULATING:
                    calculating_meshes.append(mesh)
                else:
                    # 验证基本参数
                    if mesh.name and mesh.name.strip() != "" and mesh.size > 0:
                        valid_meshes.append(mesh)

            # 检查是否有正在计算的网格
            if calculating_meshes:
                return {
                    'valid': False,
                    'message': f"⏳ 有网格正在计算中\n\n"
                              f"以下网格正在进行模态计算，请等待完成：\n" +
                              "\n".join([f"• {mesh.name}" for mesh in calculating_meshes]) +
                              f"\n\n💡 提示: 请等待当前计算完成后再进行新的批量计算。"
                }

            # 检查是否有有效的网格
            if not valid_meshes:
                return {
                    'valid': False,
                    'message': "❌ 没有可用于模态计算的网格\n\n"
                              "选中的网格参数无效或缺失。\n\n"
                              "📋 解决方案:\n"
                              "1. 检查网格名称是否有效\n"
                              "2. 确保网格尺寸大于0\n"
                              "3. 重新选择有效的网格参数"
                }

            # 验证通过
            return {
                'valid': True,
                'valid_meshes': valid_meshes,
                'message': f"✅ 验证通过\n\n"
                          f"共 {len(valid_meshes)} 个网格可以进行模态计算:\n" +
                          "\n".join([f"• {mesh.name} (尺寸: {mesh.size}mm)" for mesh in valid_meshes]) +
                          f"\n\n💡 系统将自动处理网格生成和模态计算流程。"
            }

        except Exception as e:
            logger.error(f"批量验证网格失败: {str(e)}")
            return {
                'valid': False,
                'message': f"❌ 验证过程出错\n\n"
                          f"批量验证网格时发生错误: {str(e)}\n\n"
                          f"请检查网格数据完整性或联系技术支持。"
            }

    def _fix_mesh_data_if_needed(self, mesh: MeshParameter):
        """修复网格数据中的异常值

        Args:
            mesh: 要修复的网格参数
        """
        try:
            # 检查并修复统计信息
            if not hasattr(mesh, 'statistics') or mesh.statistics is None:
                from core.mesh_manager import MeshStatistics
                mesh.statistics = MeshStatistics()
                logger.warning(f"为网格 {mesh.name} 创建了缺失的统计信息")

            # 修复负数或异常的节点数和单元数
            if mesh.statistics.node_count <= 0:
                # 基于网格尺寸估算合理的节点数
                estimated_nodes = max(100, int(1000 / (mesh.size ** 2)))
                mesh.statistics.node_count = estimated_nodes
                logger.warning(f"修复网格 {mesh.name} 的节点数: {estimated_nodes}")

            if mesh.statistics.element_count <= 0:
                # 基于节点数估算单元数
                estimated_elements = max(50, int(mesh.statistics.node_count * 0.8))
                mesh.statistics.element_count = estimated_elements
                logger.warning(f"修复网格 {mesh.name} 的单元数: {estimated_elements}")

            # 修复网格质量
            if mesh.statistics.avg_quality <= 0 or mesh.statistics.avg_quality > 1:
                mesh.statistics.avg_quality = 0.75  # 默认质量
                logger.warning(f"修复网格 {mesh.name} 的平均质量: 0.75")

            # 修复生成时间
            if mesh.statistics.generation_time <= 0:
                mesh.statistics.generation_time = 60.0  # 默认生成时间
                logger.warning(f"修复网格 {mesh.name} 的生成时间: 60.0s")

            # 确保网格状态合理
            if mesh.status == MeshStatus.NOT_GENERATED and (
                mesh.statistics.node_count > 0 and mesh.statistics.element_count > 0
            ):
                mesh.update_status(MeshStatus.GENERATED)
                logger.warning(f"修复网格 {mesh.name} 的状态: 已生成")

            logger.debug(f"网格数据修复完成: {mesh.name}")

        except Exception as e:
            logger.error(f"修复网格数据失败: {str(e)}")

    def _confirm_recalculation(self, completed_meshes: List[MeshParameter]) -> bool:
        """确认是否重新计算已完成的网格

        Args:
            completed_meshes: 已完成计算的网格列表

        Returns:
            bool: 用户是否确认重新计算
        """
        try:
            if not completed_meshes:
                return True

            # 构建详细的确认消息
            message_parts = ["🔄 重新计算确认\n"]

            if len(completed_meshes) == 1:
                mesh = completed_meshes[0]
                freq_count = len(mesh.modal_results.frequencies)
                calc_time = mesh.modal_results.calculation_time
                completion_time = mesh.updated_time.strftime("%Y-%m-%d %H:%M:%S") if mesh.updated_time else "未知"

                message_parts.extend([
                    f"网格 '{mesh.name}' 已有模态计算结果：",
                    "",
                    f"📊 现有计算结果:",
                    f"• 模态数量: {freq_count} 个",
                    f"• 计算时间: {calc_time:.1f} 秒",
                    f"• 完成时间: {completion_time}",
                    "",
                    f"⚠️ 重新计算将会:",
                    f"• 覆盖现有的计算结果",
                    f"• 重新生成所有模态频率",
                    f"• 更新计算统计信息",
                    "",
                    f"💾 建议: 现有结果将被自动备份",
                    "",
                    f"是否确认重新进行模态计算？"
                ])
            else:
                total_modals = sum(len(mesh.modal_results.frequencies) for mesh in completed_meshes)
                avg_calc_time = sum(mesh.modal_results.calculation_time for mesh in completed_meshes) / len(completed_meshes)

                message_parts.extend([
                    f"以下 {len(completed_meshes)} 个网格已有模态计算结果：",
                    ""
                ])

                for mesh in completed_meshes:
                    freq_count = len(mesh.modal_results.frequencies)
                    calc_time = mesh.modal_results.calculation_time
                    message_parts.append(f"• {mesh.name}: {freq_count} 个模态, {calc_time:.1f}s")

                message_parts.extend([
                    "",
                    f"📊 现有计算统计:",
                    f"• 总模态数: {total_modals} 个",
                    f"• 平均计算时间: {avg_calc_time:.1f} 秒",
                    "",
                    f"⚠️ 重新计算将会:",
                    f"• 覆盖所有现有的计算结果",
                    f"• 重新生成所有模态频率",
                    f"• 更新所有计算统计信息",
                    "",
                    f"💾 建议: 现有结果将被自动备份",
                    "",
                    f"是否确认重新进行批量模态计算？"
                ])

            # 显示确认对话框
            reply = CustomMessageBox.question(
                self, "重新计算确认",
                "\n".join(message_parts)
            )

            if reply == CustomMessageBox.Yes:
                # 备份现有结果
                self._backup_modal_results(completed_meshes)
                logger.info(f"用户确认重新计算 {len(completed_meshes)} 个已完成的网格")
                return True
            else:
                logger.info(f"用户取消重新计算 {len(completed_meshes)} 个已完成的网格")
                return False

        except Exception as e:
            logger.error(f"确认重新计算失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"确认重新计算时发生错误: {str(e)}")
            return False

    def _backup_modal_results(self, meshes: List[MeshParameter]):
        """备份网格的模态计算结果

        Args:
            meshes: 要备份结果的网格列表
        """
        try:
            from datetime import datetime
            backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")

            for mesh in meshes:
                if mesh.modal_results.frequencies:
                    # 创建备份数据
                    backup_data = {
                        'mesh_name': mesh.name,
                        'mesh_size': mesh.size,
                        'frequencies': mesh.modal_results.frequencies.copy(),
                        'calculation_time': mesh.modal_results.calculation_time,
                        'backup_time': backup_time,
                        'original_completion_time': mesh.updated_time.isoformat() if mesh.updated_time else None
                    }

                    # 存储到网格的备份属性中（如果没有则创建）
                    if not hasattr(mesh, 'modal_results_backup'):
                        mesh.modal_results_backup = []

                    mesh.modal_results_backup.append(backup_data)

                    # 限制备份数量（保留最近5次）
                    if len(mesh.modal_results_backup) > 5:
                        mesh.modal_results_backup = mesh.modal_results_backup[-5:]

                    logger.debug(f"备份网格 {mesh.name} 的模态计算结果")

            logger.info(f"成功备份 {len(meshes)} 个网格的模态计算结果")

        except Exception as e:
            logger.error(f"备份模态计算结果失败: {str(e)}")

    # ==================== 模态计算事件处理 ====================

    def _on_single_modal(self):
        """单个模态计算按钮点击处理"""
        logger.info("=== 单个模态计算按钮被点击 ===")
        print("DEBUG: 单个模态计算按钮被点击")  # 添加控制台输出用于调试

        try:
            # 检查是否有选中的单个网格
            selected_meshes = self.selected_meshes_for_generation
            logger.info(f"当前选中的网格数量: {len(selected_meshes)}")
            print(f"DEBUG: 当前选中的网格数量: {len(selected_meshes)}")  # 调试输出

            if len(selected_meshes) == 0:
                CustomMessageBox.warning(self, "选择错误",
                    "⚠️ 请先选择一个网格进行模态计算\n\n"
                    "操作步骤:\n"
                    "1. 在网格状态列表中选择一个网格\n"
                    "2. 确保网格状态为'已生成'\n"
                    "3. 点击'单个模态计算'按钮")
                return
            elif len(selected_meshes) > 1:
                CustomMessageBox.warning(self, "选择错误",
                    "⚠️ 单个模态计算只能选择一个网格\n\n"
                    f"当前选择了 {len(selected_meshes)} 个网格:\n" +
                    "\n".join([f"• {mesh.name}" for mesh in selected_meshes]) +
                    "\n\n请重新选择单个网格，或使用'批量模态计算'功能。")
                return

            mesh = selected_meshes[0]

            # 预处理：修复可能的网格数据问题
            self._fix_mesh_data_if_needed(mesh)

            # 详细的预计算验证
            validation_result = self._validate_mesh_for_modal_calculation(mesh)
            if not validation_result['valid']:
                CustomMessageBox.warning(self, "验证失败", validation_result['message'])
                return

            # 检查是否已有计算结果，需要重新计算确认
            if mesh.status == MeshStatus.COMPLETED:
                if not self._confirm_recalculation([mesh]):
                    return

            # 获取计算参数
            calc_params = self._get_modal_calculation_params()

            # 开始单个模态计算
            self._start_single_modal_calculation(mesh, calc_params)

        except Exception as e:
            logger.error(f"单个模态计算失败: {str(e)}")
            CustomMessageBox.critical(self, "错误",
                f"❌ 单个模态计算失败\n\n"
                f"错误详情: {str(e)}\n\n"
                f"📋 可能的解决方案:\n"
                f"1. 检查网格是否已正确生成\n"
                f"2. 重新生成该网格\n"
                f"3. 检查网格参数设置\n"
                f"4. 重启应用程序\n\n"
                f"如果问题持续存在，请联系技术支持。")

    def _on_batch_modal(self):
        """批量模态计算按钮点击处理 - 直接启动批量计算"""
        try:
            # 详细的批量验证
            validation_result = self._validate_meshes_for_batch_modal_calculation()
            if not validation_result['valid']:
                CustomMessageBox.warning(self, "验证失败", validation_result['message'])
                return

            valid_meshes = validation_result['valid_meshes']

            # 检查是否有已完成的网格需要重新计算确认
            completed_meshes = [mesh for mesh in valid_meshes if mesh.status == MeshStatus.COMPLETED]
            if completed_meshes:
                if not self._confirm_recalculation(completed_meshes):
                    return

            # 获取计算参数
            calc_params = self._get_modal_calculation_params()

            # 显示开始信息
            mesh_count = len(valid_meshes)
            modal_count = calc_params['modal_count']

            # 在主界面显示计算信息
            self.show_status_message(f"开始批量模态计算: {mesh_count} 个网格，每个 {modal_count} 阶模态")
            logger.info(f"直接启动批量模态计算: {mesh_count} 个网格")

            # 直接启动批量模态计算，跳过确认对话框
            self._start_batch_modal_calculation(valid_meshes, calc_params)

        except Exception as e:
            logger.error(f"批量模态计算失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"批量模态计算失败: {str(e)}")

    def _get_modal_calculation_params(self) -> Dict[str, Any]:
        """获取模态计算参数"""
        return {
            'modal_count': self.ui.spinBox_modal_count.value(),
            'limit_freq': self.ui.checkBox_limit_freq.isChecked(),
            'freq_min': self.ui.doubleSpinBox_freq_min.value(),
            'freq_max': self.ui.doubleSpinBox_freq_max.value()
        }

    def _start_single_modal_calculation(self, mesh: MeshParameter, calc_params: Dict[str, Any]):
        """开始单个模态计算 - 调用真正的ANSYS Workbench模态分析"""
        logger.info(f"=== 开始单个模态计算: {mesh.name} ===")
        print(f"DEBUG: 开始单个模态计算: {mesh.name}")  # 调试输出
        print(f"DEBUG: 计算参数: {calc_params}")  # 调试输出

        try:
            self.is_calculating = True
            self._update_calculation_ui(True)

            # 步骤2: 开始真正的模态计算
            mesh.update_status(MeshStatus.CALCULATING)
            self.ui.label_current_calc.setText(f"启动ANSYS模态计算: {mesh.name}")
            self.ui.progressBar_calculation.setValue(30)

            # 显示开始消息
            self.show_status_message(f"开始ANSYS模态计算: {mesh.name}")
            logger.info(f"开始真实单个模态计算: {mesh.name}")

            # 更新计算统计信息
            modal_count = calc_params['modal_count']
            freq_min = calc_params.get('freq_min', 0.0)
            freq_max = calc_params.get('freq_max', 1000.0)
            limit_freq = calc_params.get('limit_freq', True)

            stats_text = f"""
模态计算启动中...

网格信息:
• 网格名称: {mesh.name}
• 网格尺寸: {mesh.size:.2f} mm
• 节点数: {mesh.statistics.node_count}
• 单元数: {mesh.statistics.element_count}

计算参数:
• 模态阶数: {modal_count}
• 频率限制: {'是' if limit_freq else '否'}
• 频率范围: {freq_min:.1f} - {freq_max:.1f} Hz

计算状态: 正在启动ANSYS Workbench...
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 生成单模态计算配置文件
            from core.mesh_config_generator import MeshConfigGenerator
            main_window = self.window_manager.get_window(WindowType.MAIN)
            work_dir = main_window.ANSYS_Work_Dir if main_window else os.getcwd()

            try:
                config_path = MeshConfigGenerator.create_single_modal_config(mesh, work_dir)
                logger.info(f"单模态计算配置文件已生成: {config_path}")
            except Exception as e:
                logger.error(f"生成单模态计算配置文件失败: {str(e)}")
                raise

            # 调用真正的模态分析功能
            logger.info("准备调用 execute_single_modal_calculation")
            print("DEBUG: 准备调用 execute_single_modal_calculation")

            # 执行单模态计算并获取输出目录
            output_directory = execute_single_modal_calculation(self.window_manager, mesh, calc_params)

            # 计算完成后处理结果
            self._complete_single_modal_calculation(mesh, output_directory)

            logger.info(f"单个模态计算成功完成: {mesh.name}")

        except Exception as e:
            logger.error(f"单个模态计算失败: {str(e)}")
            mesh.update_status(MeshStatus.ERROR)
            self.ui.label_current_calc.setText(f"模态计算失败: {mesh.name}")

            # 显示错误状态
            stats_text = f"""
模态计算失败！

网格信息:
• 网格名称: {mesh.name}
• 网格尺寸: {mesh.size:.2f} mm

错误信息:
{str(e)}

请检查配置文件和ANSYS设置后重试。
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 显示错误消息
            self.show_status_message(f"模态计算失败: {mesh.name}")
            CustomMessageBox.critical(
                self, "模态计算失败",
                f"网格 '{mesh.name}' 模态计算失败！\n\n"
                f"错误信息: {str(e)}\n\n"
                f"请检查配置文件和ANSYS设置后重试。"
            )

        finally:
            self.is_calculating = False
            self._update_calculation_ui(False)
            self._refresh_all_ui()

    def _process_next_modal(self):
        """处理下一个模态计算 - 模拟workbench单个模态求解"""
        try:
            if not self.is_calculating or self.current_modal_index >= self.current_calc_params['modal_count']:
                self._complete_single_modal_calculation()
                return

            modal_count = self.current_calc_params['modal_count']
            freq_min = self.current_calc_params.get('freq_min', 0.0)
            freq_max = self.current_calc_params.get('freq_max', 1000.0)

            # 更新进度显示
            progress = int((self.current_modal_index + 1) / modal_count * 100)
            self.ui.progressBar_calculation.setValue(progress)
            self.ui.label_current_calc.setText(
                f"计算模态 {self.current_modal_index + 1}/{modal_count}: {self.current_calc_mesh.name}"
            )

            # 模拟workbench模态频率计算
            import random
            import math

            # 基于网格尺寸和模态阶数生成真实的频率值
            base_freq = 50.0 / self.current_calc_mesh.size  # 基础频率与网格尺寸成反比
            modal_factor = (self.current_modal_index + 1) ** 1.5  # 模态阶数影响
            random_factor = 1.0 + random.uniform(-0.1, 0.1)  # 随机扰动

            frequency = base_freq * modal_factor * random_factor

            # 应用频率限制
            if self.current_calc_params.get('limit_freq', True):
                frequency = max(freq_min, min(freq_max, frequency))

            self.calculated_frequencies.append(frequency)

            # 更新计算统计信息
            stats_text = f"""
模态计算进行中...

网格信息:
• 网格名称: {self.current_calc_mesh.name}
• 网格尺寸: {self.current_calc_mesh.size:.2f} mm
• 节点数: {self.current_calc_mesh.statistics.node_count}
• 单元数: {self.current_calc_mesh.statistics.element_count}

计算参数:
• 模态阶数: {modal_count}
• 频率范围: {freq_min:.1f} - {freq_max:.1f} Hz

计算进度: {self.current_modal_index + 1}/{modal_count}
已计算频率: {len(self.calculated_frequencies)} 个

最新频率值:
{chr(10).join([f"模态 {i+1}: {freq:.2f} Hz" for i, freq in enumerate(self.calculated_frequencies[-5:])])}
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 处理事件循环
            QApplication.processEvents()

            logger.debug(f"模拟计算模态 {self.current_modal_index + 1}: {frequency:.2f} Hz")

            self.current_modal_index += 1

        except Exception as e:
            logger.error(f"模态计算处理失败: {str(e)}")
            self._finish_modal_calculation()

    def _complete_single_modal_calculation(self, mesh: MeshParameter, output_directory: str):
        """完成单个模态计算 - 处理实际的ANSYS计算结果"""
        try:
            logger.info(f"开始处理模态计算结果: {mesh.name}, 输出目录: {output_directory}")

            # 读取计算结果文件
            result_data = read_modal_calculation_results(output_directory, mesh.size)

            if result_data['success']:
                # 更新网格数据
                update_success = update_mesh_data_from_results(mesh, result_data)

                if update_success:
                    # 更新界面状态
                    self.ui.label_current_calc.setText(f"模态计算完成: {mesh.name}")
                    self.ui.progressBar_calculation.setValue(100)

                    # 构建详细的结果统计信息
                    frequencies = result_data['frequencies']
                    freq_range = f"{min(frequencies):.2f} - {max(frequencies):.2f}" if frequencies else "无"

                    final_stats = f"""
模态计算完成！

网格信息:
• 网格名称: {mesh.name}
• 网格尺寸: {mesh.size:.2f} mm
• 节点数: {result_data['node_count']:,}
• 单元数: {result_data['element_count']:,}

计算结果:
• 模态数量: {len(frequencies)}
• 频率范围: {freq_range} Hz
• 计算时间: {result_data['calculation_time']:.1f} 秒

输出目录: {output_directory}

状态: ✅ 计算成功完成，结果已更新到界面
                    """.strip()

                    self.ui.textEdit_calc_stats.setPlainText(final_stats)

                    # 显示成功消息
                    self.show_status_message(f"模态计算完成: {mesh.name}")
                    CustomMessageBox.information(
                        self, "模态计算完成",
                        f"网格 '{mesh.name}' 模态计算完成！\n\n"
                        f"计算结果:\n"
                        f"• 模态数量: {len(frequencies)}\n"
                        f"• 频率范围: {freq_range} Hz\n"
                        f"• 节点数: {result_data['node_count']:,}\n"
                        f"• 单元数: {result_data['element_count']:,}\n"
                        f"• 计算时间: {result_data['calculation_time']:.1f} 秒\n\n"
                        f"结果已自动更新到网格信息中。"
                    )

                    logger.info(f"单个模态计算成功完成: {mesh.name}, 频率数量: {len(frequencies)}")

                else:
                    # 数据更新失败
                    mesh.update_status(MeshStatus.ERROR)
                    error_msg = "结果数据更新失败"
                    self._handle_calculation_error(mesh, error_msg)

            else:
                # 结果文件读取失败
                mesh.update_status(MeshStatus.ERROR)
                error_msg = result_data.get('error_message', '未知错误')
                self._handle_calculation_error(mesh, error_msg)

            # 保存配置（更新网格状态）
            self._save_configuration()

            # 刷新界面显示
            self._refresh_all_ui()

        except Exception as e:
            logger.error(f"完成单个模态计算失败: {str(e)}")
            mesh.update_status(MeshStatus.ERROR)
            self._handle_calculation_error(mesh, f"处理计算结果时发生错误: {str(e)}")

        finally:
            # 重置计算状态
            self.is_calculating = False
            self._update_calculation_ui(False)

    def _handle_calculation_error(self, mesh: MeshParameter, error_message: str):
        """处理计算错误的统一方法"""
        try:
            # 更新界面状态
            self.ui.label_current_calc.setText(f"计算失败: {mesh.name}")
            self.ui.progressBar_calculation.setValue(0)

            # 显示错误统计信息
            error_stats = f"""
模态计算失败！

网格信息:
• 网格名称: {mesh.name}
• 网格尺寸: {mesh.size:.2f} mm

错误信息:
{error_message}

状态: ❌ 计算失败

请检查以下项目：
1. ANSYS Workbench是否正常运行
2. 配置文件是否正确
3. 网格参数是否有效
4. 输出目录是否可写
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(error_stats)

            # 显示错误消息
            self.show_status_message(f"模态计算失败: {mesh.name}")
            CustomMessageBox.critical(
                self, "模态计算失败",
                f"网格 '{mesh.name}' 模态计算失败！\n\n"
                f"错误信息:\n{error_message}\n\n"
                f"请检查配置文件和ANSYS设置后重试。"
            )

            logger.error(f"模态计算失败: {mesh.name}, 错误: {error_message}")

        except Exception as e:
            logger.error(f"处理计算错误时发生异常: {str(e)}")


    def _start_batch_modal_calculation(self, meshes: List[MeshParameter], calc_params: Dict[str, Any]):
        """开始批量模态计算 - 真实ANSYS批量模态分析"""
        try:
            logger.info(f"开始真实批量模态计算，网格数量: {len(meshes)}")

            # 重置批量完成处理标志
            self._batch_completion_processed = False

            self.is_calculating = True
            self._update_calculation_ui(True)
 

            total_count = len(meshes)
            modal_count = calc_params['modal_count']

            # 显示开始消息
            self.show_status_message(f"开始批量模态计算: {total_count} 个网格")
            logger.info(f"开始真实批量模态计算: {total_count} 个网格")

            # 重置进度
            self.ui.progressBar_calculation.setValue(0)
            self.ui.label_current_calc.setText("🚀 初始化批量模态计算...")

            # 更新计算统计信息
            stats_text = f"""
🔬 批量模态计算开始...

📋 计算参数:
• 网格数量: {total_count}
• 每网格模态数: {modal_count}
• 总模态数: {total_count * modal_count}
• 频率范围: {calc_params.get('freq_min', 0.0):.1f} - {calc_params.get('freq_max', 1000.0):.1f} Hz

📊 计算进度: 0/{total_count} (0%)
⏱️ 计算方式: 真实ANSYS Workbench

🔄 状态: 准备启动ANSYS...
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 创建批量计算管理器
            self.batch_manager = BatchModalCalculationManager(self.window_manager, self)

            # 连接信号到现有UI更新机制
            self.batch_manager.progress_updated.connect(self._update_batch_progress)
            self.batch_manager.calculation_completed.connect(self._on_batch_completed)
            self.batch_manager.calculation_failed.connect(self._on_batch_failed)

            # 启动批量计算
            success = self.batch_manager.start_calculation(meshes, calc_params)

            if not success:
                logger.error("批量计算启动失败")
                self._finish_modal_calculation()

        except Exception as e:
            logger.error(f"批量模态计算启动失败: {str(e)}")
            self._finish_modal_calculation()

    def _update_batch_progress(self, current: int, total: int, mesh_name: str, status: str):
        """更新批量计算进度显示 - 增强版主界面进度显示"""
        try:
            # 更新进度条
            progress_percentage = int((current / total) * 100) if total > 0 else 0
            self.ui.progressBar_calculation.setValue(progress_percentage)

            # 更新状态标签 - 显示更详细的信息
            if current >= total:
                self.ui.label_current_calc.setText(f"🎉 批量计算完成! ({current}/{total})")
            else:
                self.ui.label_current_calc.setText(f"🚀 正在计算: {mesh_name} ({current}/{total}) - {status}")

            # 更新统计信息 - 增强版显示
            if hasattr(self, 'batch_manager') and self.batch_manager:
                modal_count = self.batch_manager.calc_params.get('modal_count', 0)
                completed_count = len(self.batch_manager.completed_meshes) if hasattr(self.batch_manager, 'completed_meshes') else current
                failed_count = len(self.batch_manager.failed_meshes) if hasattr(self.batch_manager, 'failed_meshes') else 0

                # 计算预估剩余时间
                remaining_meshes = total - current
                avg_time_per_mesh = 30  # 假设每个网格30秒
                estimated_remaining_time = remaining_meshes * avg_time_per_mesh

                stats_text = f"""
🔬 批量模态计算进行中...

📋 计算参数:
• 网格数量: {total} 个
• 每网格模态数: {modal_count} 阶
• 总模态数: {total * modal_count} 个
• 频率范围: {self.batch_manager.calc_params.get('freq_min', 0.0):.1f} - {self.batch_manager.calc_params.get('freq_max', 1000.0):.1f} Hz

📊 计算进度: {current}/{total} ({progress_percentage}%)
🔄 当前状态: {status}
⚡ 当前网格: {mesh_name}

📈 详细统计:
✅ 已完成: {completed_count} 个网格
❌ 失败: {failed_count} 个网格
⏳ 剩余: {remaining_meshes} 个网格
⏱️ 预估剩余时间: {estimated_remaining_time // 60}分{estimated_remaining_time % 60}秒

💡 提示: 计算过程中请勿关闭程序，可随时查看进度
            """.strip()
            else:
                stats_text = f"""
🔬 批量模态计算进行中...

📊 计算进度: {current}/{total} ({progress_percentage}%)
🔄 当前状态: {status}
⚡ 当前网格: {mesh_name}

💡 提示: 计算过程中请勿关闭程序
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 在状态栏也显示进度
            self.show_status_message(f"批量计算进度: {current}/{total} ({progress_percentage}%) - {mesh_name}")

            logger.debug(f"批量进度更新: {current}/{total}, 网格: {mesh_name}, 状态: {status}")

        except Exception as e:
            logger.error(f"更新批量进度失败: {str(e)}")

    # 移除了与ModalCalculationDialog相关的方法，现在直接在主界面显示进度

    def _on_batch_completed(self, results: List[Dict]):
        """批量计算完成回调 - 修复除零错误和结果处理逻辑，防止重复调用"""
        try:
            # 防止重复调用的保护机制
            if hasattr(self, '_batch_completion_processed') and self._batch_completion_processed:
                logger.warning("批量计算完成回调已经处理过，跳过重复调用")
                return

            self._batch_completion_processed = True
            logger.info(f"批量计算完成，结果数量: {len(results)}")

            # 检查结果数量，防止除零错误
            if len(results) == 0:
                logger.warning("批量计算完成但没有返回任何结果")
                self._handle_empty_batch_results()
                return

            # 统计结果
            successful_count = sum(1 for r in results if r.get('success', False))
            failed_count = len(results) - successful_count

            # 更新UI状态
            self.ui.progressBar_calculation.setValue(100)
            self.ui.label_current_calc.setText(f"🎉 批量计算完成: 成功 {successful_count}, 失败 {failed_count}")

            # 处理成功的结果，更新网格状态
            self._process_batch_results(results)

            # 构建完成统计信息
            all_frequencies = []
            total_calc_time = 0.0

            for result in results:
                if result.get('success', False):
                    frequencies = result.get('frequencies', [])
                    all_frequencies.extend(frequencies)
                    total_calc_time += result.get('calculation_time', 0.0)

            freq_range = f"{min(all_frequencies):.2f} - {max(all_frequencies):.2f}" if all_frequencies else "无"

            # 计算成功率，防止除零错误
            success_rate = (successful_count / len(results) * 100) if len(results) > 0 else 0.0

            final_stats = f"""
🎉 批量模态计算完成！

📊 计算结果:
• 总网格数: {len(results)} 个
• 成功计算: {successful_count} 个网格
• 计算失败: {failed_count} 个网格
• 成功率: {success_rate:.1f}%

📈 频率统计:
• 总频率数: {len(all_frequencies)} 个
• 频率范围: {freq_range} Hz
• 总计算时间: {total_calc_time:.1f} 秒

✅ 状态: 批量计算成功完成
💡 提示: 可在"结果对比"页面查看详细结果

🔄 下一步操作:
• 点击"选择计算结果"查看详细数据
• 切换到"结果对比"页面进行分析
• 导出计算结果到文件
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(final_stats)

            # 显示完成消息和提示
            self.show_status_message(f"🎉 批量模态计算完成: 成功 {successful_count}/{len(results)} 个网格")

            # 显示完成通知
            from .custom_message_box import CustomMessageBox
            CustomMessageBox.information(
                self, "批量计算完成",
                f"🎉 批量模态计算已成功完成！\n\n"
                f"📊 计算统计:\n"
                f"• 总网格数: {len(results)} 个\n"
                f"• 成功计算: {successful_count} 个\n"
                f"• 计算失败: {failed_count} 个\n"
                f"• 成功率: {success_rate:.1f}%\n\n"
                f"💡 您现在可以:\n"
                f"• 点击'选择计算结果'查看详细数据\n"
                f"• 切换到'结果对比'页面进行分析\n"
                f"• 导出计算结果到文件"
            )

            # 保存配置
            self._save_configuration()

            # 刷新界面
            self._refresh_all_ui()

            # 完成计算 - 重置UI状态
            self._finish_modal_calculation()

        except Exception as e:
            logger.error(f"处理批量计算完成失败: {str(e)}")
            self._finish_modal_calculation()

    def _handle_empty_batch_results(self):
        """处理空的批量计算结果"""
        try:
            logger.warning("批量计算完成但没有返回任何结果，可能是ANSYS执行失败或结果文件未生成")

            # 更新UI状态
            self.ui.progressBar_calculation.setValue(0)
            self.ui.label_current_calc.setText("❌ 批量计算失败: 没有结果")

            # 显示错误统计信息
            error_stats = f"""
❌ 批量模态计算失败！

📊 问题分析:
• 结果数量: 0 个
• 可能原因: ANSYS执行失败或结果文件未生成
• 建议操作: 检查ANSYS配置和日志文件

🔧 故障排除:
1. 检查ANSYS Workbench是否正常安装
2. 验证配置文件是否正确
3. 查看ANSYS执行日志
4. 确认输出目录权限

💡 提示: 可以尝试单个模态计算来验证ANSYS配置
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(error_stats)

            # 显示错误消息
            self.show_status_message("❌ 批量模态计算失败: 没有结果")

            # 显示错误通知
            from .custom_message_box import CustomMessageBox
            CustomMessageBox.critical(
                self, "批量计算失败",
                "❌ 批量模态计算失败！\n\n"
                "没有返回任何计算结果，可能的原因：\n"
                "• ANSYS Workbench执行失败\n"
                "• 配置文件错误\n"
                "• 结果文件未生成\n\n"
                "建议：\n"
                "• 检查ANSYS安装和配置\n"
                "• 尝试单个模态计算验证\n"
                "• 查看详细日志信息"
            )

        except Exception as e:
            logger.error(f"处理空结果失败: {str(e)}")

    def _process_batch_results(self, results: List[Dict]):
        """处理批量计算结果，更新网格状态 - 参考单模态计算的结果处理逻辑"""
        try:
            logger.info(f"开始处理批量计算结果，结果数量: {len(results)}")

            for result in results:
                try:
                    # 获取网格对象
                    mesh = result.get('mesh')
                    if not mesh:
                        logger.warning(f"结果中缺少网格对象: {result}")
                        continue

                    if result.get('success', False):
                        # 成功的结果 - 参考_complete_single_modal_calculation的逻辑
                        logger.info(f"处理成功的计算结果: {mesh.name}")

                        # 网格状态应该已经在BatchModalCalculationManager中更新了
                        # 这里只需要验证状态
                        if mesh.status != MeshStatus.COMPLETED:
                            logger.warning(f"网格 {mesh.name} 状态未正确更新，手动更新为COMPLETED")
                            mesh.update_status(MeshStatus.COMPLETED)

                        # 验证模态结果数据
                        if hasattr(mesh, 'modal_results') and mesh.modal_results.frequencies:
                            logger.info(f"网格 {mesh.name} 模态结果验证通过: {len(mesh.modal_results.frequencies)} 个频率")
                        else:
                            logger.warning(f"网格 {mesh.name} 模态结果数据缺失")

                    else:
                        # 失败的结果
                        error_msg = result.get('error', '未知错误')
                        logger.error(f"网格 {mesh.name} 计算失败: {error_msg}")

                        # 更新网格状态为错误
                        mesh.update_status(MeshStatus.ERROR)

                except Exception as e:
                    logger.error(f"处理单个结果失败: {str(e)}")
                    continue

            logger.info("批量计算结果处理完成")

        except Exception as e:
            logger.error(f"处理批量计算结果失败: {str(e)}")

    def _on_batch_failed(self, error_message: str):
        """批量计算失败回调"""
        try:
            logger.error(f"批量计算失败: {error_message}")

            # 更新UI状态
            self.ui.progressBar_calculation.setValue(0)
            self.ui.label_current_calc.setText("批量计算失败")

            # 显示错误统计信息
            error_stats = f"""
❌ 批量模态计算失败！

错误信息:
{error_message}

📊 计算状态:
• 已完成网格: {len(self.batch_manager.completed_meshes) if hasattr(self, 'batch_manager') else 0}
• 失败网格: {len(self.batch_manager.failed_meshes) if hasattr(self, 'batch_manager') else 0}

🔧 建议检查:
1. ANSYS Workbench是否正常运行
2. 配置文件是否正确
3. 网格参数是否有效
4. 输出目录是否可写
5. 系统资源是否充足

状态: 计算失败，请检查错误信息后重试
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(error_stats)

            # 显示错误消息
            self.show_status_message(f"批量模态计算失败: {error_message}")

            # 完成计算
            self._finish_modal_calculation()

        except Exception as e:
            logger.error(f"处理批量计算失败时发生错误: {str(e)}")
            self._finish_modal_calculation()

    def _process_next_batch_modal(self):
        """处理下一个网格的模态计算 - 模拟workbench批量处理"""
        try:
            if not self.is_calculating or self.current_batch_index >= len(self.batch_calc_meshes):
                self._complete_batch_modal_calculation()
                return

            mesh = self.batch_calc_meshes[self.current_batch_index]
            total_count = len(self.batch_calc_meshes)
            modal_count = self.batch_calc_params['modal_count']

            # 更新状态
            mesh.update_status(MeshStatus.CALCULATING)

            # 更新进度显示
            progress = int((self.current_batch_index + 1) / total_count * 100)
            self.ui.progressBar_calculation.setValue(progress)
            self.ui.label_current_calc.setText(
                f"🔄 计算网格 {self.current_batch_index + 1}/{total_count}: {mesh.name}"
            )

            # 更新模态计算对话框进度（如果存在）
            if hasattr(self, '_modal_dialog') and self._modal_dialog:
                self._modal_dialog.update_progress(
                    self.current_batch_index, total_count,
                    mesh.name, f"计算 {modal_count} 个模态..."
                )

            # 模拟workbench模态计算
            import random

            frequencies = []
            freq_min = self.batch_calc_params.get('freq_min', 0.0)
            freq_max = self.batch_calc_params.get('freq_max', 1000.0)

            for j in range(modal_count):
                # 基于网格尺寸和模态阶数生成频率
                base_freq = 50.0 / mesh.size
                modal_factor = (j + 1) ** 1.5
                random_factor = 1.0 + random.uniform(-0.1, 0.1)

                frequency = base_freq * modal_factor * random_factor

                # 应用频率限制
                if self.batch_calc_params.get('limit_freq', True):
                    frequency = max(freq_min, min(freq_max, frequency))

                frequencies.append(frequency)

            # 保存计算结果
            mesh.modal_results.frequencies = frequencies
            mesh.modal_results.calculation_time = modal_count * 0.2 + random.uniform(0.1, 0.5)
            mesh.update_status(MeshStatus.COMPLETED)

            # 记录批量结果
            result_info = {
                'mesh_name': mesh.name,
                'mesh_size': mesh.size,
                'modal_count': len(frequencies),
                'freq_range': f"{min(frequencies):.2f} - {max(frequencies):.2f}" if frequencies else "无",
                'calc_time': mesh.modal_results.calculation_time
            }
            self.batch_results.append(result_info)

            # 更新计算统计信息
            completed_count = self.current_batch_index + 1
            remaining_count = total_count - completed_count
            progress_percent = int((completed_count / total_count) * 100)

            stats_text = f"""
🔬 批量模态计算进行中...

📊 总体进度: {completed_count}/{total_count} 个网格 ({progress_percent}%)

🎯 当前网格: {mesh.name}
• 网格尺寸: {mesh.size:.2f} mm
• 模态数量: {len(frequencies)}
• 频率范围: {result_info['freq_range']} Hz
• 计算时间: {result_info['calc_time']:.1f} 秒

⏳ 剩余网格: {remaining_count} 个
⏱️ 预计剩余时间: {remaining_count * 1.5:.0f} 秒

✅ 最近完成的网格:
{chr(10).join([f"• {r['mesh_name']}: {r['modal_count']} 个模态, {r['calc_time']:.1f}s" for r in self.batch_results[-3:]])}

🔄 状态: 计算中...
            """.strip()

            self.ui.textEdit_calc_stats.setPlainText(stats_text)

            # 更新模态计算对话框（标记当前网格完成）
            if hasattr(self, '_modal_dialog') and self._modal_dialog:
                self._modal_dialog.mark_mesh_completed(mesh.name, True)
                self._modal_dialog.update_progress(
                    completed_count, total_count,
                    "", f"已完成 {completed_count}/{total_count} 个网格"
                )

            # 刷新网格状态显示
            self._refresh_mesh_status_list()

            # 处理事件循环
            QApplication.processEvents()

            logger.info(f"模拟批量计算完成: {mesh.name} - 模态数: {len(frequencies)}")

            self.current_batch_index += 1

        except Exception as e:
            logger.error(f"批量模态计算处理失败: {str(e)}")
            self._finish_modal_calculation()

    def _complete_batch_modal_calculation(self):
        """完成批量模态计算 - 模拟workbench批量完成"""
        try:
            # 停止定时器
            if hasattr(self, 'batch_calc_timer') and self.batch_calc_timer.isActive():
                self.batch_calc_timer.stop()

            if self.is_calculating:
                total_count = len(self.batch_calc_meshes)
                completed_count = len(self.batch_results)
                total_modals = sum(r['modal_count'] for r in self.batch_results)
                total_time = sum(r['calc_time'] for r in self.batch_results)

                # 更新最终统计信息
                final_stats = f"""
批量模态计算完成！

计算统计:
• 总网格数: {total_count}
• 成功计算: {completed_count}
• 失败计算: {total_count - completed_count}
• 总模态数: {total_modals}
• 总计算时间: {total_time:.1f} 秒

计算结果详情:
{chr(10).join([f"• {r['mesh_name']} ({r['mesh_size']:.2f}mm): {r['modal_count']} 个模态, {r['freq_range']} Hz" for r in self.batch_results])}

状态: 批量计算成功完成
                """.strip()

                self.ui.textEdit_calc_stats.setPlainText(final_stats)
                self.ui.label_current_calc.setText(f"批量计算完成: {completed_count}/{total_count} 个网格")

                # 显示完成消息
                self.show_status_message(f"批量模态计算完成: {completed_count} 个网格")
                CustomMessageBox.information(
                    self, "批量模态计算完成",
                    f"批量模态计算完成！\n\n"
                    f"计算统计:\n"
                    f"• 总网格数: {total_count}\n"
                    f"• 成功计算: {completed_count}\n"
                    f"• 总模态数: {total_modals}\n"
                    f"• 总计算时间: {total_time:.1f} 秒\n\n"
                    f"现在可以选择计算结果进行后续分析。"
                )

                logger.info(f"模拟批量模态计算完成: {completed_count}/{total_count} 个网格, 总模态数: {total_modals}")

            self._finish_modal_calculation()

        except Exception as e:
            logger.error(f"完成批量模态计算失败: {str(e)}")
            self._finish_modal_calculation()

    def _finish_modal_calculation(self):
        """完成模态计算 - 清理资源和更新UI，参考单模态计算的完成逻辑"""
        try:
            logger.info("开始完成模态计算，重置UI状态")

            # 重置计算状态 - 这是关键的状态重置
            self.is_calculating = False

            # 停止所有相关定时器
            if hasattr(self, 'modal_calc_timer') and self.modal_calc_timer.isActive():
                self.modal_calc_timer.stop()
                logger.debug("停止单模态计算定时器")
            if hasattr(self, 'batch_calc_timer') and self.batch_calc_timer.isActive():
                self.batch_calc_timer.stop()
                logger.debug("停止批量计算定时器")

            # 清理BatchModalCalculationManager
            if hasattr(self, 'batch_manager') and self.batch_manager:
                logger.info("清理BatchModalCalculationManager资源")
                try:
                    self.batch_manager.cleanup()
                except Exception as e:
                    logger.warning(f"清理BatchModalCalculationManager时出现警告: {str(e)}")
                self.batch_manager = None

            # 更新UI状态 - 参考单模态计算的finally块
            self._update_calculation_ui(False)

            # 重置进度条和状态标签
            if not self.ui.progressBar_calculation.value() == 100:
                self.ui.progressBar_calculation.setValue(0)

            # 检查是否有已完成的计算结果
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            if completed_meshes:
                self.ui.btn_select_results.setEnabled(True)
                logger.info(f"发现 {len(completed_meshes)} 个已完成的网格")

                # 如果有多个完成的结果，提示用户可以选择结果
                if len(completed_meshes) > 1:
                    self.show_status_message(f"已完成 {len(completed_meshes)} 个网格的模态计算，可选择结果进行分析")

            # 刷新UI - 确保所有状态都正确更新
            self._refresh_all_ui()

            logger.info("模态计算完成，UI状态已重置")

        except Exception as e:
            logger.error(f"完成模态计算失败: {str(e)}")

    def _update_calculation_ui(self, is_calculating: bool):
        """更新计算UI状态"""
        try:
            # 更新按钮状态 - 使用与_update_ui_state一致的逻辑
            has_selection = len(self.selected_meshes_for_generation) > 0

            # 单模态按钮：需要选择1个网格且不在计算中
            self.ui.btn_single_modal.setEnabled(len(self.selected_meshes_for_generation) == 1 and not is_calculating)
            # 批量模态按钮：需要有选择的网格且不在计算中
            self.ui.btn_batch_modal.setEnabled(has_selection and not is_calculating)
            # 暂停和停止按钮：只在计算中时启用
            self.ui.btn_pause_calculation.setEnabled(is_calculating)
            self.ui.btn_stop_calculation.setEnabled(is_calculating)

            if not is_calculating:
                self.ui.progressBar_calculation.setValue(0)
                self.ui.label_current_calc.setText("当前状态: 等待开始")

        except Exception as e:
            logger.error(f"更新计算UI状态失败: {str(e)}")

    def _on_pause_calculation(self):
        """暂停计算按钮点击处理 - 模拟workbench暂停功能"""
        try:
            if not self.is_calculating:
                CustomMessageBox.information(self, "提示", "当前没有正在进行的计算")
                return

            # 检查是否已经暂停
            if hasattr(self, '_calculation_paused') and self._calculation_paused:
                # 恢复计算
                self._calculation_paused = False
                self.ui.btn_pause_calculation.setText("暂停")

                # 恢复定时器
                if hasattr(self, 'modal_calc_timer') and not self.modal_calc_timer.isActive():
                    self.modal_calc_timer.start(300)
                if hasattr(self, 'batch_calc_timer') and not self.batch_calc_timer.isActive():
                    self.batch_calc_timer.start(1000)

                self.ui.label_current_calc.setText(self.ui.label_current_calc.text().replace("(已暂停)", ""))
                self.show_status_message("计算已恢复")
                logger.info("模态计算已恢复")

            else:
                # 暂停计算
                self._calculation_paused = True
                self.ui.btn_pause_calculation.setText("恢复")

                # 暂停定时器
                if hasattr(self, 'modal_calc_timer') and self.modal_calc_timer.isActive():
                    self.modal_calc_timer.stop()
                if hasattr(self, 'batch_calc_timer') and self.batch_calc_timer.isActive():
                    self.batch_calc_timer.stop()

                current_text = self.ui.label_current_calc.text()
                if "(已暂停)" not in current_text:
                    self.ui.label_current_calc.setText(current_text + " (已暂停)")

                self.show_status_message("计算已暂停")
                logger.info("模态计算已暂停")

        except Exception as e:
            logger.error(f"暂停/恢复计算失败: {str(e)}")

    def _on_stop_calculation(self):
        """停止计算按钮点击处理 - 模拟workbench停止功能"""
        try:
            if not self.is_calculating:
                CustomMessageBox.information(self, "提示", "当前没有正在进行的计算")
                return

            # 确认停止
            reply = CustomMessageBox.question(
                self, "确认停止计算",
                "确定要停止当前的模态计算吗？\n\n"
                "注意：已完成的计算结果将会保留，\n"
                "但正在进行的计算将会中断。"
            )

            if reply == CustomMessageBox.Yes:
                # 停止计算
                self.is_calculating = False
                self._calculation_paused = False

                # 更新UI
                self.ui.btn_pause_calculation.setText("暂停")

                # 更新当前计算状态
                current_text = self.ui.label_current_calc.text()
                if "(已暂停)" in current_text:
                    current_text = current_text.replace(" (已暂停)", "")
                self.ui.label_current_calc.setText(current_text + " (已停止)")

                # 更新计算统计信息
                stats_text = self.ui.textEdit_calc_stats.toPlainText()
                if stats_text:
                    stats_text += "\n\n状态: 计算已被用户停止"
                    self.ui.textEdit_calc_stats.setPlainText(stats_text)

                # 显示停止消息
                self.show_status_message("计算已停止")

                # 完成计算流程
                self._finish_modal_calculation()

                logger.info("用户停止了模态计算")

        except Exception as e:
            logger.error(f"停止计算失败: {str(e)}")

    def _on_freq_limit_toggled(self, checked: bool):
        """频率限制复选框切换处理"""
        try:
            # 启用/禁用频率范围控件
            self.ui.doubleSpinBox_freq_min.setEnabled(checked)
            self.ui.doubleSpinBox_freq_max.setEnabled(checked)
            logger.debug(f"频率限制切换: {checked}")
        except Exception as e:
            logger.error(f"频率限制切换失败: {str(e)}")

    def _on_modal_params_changed(self):
        """模态参数变化处理"""
        try:
            # 验证参数有效性
            modal_count = self.ui.spinBox_modal_count.value()
            freq_min = self.ui.doubleSpinBox_freq_min.value()
            freq_max = self.ui.doubleSpinBox_freq_max.value()

            if freq_min >= freq_max:
                self.ui.doubleSpinBox_freq_max.setValue(freq_min + 100.0)

            logger.debug(f"模态参数变化: 阶数={modal_count}, 频率范围={freq_min}-{freq_max}")
        except Exception as e:
            logger.error(f"模态参数变化处理失败: {str(e)}")

    def _update_modal_selection_info(self):
        """更新模态选择信息"""
        try:
            selected_count = len(self.selected_meshes_for_generation)
            generated_count = len([mesh for mesh in self.selected_meshes_for_generation
                                 if mesh.status == MeshStatus.GENERATED])

            if selected_count == 0:
                info_text = "请选择要进行模态计算的网格"
            elif selected_count == 1:
                mesh = self.selected_meshes_for_generation[0]
                if mesh.status == MeshStatus.GENERATED:
                    info_text = f"已选择网格: {mesh.name} (可进行计算)"
                else:
                    info_text = f"已选择网格: {mesh.name} (需要先生成)"
            else:
                info_text = f"已选择 {selected_count} 个网格，其中 {generated_count} 个可进行计算"

            self.ui.label_selected_info.setText(info_text)

        except Exception as e:
            logger.error(f"更新模态选择信息失败: {str(e)}")

    # ==================== 结果选择事件处理 ====================

    def _on_select_results(self):
        """选择计算结果按钮点击处理 - 模拟workbench结果选择"""
        try:
            # 获取已完成计算的网格
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)

            if not completed_meshes:
                CustomMessageBox.information(
                    self, "提示",
                    "没有已完成的模态计算结果可以选择。\n\n"
                    "请先完成网格生成和模态计算，然后再选择结果。"
                )
                return

            # 显示结果选择对话框
            dialog = ResultSelectionDialog(self, completed_meshes)
            dialog.results_selected.connect(self._on_results_selected)

            # 记录操作
            self.show_status_message(f"打开结果选择对话框，共 {len(completed_meshes)} 个可选结果")
            logger.info(f"打开结果选择对话框，可选结果数量: {len(completed_meshes)}")

            dialog.exec()

        except Exception as e:
            logger.error(f"选择计算结果失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"选择计算结果失败: {str(e)}")

    def _on_results_selected(self, selected_meshes: List[MeshParameter]):
        """结果选择完成处理 - 模拟workbench结果处理"""
        try:
            if selected_meshes:
                # 保存选中的结果用于后续分析
                self.selected_results = selected_meshes

                # 生成mesh_config_last.json配置文件
                self._generate_mesh_config_file(selected_meshes)

                # 统计选中结果的信息
                total_modals = sum(len(mesh.modal_results.frequencies) for mesh in selected_meshes)
                mesh_names = [mesh.name for mesh in selected_meshes]
                size_range = f"{min(mesh.size for mesh in selected_meshes):.2f} - {max(mesh.size for mesh in selected_meshes):.2f}"

                # 计算频率范围
                all_frequencies = []
                for mesh in selected_meshes:
                    all_frequencies.extend(mesh.modal_results.frequencies)

                freq_range = f"{min(all_frequencies):.2f} - {max(all_frequencies):.2f}" if all_frequencies else "无"

                # 显示选择结果摘要
                summary_message = f"""
已选择 {len(selected_meshes)} 个计算结果用于后续振动分析:

选中网格:
{chr(10).join([f"• {name}" for name in mesh_names])}

统计信息:
• 网格尺寸范围: {size_range} mm
• 总模态数量: {total_modals}
• 频率范围: {freq_range} Hz

这些结果将用于:
• 振动响应计算
• 网格无关性分析
• 收敛性评估
                """.strip()

                # 不再显示摘要消息，因为对话框已经显示了确认消息
                # CustomMessageBox.information(self, "结果选择完成", summary_message)

                # 更新状态消息
                self.show_status_message(f"已选择 {len(selected_meshes)} 个计算结果，总计 {total_modals} 个模态")

                # 不自动切换到结果对比标签页，让用户手动选择
                # self.ui.tabWidget_main.setCurrentIndex(2)

                # 不更新对比列表，保持显示所有完成的网格方案，方便对比选择
                # self._update_comparison_with_selected_results()

                logger.info(f"用户选择了 {len(selected_meshes)} 个计算结果，总模态数: {total_modals}")

        except Exception as e:
            logger.error(f"处理结果选择失败: {str(e)}")

    def _generate_mesh_config_file(self, selected_meshes: List[MeshParameter]):
        """基于用户选择的结果生成mesh_config_last.json配置文件"""
        try:
            from core.mesh_config_generator import MeshConfigGenerator

            # 获取工作目录
            main_window = self.window_manager.get_window(WindowType.MAIN)
            work_dir = main_window.ANSYS_Work_Dir if main_window else os.getcwd()

            # 创建配置生成器
            generator = MeshConfigGenerator(work_dir)

            # 如果用户选择了多个结果，使用第一个作为主要配置
            # 这符合finalscript期望单个element_size的格式
            primary_mesh = selected_meshes[0]

            # 构建选择结果数据
            selected_result = {
                'mesh': primary_mesh,
                'mesh_name': primary_mesh.name,
                'element_size': primary_mesh.size,  # 毫米单位
                'element_type': primary_mesh.element_type.value if hasattr(primary_mesh.element_type, 'value') else str(primary_mesh.element_type),
                'frequencies': primary_mesh.modal_results.frequencies if primary_mesh.modal_results else [],
                'calculation_time': primary_mesh.modal_results.calculation_time if primary_mesh.modal_results else 0.0,
                'selected_count': len(selected_meshes),
                'all_selected_meshes': [mesh.name for mesh in selected_meshes]
            }

            # 生成配置文件
            config_path = generator.generate_config_from_selection(selected_result)

            logger.info(f"已生成网格配置文件: {config_path}")
            logger.info(f"主要网格: {primary_mesh.name} (尺寸: {primary_mesh.size}mm)")
            logger.info(f"选择的网格数量: {len(selected_meshes)}")

            # 验证生成的配置文件
            if generator.validate_config_file(config_path):
                self.show_status_message(f"已生成网格配置文件: mesh_config_last.json")
            else:
                logger.warning("生成的配置文件验证失败")

        except Exception as e:
            logger.error(f"生成网格配置文件失败: {str(e)}")
            self.show_status_message("生成网格配置文件失败")

    def _update_comparison_with_selected_results(self):
        """更新对比界面显示选中的结果 - 模拟workbench结果展示"""
        try:
            if not hasattr(self, 'selected_results') or not self.selected_results:
                return

            # 清空对比列表
            comparison_list = self.ui.listWidget_comparison_meshes
            comparison_list.clear()

            # 添加选中的结果到对比列表
            for mesh in self.selected_results:
                item = QListWidgetItem()

                freq_count = len(mesh.modal_results.frequencies)
                calc_time = mesh.modal_results.calculation_time
                freq_range = f"{min(mesh.modal_results.frequencies):.2f} - {max(mesh.modal_results.frequencies):.2f}" if mesh.modal_results.frequencies else "无"

                display_text = f"{mesh.name}\n"
                display_text += f"尺寸: {mesh.size:.2f}mm | "
                display_text += f"模态数: {freq_count} | "
                display_text += f"频率: {freq_range} Hz"

                item.setText(display_text)
                item.setData(Qt.ItemDataRole.UserRole, mesh.id)
                item.setBackground(QColor(240, 255, 240))  # 浅绿色表示选中
                item.setSelected(True)  # 默认选中

                comparison_list.addItem(item)

            # 更新图表占位符显示
            placeholder_text = f"""
网格无关性分析图表

已选择 {len(self.selected_results)} 个计算结果:
{chr(10).join([f"• {mesh.name} ({mesh.size:.2f}mm)" for mesh in self.selected_results])}

图表功能:
• 频率收敛性分析
• 网格尺寸 vs 频率曲线
• 模态形状对比
• 计算效率分析

注意: 这是模拟界面，实际图表将在集成真实workbench后显示
            """.strip()

            self.ui.label_chart_placeholder.setText(placeholder_text)

            logger.info(f"对比界面已更新，显示 {len(self.selected_results)} 个选中结果")

        except Exception as e:
            logger.error(f"更新对比界面失败: {str(e)}")

    # ==================== 结果对比事件处理 ====================

    def _on_import_results(self):
        """导入结果按钮点击处理 - 简化版本"""
        try:
            if not self.modal_data_manager:
                CustomMessageBox.warning(self, "提示", "数据管理器不可用，无法导入结果")
                return

            # 直接打开文件选择对话框
            file_dialog = QFileDialog(self)
            file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
            file_dialog.setNameFilter(
                "模态结果文件 (*.json *.csv *.txt);;JSON文件 (*.json);;CSV文件 (*.csv);;文本文件 (*.txt)"
            )
            file_dialog.setWindowTitle("选择要导入的模态分析结果文件")

            if file_dialog.exec():
                selected_files = file_dialog.selectedFiles()
                if selected_files:
                    self._import_files_directly(selected_files)

        except Exception as e:
            logger.error(f"导入结果失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"导入结果失败: {str(e)}")

    def _import_files_directly(self, file_paths):
        """直接导入文件"""
        try:
            success_count = 0
            total_count = len(file_paths)

            for file_path in file_paths:
                if os.path.exists(file_path):
                    if self.modal_data_manager.import_from_file(file_path):
                        success_count += 1
                        logger.info(f"成功导入: {os.path.basename(file_path)}")
                    else:
                        logger.warning(f"导入失败: {os.path.basename(file_path)}")
                else:
                    logger.warning(f"文件不存在: {file_path}")

            # 显示导入结果
            if success_count > 0:
                # 刷新对比列表以显示导入的结果
                self._refresh_comparison_list()

                # 显示成功消息
                CustomMessageBox.information(
                    self, "导入完成",
                    f"成功导入 {success_count}/{total_count} 个文件\n\n"
                    f"导入的结果已添加到对比网格列表中，\n"
                    f"您可以选择它们与当前计算结果进行对比分析。"
                )

                # 更新状态消息
                imported_count = len(self.modal_data_manager.get_imported_results())
                self.show_status_message(f"导入完成，当前有 {imported_count} 个外部结果可用于对比")

            else:
                CustomMessageBox.warning(self, "导入失败", "没有文件被成功导入，请检查文件格式。")

        except Exception as e:
            logger.error(f"批量导入文件失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"批量导入文件失败: {str(e)}")


    def _show_comparison_context_menu(self, position):
        """显示对比列表右键菜单 - 支持多选批量删除"""
        try:
            list_widget = self.ui.listWidget_comparison_meshes
            clicked_item = list_widget.itemAt(position)
            selected_items = list_widget.selectedItems()

            if not selected_items:
                return

            # 筛选出导入的结果
            imported_items = [item for item in selected_items
                             if item.data(Qt.ItemDataRole.UserRole + 1) == "imported"]

            if not imported_items:
                return

            menu = QMenu(self)

            # 根据选中的导入结果数量显示不同的删除选项
            if len(imported_items) == 1:
                delete_action = QAction("🗑️ 删除导入结果", self)
                delete_action.setToolTip("删除这个导入的模态分析结果")
                delete_action.triggered.connect(lambda: self._delete_imported_results(imported_items))
            else:
                delete_action = QAction(f"🗑️ 删除选中的 {len(imported_items)} 个导入结果", self)
                delete_action.setToolTip(f"批量删除选中的 {len(imported_items)} 个导入的模态分析结果")
                delete_action.triggered.connect(lambda: self._delete_imported_results(imported_items))

            menu.addAction(delete_action)

            # 添加清空所有导入结果选项
            if self.modal_data_manager:
                total_imported = len(self.modal_data_manager.get_imported_results())
                if total_imported > len(imported_items):  # 只有当还有其他导入结果时才显示清空选项
                    menu.addSeparator()
                    clear_action = QAction(f"🧹 清空所有导入结果 ({total_imported}个)", self)
                    clear_action.setToolTip("一次性删除所有导入的模态分析结果")
                    clear_action.triggered.connect(self._clear_all_imported_results)
                    menu.addAction(clear_action)

            menu.exec(list_widget.mapToGlobal(position))

        except Exception as e:
            logger.error(f"显示右键菜单失败: {str(e)}")

    def _delete_imported_result(self, item):
        """删除导入的结果"""
        try:
            if not self.modal_data_manager:
                CustomMessageBox.warning(self, "提示", "数据管理器不可用")
                return

            # 获取项目信息
            item_id = item.data(Qt.ItemDataRole.UserRole)
            data_type = item.data(Qt.ItemDataRole.UserRole + 1)

            # 确保只能删除导入的结果
            if data_type != "imported":
                CustomMessageBox.warning(self, "提示", "只能删除导入的结果，不能删除当前计算结果")
                return

            # 获取结果名称用于确认对话框
            imported_index = int(item_id.replace("imported_", ""))
            imported_results = self.modal_data_manager.get_imported_results()

            if 0 <= imported_index < len(imported_results):
                result = imported_results[imported_index]

                # 显示确认对话框
                reply = CustomMessageBox.question(
                    self, "确认删除",
                    f"确定要删除导入的结果吗？\n\n"
                    f"名称: {result.name}\n"
                    f"网格尺寸: {result.size:.2f}mm\n"
                    f"模态数: {len(result.frequencies)}\n\n"
                    f"删除后将无法恢复。"
                )

                if reply == CustomMessageBox.Yes:
                    # 执行删除操作
                    if self.modal_data_manager.remove_imported_result(imported_index):
                        # 刷新对比列表
                        self._refresh_comparison_list()

                        # 显示成功消息
                        remaining_count = len(self.modal_data_manager.get_imported_results())
                        CustomMessageBox.information(
                            self, "删除成功",
                            f"已成功删除导入结果 '{result.name}'\n\n"
                            f"当前还有 {remaining_count} 个导入结果可用于对比。\n\n"
                            f"如果当前图表包含被删除的数据，请重新选择数据并更新图表。"
                        )

                        # 更新状态消息
                        self.show_status_message(f"已删除导入结果 '{result.name}'，当前有 {remaining_count} 个导入结果")

                        logger.info(f"成功删除导入结果: {result.name}")

                    else:
                        CustomMessageBox.critical(self, "删除失败", "删除导入结果失败，请重试")
            else:
                CustomMessageBox.warning(self, "错误", "无效的导入结果索引")

        except Exception as e:
            logger.error(f"删除导入结果失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"删除导入结果失败: {str(e)}")

    def _clear_all_imported_results(self):
        """清空所有导入结果"""
        try:
            if not self.modal_data_manager:
                CustomMessageBox.warning(self, "提示", "数据管理器不可用")
                return

            imported_results = self.modal_data_manager.get_imported_results()
            imported_count = len(imported_results)

            if imported_count == 0:
                CustomMessageBox.information(self, "提示", "当前没有导入的结果需要清空")
                return

            # 显示确认对话框
            reply = CustomMessageBox.question(
                self, "确认清空",
                f"确定要清空所有 {imported_count} 个导入结果吗？\n\n"
                f"此操作将删除所有导入的模态分析结果：\n"
                f"• 包含 {imported_count} 个不同的网格结果\n"
                f"• 涵盖各种网格尺寸和模态数据\n\n"
                f"删除后将无法恢复，建议确认不再需要这些数据。"
            )

            if reply == CustomMessageBox.Yes:
                # 执行清空操作
                if self.modal_data_manager.clear_all_imported_results():
                    # 刷新对比列表
                    self._refresh_comparison_list()

                    # 显示成功消息
                    CustomMessageBox.information(
                        self, "清空完成",
                        f"已成功清空所有 {imported_count} 个导入结果。\n\n"
                        f"所有导入的模态分析数据已从系统中移除，\n"
                        f"如果当前图表包含被删除的数据，请重新选择数据并更新图表。\n\n"
                        f"您可以重新导入需要的数据文件进行对比分析。"
                    )

                    # 更新状态消息
                    self.show_status_message("已清空所有导入结果，可重新导入数据进行对比分析")

                    logger.info(f"成功清空所有 {imported_count} 个导入结果")

                else:
                    CustomMessageBox.critical(self, "清空失败", "清空导入结果失败，请重试")

        except Exception as e:
            logger.error(f"清空所有导入结果失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"清空所有导入结果失败: {str(e)}")

    def _delete_imported_results(self, items):
        """批量删除导入结果"""
        try:
            if not items or not self.modal_data_manager:
                return

            # 获取要删除的结果信息
            results_info = []
            indices_to_delete = []

            for item in items:
                item_id = item.data(Qt.ItemDataRole.UserRole)
                data_type = item.data(Qt.ItemDataRole.UserRole + 1)

                if data_type == "imported" and item_id.startswith("imported_"):
                    index = int(item_id.replace("imported_", ""))
                    imported_results = self.modal_data_manager.get_imported_results()
                    if 0 <= index < len(imported_results):
                        result = imported_results[index]
                        results_info.append(f"• {result.name} (尺寸: {result.size:.2f}mm, 模态: {len(result.frequencies)}个)")
                        indices_to_delete.append(index)

            if not indices_to_delete:
                CustomMessageBox.warning(self, "提示", "没有找到有效的导入结果可删除")
                return

            # 显示批量删除确认对话框
            if len(indices_to_delete) == 1:
                message = f"确定要删除这个导入结果吗？\n\n{results_info[0]}\n\n删除后将无法恢复。"
                title = "确认删除"
            else:
                message = f"确定要删除这 {len(indices_to_delete)} 个导入结果吗？\n\n"
                message += "\n".join(results_info[:5])  # 最多显示前5个
                if len(results_info) > 5:
                    message += f"\n... 还有 {len(results_info) - 5} 个结果"
                message += "\n\n删除后将无法恢复。"
                title = "确认批量删除"

            reply = CustomMessageBox.question(self, title, message)

            if reply == CustomMessageBox.Yes:
                # 按索引倒序删除，避免索引变化问题
                success_count = 0
                for index in sorted(indices_to_delete, reverse=True):
                    if self.modal_data_manager.remove_imported_result(index):
                        success_count += 1

                # 刷新对比列表
                self._refresh_comparison_list()

                # 显示删除结果
                remaining_count = len(self.modal_data_manager.get_imported_results())
                if success_count == len(indices_to_delete):
                    if len(indices_to_delete) == 1:
                        CustomMessageBox.information(
                            self, "删除完成",
                            f"已成功删除导入结果。\n\n"
                            f"当前还有 {remaining_count} 个导入结果可用于对比。\n\n"
                            f"如果当前图表包含被删除的数据，请重新选择数据并更新图表。"
                        )
                    else:
                        CustomMessageBox.information(
                            self, "批量删除完成",
                            f"已成功删除 {success_count} 个导入结果。\n\n"
                            f"当前还有 {remaining_count} 个导入结果可用于对比。\n\n"
                            f"如果当前图表包含被删除的数据，请重新选择数据并更新图表。"
                        )
                else:
                    CustomMessageBox.warning(
                        self, "部分删除失败",
                        f"成功删除 {success_count}/{len(indices_to_delete)} 个导入结果，\n"
                        f"部分删除操作失败，请重试。"
                    )

                # 更新状态消息
                self.show_status_message(f"已删除 {success_count} 个导入结果，当前有 {remaining_count} 个导入结果")

                logger.info(f"批量删除完成: 成功删除 {success_count}/{len(indices_to_delete)} 个导入结果")

        except Exception as e:
            logger.error(f"批量删除导入结果失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"批量删除导入结果失败: {str(e)}")

    def _on_export_results(self):
        """导出结果按钮点击处理"""
        try:
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            if not completed_meshes:
                CustomMessageBox.information(self, "提示", "没有已完成的分析结果可以导出")
                return

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出分析结果", "analysis_results.json", "JSON文件 (*.json);;所有文件 (*)"
            )

            if file_path:
                # 构建导出数据
                export_data = {
                    "results": [],
                    "export_info": {
                        "count": len(completed_meshes),
                        "timestamp": datetime.now().isoformat(),
                        "source": "网格无关性验证系统"
                    }
                }

                for mesh in completed_meshes:
                    mesh_data = {
                        "name": mesh.name,
                        "size": mesh.size,
                        "element_type": mesh.element_type.value,
                        "frequencies": mesh.modal_results.frequencies,
                        "calculation_time": mesh.modal_results.calculation_time,
                        "statistics": {
                            "node_count": mesh.statistics.node_count,
                            "element_count": mesh.statistics.element_count,
                            "avg_quality": mesh.statistics.avg_quality
                        }
                    }
                    export_data["results"].append(mesh_data)

                # 保存文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)

                CustomMessageBox.information(self, "成功", f"成功导出 {len(completed_meshes)} 个分析结果到:\n{file_path}")
                logger.info(f"成功导出分析结果到: {file_path}")

        except Exception as e:
            logger.error(f"导出分析结果失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出分析结果失败: {str(e)}")

    def _on_update_chart(self):
        """更新图表按钮点击处理"""
        try:
            # 获取选中的网格结果
            selected_items = self.ui.listWidget_comparison_meshes.selectedItems()
            if not selected_items:
                CustomMessageBox.warning(self, "提示", "请先选择要对比的网格结果")
                return

            # 获取选中的图表类型
            chart_type = "frequency_comparison"  # 默认
            if self.ui.radioButton_mode_distribution.isChecked():
                chart_type = "mode_distribution"
            elif self.ui.radioButton_mesh_convergence.isChecked():
                chart_type = "mesh_convergence"

            # 更新图表标题
            chart_titles = {
                "frequency_comparison": "模态频率对比分析",
                "mode_distribution": "模态分布分析",
                "mesh_convergence": "网格收敛性分析"
            }
            self.ui.label_chart_title.setText(chart_titles.get(chart_type, "模态分析图表"))

            # 这里可以添加实际的图表生成逻辑
            self._update_modal_chart(selected_items, chart_type)

            logger.info(f"更新模态分析图表: {chart_type}, 选中网格数: {len(selected_items)}")

        except Exception as e:
            logger.error(f"更新图表失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"更新图表失败: {str(e)}")

    def _on_save_chart(self):
        """保存图表按钮点击处理"""
        try:
            if not self.modal_chart_widget:
                CustomMessageBox.warning(self, "提示", "图表组件不可用")
                return

            # 获取保存路径
            from PySide6.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存图表",
                f"模态分析图表_{self.modal_chart_widget.current_chart_type}.png",
                "PNG图片 (*.png);;JPEG图片 (*.jpg);;PDF文件 (*.pdf)"
            )

            if file_path:
                self.modal_chart_widget.save_chart(file_path)
                CustomMessageBox.information(self, "成功", f"图表已保存到:\n{file_path}")
                logger.info(f"图表已保存到: {file_path}")

        except Exception as e:
            logger.error(f"保存图表失败: {str(e)}")
            CustomMessageBox.critical(self, "错误", f"保存图表失败: {str(e)}")

    def _on_chart_updated(self, chart_type: str):
        """图表更新信号处理"""
        try:
            logger.debug(f"图表已更新: {chart_type}")
            # 可以在这里添加图表更新后的处理逻辑
        except Exception as e:
            logger.error(f"图表更新信号处理失败: {str(e)}")

    def _on_modal_option_changed(self):
        """模态分析选项变化处理"""
        try:
            logger.debug("模态分析显示选项发生变化")
            # 可以在这里根据选项更新图表显示
            self._update_chart_display_options()
        except Exception as e:
            logger.error(f"模态分析选项变化处理失败: {str(e)}")

    def _on_chart_type_changed(self):
        """图表类型变化处理"""
        try:
            logger.debug("图表类型选择发生变化")
            # 可以在这里根据图表类型更新显示
        except Exception as e:
            logger.error(f"图表类型变化处理失败: {str(e)}")

    def _update_modal_chart(self, selected_items, chart_type):
        """更新模态分析图表 - 支持当前结果和导入结果混合对比"""
        try:
            # 获取选中项目的模态数据（包括当前计算结果和导入结果）
            mesh_data = []
            for item in selected_items:
                item_id = item.data(Qt.ItemDataRole.UserRole)
                data_type = item.data(Qt.ItemDataRole.UserRole + 1)

                if data_type == "current":
                    # 处理当前计算结果
                    mesh_id = item_id.replace("current_", "")
                    mesh = self.mesh_manager.get_mesh_by_id(mesh_id)
                    if mesh and mesh.modal_results:
                        mesh_data.append({
                            'name': f"[current] {mesh.name}",
                            'size': mesh.size,
                            'frequencies': mesh.modal_results.frequencies,
                            'node_count': getattr(mesh, 'node_count', 0),
                            'element_count': getattr(mesh, 'element_count', 0),
                            'source': 'current'
                        })

                elif data_type == "imported" and self.modal_data_manager:
                    # 处理导入的结果
                    imported_index = int(item_id.replace("imported_", ""))
                    imported_results = self.modal_data_manager.get_imported_results()
                    if 0 <= imported_index < len(imported_results):
                        result = imported_results[imported_index]
                        mesh_data.append({
                            'name': f"[imported] {result.name}",
                            'size': result.size,
                            'frequencies': result.frequencies,
                            'node_count': result.node_count,
                            'element_count': result.element_count,
                            'source': 'imported'
                        })

            # 获取显示选项（包含导入结果显示选项）
            chart_options = {
                'show_frequencies': self.ui.checkBox_show_frequencies.isChecked(),
                'show_mode_shapes': self.ui.checkBox_show_mode_shapes.isChecked(),
                'show_mesh_info': self.ui.checkBox_show_mesh_info.isChecked(),
                'show_current': True,  # 始终显示当前结果
                'show_imported': True  # 始终显示导入结果
            }

            # 使用matplotlib图表组件更新图表
            if self.modal_chart_widget:
                self.modal_chart_widget.update_chart(chart_type, mesh_data, chart_options)
            else:
                # 降级到文本显示
                self._fallback_text_chart(mesh_data, chart_type)

        except Exception as e:
            logger.error(f"更新模态图表失败: {str(e)}")

    def _fallback_text_chart(self, mesh_data, chart_type):
        """降级文本图表显示（当matplotlib不可用时）"""
        try:
            if not mesh_data:
                self.ui.label_chart_placeholder.setText(
                    "选中的网格暂无模态分析结果\n\n请先完成模态计算后再进行对比分析"
                )
                return

            # 根据图表类型生成不同的显示内容
            if chart_type == "frequency_comparison":
                self._generate_frequency_comparison_chart(mesh_data)
            elif chart_type == "mode_distribution":
                self._generate_mode_distribution_chart(mesh_data)
            elif chart_type == "mesh_convergence":
                self._generate_mesh_convergence_chart(mesh_data)

        except Exception as e:
            logger.error(f"降级文本图表显示失败: {str(e)}")

    def _generate_frequency_comparison_chart(self, mesh_data):
        """生成频率对比图表"""
        try:
            # 生成频率对比的文本显示（占位符实现）
            chart_text = "📊 模态频率对比分析\n\n"

            for i, data in enumerate(mesh_data, 1):
                chart_text += f"网格 {i}: {data['name']}\n"
                chart_text += f"  尺寸: {data['size']:.2f}mm\n"
                if data['frequencies']:
                    chart_text += f"  频率范围: {min(data['frequencies']):.2f} - {max(data['frequencies']):.2f} Hz\n"
                    chart_text += f"  模态数量: {len(data['frequencies'])} 阶\n"
                chart_text += "\n"

            chart_text += "💡 提示: 完整的图表功能将在后续版本中实现"

            self.ui.label_chart_placeholder.setText(chart_text)

        except Exception as e:
            logger.error(f"生成频率对比图表失败: {str(e)}")

    def _generate_mode_distribution_chart(self, mesh_data):
        """生成模态分布图表"""
        try:
            chart_text = "📈 模态分布分析\n\n"
            chart_text += "各网格模态阶数分布:\n\n"

            for i, data in enumerate(mesh_data, 1):
                chart_text += f"网格 {i}: {data['name']}\n"
                chart_text += f"  模态阶数: {len(data['frequencies'])} 阶\n"
                if data['frequencies']:
                    # 简单的频率分布统计
                    freqs = data['frequencies']
                    low_freq = [f for f in freqs if f < 100]
                    mid_freq = [f for f in freqs if 100 <= f < 500]
                    high_freq = [f for f in freqs if f >= 500]

                    chart_text += f"  低频段(<100Hz): {len(low_freq)} 阶\n"
                    chart_text += f"  中频段(100-500Hz): {len(mid_freq)} 阶\n"
                    chart_text += f"  高频段(≥500Hz): {len(high_freq)} 阶\n"
                chart_text += "\n"

            self.ui.label_chart_placeholder.setText(chart_text)

        except Exception as e:
            logger.error(f"生成模态分布图表失败: {str(e)}")

    def _generate_mesh_convergence_chart(self, mesh_data):
        """生成网格收敛性分析图表"""
        try:
            chart_text = "🔍 网格收敛性分析\n\n"

            # 按网格尺寸排序
            sorted_data = sorted(mesh_data, key=lambda x: x['size'])

            chart_text += "网格尺寸 vs 模态频率收敛性:\n\n"

            for data in sorted_data:
                chart_text += f"尺寸 {data['size']:.2f}mm: {data['name']}\n"
                if data['frequencies']:
                    first_freq = data['frequencies'][0] if data['frequencies'] else 0
                    chart_text += f"  第一阶频率: {first_freq:.2f} Hz\n"
                    chart_text += f"  总模态数: {len(data['frequencies'])} 阶\n"
                chart_text += "\n"

            chart_text += "💡 提示: 网格尺寸越小，计算精度越高，但计算时间也会增加"

            self.ui.label_chart_placeholder.setText(chart_text)

        except Exception as e:
            logger.error(f"生成网格收敛性图表失败: {str(e)}")

    def _update_chart_display_options(self):
        """更新图表显示选项"""
        try:
            # 根据选中的显示选项更新图表
            show_frequencies = self.ui.checkBox_show_frequencies.isChecked()
            show_mode_shapes = self.ui.checkBox_show_mode_shapes.isChecked()
            show_mesh_info = self.ui.checkBox_show_mesh_info.isChecked()

            logger.debug(f"图表显示选项: 频率={show_frequencies}, 模态={show_mode_shapes}, 网格={show_mesh_info}")

        except Exception as e:
            logger.error(f"更新图表显示选项失败: {str(e)}")

    # ==================== 导航事件处理 ====================

    def _on_generate_mesh_nav(self):
        """底部导航生成网格按钮点击处理"""
        try:
            # 切换到网格生成与模态分析标签页
            self.ui.tabWidget_main.setCurrentIndex(1)
            logger.info("切换到网格生成与模态分析标签页")
        except Exception as e:
            logger.error(f"切换到网格生成标签页失败: {str(e)}")

    def _on_view_results(self):
        """查看结果按钮点击处理"""
        try:
            # 检查是否有完成的计算结果
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)

            if completed_meshes:
                # 切换到结果对比标签页
                self.ui.tabWidget_main.setCurrentIndex(2)

                # 刷新对比列表显示所有完成的网格方案
                self._refresh_comparison_list()

                self.show_status_message(f"查看 {len(completed_meshes)} 个网格方案的对比分析")
                logger.info(f"切换到结果对比标签页，显示 {len(completed_meshes)} 个网格方案")
            else:
                # 没有完成的结果，提示用户先完成计算
                CustomMessageBox.information(self, "提示",
                    "📊 暂无完成的计算结果\n\n"
                    "请先在'网格生成与模态分析'标签页中:\n"
                    "1. 生成网格\n"
                    "2. 完成模态计算\n\n"
                    "然后再查看结果对比分析。")

                logger.info("用户尝试查看结果但没有完成的计算")

        except Exception as e:
            logger.error(f"切换到结果对比标签页失败: {str(e)}")

    def _on_previous(self):
        """上一步按钮点击处理 - 跳转到约束设置"""
        try:
            from core.navigation_manager import navigate_to_previous_step
            from window_manager import WindowType
            success = navigate_to_previous_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到约束设置界面")
            else:
                logger.warning("跳转到约束设置失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到约束设置界面")
        except Exception as e:
            logger.error(f"跳转到约束设置失败: {str(e)}")
            QMessageBox.warning(self, "跳转失败", f"无法跳转到约束设置界面: {str(e)}")

    def _on_next(self):
        """下一步按钮点击处理 - 跳转到连接设置"""
        try:
            from core.navigation_manager import navigate_to_next_step
            from window_manager import WindowType
            success = navigate_to_next_step(self.window_manager, WindowType.MESH)
            if success:
                logger.info("成功跳转到连接设置界面")
            else:
                logger.warning("跳转到连接设置失败")
                QMessageBox.warning(self, "跳转失败", "无法跳转到连接设置界面")
        except Exception as e:
            logger.error(f"跳转到连接设置失败: {str(e)}")
            QMessageBox.warning(self, "跳转失败", f"无法跳转到连接设置界面: {str(e)}")

    def _on_main_menu(self):
        """主菜单按钮点击处理"""
        try:
            from ctrl.mesh_slot import to_main_slot
            to_main_slot(self.window_manager)
            logger.info("成功跳转到主菜单")
        except Exception as e:
            logger.error(f"跳转到主菜单失败: {str(e)}")
            QMessageBox.warning(self, "跳转失败", f"无法跳转到主菜单: {str(e)}")

    def _on_tab_changed(self, index: int):
        """标签页切换处理"""
        try:
            tab_names = ["网格管理", "网格生成与模态分析", "结果对比"]
            if 0 <= index < len(tab_names):
                logger.debug(f"切换到标签页: {tab_names[index]}")

                # 根据标签页更新UI状态
                if index == 1:  # 网格生成与模态分析
                    self._refresh_mesh_status_list()
                elif index == 2:  # 结果对比
                    self._refresh_comparison_list()

        except Exception as e:
            logger.error(f"标签页切换处理失败: {str(e)}")

    # ==================== 网格管理器信号处理 ====================

    def _on_mesh_added(self, mesh_id: str):
        """网格添加信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格添加UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格添加信号失败: {str(e)}")

    def _on_mesh_removed(self, mesh_id: str):
        """网格删除信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格删除UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格删除信号失败: {str(e)}")

    def _on_mesh_updated(self, mesh_id: str):
        """网格更新信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格更新UI更新: {mesh_id}")
        except Exception as e:
            logger.error(f"处理网格更新信号失败: {str(e)}")

    def _on_mesh_status_changed(self, mesh_id: str, old_status: MeshStatus, new_status: MeshStatus):
        """网格状态变化信号处理"""
        try:
            self._refresh_all_ui()
            logger.debug(f"网格状态变化: {mesh_id} {old_status.value} -> {new_status.value}")
        except Exception as e:
            logger.error(f"处理网格状态变化信号失败: {str(e)}")

    def _on_current_mesh_changed(self, mesh_id: Optional[str]):
        """当前网格变更信号处理"""
        try:
            self._update_mesh_stats()
            self._update_ui_state()
            logger.debug(f"当前网格变更: {mesh_id}")
        except Exception as e:
            logger.error(f"处理当前网格变更信号失败: {str(e)}")

    def _on_mesh_error(self, mesh_id: str, error_message: str):
        """网格错误信号处理"""
        try:
            QMessageBox.warning(self, "网格操作错误", error_message)
            logger.warning(f"网格操作错误 (ID: {mesh_id}): {error_message}")
        except Exception as e:
            logger.error(f"处理网格错误信号失败: {str(e)}")

    # ==================== UI刷新方法 ====================

    def _refresh_all_ui(self):
        """刷新所有UI组件"""
        try:
            self._refresh_mesh_table()
            self._refresh_mesh_status_list()
            self._refresh_comparison_list()
            self._update_mesh_stats()
            self._update_ui_state()
            logger.debug("所有UI组件刷新完成")
        except Exception as e:
            logger.error(f"刷新所有UI组件失败: {str(e)}")

    def _refresh_mesh_table(self):
        """刷新网格参数表格"""
        try:
            table = self.ui.tableWidget_mesh_params
            table.setRowCount(0)

            meshes = self.mesh_manager.get_all_meshes()
            for row, mesh in enumerate(meshes):
                table.insertRow(row)

                # 名称列
                name_item = QTableWidgetItem(mesh.name)
                name_item.setData(Qt.ItemDataRole.UserRole, mesh.id)
                table.setItem(row, 0, name_item)

                # 尺寸列
                size_item = QTableWidgetItem(f"{mesh.size:.2f}")
                table.setItem(row, 1, size_item)

                # 单元类型列
                type_item = QTableWidgetItem(mesh.element_type.value)
                table.setItem(row, 2, type_item)

                # 状态列
                status_item = QTableWidgetItem(mesh.status.value)
                # 根据状态设置颜色
                if mesh.status == MeshStatus.COMPLETED:
                    status_item.setBackground(QColor(200, 255, 200))
                elif mesh.status == MeshStatus.GENERATED:
                    status_item.setBackground(QColor(255, 255, 200))
                elif mesh.status == MeshStatus.GENERATING or mesh.status == MeshStatus.CALCULATING:
                    status_item.setBackground(QColor(255, 200, 200))
                table.setItem(row, 3, status_item)

                # 创建时间列
                time_item = QTableWidgetItem(mesh.created_time.strftime('%Y-%m-%d %H:%M'))
                table.setItem(row, 4, time_item)

                # 操作列 - 现代化按钮设计
                operations_widget = QWidget()
                operations_layout = QHBoxLayout(operations_widget)
                operations_layout.setContentsMargins(4, 4, 4, 4)
                operations_layout.setSpacing(6)

                # 编辑按钮 - 蓝色系
                edit_btn = QPushButton("✏️ 编辑")
                edit_btn.setMinimumSize(60, 28)
                edit_btn.setMaximumSize(60, 28)
                edit_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #2196f3;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 11px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #1976d2;
                        border: 1px solid #0d47a1;
                    }
                    QPushButton:pressed {
                        background-color: #0d47a1;
                        border: 1px solid #1976d2;
                    }
                """)
                edit_btn.clicked.connect(lambda checked=False, m_id=mesh.id: self._edit_mesh(m_id))

                # 删除按钮 - 红色系
                delete_btn = QPushButton("🗑️ 删除")
                delete_btn.setMinimumSize(60, 28)
                delete_btn.setMaximumSize(60, 28)
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #f44336;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        font-weight: bold;
                        font-size: 11px;
                        padding: 4px 8px;
                    }
                    QPushButton:hover {
                        background-color: #d32f2f;
                        border: 1px solid #b71c1c;
                    }
                    QPushButton:pressed {
                        background-color: #b71c1c;
                        border: 1px solid #d32f2f;
                    }
                """)
                delete_btn.clicked.connect(lambda checked=False, m_id=mesh.id: self._delete_mesh(m_id))

                operations_layout.addWidget(edit_btn)
                operations_layout.addWidget(delete_btn)
                table.setCellWidget(row, 5, operations_widget)

            logger.debug(f"网格参数表格刷新完成，共 {len(meshes)} 行")

        except Exception as e:
            logger.error(f"刷新网格参数表格失败: {str(e)}")

    def _refresh_mesh_status_list(self):
        """刷新网格状态列表"""
        try:
            list_widget = self.ui.listWidget_mesh_status
            list_widget.clear()

            meshes = self.mesh_manager.get_all_meshes()
            for mesh in meshes:
                item = QListWidgetItem()

                # 设置显示文本
                display_text = f"{mesh.name}\n"
                display_text += f"尺寸: {mesh.size:.2f}mm | "
                display_text += f"状态: {mesh.status.value}"

                if mesh.status == MeshStatus.GENERATED:
                    display_text += " | 可进行模态计算"
                elif mesh.status == MeshStatus.COMPLETED:
                    freq_count = len(mesh.modal_results.frequencies)
                    display_text += f" | 已完成 ({freq_count} 个频率)"

                item.setText(display_text)
                item.setData(Qt.ItemDataRole.UserRole, mesh.id)

                # 设置颜色
                if mesh.status == MeshStatus.COMPLETED:
                    item.setBackground(QColor(200, 255, 200))  # 浅绿色
                elif mesh.status == MeshStatus.GENERATED:
                    item.setBackground(QColor(255, 255, 200))  # 浅黄色
                elif mesh.status == MeshStatus.GENERATING or mesh.status == MeshStatus.CALCULATING:
                    item.setBackground(QColor(255, 200, 200))  # 浅红色

                list_widget.addItem(item)

            logger.debug(f"网格状态列表刷新完成，共 {len(meshes)} 个网格")

        except Exception as e:
            logger.error(f"刷新网格状态列表失败: {str(e)}")

    def _refresh_comparison_list(self):
        """刷新对比网格列表 - 显示当前计算结果和导入的外部结果"""
        try:
            list_widget = self.ui.listWidget_comparison_meshes
            list_widget.clear()

            # 1. 添加当前计算完成的网格结果
            completed_meshes = self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)
            for mesh in completed_meshes:
                item = QListWidgetItem()

                freq_count = len(mesh.modal_results.frequencies)
                calc_time = mesh.modal_results.calculation_time

                display_text = f"[当前] {mesh.name}\n"
                display_text += f"尺寸: {mesh.size:.2f}mm | "
                display_text += f"模态数: {freq_count} | "
                display_text += f"计算时间: {calc_time:.1f}s"

                item.setText(display_text)
                item.setData(Qt.ItemDataRole.UserRole, f"current_{mesh.id}")
                item.setData(Qt.ItemDataRole.UserRole + 1, "current")  # 标记数据类型

                # 设置当前结果的背景色（浅蓝色）
                item.setBackground(QColor(230, 240, 255))
                list_widget.addItem(item)

            # 2. 添加导入的外部结果
            if self.modal_data_manager:
                imported_results = self.modal_data_manager.get_imported_results()
                for i, result in enumerate(imported_results):
                    item = QListWidgetItem()

                    freq_count = len(result.frequencies)

                    display_text = f"[imported] {result.name}\n"
                    display_text += f"size: {result.size:.2f}mm | "
                    display_text += f"freq_count: {freq_count} | "
                    display_text += f"node_count: {result.node_count:,}"

                    item.setText(display_text)
                    item.setData(Qt.ItemDataRole.UserRole, f"imported_{i}")
                    item.setData(Qt.ItemDataRole.UserRole + 1, "imported")  # 标记数据类型

                    # 设置导入结果的背景色（浅绿色）
                    item.setBackground(QColor(240, 255, 240))
                    list_widget.addItem(item)

            # 更新图表占位符
            if completed_meshes:
                # 显示选择状态信息
                selected_info = ""
                if hasattr(self, 'selected_results') and self.selected_results:
                    selected_info = f"\n已选择用于分析: {len(self.selected_results)} 个结果"

                placeholder_text = f"""
网格无关性分析图表

可用网格方案: {len(completed_meshes)} 个
{chr(10).join([f"• {mesh.name} ({mesh.size:.2f}mm)" for mesh in completed_meshes])}{selected_info}

💡 提示: 左侧显示所有完成的网格方案，方便对比选择最优网格

图表功能:
• 频率收敛性分析
• 网格尺寸 vs 频率曲线
• 模态形状对比
• 计算效率分析

注意: 这是模拟界面，实际图表将在集成真实workbench后显示
                """.strip()
            else:
                placeholder_text = """
网格无关性分析图表

暂无可用的计算结果

请先完成以下步骤:
1. 在"网格管理"标签页创建网格参数
2. 在"网格生成与模态分析"标签页生成网格
3. 完成模态计算

注意: 这是模拟界面，实际图表将在集成真实workbench后显示
                """.strip()

            # 安全地更新图表占位符文本
            try:
                if (hasattr(self, 'ui') and self.ui and
                    hasattr(self.ui, 'label_chart_placeholder') and
                    self.ui.label_chart_placeholder is not None):
                    self.ui.label_chart_placeholder.setText(placeholder_text)
            except (RuntimeError, AttributeError) as e:
                # UI对象已被删除或不存在，忽略此错误
                logger.debug(f"图表占位符标签不可用，跳过文本更新: {str(e)}")
            except Exception as e:
                # 其他意外错误，记录但不中断流程
                logger.warning(f"更新图表占位符文本时发生意外错误: {str(e)}")

            logger.debug(f"对比网格列表刷新完成，共 {len(completed_meshes)} 个网格")

        except Exception as e:
            logger.error(f"刷新对比网格列表失败: {str(e)}")

    def _update_mesh_stats(self):
        """更新网格统计信息"""
        try:
            meshes = self.mesh_manager.get_all_meshes()
            total_count = len(meshes)

            status_counts = {}
            for status in MeshStatus:
                status_counts[status] = len(self.mesh_manager.get_meshes_by_status(status))

            stats_text = f"""
网格统计信息:

总网格数: {total_count}

状态分布:
• 未生成: {status_counts[MeshStatus.NOT_GENERATED]}
• 生成中: {status_counts[MeshStatus.GENERATING]}
• 已生成: {status_counts[MeshStatus.GENERATED]}
• 计算中: {status_counts[MeshStatus.CALCULATING]}
• 已完成: {status_counts[MeshStatus.COMPLETED]}
• 错误: {status_counts[MeshStatus.ERROR]}

当前选择: {self.mesh_manager.current_mesh.name if self.mesh_manager.current_mesh else "无"}
            """.strip()

            self.ui.textEdit_mesh_stats.setPlainText(stats_text)

        except Exception as e:
            logger.error(f"更新网格统计信息失败: {str(e)}")

    def _update_ui_state(self):
        """更新UI状态"""
        try:
            has_meshes = self.mesh_manager.mesh_count > 0
            has_selection = len(self.selected_meshes_for_generation) > 0
            has_generated = len([mesh for mesh in self.selected_meshes_for_generation
                               if mesh.status == MeshStatus.GENERATED]) > 0
            has_completed = len(self.mesh_manager.get_meshes_by_status(MeshStatus.COMPLETED)) > 0

            # 更新按钮状态（安全检查）
            if hasattr(self.ui, 'btn_export_mesh'):
                self.ui.btn_export_mesh.setEnabled(has_meshes)

            if hasattr(self.ui, 'btn_single_modal'):
                # 修改启用条件：只需要选择1个网格且不在计算中（移除has_generated限制）
                self.ui.btn_single_modal.setEnabled(len(self.selected_meshes_for_generation) == 1 and
                                                  not self.is_calculating)
            if hasattr(self.ui, 'btn_batch_modal'):
                # 修改启用条件：只需要有选择的网格且不在计算中（移除has_generated限制）
                self.ui.btn_batch_modal.setEnabled(has_selection and not self.is_calculating)
            self.ui.btn_select_results.setEnabled(has_completed)
            self.ui.btn_export_results.setEnabled(has_completed)

            logger.debug(f"UI状态更新完成: 网格数={self.mesh_manager.mesh_count}, 选中={has_selection}")

        except Exception as e:
            logger.error(f"更新UI状态失败: {str(e)}")

    # ==================== 辅助方法 ====================

    def _edit_mesh(self, mesh_id: str):
        """编辑网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                dialog = MeshParameterDialog(self, mesh_param)
                dialog.parameter_accepted.connect(lambda updated_param: self._update_mesh(mesh_id, updated_param))
                dialog.exec()
        except Exception as e:
            logger.error(f"编辑网格失败: {str(e)}")

    def _delete_mesh(self, mesh_id: str):
        """删除网格"""
        try:
            mesh_param = self.mesh_manager.get_mesh_by_id(mesh_id)
            if mesh_param:
                reply = QMessageBox.question(
                    self, "确认删除",
                    f"确定要删除网格 '{mesh_param.name}' 吗？",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    success = self.mesh_manager.remove_mesh(mesh_id)
                    if success:
                        self._save_configuration()
                        CustomMessageBox.information(self, "删除成功", f"网格 '{mesh_param.name}' 已删除")
                        logger.info(f"网格删除成功: {mesh_param.name}")
                    else:
                        QMessageBox.warning(self, "删除失败", "网格删除失败")
        except Exception as e:
            logger.error(f"删除网格失败: {str(e)}")

    def _save_configuration(self):
        """保存配置"""
        try:
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()

            # 保存网格参数
            mesh_data = {}
            for mesh in self.mesh_manager.get_all_meshes():
                mesh_data[mesh.id] = mesh.to_dict()

            config_manager.set_mesh_parameters(mesh_data)

            # 保存当前网格ID
            current_mesh = self.mesh_manager.current_mesh
            if current_mesh:
                config_manager.set_current_mesh_id(current_mesh.id)

            # 关键修复：实际保存配置到文件
            success = config_manager.save_config()
            if success:
                logger.info("配置保存成功")
            else:
                logger.warning("配置保存失败")

        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}")

    def show_status_message(self, message: str, timeout: int = 3000):
        """显示状态消息"""
        try:
            # 如果窗口有状态栏，显示在状态栏
            if hasattr(self, 'statusBar') and self.statusBar():
                self.statusBar().showMessage(message, timeout)
            else:
                # 否则在控制台输出
                logger.info(f"状态消息: {message}")
        except Exception as e:
            logger.error(f"显示状态消息失败: {str(e)}")
