"""
简化的matplotlib图表集成测试

此脚本验证核心的matplotlib图表功能是否正常工作
专注于最重要的功能测试，避免复杂的UI集成测试

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_core_functionality():
    """测试核心功能"""
    print("🧪 测试matplotlib图表核心功能...")
    
    success_count = 0
    total_tests = 4
    
    # 1. 测试matplotlib可用性
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用无GUI后端避免显示问题
        import matplotlib.pyplot as plt
        print(f"✅ matplotlib可用 (版本: {matplotlib.__version__})")
        success_count += 1
    except Exception as e:
        print(f"❌ matplotlib不可用: {str(e)}")
    
    # 2. 测试图表组件创建
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        chart_widget = ModalChartWidget()
        print("✅ 图表组件创建成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 图表组件创建失败: {str(e)}")
    
    # 3. 测试图表绘制
    try:
        test_data = [
            {
                'name': '测试网格1',
                'size': 2.0,
                'frequencies': [45.2, 78.5, 112.3],
                'node_count': 5000,
                'element_count': 4000
            },
            {
                'name': '测试网格2',
                'size': 5.0,
                'frequencies': [44.8, 77.9, 111.2],
                'node_count': 3000,
                'element_count': 2500
            }
        ]
        
        chart_widget.update_chart("frequency_comparison", test_data)
        print("✅ 图表绘制成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 图表绘制失败: {str(e)}")
    
    # 4. 测试图表保存
    try:
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        chart_widget.save_chart(temp_path)
        
        if os.path.exists(temp_path):
            file_size = os.path.getsize(temp_path)
            print(f"✅ 图表保存成功 (大小: {file_size} 字节)")
            os.unlink(temp_path)  # 清理临时文件
            success_count += 1
        else:
            print("❌ 图表保存失败 - 文件不存在")
    except Exception as e:
        print(f"❌ 图表保存失败: {str(e)}")
    
    return success_count, total_tests

def test_chart_types():
    """测试三种图表类型"""
    print("\n🧪 测试三种图表类型...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        chart_widget = ModalChartWidget()
        
        test_data = [
            {
                'name': '细网格',
                'size': 1.0,
                'frequencies': [42.5, 75.8, 108.2, 145.6, 189.3],
                'node_count': 10000,
                'element_count': 8000
            },
            {
                'name': '粗网格',
                'size': 5.0,
                'frequencies': [41.2, 73.1, 105.8, 142.3, 185.7],
                'node_count': 3000,
                'element_count': 2500
            }
        ]
        
        chart_types = [
            "frequency_comparison",
            "mode_distribution", 
            "mesh_convergence"
        ]
        
        success_count = 0
        for chart_type in chart_types:
            try:
                chart_widget.update_chart(chart_type, test_data)
                print(f"✅ {chart_type} 图表绘制成功")
                success_count += 1
            except Exception as e:
                print(f"❌ {chart_type} 图表绘制失败: {str(e)}")
        
        return success_count, len(chart_types)
        
    except Exception as e:
        print(f"❌ 图表类型测试失败: {str(e)}")
        return 0, 3

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 matplotlib图表集成 - 简化测试")
    print("=" * 60)
    
    # 配置日志
    logging.basicConfig(
        level=logging.ERROR,  # 只显示错误
        format='%(levelname)s: %(message)s'
    )
    
    total_success = 0
    total_tests = 0
    
    # 测试核心功能
    success, tests = test_core_functionality()
    total_success += success
    total_tests += tests
    
    # 测试图表类型
    success, tests = test_chart_types()
    total_success += success
    total_tests += tests
    
    print("\n" + "=" * 60)
    print(f"🎉 测试完成！成功 {total_success}/{total_tests} 项测试")
    
    if total_success == total_tests:
        print("✅ 所有测试通过！")
        print("✅ matplotlib图表集成功能正常")
        print("✅ 三种图表类型都能正确绘制")
        print("✅ 图表保存功能正常")
        print("\n🎯 集成状态: 完全正常 ✨")
    elif total_success >= total_tests * 0.75:
        print("⚠️ 大部分测试通过，有少量问题")
        print("🎯 集成状态: 基本正常，建议优化")
    else:
        print("❌ 多项测试失败，需要修复")
        print("🎯 集成状态: 需要修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
