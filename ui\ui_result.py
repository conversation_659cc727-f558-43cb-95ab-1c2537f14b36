# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'result.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QGroupBox, QHBoxLayout, QMainWindow,
    QPlainTextEdit, QPushButton, QSizePolicy, QStatusBar,
    QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(938, 657)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.groupBox_6 = QGroupBox(self.centralwidget)
        self.groupBox_6.setObjectName(u"groupBox_6")
        self.verticalLayout_6 = QVBoxLayout(self.groupBox_6)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.calc_all_check = QPlainTextEdit(self.groupBox_6)
        self.calc_all_check.setObjectName(u"calc_all_check")
        self.calc_all_check.setReadOnly(True)

        self.verticalLayout_6.addWidget(self.calc_all_check)


        self.verticalLayout.addWidget(self.groupBox_6)

        self.groupBox_5 = QGroupBox(self.centralwidget)
        self.groupBox_5.setObjectName(u"groupBox_5")
        self.verticalLayout_5 = QVBoxLayout(self.groupBox_5)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.calc_text = QPlainTextEdit(self.groupBox_5)
        self.calc_text.setObjectName(u"calc_text")
        self.calc_text.setReadOnly(True)

        self.verticalLayout_5.addWidget(self.calc_text)


        self.verticalLayout.addWidget(self.groupBox_5)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.push_finish = QPushButton(self.centralwidget)
        self.push_finish.setObjectName(u"push_finish")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.push_finish.sizePolicy().hasHeightForWidth())
        self.push_finish.setSizePolicy(sizePolicy)
        self.push_finish.setMinimumSize(QSize(200, 65))
        font = QFont()
        font.setFamilies([u"\u5b8b\u4f53"])
        font.setPointSize(20)
        self.push_finish.setFont(font)

        self.horizontalLayout.addWidget(self.push_finish)

        self.push_constrainui = QPushButton(self.centralwidget)
        self.push_constrainui.setObjectName(u"push_constrainui")
        sizePolicy.setHeightForWidth(self.push_constrainui.sizePolicy().hasHeightForWidth())
        self.push_constrainui.setSizePolicy(sizePolicy)
        self.push_constrainui.setMinimumSize(QSize(250, 65))
        self.push_constrainui.setFont(font)

        self.horizontalLayout.addWidget(self.push_constrainui)

        self.push_postui = QPushButton(self.centralwidget)
        self.push_postui.setObjectName(u"push_postui")
        sizePolicy.setHeightForWidth(self.push_postui.sizePolicy().hasHeightForWidth())
        self.push_postui.setSizePolicy(sizePolicy)
        self.push_postui.setMinimumSize(QSize(250, 65))
        self.push_postui.setFont(font)

        self.horizontalLayout.addWidget(self.push_postui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font)

        self.horizontalLayout.addWidget(self.push_mainui)


        self.verticalLayout.addLayout(self.horizontalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.groupBox_6.setTitle(QCoreApplication.translate("MainWindow", u"\u6574\u4f53\u8bbe\u7f6e", None))
        self.groupBox_5.setTitle(QCoreApplication.translate("MainWindow", u"\u8ba1\u7b97\u8fc7\u7a0b\u663e\u793a", None))
        self.push_finish.setText(QCoreApplication.translate("MainWindow", u"\u5f00\u59cb\u8ba1\u7b97", None))
        self.push_constrainui.setText(QCoreApplication.translate("MainWindow", u"上一步(网格无关性验证)", None))
        self.push_postui.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u540e\u5904\u7406)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

