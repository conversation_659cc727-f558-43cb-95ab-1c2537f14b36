# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'help.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (Q<PERSON>rush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QHeaderView, QLineEdit,
    QMainWindow, QPushButton, QSizePolicy, QSplitter,
    QStatusBar, QTextBrowser, QTreeWidget, QTreeWidgetItem,
    QVBoxLayout, QWidget)

class Ui_HelpWindow(object):
    def setupUi(self, HelpWindow):
        if not HelpWindow.objectName():
            HelpWindow.setObjectName(u"HelpWindow")
        HelpWindow.resize(800, 600)
        self.centralwidget = QWidget(HelpWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.searchLineEdit = QLineEdit(self.centralwidget)
        self.searchLineEdit.setObjectName(u"searchLineEdit")

        self.horizontalLayout.addWidget(self.searchLineEdit)

        self.searchButton = QPushButton(self.centralwidget)
        self.searchButton.setObjectName(u"searchButton")

        self.horizontalLayout.addWidget(self.searchButton)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.splitter = QSplitter(self.centralwidget)
        self.splitter.setObjectName(u"splitter")
        self.splitter.setOrientation(Qt.Horizontal)
        self.topicsTreeWidget = QTreeWidget(self.splitter)
        self.topicsTreeWidget.setObjectName(u"topicsTreeWidget")
        self.topicsTreeWidget.setMaximumSize(QSize(250, 16777215))
        self.splitter.addWidget(self.topicsTreeWidget)
        self.contentBrowser = QTextBrowser(self.splitter)
        self.contentBrowser.setObjectName(u"contentBrowser")
        self.splitter.addWidget(self.contentBrowser)

        self.verticalLayout.addWidget(self.splitter)

        HelpWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(HelpWindow)
        self.statusbar.setObjectName(u"statusbar")
        HelpWindow.setStatusBar(self.statusbar)

        self.retranslateUi(HelpWindow)

        QMetaObject.connectSlotsByName(HelpWindow)
    # setupUi

    def retranslateUi(self, HelpWindow):
        HelpWindow.setWindowTitle(QCoreApplication.translate("HelpWindow", u"\u5e2e\u52a9\u4e2d\u5fc3", None))
        self.searchLineEdit.setPlaceholderText(QCoreApplication.translate("HelpWindow", u"\u641c\u7d22\u5e2e\u52a9\u4e3b\u9898...", None))
        self.searchButton.setText(QCoreApplication.translate("HelpWindow", u"\u641c\u7d22", None))
        ___qtreewidgetitem = self.topicsTreeWidget.headerItem()
        ___qtreewidgetitem.setText(0, QCoreApplication.translate("HelpWindow", u"\u5e2e\u52a9\u4e3b\u9898", None));
    # retranslateUi

