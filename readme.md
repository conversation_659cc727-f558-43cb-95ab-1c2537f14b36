# 🎯 ANSYS Workbench 振动传递计算软件 v0.2.1

<div align="center">

[![Python](https://img.shields.io/badge/Python-3.12+-3776ab?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![PySide6](https://img.shields.io/badge/PySide6-6.5.0+-41cd52?style=for-the-badge&logo=qt&logoColor=white)](https://wiki.qt.io/Qt_for_Python)
[![ANSYS](https://img.shields.io/badge/ANSYS-2023R2+-ff6b35?style=for-the-badge&logo=ansys&logoColor=white)](https://www.ansys.com/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.111.0-009688?style=for-the-badge&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com/)
[![License](https://img.shields.io/badge/License-MIT-ffd93d?style=for-the-badge)](LICENSE)

<img src="https://www.ansys.com/-/media/ansys/corporate/content/solutions/simulation-technology/mechanical-simulation/mechanical-simulation-workbench-platform-2.jpg" alt="ANSYS Workbench" width="700" style="border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"/>

### 🏆 专业的 ANSYS Workbench 自动化平台 | 振动传递分析专业工具

*基于现代化 Python 技术栈，采用 MVC 架构模式，实现 ANSYS Workbench 全流程自动化*

</div>

---

## 📖 项目概述

**ANSYS Workbench 振动传递计算软件** 是一个专业的工程仿真自动化平台，专为振动传递分析而设计。软件通过现代化的 Qt 界面，提供了从项目创建到结果分析的完整 ANSYS Workbench 自动化工作流程。

### 🎯 主要功能

- **🚀 四合一自动化脚本**: 集成清理、创建、规范、导出四大核心功能的ANSYS自动化脚本
- **📊 振动传递分析**: 专业的振动数据处理和频域分析工具，支持FFT和1/3倍频程分析
- **🎨 现代化界面**: 基于 PySide6 的响应式设计，支持实时进度监控和中文编码处理
- **⚡ 智能批处理**: 无人值守的 ANSYS 脚本执行，支持实时日志监控和进度跟踪
- **🌐 API 集成**: FastAPI 驱动的 RESTful API，支持外部系统集成
- **📍 监控点管理**: 完整的监控点管理系统，支持JSON格式导入导出和自动清理机制

### 🌟 最新改进 (v0.2.1)

- **✨ ANSYS四合一自动化脚本**: 全新的四合一自动化脚本，支持命名选择清理、创建、规范化和面选择导出
- **🔄 实时进度监控系统**: 智能进度跟踪，7步骤可视化ANSYS脚本执行流程，支持关键词匹配
- **🎨 编码问题完全修复**: 统一UTF-8编码处理，完美支持中文日志显示和实时监控
- **🔧 多编码兼容性**: 支持UTF-8、GBK、CP936多种编码自动检测和转换
- **🧵 增强线程安全**: 改进多线程日志监控，确保UI响应性和数据一致性
- **📍 监控点管理系统**: 完整的监控点管理界面，支持文件导入、验证和自动清理

---

## 🚀 ANSYS四合一自动化脚本 (v0.2.1 核心功能)

<div align="center">

### 📋 智能化 ANSYS Workbench 自动化处理

</div>

<details>
<summary><b>🎯 四合一自动化功能</b> (点击展开)</summary>
<br>

- **🧹 任务1: 清理数字命名选择**
  - 自动扫描并清理数字命名的选择
  - 智能识别需要清理的命名模式
  - 保护重要的命名选择不被误删

- **🔧 任务2: 创建/更新命名选择**
  - 根据几何体名称自动创建命名选择
  - 支持ROTOR等关键组件的智能识别
  - 自动处理重复命名和版本更新

- **📝 任务3: 统一命名规范**
  - 将所有命名选择统一为小写格式
  - 智能处理命名冲突和重复问题
  - 保持命名的一致性和规范性

- **📤 任务4: 导出面命名选择**
  - 自动识别并导出指定的面命名选择
  - 生成JSON格式的面选择数据文件
  - 支持多种面类型的批量导出

</details>

### 📊 ANSYS四合一自动化脚本执行流程图

```mermaid
flowchart TD
    A[🚀 开始执行四合一自动化脚本] --> B{🔧 检查ANSYS配置}
    B -->|✅ 配置正确| C[📊 启动进度监控对话框]
    B -->|❌ 配置错误| Z[⚠️ 显示错误信息]

    C --> D[📝 创建临时脚本文件]
    D --> E[🔄 启动ANSYS Workbench进程]
    E --> F[📡 开始实时日志监控]

    F --> G[🧹 任务1: 清理数字命名选择]
    G --> G1[🔍 扫描命名选择列表]
    G1 --> G2[🎯 识别数字命名模式]
    G2 --> G3[🗑️ 安全删除数字命名选择]
    G3 --> H[✅ 任务1完成]

    H --> I[🔧 任务2: 创建/更新命名选择]
    I --> I1[🔍 搜索几何体名称]
    I1 --> I2[🎯 匹配ROTOR等关键组件]
    I2 --> I3[➕ 创建新的命名选择]
    I3 --> I4[🗑️ 删除旧版本命名选择]
    I4 --> J[✅ 任务2完成]

    J --> K[📝 任务3: 统一命名规范]
    K --> K1[🔍 分析命名冲突]
    K1 --> K2[🔄 执行重命名操作]
    K2 --> K3[📝 统一为小写格式]
    K3 --> L[✅ 任务3完成]

    L --> M[📤 任务4: 导出面命名选择]
    M --> M1[🔍 扫描面命名选择]
    M1 --> M2[📋 提取面ID和属性]
    M2 --> M3[📄 生成JSON数据文件]
    M3 --> M4[💾 保存到output目录]
    M4 --> N[✅ 任务4完成]

    N --> O[🔄 刷新UI树]
    O --> P[🎉 所有任务执行完毕]
    P --> Q[🧹 清理临时文件]
    Q --> R[📊 更新进度状态]
    R --> S[📋 显示执行结果]

    %% 并行监控流程
    F --> T[🧵 LogMonitorThread启动]
    T --> U[📖 实时读取日志文件]
    U --> V[🌐 多编码格式检测]
    V --> W[🔍 关键词匹配]
    W --> X[📊 更新进度条]
    X --> Y[📝 显示中文日志]
    Y --> U

    %% 错误处理
    G --> |❌ 执行失败| ERROR1[📝 记录错误日志]
    I --> |❌ 执行失败| ERROR2[📝 记录错误日志]
    K --> |❌ 执行失败| ERROR3[📝 记录错误日志]
    M --> |❌ 执行失败| ERROR4[📝 记录错误日志]

    ERROR1 --> RECOVERY[🔧 错误恢复处理]
    ERROR2 --> RECOVERY
    ERROR3 --> RECOVERY
    ERROR4 --> RECOVERY
    RECOVERY --> S

    %% 样式定义
    classDef startEnd fill:#e8f5e8,stroke:#2e7d32,stroke-width:3px
    classDef process fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef task fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef error fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef monitor fill:#e0f2f1,stroke:#00695c,stroke-width:2px

    class A,S startEnd
    class C,D,E,F,O,P,Q,R process
    class B decision
    class G,I,K,M task
    class H,J,L,N success
    class Z,ERROR1,ERROR2,ERROR3,ERROR4,RECOVERY error
    class T,U,V,W,X,Y monitor
```

#### 🔍 流程图说明

**主要执行路径**：
- 🟢 **绿色节点**：开始/结束和成功完成状态
- 🔵 **蓝色节点**：核心处理流程
- 🟠 **橙色节点**：决策判断节点
- 🟣 **紫色节点**：四个主要任务
- 🔴 **红色节点**：错误处理和恢复
- 🟦 **青色节点**：并行监控流程

**关键特性**：
- **并行监控**：LogMonitorThread与主执行流程并行运行，实时监控日志文件
- **智能进度跟踪**：通过关键词匹配自动更新进度状态
- **多编码支持**：自动检测和处理UTF-8、GBK、CP936编码格式
- **错误恢复**：完整的错误处理机制，确保系统稳定性

<details>
<summary><b>📊 实时进度监控系统</b> (点击展开)</summary>
<br>

- **🔄 7步骤进度跟踪**
  1. ⏳ 脚本开始执行
  2. ⏳ 任务1: 清理数字命名选择
  3. ⏳ 任务2: 创建/更新命名选择
  4. ⏳ 任务3: 统一命名规范
  5. ⏳ 任务4: 导出面命名选择
  6. ⏳ UI树已刷新
  7. ⏳ 所有任务执行完毕

- **📝 智能日志监控**
  - 多编码支持（UTF-8、GBK、CP936）
  - 实时关键词匹配和进度更新
  - 自动滚动到最新日志内容
  - 深色主题代码风格显示

- **🎛️ 增强按钮管理**
  - 执行中：❌ 取消 (启用) | ✅ 关闭 (禁用)
  - 完成后：已完成 (禁用) | ✅ 关闭 (启用)
  - 线程安全的状态更新和错误处理

</details>

<details>
<summary><b>🔧 技术实现特点</b> (点击展开)</summary>
<br>

- **🧵 多线程架构**
  - 后台日志监控线程（LogMonitorThread）
  - ANSYS日志文件监控线程
  - 进程输出捕获线程
  - 主线程 UI 更新保证响应性
  - 线程安全的进度状态管理

- **📋 编码处理机制**
  - 统一UTF-8编码标准
  - 多编码自动检测（UTF-8、GBK、CP936）
  - 智能编码转换和错误恢复
  - 中文字符完美显示支持

- **🛡️ 错误处理机制**
  - 完整的异常捕获和处理
  - 编码错误自动恢复
  - 用户友好的错误提示
  - 自动资源清理和恢复

- **📊 监控点管理**
  - JSON格式监控点文件支持
  - 自动清理机制，防止文件堆积
  - 智能文件版本管理
  - 监控点数据验证和格式检查

</details>

---

## 🌟 项目亮点

<table>
<tr>
<td width="50%">

### 🎯 **核心特性**
- 🚀 **四合一自动化脚本** - 集成清理、创建、规范、导出的智能ANSYS脚本
- 🔄 **全流程自动化** - 从前处理到后处理的完整工作流，支持实时监控
- 🖥️ **现代化界面** - 基于 PySide6 的专业 GUI 设计，支持中文编码
- 📊 **实时进度监控** - 7步骤可视化ANSYS脚本执行流程，智能关键词匹配
- 📈 **振动分析引擎** - 集成 FFT 频谱和 1/3 倍频程分析，支持多方向数据
- 🌐 **API 生态系统** - 支持与上游 Electron 应用无缝集成
- 📍 **监控点管理** - 完整的监控点管理系统，支持JSON格式和自动清理

</td>
<td width="50%">

### 🏗️ **技术架构**
- 💉 **依赖注入模式** - 提高代码可测试性和解耦性
- 🔍 **观察者模式** - 优雅的窗口状态管理
- 📚 **MVC 架构** - 清晰的代码组织结构
- 🧵 **线程安全设计** - 多线程 UI 更新和日志监控，支持实时进度跟踪
- 🛡️ **全局异常处理** - 企业级错误处理机制，包含编码错误恢复
- 📝 **集中式日志** - 完善的调试和监控体系，支持多编码格式
- 🔄 **实时监控系统** - 智能关键词匹配和进度更新机制

</td>
</tr>
</table>

### 🏗️ 系统技术架构图

```mermaid
graph TB
    subgraph "🖥️ 用户界面层 (UI Layer)"
        A[主窗口 MainWindow]
        B[前处理界面 PreProcessing]
        C[约束设置界面 ConstrainSettings]
        D[结果分析界面 ResultAnalysis]
        E[项目进度对话框 ProjectProgressDialog]
    end

    subgraph "🎮 控制层 (Controller Layer)"
        F[前处理控制器 PreSlot]
        G[约束控制器 ConstrainSlot]
        H[结果控制器 ResultSlot]
        I[主控制器 MainSlot]
    end

    subgraph "🌐 编码处理层 (Encoding Layer)"
        J[多编码检测器 EncodingDetector]
        K[智能编码转换器 EncodingConverter]
        L[编码错误恢复器 ErrorRecovery]
    end

    subgraph "📊 监控管理层 (Monitoring Layer)"
        M[实时日志监控 LogMonitorThread]
        N[进度跟踪器 ProgressTracker]
        O[关键词匹配器 KeywordMatcher]
        P[文件清理器 FileCleanup]
    end

    subgraph "💼 业务逻辑层 (Business Layer)"
        Q[ANSYS四合一脚本 FourInOneScript]
        R[监控点管理器 MonitorPointManager]
        S[振动分析引擎 VibrationAnalyzer]
        T[配置管理器 ConfigManager]
    end

    subgraph "💾 数据访问层 (Data Layer)"
        U[文件I/O处理器 FileIOHandler]
        V[JSON数据处理器 JSONProcessor]
        W[日志写入器 LogWriter]
        X[配置文件管理 ConfigFileManager]
    end

    subgraph "🔗 集成层 (Integration Layer)"
        Y[ANSYS Workbench API]
        Z[FastAPI服务器 APIServer]
        AA[进程管理器 ProcessManager]
    end

    subgraph "🌍 外部系统 (External Systems)"
        BB[ANSYS Workbench]
        CC[CAD文件系统]
        DD[Electron前端应用]
    end

    %% 连接关系
    A --> F
    B --> F
    C --> G
    D --> H
    E --> M

    F --> Q
    F --> J
    G --> R
    G --> P
    H --> S

    J --> K
    K --> L

    M --> N
    N --> O

    Q --> U
    R --> V
    S --> U
    T --> X

    U --> W
    V --> W

    Y --> BB
    Z --> DD
    AA --> Y

    F --> AA
    G --> AA

    %% 样式定义
    classDef uiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef controlLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef encodingLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef monitorLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef businessLayer fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef dataLayer fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef integrationLayer fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef externalLayer fill:#fafafa,stroke:#424242,stroke-width:2px

    class A,B,C,D,E uiLayer
    class F,G,H,I controlLayer
    class J,K,L encodingLayer
    class M,N,O,P monitorLayer
    class Q,R,S,T businessLayer
    class U,V,W,X dataLayer
    class Y,Z,AA integrationLayer
    class BB,CC,DD externalLayer
```

#### 🏗️ 架构图说明

**分层架构设计**：
- 🔵 **用户界面层**：基于PySide6的现代化GUI界面
- 🟣 **控制层**：MVC架构的控制器，处理用户交互逻辑
- 🟢 **编码处理层**：v0.2.1新增，专门处理多编码兼容性
- 🟠 **监控管理层**：v0.2.1增强，实时监控和进度跟踪
- 🟡 **业务逻辑层**：核心业务功能，包括四合一脚本和分析引擎
- 🟢 **数据访问层**：统一的数据处理和文件I/O操作
- 🟦 **集成层**：与外部系统的接口和API
- ⚪ **外部系统**：ANSYS Workbench、CAD系统等

**关键创新**：
- **编码处理层**：解决中文显示和多编码兼容问题
- **监控管理层**：提供实时进度跟踪和智能文件管理
- **线程安全设计**：确保多线程环境下的稳定性
- **模块化架构**：高内聚低耦合的设计原则

---

## 📚 导航目录

<div align="center">

| 🚀 快速开始 | 🏗️ 架构设计 | 🔧 开发指南 | 📖 参考文档 |
|------------|------------|------------|------------|
| [四合一自动化](#-ansys四合一自动化脚本-v021-核心功能) | [项目结构](#-项目架构蓝图) | [环境设置](#-环境设置与项目迁移) | [API接口](#-api接口) |
| [使用指南](#-使用指南) | [模块化架构](#-模块化架构) | [编码处理](#-编码处理与故障排查) | [帮助系统](#-内嵌帮助系统) |
| [工作流程](#-工作流程) | [核心功能](#-核心功能实现) | [监控点管理](#-监控点管理系统) | [常见问题](#-常见问题解决) |

</div>

---

## 📖 使用指南

### 🚀 快速开始

<details>
<summary><b>1. 启动应用程序</b></summary>
<br>

```bash
# 在项目根目录执行
python qt_new.py
```

- 应用程序将自动启动并显示主窗口
- 首次运行时需要配置 ANSYS Workbench 路径
- 检查日志输出确认所有组件正常加载

</details>

<details>
<summary><b>2. 执行ANSYS四合一自动化脚本</b></summary>
<br>

1. **启动前处理模块**
   - 在主窗口选择 "前处理" 模块
   - 确认ANSYS Workbench路径配置正确
   - 确认项目文件已正确加载

2. **执行自动化脚本**
   - 点击 "执行脚本" 按钮启动四合一自动化
   - 系统将自动执行以下任务：
     - 任务1: 清理数字命名选择
     - 任务2: 创建/更新命名选择
     - 任务3: 统一命名规范
     - 任务4: 导出面命名选择

3. **监控执行进度**
   - 实时查看 7 步骤进度状态
   - 监控详细的中文日志输出
   - 观察关键词匹配和进度更新
   - 等待所有任务执行完成

4. **查看执行结果**
   - 检查生成的JSON面选择文件
   - 验证ANSYS模型中的命名选择
   - 查看详细的执行日志

</details>

<details>
<summary><b>3. 振动分析工作流程</b></summary>
<br>

1. **数据准备**
   - 准备振动数据文件（支持多种格式）
   - 确认数据文件编码和格式正确

2. **启动振动分析**
   - 在主窗口选择 "振动分析" 模块
   - 加载振动数据文件
   - 配置分析参数

3. **查看分析结果**
   - 实时查看频谱分析结果
   - 切换不同方向的数据显示
   - 导出分析结果到 Excel

</details>

### 🔧 配置管理

<details>
<summary><b>ANSYS 路径配置</b></summary>
<br>

- **启动文件路径**: 配置 ANSYS Workbench 的启动程序路径
- **工作目录**: 设置 ANSYS 工作目录，建议使用绝对路径
- **项目文件**: 指定当前工作的 Workbench 项目文件

配置文件位置：`config/settings.json`

</details>

---

## 🔧 编码处理与故障排查

### 📋 编码处理机制

<details>
<summary><b>🌐 多编码支持</b> (点击展开)</summary>
<br>

- **统一编码标准**
  - 系统内部统一使用UTF-8编码
  - 日志文件和配置文件采用UTF-8格式
  - 确保中文字符正确显示和处理

- **多编码兼容性**
  - 自动检测UTF-8、GBK、CP936编码格式
  - 智能编码转换和错误恢复机制
  - 支持ANSYS输出的各种编码格式

- **编码错误处理**
  - 编码错误自动恢复机制
  - 容错模式处理损坏的字符
  - 详细的编码错误日志记录

</details>

### 🔍 故障排查指南

<details>
<summary><b>❌ 中文显示乱码问题</b> (点击展开)</summary>
<br>

**问题现象**: 命令行窗口或日志中中文显示为乱码

**解决方案**:
1. 检查系统区域设置是否为中文
2. 确认ANSYS输出编码格式
3. 运行编码测试脚本诊断问题
4. 查看详细的调试日志

**预防措施**:
- 使用统一的UTF-8编码配置
- 定期检查编码处理机制
- 保持系统编码设置一致性

</details>

<details>
<summary><b>⏳ 进度监控不更新问题</b> (点击展开)</summary>
<br>

**问题现象**: 进度条停滞，关键词匹配失败

**解决方案**:
1. 检查关键词映射是否与脚本输出匹配
2. 验证日志文件编码格式
3. 确认LogMonitorThread正常运行
4. 检查时间戳处理逻辑

**调试方法**:
- 启用详细调试日志
- 使用进度监控测试工具
- 检查关键词匹配算法

</details>

---

## 📍 监控点管理系统

### 🎯 监控点功能特性

<details>
<summary><b>📊 监控点管理界面</b> (点击展开)</summary>
<br>

- **文件导入功能**
  - 支持JSON和TXT格式监控点文件
  - 自动格式验证和数据检查
  - 实时预览导入的监控点数据

- **数据验证机制**
  - 坐标数据格式验证
  - 监控点数量和范围检查
  - 重复数据自动检测和处理

- **自动清理机制**
  - 智能识别监控点文件模式
  - 按时间戳排序，保留最新文件
  - 支持自定义保留数量和时间

</details>

<details>
<summary><b>📁 文件格式支持</b> (点击展开)</summary>
<br>

**JSON格式示例**:
```json
{
  "monitor_points": [
    {
      "name": "Point_1",
      "coordinates": [0.0, 0.0, 0.0],
      "id": 1,
      "created_time": "2025-06-23T00:00:00",
      "source": "user_input"
    }
  ],
  "monitor_points_count": 1,
  "monitor_points_source": "manual"
}
```

**TXT格式示例**:
```
# 监控点坐标文件
# X, Y, Z
0.0, 0.0, 0.0
1.0, 1.0, 1.0
```

</details>

---

## 🔄 工作流程

<details>
<summary><b>1. 前处理阶段</b> (点击展开)</summary>
<br>

- 📥 导入几何模型
- 🧪 设置材料属性
- 🔧 配置面处理参数
- 💾 生成前处理配置文件

</details>

<details>
<summary><b>2. 网格划分阶段</b> (点击展开)</summary>
<br>

- ⚙️ 设置网格参数
- 🔄 自动生成网格
- 🔍 网格质量检查
- 📤 输出网格结果

</details>

<details>
<summary><b>3. 连接设置阶段</b> (点击展开)</summary>
<br>

- 🔗 配置轴承连接
- 🛠️ 设置 Bushing 参数
- 🔍 自动识别连接面
- ✅ 验证连接有效性

</details>

<details>
<summary><b>4. 分析配置阶段</b> (点击展开)</summary>
<br>

- 📊 设置分析类型
- ⚙️ 配置求解参数
- 🔒 定义边界条件
- 💾 生成分析配置

</details>

<details>
<summary><b>5. 约束设置阶段</b> (点击展开)</summary>
<br>

- 🔒 **力约束设置**
  - 加载力文件
  - 验证力文件格式
  - 配置力作用方向
- 🔗 **位移约束设置**
  - 定义固定约束
  - 设置远程位移
- 🔄 **旋转设置**
  - 配置旋转速度
  - 定义旋转轴向
- 📍 **监测点设置**
  - 添加关键监测点
  - 配置监测参数

</details>

<details>
<summary><b>6. 结果处理阶段</b> (点击展开)</summary>
<br>

- 📊 **结果数据可视化**
  - 自动生成结果图表
  - 支持多种数据展示方式
  - 实时更新结果显示
  - 交互式数据浏览
- 📁 **结果文件管理**
  - 自动清理临时文件
  - 版本化配置文件
  - 结果文件组织
  - 历史记录追踪
- 🔄 **计算流程控制**
  - 前置步骤验证
  - 进度状态跟踪
  - 错误处理机制
  - 资源清理机制
- 📤 **数据导出功能**
  - 支持多种导出格式
  - 批量数据导出
  - 自定义导出内容
  - 导出历史记录

</details>

<details>
<summary><b>7. 振动分析阶段</b> (点击展开)</summary>
<br>

- 📈 **振动数据处理**
  - 时域数据加载与预处理
  - FFT频谱分析
  - 1/3倍频程分析
  - 总振动加速度级计算
- 🔄 **多方向分析**
  - X、Y、Z三个方向的振动数据分析
  - 色彩编码标识不同振动级别
  - 方向切换的实时更新
- 📊 **频段范围选择**
  - **低频段 (10-315 Hz)**：16个1/3倍频程频段（默认）
  - **总频段 (10-10k Hz)**：31个1/3倍频程频段
  - 实时频段切换，支持所有数据模式
- 🎨 **可视化呈现**
  - 频谱图与条形图展示
  - 数据表格视图
  - 交互式图表工具
  - 数据模式标识（组合模式/电机数据）
- 📤 **智能数据导出**
  - 导出到Excel格式
  - 多轴向数据汇总
  - 组合数据详细信息
  - 频段信息完整记录

</details>

---

## 💻 技术架构总览

<div align="center">

### 🎨 技术栈矩阵

</div>

<table>
<tr>
<th width="20%">🏗️ 架构层次</th>
<th width="25%">🔧 核心技术</th>
<th width="30%">✨ 关键特性</th>
<th width="25%">🎯 应用场景</th>
</tr>
<tr>
<td><strong>🖥️ 表示层</strong></td>
<td>PySide6 + Qt Designer</td>
<td>现代化UI、信号槽机制、响应式布局</td>
<td>用户交互、数据展示</td>
</tr>
<tr>
<td><strong>🎮 控制层</strong></td>
<td>MVC模式 + 观察者模式</td>
<td>依赖注入、事件驱动、状态管理</td>
<td>业务逻辑、流程控制</td>
</tr>
<tr>
<td><strong>🔧 业务层</strong></td>
<td>NumPy + SciPy + Matplotlib</td>
<td>FFT分析、频谱处理、数据可视化</td>
<td>振动分析、结果计算</td>
</tr>
<tr>
<td><strong>🌐 接口层</strong></td>
<td>FastAPI + Uvicorn</td>
<td>RESTful API、异步处理、参数验证</td>
<td>外部集成、实时通信</td>
</tr>
<tr>
<td><strong>💾 数据层</strong></td>
<td>JSON + 配置管理</td>
<td>版本化存储、自动备份、数据验证</td>
<td>配置持久化、数据管理</td>
</tr>
<tr>
<td><strong>🔗 集成层</strong></td>
<td>ANSYS Workbench API</td>
<td>批处理自动化、脚本生成、结果提取</td>
<td>CAE仿真、自动化分析</td>
</tr>
<tr>
<td><strong>🌐 编码处理层</strong></td>
<td>多编码支持 + 智能转换</td>
<td>UTF-8/GBK/CP936兼容、错误恢复、实时监控</td>
<td>中文日志处理、编码转换</td>
</tr>
<tr>
<td><strong>📊 监控管理层</strong></td>
<td>实时监控 + 进度跟踪</td>
<td>关键词匹配、进度更新、文件清理</td>
<td>脚本执行监控、状态管理</td>
</tr>
</table>

### 🛠️ 开发环境要求

<div align="center">

| 🐍 **Python环境** | 🖥️ **系统要求** | 🔧 **开发工具** |
|:---:|:---:|:---:|
| Python 3.12+ | Windows 10/11 | Qt Designer |
| pip 23.0+ | 8GB+ RAM | VS Code/PyCharm |
| venv/conda | 2GB+ 磁盘空间 | Git 2.30+ |

</div>

### 📦 核心依赖清单

```yaml
🎨 界面框架:
  PySide6: ">=6.5.0"     # 现代化GUI框架

🔢 科学计算:
  numpy: ">=1.22.0"      # 数值计算基础
  scipy: ">=1.8.0"       # 科学计算工具
  pandas: ">=1.4.0"      # 数据处理分析

📊 数据可视化:
  matplotlib: ">=3.5.0"  # 图表绘制
  seaborn: ">=0.11.0"    # 统计图表

🌐 API服务:
  fastapi: "==0.111.0"   # 高性能API框架
  uvicorn: "==0.29.0"    # ASGI服务器
  pydantic: "==2.7.1"    # 数据验证

🎵 音频处理:
  librosa: ">=0.9.0"     # 音频信号处理

📝 配置管理:
  pyyaml: ">=6.0"        # YAML配置文件
  jsonschema: ">=4.0.0"  # JSON数据验证
```

---

## 🚀 环境设置与项目迁移

### 环境设置

<details>
<summary><b>1. 安装 Python</b></summary>
<br>

```bash
# 下载并安装 Python 3.12 或更高版本
# 确保勾选 "Add Python to PATH" 选项
```
- 验证安装: `python --version`
- **注意**: 新版本需要Python 3.12+以支持最新的线程安全特性

</details>

<details>
<summary><b>2. 安装依赖库</b></summary>
<br>

```bash
# 使用 pip 安装依赖
pip install -r requirements.txt

# 或者使用 pyproject.toml (推荐)
pip install -e .

# 核心依赖快速安装
pip install PySide6>=6.5.0 numpy>=1.22.0 scipy>=1.8.0 matplotlib>=3.5.0 pandas>=1.4.0 fastapi==0.111.0 uvicorn==0.29.0
```

**依赖说明**:
- `PySide6`: Qt6 Python 绑定，提供现代化 GUI 框架
- `numpy/scipy`: 科学计算基础库，用于数值分析
- `matplotlib/seaborn`: 数据可视化库，生成专业图表
- `pandas`: 数据处理和分析工具
- `fastapi/uvicorn`: 高性能 API 框架和服务器
- `librosa`: 音频信号处理库，用于振动分析
- `pydantic`: 数据验证和序列化

</details>

<details>
<summary><b>3. 安装 ANSYS Workbench</b></summary>
<br>

- 安装与原项目兼容的 ANSYS Workbench 版本（推荐 2023 R2 或更高版本）
- 确保 ANSYS 许可证正确配置
- 验证安装: 手动启动 ANSYS Workbench 确认可以正常运行

</details>

### 项目迁移步骤

1. **复制项目文件** 📂
   - 将整个项目文件夹复制到新电脑
   - 保持完整的目录结构

2. **配置 ANSYS 路径** ⚙️
   - 首次运行时，使用主界面中的设置选项配置 ANSYS 启动文件路径
   - 设置工作目录（推荐使用绝对路径）

3. **测试运行** ▶️
   ```bash
   # 在项目根目录执行
   python qt_new.py
   ```
   - 验证界面是否正常显示
   - 测试基本功能是否正常工作

### 🔧 开发环境设置

<details>
<summary><b>开发依赖安装</b></summary>
<br>

```bash
# 克隆项目
git clone https://github.com/jacksu666/qtproject.git
cd qtproject

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 开发模式安装
pip install -e .
```

</details>

<details>
<summary><b>开发工具配置</b></summary>
<br>

- **IDE 推荐**: VS Code 或 PyCharm
- **Qt Designer**: 用于 UI 设计和修改
- **调试工具**: 内置日志系统和异常处理
- **代码格式化**: Black + isort (配置在 pyproject.toml)

</details>

---

## 👨‍💻 开发者指南

### 📝 开发注意事项

<details>
<summary><b>代码规范</b></summary>
<br>

- **命名约定**: 使用 snake_case 命名变量和函数，PascalCase 命名类
- **文档字符串**: 所有公共方法必须包含详细的文档字符串
- **类型提示**: 使用 Python 类型提示提高代码可读性
- **异常处理**: 使用自定义异常类，避免裸露的 try-except

**示例**:
```python
def create_new_project(window_manager: WindowManager) -> None:
    """创建新的Workbench项目

    Args:
        window_manager: 窗口管理器实例

    Raises:
        ValidationError: 输入验证失败
        FileOperationError: 文件操作失败
    """
```

</details>

<details>
<summary><b>UI 开发最佳实践</b></summary>
<br>

- **信号槽连接**: 使用 Qt 信号槽机制处理 UI 事件
- **线程安全**: UI 更新必须在主线程中进行
- **样式分离**: 使用 QSS 样式表，避免硬编码样式
- **国际化支持**: 使用 `tr()` 函数包装所有用户可见文本

**UI 更新模式**:
```python
# 正确的线程安全 UI 更新
def update_progress(self, progress_value: int):
    QMetaObject.invokeMethod(
        self.progress_bar,
        "setValue",
        Qt.QueuedConnection,
        Q_ARG(int, progress_value)
    )
```

</details>

<details>
<summary><b>测试策略</b></summary>
<br>

- **单元测试**: 为核心业务逻辑编写单元测试
- **集成测试**: 测试窗口间的交互和数据流
- **UI 测试**: 使用 Qt Test 框架进行 UI 自动化测试
- **性能测试**: 监控内存使用和响应时间

**测试文件组织**:
```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── ui/            # UI 测试
└── fixtures/      # 测试数据
```

</details>

### 🔄 版本控制

<details>
<summary><b>Git 工作流程</b></summary>
<br>

- **分支策略**: 使用 Git Flow 分支模型
- **提交规范**: 遵循 Conventional Commits 规范
- **代码审查**: 所有功能分支必须通过 Pull Request
- **版本标签**: 使用语义化版本号 (SemVer)

**提交消息格式**:
```
feat(ui): add new project creation dialog
fix(threading): resolve button state management issue
docs(readme): update installation instructions
```

</details>

---

## 📂 项目架构蓝图

<div align="center">

### 🏗️ 模块化设计 - 清晰的职责分离

</div>

```
🎯 振动传递计算软件/
├── 🚀 核心启动层
│   ├── qt_new.py                    # 🎯 应用程序主入口 (v0.2.0)
│   ├── qt_original.py               # 📜 原始版本备份
│   └── window_manager.py            # 🎮 窗口管理器 (依赖注入核心)
│
├── 🖥️ 视图表示层 (views/)
│   ├── base_window.py               # 🏗️ 基础窗口类 (观察者模式)
│   ├── main_window.py               # 🏠 主窗口 (配置管理中心)
│   ├── new_project_dialog.py        # 🆕 新建项目对话框 (v0.2.0)
│   ├── project_progress_dialog.py   # 📊 项目进度监控对话框 (v0.2.0)
│   ├── mesh_window.py               # 🕸️ 网格划分界面
│   ├── pre_window.py                # ⚙️ 前处理界面
│   ├── connection_window.py         # 🔗 连接设置界面
│   ├── analysis_window.py           # 📊 分析配置界面
│   ├── constrain_window.py          # 🔒 约束设置界面
│   ├── result_window.py             # 📈 结果展示界面
│   ├── help_dialog.py               # ❓ 帮助文档对话框
│   └── log_viewer.py                # 📝 日志查看器
│
├── 🎮 控制器业务层 (ctrl/)
│   ├── main_slot.py                 # 🎯 主界面控制器
│   ├── new_project_slot.py          # 🆕 新建项目控制器 (v0.2.0)
│   ├── mesh_slot.py                 # 🕸️ 网格控制器
│   ├── pre_slot.py                  # ⚙️ 前处理控制器
│   ├── connection_slot.py           # 🔗 连接控制器
│   ├── analysis_slot.py             # 📊 分析控制器
│   ├── constrain_slot.py            # 🔒 约束控制器
│   ├── result_slot.py               # 📈 结果控制器
│   ├── vibration_analysis.py        # 🎵 振动分析引擎
│   ├── api_server.py                # 🌐 FastAPI 服务器
│   └── startup_config.py            # 🚀 启动配置管理
│
├── 🔧 核心功能层 (core/)
│   ├── __init__.py                  # 📦 模块导出定义
│   ├── config_manager.py            # ⚙️ 配置管理器 (单例模式)
│   ├── exception_handler.py         # 🛡️ 全局异常处理
│   ├── error_dialog.py              # ❌ 错误对话框
│   └── logger_setup.py              # 📝 日志系统配置
│
├── 🎨 界面定义层 (ui/)
│   ├── ui_main.py                   # 🏠 主界面UI定义
│   ├── ui_mesh.py                   # 🕸️ 网格界面UI
│   ├── ui_pre.py                    # ⚙️ 前处理界面UI
│   ├── ui_connection.py             # 🔗 连接界面UI
│   ├── ui_analysis.py               # 📊 分析界面UI
│   ├── ui_constrain.py              # 🔒 约束界面UI
│   └── ui_result.py                 # 📈 结果界面UI
│
├── 🎭 资源素材层
│   ├── assets/                      # 🎨 静态资源
│   │   ├── icons/                   # 🖼️ 图标库
│   │   └── styles/                  # 🎨 样式表
│   ├── help/                        # 📚 帮助文档
│   │   ├── html/                    # 🌐 HTML帮助页面
│   │   └── images/                  # 🖼️ 帮助图片
│   └── script/                      # 📜 ANSYS自动化脚本
│       ├── finalscript_copy.py      # 🔧 主要分析脚本
│       └── versions/                # 📂 脚本版本历史
│
├── 💾 数据存储层
│   ├── config/                      # ⚙️ 配置文件
│   │   └── settings.json            # 🔧 应用程序配置
│   ├── json/                        # 📄 JSON数据文件
│   │   ├── analysis_config_*.json   # 📊 分析配置
│   │   ├── connection_config_*.json # 🔗 连接配置
│   │   └── constrain_config_*.json  # 🔒 约束配置
│   ├── logs/                        # 📝 日志文件目录
│   ├── temp/                        # 🗂️ 临时文件
│   └── output/                      # 📤 计算结果输出
│
└── 📋 项目管理层
    ├── pyproject.toml               # 📦 项目配置 (现代Python标准)
    ├── requirements.txt             # 📋 依赖清单
    ├── setup.py                     # 🔧 安装脚本
    ├── README.md                    # 📖 项目文档
    ├── API_README.md                # 🌐 API接口文档
    └── LICENSE                      # 📄 开源许可证
```

### 🎯 架构设计原则

<table>
<tr>
<td width="25%">

**🏗️ 分层架构**
- 表示层分离
- 业务逻辑独立
- 数据访问抽象

</td>
<td width="25%">

**🔧 设计模式**
- 依赖注入
- 观察者模式
- 单例模式
- MVC架构

</td>
<td width="25%">

**📦 模块化**
- 高内聚低耦合
- 接口标准化
- 功能可插拔

</td>
<td width="25%">

**🛡️ 可靠性**
- 全局异常处理
- 配置验证
- 日志追踪
- 错误恢复

</td>
</tr>
</table>

---

## 🔧 技术实现详情

### 🧵 线程安全设计

<details>
<summary><b>多线程架构</b></summary>
<br>

- **主线程**: 负责 UI 渲染和用户交互
- **日志监控线程**: 后台监控 ANSYS 日志文件变化
- **API 服务线程**: 处理外部 API 请求
- **线程间通信**: 使用 Qt 信号槽机制确保线程安全

**关键实现**:
```python
# 线程安全的 UI 更新
def update_progress(self, log_line: str):
    for keyword, index in self.progress_keywords.items():
        if keyword in log_line:
            self.mark_progress_completed(index)
            break
```

</details>

### 🌐 多编码处理系统

<details>
<summary><b>智能编码处理机制</b></summary>
<br>

- **统一编码标准**: 系统内部统一使用UTF-8编码
- **多编码兼容**: 自动检测UTF-8、GBK、CP936编码格式
- **智能转换**: 编码错误自动恢复和转换机制
- **实时监控**: 支持多编码日志文件的实时读取和显示

**核心实现**:
```python
# 多编码尝试读取
for encoding in ['utf-8', 'gbk', 'cp936']:
    try:
        with open(log_file_path, 'r', encoding=encoding, errors='ignore') as f:
            content = f.read()
            break
    except UnicodeDecodeError:
        continue
else:
    # 使用容错模式
    content = content.decode('utf-8', errors='replace')
```

**进程输出捕获**:
```python
def capture_process_output(process, log_writer):
    """增强的进程输出捕获，支持多编码处理"""
    for line in iter(process.stdout.readline, ''):
        if isinstance(line, bytes):
            for encoding in ['utf-8', 'gbk', 'cp936']:
                try:
                    clean_line = line.decode(encoding)
                    break
                except UnicodeDecodeError:
                    continue
        log_writer.write_line(clean_line.strip())
```

</details>

<details>
<summary><b>实时监控系统</b></summary>
<br>

- **关键词匹配**: 智能关键词匹配算法，支持中文和特殊字符
- **进度跟踪**: 7步骤进度跟踪，实时更新执行状态
- **日志解析**: 自动去除时间戳，提取关键信息
- **错误恢复**: 编码错误自动恢复，确保监控不中断

**关键词匹配实现**:
```python
def update_progress(self, log_line: str):
    # 清理日志行，移除时间戳
    clean_line = log_line.strip()
    if clean_line.startswith('[') and ']' in clean_line:
        clean_line = clean_line.split(']', 1)[1].strip()

    # 关键词匹配
    for keyword, index in self.progress_keywords.items():
        if keyword in clean_line:
            self.mark_progress_completed(index)
            return
```

</details>

### 🎨 现代化 CSS 样式

<details>
<summary><b>扁平化设计实现</b></summary>
<br>

- **设计原则**: 采用 Material Design 和扁平化设计原则
- **颜色系统**: 使用专业的颜色调色板，确保 4.5:1 对比度
- **响应式布局**: 支持不同屏幕尺寸的自适应布局
- **视觉反馈**: 丰富的悬停、点击和状态变化效果

**样式特点**:
- 渐变背景和圆角设计
- 阴影效果增强层次感
- 一致的间距和字体系统
- 无障碍设计支持

</details>

### 🛡️ 错误处理机制

<details>
<summary><b>企业级异常处理</b></summary>
<br>

- **全局异常捕获**: 统一的异常处理和错误报告
- **用户友好提示**: 将技术错误转换为用户可理解的信息
- **日志记录**: 详细的错误日志记录，便于调试
- **资源清理**: 确保异常情况下的资源正确释放

**错误分类**:
- `ValidationError`: 输入验证错误
- `FileOperationError`: 文件操作错误
- `AnsysError`: ANSYS 相关错误
- `AppError`: 应用程序通用错误

</details>

---

## 🏗️ 模块化架构

项目采用MVC（模型-视图-控制器）架构模式，通过模块化设计提高代码的可维护性和可扩展性。

### 架构概述

<details>
<summary><b>视图层 (Views)</b></summary>
<br>

视图层负责用户界面的显示和交互，包含以下组件：

- **BaseWindow**：所有窗口的基类，提供通用功能
  - 窗口关闭事件处理
  - 帮助系统集成
  - 窗口管理器注册

- **专用窗口类**：
  - **MainWindow**：主程序窗口，负责应用程序初始化和配置
  - **MeshWindow**：网格划分窗口，处理网格参数设置
  - **PreWindow**：前处理窗口，处理几何和材料设置
  - **ConnectionWindow**：连接设置窗口，管理模型连接
  - **AnalysisWindow**：分析设置窗口，配置分析参数
  - **ConstrainWindow**：约束设置窗口，管理力和位移约束
  - **ResultWindow**：结果显示窗口，展示计算结果
  - **HelpDialog**：帮助对话框，显示内嵌帮助文档

每个窗口类专注于特定功能，只包含与该窗口相关的UI逻辑。

</details>

<details>
<summary><b>控制器层 (Controllers)</b></summary>
<br>

控制器层连接视图和模型，处理用户输入和业务逻辑：

- **槽函数模块**：
  - **main_slot.py**：处理主窗口事件
  - **mesh_slot.py**：处理网格窗口事件
  - **pre_slot.py**：处理前处理窗口事件
  - **connection_slot.py**：处理连接窗口事件
  - **analysis_slot.py**：处理分析窗口事件
  - **constrain_slot.py**：处理约束窗口事件
  - **result_slot.py**：处理结果窗口事件

- **功能模块**：
  - **vibration_analysis.py**：振动数据分析功能
  - **api_server.py**：API服务器实现
  - **startup_config.py**：启动配置管理

</details>

<details>
<summary><b>模型层 (Models)</b></summary>
<br>

模型层负责数据处理和业务逻辑：

- **核心模块**：
  - **config_manager.py**：配置管理，负责读写配置文件
  - **exception_handler.py**：全局异常处理机制
  - **error_dialog.py**：错误显示对话框
  - **logger_setup.py**：集中式日志系统

- **窗口管理器**：
  - **window_manager.py**：管理所有窗口实例，处理窗口切换

</details>

### 设计模式

项目中应用了多种设计模式，提高代码质量和可维护性：

1. **依赖注入模式**：
   - 窗口管理器 (WindowManager) 注入到各窗口类
   - 提高代码可测试性和解耦性

2. **观察者模式**：
   - 窗口切换通知机制
   - 配置变更通知

3. **MVC模式**：
   - 视图 (views/) - 用户界面
   - 控制器 (ctrl/) - 业务逻辑
   - 模型 (core/) - 数据处理

4. **工厂模式**：
   - 窗口创建和管理

### 模块通信

模块间通过以下机制进行通信：

1. **信号槽机制**：
   - 窗口间事件传递
   - UI更新通知

2. **窗口管理器**：
   - 窗口实例注册和获取
   - 窗口状态管理
   - 窗口切换控制

3. **共享数据**：
   - 配置管理器提供全局配置访问
   - 窗口管理器存储共享数据

4. **API接口**：
   - 与外部系统通信
   - 实时数据更新

### 扩展性设计

模块化架构使系统易于扩展：

1. **添加新窗口**：
   - 创建新的窗口类继承 BaseWindow
   - 实现特定功能
   - 注册到窗口管理器

2. **添加新功能**：
   - 在相应模块中实现新功能
   - 通过信号槽连接到UI

3. **集成新组件**：
   - 创建新的控制器模块
   - 连接到现有系统

---

## 🔍 函数调用关系分析

<div align="center">

### 📊 系统调用链路深度解析

</div>

### 🎯 核心函数重要性排序

<table>
<tr>
<th width="5%">🏆</th>
<th width="25%">🔧 函数名</th>
<th width="20%">📍 位置</th>
<th width="35%">🎯 核心作用</th>
<th width="15%">⭐ 重要性</th>
</tr>
<tr>
<td>1</td>
<td><code>initialize_application()</code></td>
<td>qt_new.py</td>
<td>🚀 应用程序主入口，系统初始化核心</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td>2</td>
<td><code>WindowManager.switch_to()</code></td>
<td>window_manager.py</td>
<td>🎮 窗口切换核心，界面跳转管理</td>
<td>⭐⭐⭐⭐⭐</td>
</tr>
<tr>
<td>3</td>
<td><code>main_slot()</code></td>
<td>ctrl/main_slot.py</td>
<td>🎯 主界面控制器，槽函数连接中心</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td>4</td>
<td><code>run_api_server()</code></td>
<td>ctrl/api_server.py</td>
<td>🌐 API服务器启动，外部接口核心</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td>5</td>
<td><code>ConfigManager.get/set()</code></td>
<td>core/config_manager.py</td>
<td>⚙️ 配置管理，数据持久化核心</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td>6</td>
<td><code>setup_exception_handling()</code></td>
<td>core/exception_handler.py</td>
<td>🛡️ 全局异常处理，系统稳定性保障</td>
<td>⭐⭐⭐⭐</td>
</tr>
<tr>
<td>7</td>
<td><code>update_ui_from_api()</code></td>
<td>views/main_window.py</td>
<td>🔄 API数据更新，实时界面刷新</td>
<td>⭐⭐⭐</td>
</tr>
<tr>
<td>8</td>
<td><code>apply_initial_data_to_windows()</code></td>
<td>qt_new.py</td>
<td>📊 初始数据应用，启动时数据分发</td>
<td>⭐⭐⭐</td>
</tr>
</table>

### 🔄 主要调用流程图

#### 🚀 应用启动调用链
```mermaid
graph TD
    A[__main__] --> B[initialize_application]
    B --> C[setup_logging]
    B --> D[QApplication创建]
    B --> E[setup_exception_handling]
    B --> F[load_initial_data]
    B --> G[WindowManager创建]
    B --> H[创建所有窗口实例]
    B --> I[注册窗口到WindowManager]
    B --> J[初始化槽函数]
    B --> K[启动API服务器]
    B --> L[显示主窗口]
    B --> M[app.exec]
```

#### 🎮 窗口切换调用链
```mermaid
graph TD
    A[用户点击按钮] --> B[Qt信号触发]
    B --> C[to_*_slot函数]
    C --> D[window_manager.switch_to]
    D --> E[隐藏当前窗口]
    E --> F[显示新窗口]
    F --> G[通知观察者]
    G --> H[更新窗口状态]
```

#### 🌐 API服务调用链
```mermaid
graph TD
    A[外部HTTP请求] --> B[FastAPI路由]
    B --> C[参数验证]
    C --> D[update_callback调用]
    D --> E[update_ui_from_api]
    E --> F[更新界面控件]
    F --> G[返回响应]
```

### 🔗 模块依赖关系

#### 📦 紧耦合模块
- **qt_new.py ↔ window_manager.py** - 高度紧耦合，主入口直接管理WindowManager
- **views/* ↔ window_manager.py** - 中度紧耦合，依赖注入模式
- **ctrl/main_slot.py ↔ window_manager.py** - 高度紧耦合，界面跳转核心

#### 🔓 松耦合模块
- **ctrl/api_server.py ↔ views/main_window.py** - 松耦合，回调函数机制
- **core/* 模块** - 松耦合，统一接口服务

### 🎯 架构优势分析

<table>
<tr>
<td width="50%">

#### ✅ **设计优势**
- 🏗️ **清晰的MVC架构** - 视图、控制器、模型分离明确
- 💉 **依赖注入模式** - WindowManager注入提高可测试性
- 👁️ **观察者模式** - 窗口切换通知机制设计良好
- 🔌 **API解耦设计** - 通过回调函数实现松耦合

</td>
<td width="50%">

#### ⚠️ **潜在风险**
- 🎯 **WindowManager过度依赖** - 几乎所有模块都依赖它
- 🔄 **循环导入风险** - views和ctrl模块间可能存在循环导入
- 🛡️ **异常处理复杂性** - 多层异常处理可能导致调试困难
- 📈 **性能瓶颈** - 频繁的窗口切换可能影响性能

</td>
</tr>
</table>

---

## 🔧 核心功能实现

### 🎯 自动化流程控制

---

## 🌐 API接口

振动传递计算软件提供了与上游应用程序通信的API接口，支持两种通信方式：

### 1. 启动时配置文件加载

上游应用在启动Python应用前，将参数写入临时配置文件：

```json
{
  "timeStep": 0.01,
  "endTime": 5.0,
  "forceOutputFolder": "D:/simulation_results/forces",
  "stiffnessCoefficient": 0.05,
  "massCoefficient": 0.01,
  "rotationSpeed": 1500,
  "rotationAxis": "z"
}
```

### 2. 运行时API接口

Python应用运行后，通过FastAPI提供的HTTP接口接收实时参数更新：

- 基础URL: `http://127.0.0.1:8000`
- 参数更新端点: `POST /api/v1/simulation-params`
- 健康检查端点: `GET /health`

详细API文档请参阅 [API_README.md](API_README.md)

---

## 🎨 界面优化

项目的界面设计采用了现代化的UI设计理念，注重用户体验和视觉美感。

### 设计原则

- **简洁美观**：精简布局，突出重点功能
- **一致性**：统一的颜色方案和交互模式
- **响应性**：合理的控件尺寸和间距，提高操作舒适度
- **专业性**：符合工程领域的专业视觉语言

### 主题色彩系统

- **主色**：蓝色系 (#3498db) - 传递专业、稳定的视觉感受
- **强调色**：绿色系 (#2ecc71) - 用于重要操作按钮和成功状态
- **警告色**：红色系 (#e74c3c) - 用于错误提示和警告信息
- **中性色**：灰色系 (#34495e, #f8fafc) - 用于文本和背景

### 样式实现

项目使用QSS样式表实现界面样式定制：

```css
/* 按钮样式示例 */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    min-width: 80px;
    font-weight: 500;
}

/* 分组框样式示例 */
QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
}
```

### 交互设计

- **窗口切换**：平滑的窗口过渡效果
- **状态反馈**：按钮状态变化和悬停效果
- **数据可视化**：直观的图表展示和交互工具
- **动态更新**：数据变化时的实时界面更新

---

## 📚 内嵌帮助系统

### 功能概述

内嵌帮助系统允许用户在应用程序内直接访问帮助文档，无需打开外部浏览器。这提高了用户体验并简化了操作流程。

### 主要特点

- **应用内浏览**：直接在应用程序窗口中查看帮助文档
- **结构化目录**：清晰的分类导航，快速定位所需信息
- **全文搜索**：支持在帮助内容中搜索关键词
- **打印功能**：可直接打印当前查看的帮助页面
- **响应式设计**：适配不同屏幕尺寸的布局
- **快捷键支持**：使用F1键从任何界面快速调出帮助

### 使用方法

1. **调出帮助**：
   - 点击菜单栏中的"帮助"→"关于"
   - 在任何界面按下F1键

2. **导航帮助内容**：
   - 使用左侧目录树快速跳转到不同章节
   - 使用顶部的前进/后退按钮在浏览历史中导航
   - 点击"主页"按钮返回帮助首页

3. **搜索功能**：
   - 在左侧搜索框中输入关键词
   - 按回车键或点击搜索按钮开始搜索
   - 搜索结果将在当前页面中高亮显示

4. **打印帮助**：
   - 导航到需要打印的页面
   - 点击工具栏中的"打印"按钮
   - 在打印对话框中选择打印机和选项

### 技术实现

帮助系统采用双重实现方式，确保在不同环境下都能正常工作：

- **优先使用 QtWebEngine**：提供完整的HTML渲染和JavaScript支持
- **备用方案 QTextBrowser**：在无法使用WebEngine的环境中自动切换

帮助内容使用HTML格式编写，支持富文本、图像和链接，便于维护和更新。

---

## 📝 日志系统

### 功能概述

集中式日志系统提供了统一的日志记录和管理机制，便于问题排查和系统监控。

### 主要特点

- **分级日志**：支持 DEBUG、INFO、WARNING、ERROR、CRITICAL 五个日志级别
- **双重输出**：同时输出到控制台和文件
- **自动轮转**：日志文件按天轮转，自动保留最近7天的日志
- **统一格式**：标准化的日志格式，包含时间戳、日志级别、模块名和行号
- **线程安全**：支持多线程环境下的安全日志记录

### 日志格式

```
2023-11-15 14:30:22 - INFO - [qt_new:123] - 应用程序启动...
2023-11-15 14:30:23 - DEBUG - [config_manager:45] - 配置验证通过
2023-11-15 14:30:25 - WARNING - [api_server:78] - API请求参数无效: timeStep
2023-11-15 14:30:30 - ERROR - [exception_handler:156] - 程序错误: 无法连接到服务器
```

### 使用方法

在代码中使用日志系统：

```python
import logging

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("普通信息")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")

# 记录带异常堆栈的日志
try:
    # 可能抛出异常的代码
    result = 1 / 0
except Exception as e:
    logger.error(f"计算失败: {e}", exc_info=True)
```

## 🚀 最近优化

### 1. 依赖注入模式

替换了原有的单例模式，使用依赖注入模式重构了窗口管理器：

- **解耦合**：减少了组件间的紧耦合
- **可测试性**：提高了代码的可测试性
- **灵活性**：允许更灵活的组件配置和替换
- **透明性**：使依赖关系更加明确和透明

实现方式：

```python
# 窗口类接收窗口管理器作为构造参数
class MainWindow(BaseWindow):
    def __init__(self, window_manager=None, initial_data=None):
        super().__init__(window_manager)
        # 窗口初始化代码...

# 应用程序启动时创建和注入依赖
window_manager = WindowManager()
main_window = MainWindow(window_manager=window_manager)
```

### 2. 集中式日志系统

实现了统一的日志记录和管理机制：

- **日志轮转**：按天轮转日志文件，自动管理日志大小
- **统一配置**：集中配置日志格式和级别
- **上下文信息**：记录详细的上下文信息，便于问题排查
- **异常集成**：与异常处理系统无缝集成

### 3. 启动流程优化

优化了应用程序的启动流程：

- **模块化初始化**：分步骤初始化各个模块
- **错误恢复**：增强了启动过程中的错误处理能力
- **日志记录**：详细记录启动过程中的关键事件
- **资源管理**：改进了资源加载和释放机制

### 4. 代码质量提升

- **命名规范**：统一了变量、函数和类的命名风格
- **注释完善**：添加了详细的文档字符串和注释
- **错误处理**：增强了异常捕获和处理机制
- **代码复用**：提取了通用功能到基类或工具函数

### 5. 日志查看器优化

修复了日志查看器窗口显示和隐藏的问题：

- **窗口管理逻辑改进**：修复了日志查看器关闭后，再次打开时主窗口不隐藏的问题
- **窗口状态一致性**：确保窗口状态在多次操作后保持一致
- **继承机制优化**：利用 `BaseWindow` 的 `closeEvent` 处理，避免重复代码
- **窗口切换参数设置**：调整了 `switch_to` 方法的参数，确保窗口正确显示和隐藏

### 6. 资源管理器增强

改进了资源管理器的临时文件清理功能：

- **参数化过滤**：增加了按文件前缀、后缀过滤的能力
- **排除路径支持**：添加了排除特定文件路径的功能
- **灵活的清理策略**：支持按小时或天数设置文件保留时间
- **增强的日志记录**：详细记录文件清理操作和结果

```python
# 增强的资源文件清理方法
def clean_temp_files(self, max_age_days=7, max_age_hours=None, 
                     prefix=None, suffix=None, exclude_paths=None):
    # 支持按前缀、后缀过滤和排除特定路径
    # 实现文件清理逻辑...
```

这些优化大大提高了应用程序的可靠性和用户体验，特别是在处理日志查看和临时文件管理方面。

---

## ❓ 常见问题解决

<details>
<summary><b>1. ANSYS路径设置问题</b></summary>
<br>

**问题**: 程序无法找到ANSYS Workbench启动文件  
**解决**: 通过主界面的"设置" -> "ANSYS启动文件"菜单重新配置正确的路径

</details>

<details>
<summary><b>2. 界面样式异常</b></summary>
<br>

**问题**: 界面样式显示不正常或部分控件样式缺失  
**解决**: 
- 检查样式表文件是否完整
- 确保QSS语法正确
- 尝试重启应用程序

</details>

<details>
<summary><b>3. API服务器启动失败</b></summary>
<br>

**问题**: API服务器无法启动或端口被占用  
**解决**:
- 检查端口8000是否被其他程序占用
- 确认已安装fastapi和uvicorn依赖
- 查看控制台输出的错误信息

</details>

<details>
<summary><b>4. 振动分析模块加载失败</b></summary>
<br>

**问题**: 振动分析窗口无法加载或分析功能无法使用  
**解决**:
- 确认已安装numpy, scipy, matplotlib和pandas依赖
- 检查数据文件格式是否正确
- 尝试重新安装依赖库

</details>

<details>
<summary><b>5. QKeySequence导入错误</b></summary>
<br>

**问题**: 启动时出现与QKeySequence相关的导入错误  
**解决**:
- 确保从PySide6.QtGui而不是PySide6.QtCore导入QKeySequence
- 检查导入语句是否正确

</details>

---

## ⚠️ 注意事项

- 工作目录不支持中文路径，请使用英文路径
- 确保ANSYS Workbench已正确安装并配置
- 程序运行前需完成ANSYS路径配置
- 首次使用时可能需要管理员权限
- API服务器默认使用8000端口，确保该端口未被占用
- 使用API接口时，确保参数格式正确
- 日志文件保存在logs目录下，按天轮转
- **新功能**: 语言切换功能需要重新生成翻译文件后才能使用
- **新功能**: 线程安全机制自动启用，无需手动配置
- 详细的优化文档请参阅 [docs/optimization_summary.md](docs/optimization_summary.md)

---

## 🎯 项目总结

<div align="center">

### 🎯 技术成就与创新亮点

</div>

<table>
<tr>
<td width="50%">

### 🏆 **核心优势**

#### 🎨 **架构设计**
- ✅ **现代化MVC架构** - 清晰的分层设计
- ✅ **依赖注入模式** - 高可测试性和解耦性
- ✅ **观察者模式** - 优雅的事件驱动机制
- ✅ **模块化设计** - 高内聚低耦合的组件结构

#### 🔧 **技术实现**
- ✅ **全栈Python解决方案** - 统一技术栈
- ✅ **企业级异常处理** - 完善的错误恢复机制
- ✅ **集中式日志系统** - 专业的调试和监控
- ✅ **RESTful API集成** - 现代化的外部接口

#### 🎯 **业务价值**
- ✅ **全流程自动化** - 显著提升工作效率
- ✅ **专业振动分析** - 符合工程标准的分析方法
- ✅ **用户体验优化** - 直观友好的操作界面
- ✅ **可扩展架构** - 支持未来功能扩展

</td>
<td width="50%">

### 📊 **项目指标**

#### 📈 **代码质量**
- 🎯 **代码行数**: ~15,000+ LOC
- 🎯 **模块数量**: 25+ 核心模块
- 🎯 **函数数量**: 200+ 核心函数
- 🎯 **设计模式**: 5+ 种设计模式应用

#### 🔧 **技术栈**
- 🐍 **Python 3.12+** - 现代Python特性
- 🖥️ **PySide6** - 跨平台GUI框架
- 🌐 **FastAPI** - 高性能API框架
- 📊 **NumPy/SciPy** - 科学计算基础
- 🎨 **Matplotlib** - 专业数据可视化

#### 🎯 **功能覆盖**
- ⚙️ **前处理模块** - 几何和材料设置
- 🕸️ **网格划分** - 自动化网格生成
- 🔗 **连接设置** - 轴承和Bushing配置
- 📊 **分析配置** - 求解参数设置
- 🔒 **约束管理** - 力和位移约束
- 📈 **结果处理** - 数据可视化和导出
- 🎵 **振动分析** - FFT和频谱分析

</td>
</tr>
</table>

### 🎖️ **项目成就**

<div align="center">

| 🏆 成就类别 | 📊 具体指标 | 🎯 技术亮点 |
|:---:|:---:|:---:|
| **架构设计** | MVC + 依赖注入 | 企业级软件架构模式 |
| **代码质量** | 15,000+ LOC | 高质量、可维护的代码 |
| **用户体验** | 现代化GUI | 专业工程软件界面标准 |
| **技术创新** | API生态系统 | 支持外部系统无缝集成 |
| **工程应用** | 全流程自动化 | 显著提升分析效率 |

</div>

---

## ❓ 常见问题解决

### 🔧 安装和配置问题

<details>
<summary><b>❌ Python依赖安装失败</b></summary>
<br>

**问题**: `pip install` 时出现编译错误或依赖冲突

**解决方案**:
```bash
# 升级pip到最新版本
python -m pip install --upgrade pip

# 使用清华源加速下载
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 如果仍有问题，尝试创建新的虚拟环境
python -m venv fresh_env
fresh_env\Scripts\activate
pip install -r requirements.txt
```

</details>

<details>
<summary><b>⚙️ ANSYS路径配置问题</b></summary>
<br>

**问题**: 找不到ANSYS Workbench启动文件

**解决方案**:
1. 确认ANSYS已正确安装
2. 找到RunWB2.exe文件位置，通常在：
   ```
   C:\Program Files\ANSYS Inc\v232\Framework\bin\Win64\RunWB2.exe
   ```
3. 在主界面设置中配置正确的路径
4. 确保ANSYS许可证服务正常运行

</details>

### 🌐 编码和显示问题

<details>
<summary><b>🔤 中文显示乱码</b></summary>
<br>

**问题**: 日志或界面中中文显示为乱码

**解决方案**:
1. **检查系统编码设置**:
   - Windows: 控制面板 → 区域 → 管理 → 更改系统区域设置
   - 确保设置为"中文(简体，中国)"

2. **命令行编码设置**:
   ```cmd
   chcp 65001
   ```

3. **应用程序编码修复**:
   - 系统已内置多编码支持
   - 自动检测UTF-8、GBK、CP936格式
   - 如仍有问题，请查看详细日志

</details>

<details>
<summary><b>📊 进度监控不更新</b></summary>
<br>

**问题**: ANSYS脚本执行时进度条不更新

**解决方案**:
1. **检查关键词匹配**:
   - 确认脚本输出包含预期的关键词
   - 查看日志文件确认内容格式

2. **编码问题排查**:
   - 检查日志文件编码格式
   - 确认中文字符正确显示

3. **重启监控**:
   - 关闭进度对话框重新执行
   - 检查LogMonitorThread是否正常运行

</details>

### 🚀 性能和稳定性问题

<details>
<summary><b>💾 内存占用过高</b></summary>
<br>

**问题**: 长时间运行后内存占用持续增长

**解决方案**:
1. **定期清理临时文件**:
   - 系统自动清理机制已启用
   - 手动清理temp/目录下的临时文件

2. **重启应用程序**:
   - 长时间运行后建议重启应用
   - 确保所有资源正确释放

3. **监控资源使用**:
   - 使用任务管理器监控内存使用
   - 如发现异常，请查看错误日志

</details>

<details>
<summary><b>🔄 界面响应缓慢</b></summary>
<br>

**问题**: 界面操作响应缓慢或卡顿

**解决方案**:
1. **检查后台任务**:
   - 确认ANSYS脚本是否在执行
   - 检查日志监控线程状态

2. **系统资源检查**:
   - 确保系统有足够的可用内存
   - 关闭不必要的其他应用程序

3. **重置界面状态**:
   - 重启应用程序
   - 清理配置文件缓存

</details>

### 📁 文件和数据问题

<details>
<summary><b>📄 配置文件损坏</b></summary>
<br>

**问题**: 应用启动时提示配置文件错误

**解决方案**:
1. **备份现有配置**:
   ```bash
   copy config\settings.json config\settings.json.bak
   ```

2. **重置配置文件**:
   - 删除损坏的配置文件
   - 重启应用程序自动生成新配置

3. **手动修复**:
   - 使用文本编辑器检查JSON格式
   - 修复语法错误或缺失字段

</details>

<details>
<summary><b>🗂️ 监控点文件格式错误</b></summary>
<br>

**问题**: 导入监控点文件时格式验证失败

**解决方案**:
1. **检查文件格式**:
   - JSON文件: 确保语法正确，包含必需字段
   - TXT文件: 确保坐标格式为 "X, Y, Z"

2. **使用示例文件**:
   - 参考项目中的示例文件格式
   - 复制示例格式修改数据

3. **编码问题**:
   - 确保文件保存为UTF-8编码
   - 避免使用特殊字符

</details>

### 🔗 API和集成问题

<details>
<summary><b>🌐 API服务启动失败</b></summary>
<br>

**问题**: FastAPI服务无法启动或端口被占用

**解决方案**:
1. **检查端口占用**:
   ```cmd
   netstat -ano | findstr :8000
   ```

2. **更改端口配置**:
   - 修改配置文件中的端口设置
   - 使用其他可用端口

3. **防火墙设置**:
   - 确保防火墙允许应用程序网络访问
   - 添加端口例外规则

</details>

---

## 👥 贡献指南

<div align="center">

### 🤝 欢迎加入我们的开发团队！

</div>

#### 🚀 **快速开始贡献**

1. **🍴 Fork 项目**
   ```bash
   git clone https://github.com/yourusername/vibration_transfer.git
   cd vibration_transfer
   ```

2. **🌿 创建功能分支**
   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **💻 开发和测试**
   ```bash
   # 安装开发依赖
   pip install -r requirements.txt

   # 运行测试
   python -m pytest tests/

   # 代码格式化
   black . && isort .
   ```

4. **📝 提交更改**
   ```bash
   git commit -m "✨ Add amazing feature"
   git push origin feature/amazing-feature
   ```

5. **🔄 创建 Pull Request**

#### 🎯 **贡献领域**

<table>
<tr>
<td width="25%">

**🐛 Bug修复**
- 问题排查
- 性能优化
- 兼容性改进

</td>
<td width="25%">

**✨ 新功能**
- 模块扩展
- 算法优化
- 界面改进

</td>
<td width="25%">

**📚 文档完善**
- API文档
- 用户手册
- 代码注释

</td>
<td width="25%">

**🧪 测试增强**
- 单元测试
- 集成测试
- 性能测试

</td>
</tr>
</table>

---

## 🎵 振动分析功能详解

### 📊 频段范围配置

<div align="center">

| 🎯 **频段类型** | 📏 **频率范围** | 🔢 **频段数量** | 🎚️ **默认设置** |
|:---:|:---:|:---:|:---:|
| 低频段 | 10-315 Hz | 16个1/3倍频程 | ✅ 默认选择 |
| 总频段 | 10-10k Hz | 31个1/3倍频程 | 可选切换 |

</div>

### 🔄 多数据源支持

<table>
<tr>
<th width="25%">📁 数据类型</th>
<th width="25%">📊 支持方向</th>
<th width="25%">📋 文件格式</th>
<th width="25%">🎯 应用场景</th>
</tr>
<tr>
<td><strong>流体激振力数据</strong></td>
<td>X、Y、Z 三方向</td>
<td>.txt 文本文件</td>
<td>流体诱发振动分析</td>
</tr>
<tr>
<td><strong>电机数据</strong></td>
<td>Z 方向（主要）</td>
<td>.xlsx/.xls Excel文件</td>
<td>电机振动分析</td>
</tr>
<tr>
<td><strong>组合数据</strong></td>
<td>Z 方向组合，X/Y为0</td>
<td>自动处理</td>
<td>综合振动评估</td>
</tr>
</table>

### 🧮 数据组合算法

**对数加法公式**：
```
L_total = 10 × lg(10^(L_a1/10) + 10^(L_a2/10))
```

- **L_a1**：流体激振力总振动级
- **L_a2**：电机数据总振动级
- **L_total**：组合后总振动级

### 🎨 智能界面特性

- **🏷️ 模式标识**：图表标题自动显示当前数据模式
- **🔄 实时切换**：频段范围实时切换，所有数据模式同步更新
- **📊 状态同步**：清除数据时自动切换到相应模式
- **📤 智能导出**：导出数据包含完整的模式和频段信息

### 📈 分析工作流

1. **📂 数据加载**
   - 选择流体数据文件（可选）
   - 选择电机数据文件（可选）
   - 支持单独或组合加载

2. **⚙️ 参数配置**
   - 选择频段范围（低频段/总频段）
   - 选择显示方向（Z/X/Y）
   - 系统自动检测数据模式

3. **🔬 数据分析**
   - 自动执行FFT变换
   - 计算1/3倍频程分析
   - 应用组合算法（如适用）

4. **📊 结果展示**
   - 时域信号图
   - 频谱分析图
   - 1/3倍频程条形图
   - 数据表格显示

5. **📤 结果导出**
   - Excel格式导出
   - 包含所有分析数据
   - 详细的模式和频段信息

---

## 📋 更新日志

### v0.2.1 (2025-06-23)

#### 🚀 新增功能
- **ANSYS四合一自动化脚本**: 集成清理、创建、规范、导出四大核心功能
- **多编码处理系统**: 完整的UTF-8、GBK、CP936编码兼容性
- **监控点管理系统**: 支持JSON和TXT格式的监控点文件管理
- **实时进度监控**: 7步骤可视化进度跟踪，智能关键词匹配
- **自动文件清理**: 智能监控点文件清理机制

#### 🔧 技术改进
- **编码处理优化**: 统一UTF-8编码标准，智能编码转换
- **线程安全增强**: 改进多线程日志监控和UI更新机制
- **错误恢复机制**: 编码错误自动恢复，确保系统稳定性
- **进程输出捕获**: 增强ANSYS进程输出捕获和处理能力

#### 🐛 问题修复
- 修复中文日志显示乱码问题
- 解决进度监控关键词匹配失败问题
- 修复监控点文件导入验证问题
- 改善命令行编码显示问题

#### 📚 文档更新
- 全面更新README文档，反映最新功能特性
- 新增编码处理技术指南
- 更新故障排查指南，包含编码问题解决方案
- 完善监控点管理使用说明

### v0.2.0 (2025-06-20)

#### 🚀 新增功能
- **现代化项目创建对话框**: 支持CAD文件导入和项目配置
- **实时进度监控**: 8步骤可视化项目创建流程
- **GBK编码支持**: 完美支持中文日志文件的实时监控
- **线程安全架构**: 改进多线程处理，确保UI响应性

#### 🎨 界面改进
- 采用扁平化设计原则，提升用户体验
- 现代化UI设计，支持深色主题
- 改进按钮状态管理和用户交互

#### 🔧 技术优化
- 完整的异常捕获和处理机制
- 自动资源清理和恢复
- 线程安全的状态更新

---

## 📄 许可证

<div align="center">

**本项目采用 MIT 许可证**

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](LICENSE)

*详情请参阅 [LICENSE](LICENSE) 文件*

---

### 🌟 如果这个项目对您有帮助，请给我们一个 Star！

**感谢您的支持与贡献！** 🙏

</div>

<div align="center">
  <p>© 2023-2024 ANSYS Workbench 自动化项目团队</p>
  <p>
    <a href="https://github.com/yourusername/your-repo">GitHub</a> •
    <a href="https://www.ansys.com/">ANSYS</a> •
    <a href="https://wiki.qt.io/Qt_for_Python">PySide6</a>
  </p>
</div>