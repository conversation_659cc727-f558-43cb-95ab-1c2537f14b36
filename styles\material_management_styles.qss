/* 材料管理界面样式表 */

/* ===== 全局样式 ===== */
QWidget {
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* ===== 选项卡样式 ===== */
QTabWidget::pane {
    border: 1px solid #d0d0d0;
    border-radius: 8px;
    background-color: #fafafa;
    margin-top: -1px;
}

QTabWidget::tab-bar {
    alignment: left;
}

QTabBar::tab {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f0f0f0, stop: 1 #e0e0e0);
    border: 1px solid #c0c0c0;
    border-bottom-color: #d0d0d0;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    min-width: 120px;
    padding: 8px 16px;
    margin-right: 2px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ffffff, stop: 1 #f8f8f8);
    border-color: #0078d4;
    border-bottom-color: #fafafa;
    color: #0078d4;
    font-weight: 600;
}

QTabBar::tab:hover:!selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f8f8f8, stop: 1 #e8e8e8);
    border-color: #0078d4;
}

/* ===== 材料搜索框样式 ===== */
QLineEdit#lineEdit_material_search {
    border: 2px solid #e0e0e0;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 10pt;
    background-color: #ffffff;
    selection-background-color: #0078d4;
}

QLineEdit#lineEdit_material_search:focus {
    border-color: #0078d4;
    background-color: #ffffff;
    /* Qt不支持box-shadow，使用边框效果替代 */
    border-width: 3px;
}

QLineEdit#lineEdit_material_search:hover {
    border-color: #106ebe;
}

/* ===== 材料库树形视图样式 ===== */
QTreeWidget#treeWidget_material_library {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    selection-background-color: #e3f2fd;
    selection-color: #1976d2;
    outline: none;
    font-size: 9pt;
}

QTreeWidget#treeWidget_material_library::item {
    padding: 6px 8px;
    border: none;
    border-bottom: 1px solid #f0f0f0;
}

QTreeWidget#treeWidget_material_library::item:selected {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #e3f2fd, stop: 1 #bbdefb);
    color: #1976d2;
    font-weight: 500;
}

QTreeWidget#treeWidget_material_library::item:hover {
    background-color: #f5f5f5;
}

QTreeWidget#treeWidget_material_library::branch:has-children:!has-siblings:closed,
QTreeWidget#treeWidget_material_library::branch:closed:has-children:has-siblings {
    border-image: none;
    image: url(:/icons/branch-closed.png);
}

QTreeWidget#treeWidget_material_library::branch:open:has-children:!has-siblings,
QTreeWidget#treeWidget_material_library::branch:open:has-children:has-siblings {
    border-image: none;
    image: url(:/icons/branch-open.png);
}

/* ===== 分组框样式 ===== */
QGroupBox {
    font-weight: 600;
    font-size: 10pt;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    margin-top: 12px;
    padding-top: 8px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top left;
    padding: 4px 12px;
    background-color: #0078d4;
    color: white;
    border-radius: 6px;
    margin-left: 10px;
    font-weight: 600;
}

QGroupBox#groupBox_material_properties {
    border-color: #4caf50;
}

QGroupBox#groupBox_material_properties::title {
    background-color: #4caf50;
}

QGroupBox#groupBox_material_assignment {
    border-color: #ff9800;
}

QGroupBox#groupBox_material_assignment::title {
    background-color: #ff9800;
}

/* ===== 表单标签样式 ===== */
QLabel {
    color: #333333;
    font-weight: 500;
    padding: 2px 0;
}

/* ===== 输入控件样式 ===== */
QLineEdit, QDoubleSpinBox, QComboBox {
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 9pt;
    background-color: #ffffff;
    selection-background-color: #0078d4;
}

QLineEdit:focus, QDoubleSpinBox:focus, QComboBox:focus {
    border-color: #0078d4;
    background-color: #ffffff;
}

QLineEdit:hover, QDoubleSpinBox:hover, QComboBox:hover {
    border-color: #106ebe;
}

QLineEdit:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {
    background-color: #f5f5f5;
    color: #999999;
    border-color: #d0d0d0;
}

/* ===== 数值输入框特殊样式 ===== */
QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
    width: 20px;
    border: none;
    background-color: #f0f0f0;
}

QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #e0e0e0;
}

QDoubleSpinBox::up-arrow {
    image: url(:/icons/arrow-up.png);
    width: 8px;
    height: 8px;
}

QDoubleSpinBox::down-arrow {
    image: url(:/icons/arrow-down.png);
    width: 8px;
    height: 8px;
}

/* ===== 下拉框样式 ===== */
QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left: 1px solid #e0e0e0;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    background-color: #f8f8f8;
}

QComboBox::down-arrow {
    image: url(:/icons/arrow-down.png);
    width: 8px;
    height: 8px;
}

QComboBox QAbstractItemView {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #ffffff;
    selection-background-color: #e3f2fd;
    outline: none;
}

/* ===== 按钮样式 ===== */
QPushButton {
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    font-size: 9pt;
    min-height: 20px;
}

/* 主要按钮样式 */
QPushButton#pushButton_material_new {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #4caf50, stop: 1 #45a049);
    color: white;
}

QPushButton#pushButton_material_new:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #45a049, stop: 1 #3d8b40);
}

QPushButton#pushButton_material_new:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #3d8b40, stop: 1 #357a38);
}

/* 复制按钮样式 */
QPushButton#pushButton_material_copy {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #2196f3, stop: 1 #1976d2);
    color: white;
}

QPushButton#pushButton_material_copy:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1976d2, stop: 1 #1565c0);
}

QPushButton#pushButton_material_copy:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #1565c0, stop: 1 #0d47a1);
}

/* 删除按钮样式 */
QPushButton#pushButton_material_delete {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f44336, stop: 1 #d32f2f);
    color: white;
}

QPushButton#pushButton_material_delete:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #d32f2f, stop: 1 #c62828);
}

QPushButton#pushButton_material_delete:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #c62828, stop: 1 #b71c1c);
}

/* 分配按钮样式 */
QPushButton#pushButton_assign_material, QPushButton#pushButton_apply_all {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ff9800, stop: 1 #f57c00);
    color: white;
}

QPushButton#pushButton_assign_material:hover, QPushButton#pushButton_apply_all:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #f57c00, stop: 1 #ef6c00);
}

QPushButton#pushButton_assign_material:pressed, QPushButton#pushButton_apply_all:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #ef6c00, stop: 1 #e65100);
}

/* 禁用状态 */
QPushButton:disabled {
    background-color: #e0e0e0;
    color: #999999;
}

/* ===== 导航按钮样式 ===== */
QPushButton#push_finish, QPushButton#push_meshui, QPushButton#push_mainui {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #0078d4, stop: 1 #106ebe);
    color: white;
    border-radius: 8px;
    font-weight: 600;
    font-size: 12pt;
}

QPushButton#push_finish:hover, QPushButton#push_meshui:hover, QPushButton#push_mainui:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #106ebe, stop: 1 #005a9e);
}

QPushButton#push_finish:pressed, QPushButton#push_meshui:pressed, QPushButton#push_mainui:pressed {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #005a9e, stop: 1 #004578);
}

/* ===== 滚动条样式 ===== */
QScrollBar:vertical {
    background-color: #f0f0f0;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #c0c0c0;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #a0a0a0;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* ===== 工具提示样式 ===== */
QToolTip {
    background-color: #333333;
    color: white;
    border: 1px solid #555555;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 9pt;
}
