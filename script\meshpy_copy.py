# -*- coding: utf-8 -*-
import os
import math
import csv
import logging
import json

# The following objects are assumed to be available in the runtime environment
# ExtAPI, Model, DataModel, etc.

class LoggerManager:
    """
    Manages logging configuration and provides a logger instance.
    """
    def __init__(self, target_directory):
        if not os.path.exists(target_directory):
            try:
                os.makedirs(target_directory)
                print("Log directory created: ", target_directory)
            except Exception as e:
                print("Failed to create target directory {}: {}".format(target_directory,e))
                raise
        
        if not os.access(target_directory, os.W_OK):
            raise PermissionError("Target directory is not writable: {}".format(target_directory))
        
        self.log_file_path = os.path.join(target_directory, "ansys_workbench.log")
        try:
            self.logger = self.setup_logger(self.log_file_path)
        except Exception as e:
            print("Error initializing logger: {}".format(e))
            raise

    
    def setup_logger(self, log_file):
        logger = logging.getLogger('AnsysWorkbenchLogger')
        if not logger.handlers:  # 防止重复添加处理器
            logger.setLevel(logging.DEBUG)
            fh = logging.FileHandler(log_file)
            fh.setLevel(logging.DEBUG)
            ch = logging.StreamHandler()
            ch.setLevel(logging.ERROR)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            fh.setFormatter(formatter)
            ch.setFormatter(formatter)
            logger.addHandler(fh)
            logger.addHandler(ch)
        return logger

    
    def get_logger(self):
        return self.logger


class MeshManager:
    """
    Responsible for mesh generation and collecting body IDs.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.Model = ExtAPI.DataModel.Project.Model
        self.ExtAPI = ExtAPI
        self.selection_ALLBODY = None

    def store_all_bodies(self):
        self.selection_ALLBODY = []
        for assembly in self.ExtAPI.DataModel.GeoData.Assemblies:
            for part in assembly.Parts:
                for body in part.Bodies:
                    self.selection_ALLBODY.append(body.Id)
        self.logger.info("All Body IDs stored.")
        return self.selection_ALLBODY

    def generate_allbody_mesh(self, element_size=0.01):
        mesh = self.Model.Mesh
        mesh.ClearGeneratedData()
        for ch in mesh.Children:
            ExtAPI.DataModel.Remove(ch)
        automatic_method = mesh.AddAutomaticMethod()
        selection_info = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
        selection_info.Ids = self.selection_ALLBODY
        automatic_method.Location = selection_info
        sizing = mesh.AddSizing()
        sizing.Location = selection_info
        sizing.ElementSize = Quantity(element_size, "m")
        mesh.GenerateMesh()
        self.logger.info("Else mesh updated successfully.")
    
    def generate_else_mesh(self,named_selections_dict, element_name, element_size):
        mesh = self.Model.Mesh
        selection_info = self.ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
        selection_info.Ids = named_selections_dict.get(element_name, [])
        sizing = mesh.AddSizing()
        sizing.Location = selection_info
        sizing.ElementSize = Quantity(element_size, "m")
        mesh.Update()
        mesh.GenerateMesh()

    def get_mesh_informationandpng(self, file):
        mesh = self.Model.Mesh
        Elements = mesh.Elements
        Nodes = mesh.Nodes
        mesh.Activate()
        setting2d = Ansys.Mechanical.Graphics.GraphicsImageExportSettings()
        Graphics.Camera.SetSpecificViewOrientation(ViewOrientationType.Left)

        Graphics.Camera.SetSpecificViewOrientation(ViewOrientationType.Iso)
        Graphics.Camera.SceneHeight = Quantity(1.5, 'm')
        Graphics.ViewOptions.ShowRuler = False
        Graphics.ViewOptions.ShowTriad = False
        Graphics.ViewOptions.ShowLegend = False

        Graphics.ExportImage(file, GraphicsImageExportFormat.PNG, setting2d)
        Graphics.ViewOptions.ShowRuler = True
        Graphics.ViewOptions.ShowTriad = True
        Graphics.ViewOptions.ShowLegend = True
        return Elements, Nodes

class SelectionManagerWrapper:
    """
    Encapsulates selection operations and named selections retrieval.
    """
    def __init__(self, logger, ExtAPI):
        self.logger = logger
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model

    def get_named_selections_dict(self):
        named_selections_dict = {
            ns.Name: ns.Ids for ns in self.Model.NamedSelections.Children
        }
        self.logger.info("Retrieved all named selections.")
        for name, ids in named_selections_dict.iteritems():
            self.logger.debug("Named Selection ID: %s, Name: %s", ids, name)
        return named_selections_dict

    def create_selection(self, entity_ids, entity_type=SelectionTypeEnum.GeometryEntities):
        selection = self.ExtAPI.SelectionManager.CreateSelectionInfo(entity_type)
        selection.Ids = entity_ids
        return selection


class FileIO:
    """
    Handles file I/O operations, such as reading force files.
    """
    def __init__(self, logger):
        self.logger = logger

    def read_force_file(self, file_name):
        time_list = []
        force_list = []
        if not os.path.exists(file_name):
            self.logger.warning("File does not exist: %s", file_name)
            return time_list, force_list
        try:
            with open(file_name, "r") as file:
                reader = csv.reader(file, delimiter=' ')
                for row in reader:
                    if not row or row[0].startswith(('"', '(')):
                        continue
                    if len(row) >= 3:
                        try:
                            force = float(row[1])
                            t = float(row[2])
                            force_list.append(Quantity("{0} [N]".format(force)))
                            time_list.append(Quantity("{0} [s]".format(t)))
                        except ValueError:
                            self.logger.warning("Invalid data in file: %s", file_name)
            self.logger.info("Read force file: %s", file_name)
            return time_list, force_list
        except Exception as e:
            self.logger.error("Error occurred while reading file %s: %s", file_name, str(e), exc_info=True)
            return time_list, force_list

class MainApp:
    """
    Main application class coordinating all operations.
    """
    def __init__(self, ExtAPI):
        self.ExtAPI = ExtAPI
        self.Model=ExtAPI.DataModel.Project.Model
        try:
            target_directory = r"D:/data/all-XM/autoworkbench/csdaima/cs"
            self.logger = LoggerManager(target_directory).get_logger()
            self.logger.info("Logger initialized successfully.")
        except Exception as e:
            print("Failed to initialize logger: {}".format(e))
            raise

        self.analysis = ExtAPI.DataModel.Project.Model.Analyses[0]
        self.file_io = FileIO(self.logger)
        self.mesh_manager = MeshManager(self.logger, ExtAPI)
        self.selection_mgr = SelectionManagerWrapper(self.logger, ExtAPI)
       
    def run(self):
        target_directory=r"D:/data/all-XM/autoworkbench/csdaima/cs"
        output_directory=r"D:/data/all-XM/autoworkbench/csdaima/cs/temp/mesh_output__20250627_163646"
        current_directory = os.getcwd()
        self.logger.info("Current working directory: %s", current_directory)
        
        try:
            os.chdir(target_directory)
            self.logger.info("Successfully changed to target folder: %s", os.getcwd())
            if not os.path.exists(output_directory):
                    os.makedirs(output_directory)
            self.logger.info("Created output directory: %s", output_directory)
        except OSError as e:
            self.logger.error("Failed to setup directories: %s", str(e))
            raise
        
        # Store bodies and generate mesh
        all_bodies = self.mesh_manager.store_all_bodies()
        self.mesh_manager.generate_allbody_mesh(0.02)

        # Get named selections
        named_selections_dict = self.selection_mgr.get_named_selections_dict()
        
        # Save mesh preview image
        mesh_png = os.path.join(output_directory, "mesh.png")
        Elements, Nodes = self.mesh_manager.get_mesh_informationandpng(mesh_png)
        
        # Save mesh information
        outjson_file = os.path.join(output_directory, "mesh_outjson.json")
        outjson={
            "Elements": Elements,
            "Nodes": Nodes
        }
        with open(outjson_file, "w") as f:
            json.dump(outjson, f, indent=4)
        self.logger.info("MainApp run completed. Results saved to: %s", output_directory)

        pass

app = MainApp(ExtAPI)
app.run()

