"""
测试英文标签版本的图表显示

验证使用英文标签后是否解决了字体显示问题
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_english_labels():
    """测试英文标签版本"""
    print("🧪 测试英文标签版本的图表...")
    
    try:
        # 设置matplotlib使用Agg后端
        import matplotlib
        matplotlib.use('Agg')
        
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        
        # 创建图表组件
        chart_widget = ModalChartWidget()
        
        # 准备测试数据
        test_data = [
            {
                'name': 'Fine Mesh',
                'size': 1.0,
                'frequencies': [42.5, 75.8, 108.2, 145.6, 189.3],
                'node_count': 10000,
                'element_count': 8000
            },
            {
                'name': 'Medium Mesh',
                'size': 2.5,
                'frequencies': [41.8, 74.2, 106.5, 143.1, 186.2],
                'node_count': 5000,
                'element_count': 4000
            },
            {
                'name': 'Coarse Mesh',
                'size': 5.0,
                'frequencies': [40.2, 71.9, 103.8, 139.7, 181.5],
                'node_count': 2000,
                'element_count': 1500
            }
        ]
        
        # 测试三种图表类型
        chart_types = [
            ("frequency_comparison", "Frequency Comparison"),
            ("mode_distribution", "Mode Distribution"),
            ("mesh_convergence", "Mesh Convergence")
        ]
        
        success_count = 0
        for chart_type, description in chart_types:
            try:
                print(f"  📊 Testing {description}...")
                chart_widget.update_chart(chart_type, test_data)
                
                # 保存图表
                filename = f"english_labels_{chart_type}.png"
                chart_widget.save_chart(filename, dpi=150)
                
                if os.path.exists(filename):
                    file_size = os.path.getsize(filename)
                    print(f"  ✅ {description} generated successfully ({file_size} bytes)")
                    success_count += 1
                else:
                    print(f"  ❌ {description} file not generated")
                    
            except Exception as e:
                print(f"  ❌ {description} generation failed: {str(e)}")
        
        return success_count, len(chart_types)
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return 0, 3

def compare_file_sizes():
    """比较中文和英文版本的文件大小"""
    print("\n📊 比较文件大小...")
    
    chinese_files = [
        "chinese_font_test_frequency_comparison.png",
        "chinese_font_test_mode_distribution.png", 
        "chinese_font_test_mesh_convergence.png"
    ]
    
    english_files = [
        "english_labels_frequency_comparison.png",
        "english_labels_mode_distribution.png",
        "english_labels_mesh_convergence.png"
    ]
    
    for chinese_file, english_file in zip(chinese_files, english_files):
        if os.path.exists(chinese_file) and os.path.exists(english_file):
            chinese_size = os.path.getsize(chinese_file)
            english_size = os.path.getsize(english_file)
            
            chart_type = chinese_file.replace("chinese_font_test_", "").replace(".png", "")
            print(f"  {chart_type}:")
            print(f"    Chinese: {chinese_size} bytes")
            print(f"    English: {english_size} bytes")
            print(f"    Difference: {english_size - chinese_size:+d} bytes")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🔤 英文标签版本测试")
    print("=" * 60)
    
    # 配置日志
    logging.basicConfig(level=logging.WARNING)  # 只显示警告和错误
    
    # 测试英文标签版本
    success, tests = test_english_labels()
    
    # 比较文件大小
    compare_file_sizes()
    
    print("\n" + "=" * 60)
    print("📋 测试结果:")
    print(f"英文标签测试: {success}/{tests} 通过")
    
    if success == tests:
        print("\n🎉 英文标签版本测试成功！")
        print("✅ 所有图表都能正确生成")
        print("✅ 避免了中文字体显示问题")
        print("✅ 专业的英文界面更适合工程软件")
    else:
        print("\n⚠️ 部分测试失败")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
