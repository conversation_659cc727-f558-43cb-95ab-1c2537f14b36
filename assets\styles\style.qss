/* 主窗口样式 */
QMainWindow {
    background-color: #f8fafc;  /* 更柔和的背景色 */
}

/* 主题颜色定义 */
/* 主色: #3498db (蓝色)
   强调色: #2ecc71 (绿色)
   警告色: #e74c3c (红色)
   中性色: #34495e (深灰蓝)
   背景色: #f8fafc (浅灰蓝)
*/

/* 按钮通用样式 */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #1f6aa6;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #f5f5f5;
}

/* 特殊按钮样式 */
QPushButton#push_finish, QPushButton#result, QPushButton#post {
    background-color: #2ecc71;
}

QPushButton#push_finish:hover, QPushButton#result:hover, QPushButton#post:hover {
    background-color: #27ae60;
}

/* 返回主界面按钮样式 - 仅在子窗口中存在 */
QPushButton#push_constrainui {
    background-color: #7f8c8d;
}

QPushButton#push_constrainui:hover {
    background-color: #6c7a7d;
}

/* 标签样式 */
QLabel {
    color: #34495e;
    font-size: 12px;
}

QLabel[isTitle="true"] {
    font-size: 14px;
    font-weight: bold;
    color: #2c3e50;
}

/* 组合框样式 */
QComboBox {
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;
    min-height: 25px;
    padding-right: 20px;
}

QComboBox:hover {
    border: 1px solid #3498db;
}

QComboBox:focus {
    border: 1px solid #3498db;
}

/* 下拉按钮区域样式 */
QComboBox::drop-down {
    border: none;
    width: 20px;
}

/* 下拉箭头样式 */
QComboBox::down-arrow {
    width: 8px;
    height: 8px;
    background: none;
    border-top: 2px solid #7f8c8d;
    border-right: 2px solid #7f8c8d;
    transform: rotate(135deg);
    margin-top: -3px;
}

/* 下拉列表样式 */
QComboBox QAbstractItemView {
    border: 1px solid #dcdfe6;
    background-color: white;
    selection-background-color: #3498db;
    selection-color: white;
    border-radius: 0px 0px 4px 4px;
}

QComboBox:disabled {
    background-color: #f5f7fa;
    color: #c0c4cc;
}

/* 输入框样式 */
QLineEdit {
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 6px 8px;
    selection-background-color: #3498db;
    selection-color: white;
}

QLineEdit:focus {
    border: 1px solid #3498db;
}

QLineEdit:disabled {
    background-color: #f5f7fa;
    color: #c0c4cc;
}

/* 文本编辑框样式 */
QTextEdit, QPlainTextEdit {
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;
    selection-background-color: #3498db;
    selection-color: white;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border: 1px solid #3498db;
}

/* 分组框样式 */
QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #e9eaec;
    border-radius: 0 6px 6px 6px;
    background: white;
    top: -1px;
}

QTabBar::tab {
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    min-width: 80px;
    padding: 8px 15px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background: white;
    border-bottom-color: white;
    color: #3498db;
}

QTabBar::tab:hover:!selected {
    background: #ecf0f1;
}

/* 表格样式 */
QTableView, QTableWidget {
    border: 1px solid #dcdfe6;
    background-color: white;
    gridline-color: #e9eaec;
    selection-background-color: #3498db;
    selection-color: white;
    alternate-background-color: #f5f7fa;
}

QTableView::item, QTableWidget::item {
    padding: 5px;
}

QHeaderView::section {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    padding: 5px;
    font-weight: bold;
}

/* 滚动条样式 */
QScrollBar:vertical {
    border: none;
    background: #f5f7fa;
    width: 10px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background: #c0c4cc;
    border-radius: 5px;
    min-height: 30px;
}

QScrollBar::handle:vertical:hover {
    background: #a0a4ac;
}

QScrollBar:horizontal {
    border: none;
    background: #f5f7fa;
    height: 10px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background: #c0c4cc;
    border-radius: 5px;
    min-width: 30px;
}

QScrollBar::handle:horizontal:hover {
    background: #a0a4ac;
}

/* 移除滚动条上下按钮 */
QScrollBar::add-line, QScrollBar::sub-line {
    height: 0px;
    width: 0px;
}

/* 状态栏样式 */
QStatusBar {
    background-color: #f5f7fa;
    color: #34495e;
    border-top: 1px solid #dcdfe6;
}

/* 菜单栏样式 */
QMenuBar {
    background-color: #f8fafc;
    border-bottom: 1px solid #e9eaec;
}

QMenuBar::item {
    padding: 8px 15px;
    background: transparent;
}

QMenuBar::item:selected {
    background-color: #ecf0f1;
    border-radius: 4px;
}

QMenu {
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

QMenu::item {
    padding: 6px 25px 6px 20px;
}

QMenu::item:selected {
    background-color: #ecf0f1;
}

/* 复选框样式 */
QCheckBox {
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
}

QCheckBox::indicator:unchecked {
    background-color: white;
}

QCheckBox::indicator:checked {
    background-color: #3498db;
    border-color: #3498db;
    image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3E%3Cpath d='M16.707 5.293a1 1 0 00-1.414 0L8 12.586l-2.293-2.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l8-8a1 1 0 000-1.414z'/%3E%3C/svg%3E");
}

/* 单选按钮样式 */
QRadioButton {
    spacing: 8px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
}

QRadioButton::indicator:unchecked {
    background-color: white;
}

QRadioButton::indicator:checked {
    background-color: white;
    border: 2px solid #3498db;
}

QRadioButton::indicator:checked {
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background-color: #3498db;
}

/* 微调框样式 */
QSpinBox, QDoubleSpinBox {
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 5px;
}

QSpinBox:focus, QDoubleSpinBox:focus {
    border: 1px solid #3498db;
}

QSpinBox::up-button, QDoubleSpinBox::up-button,
QSpinBox::down-button, QDoubleSpinBox::down-button {
    width: 16px;
    background-color: #f5f7fa;
    border: none;
}

QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
    background-color: #ecf0f1;
}

/* 工具提示 */
QToolTip {
    background-color: #34495e;
    color: white;
    border: none;
    padding: 5px;
    opacity: 230;
} 