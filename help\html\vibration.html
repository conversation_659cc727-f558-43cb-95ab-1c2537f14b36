<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>振动分析 - 帮助文档</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>振动分析模块</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>功能概述</h2>
            <p>振动分析模块是本软件的核心功能之一，提供了强大的振动数据处理和可视化能力。该模块支持多种数据源（流体激振力数据和电机数据），可以帮助工程师分析结构在不同方向上的振动特性，计算关键指标，并通过直观的图形界面展示分析结果。</p>

            <div class="highlight-box">
                <h3>🎯 频段范围配置</h3>
                <p><strong>默认设置：</strong>低频段 (10-315 Hz) - 16个1/3倍频程频段</p>
                <p><strong>可选范围：</strong>总频段 (10-10k Hz) - 31个1/3倍频程频段</p>
                <p>支持实时频段切换，所有数据模式同步更新。</p>
            </div>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>多数据源支持</h3>
                    <ul>
                        <li>流体激振力数据（X、Y、Z三方向）</li>
                        <li>电机数据（Z方向）</li>
                        <li>数据组合分析（流体+电机）</li>
                        <li>智能模式识别和切换</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>频段范围选择</h3>
                    <ul>
                        <li>低频段 (10-315 Hz) - 16个频段</li>
                        <li>总频段 (10-10k Hz) - 31个频段</li>
                        <li>实时频段切换</li>
                        <li>支持所有数据模式</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>智能数据分析</h3>
                    <ul>
                        <li>快速傅里叶变换(FFT)</li>
                        <li>1/3倍频程分析</li>
                        <li>对数加法组合算法</li>
                        <li>A计权处理</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>可视化与导出</h3>
                    <ul>
                        <li>时域信号图</li>
                        <li>频谱图与条形图</li>
                        <li>数据模式标识</li>
                        <li>智能Excel导出</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>界面说明</h2>
            <p>振动分析界面分为以下几个主要区域：</p>

            <div class="ui-description">
                <h3>1. 数据加载区</h3>
                <p>位于界面左上方，支持多种数据源加载。</p>
                <ul>
                    <li><strong>流体数据</strong> - 支持.txt格式，包含X、Y、Z三方向数据</li>
                    <li><strong>电机数据</strong> - 支持.xlsx/.xls格式，主要为Z方向数据</li>
                    <li><strong>清除按钮</strong> - 独立清除各类数据，自动切换模式</li>
                    <li><strong>状态指示</strong> - 显示当前数据加载状态和模式</li>
                </ul>
            </div>

            <div class="ui-description">
                <h3>2. 分析控制区</h3>
                <p>位于界面左侧中部，包含分析参数和控制选项。</p>
                <ul>
                    <li><strong>频段范围选择</strong> - 低频段(10-315 Hz)或总频段(10-10k Hz)</li>
                    <li><strong>方向选择</strong> - 下拉菜单，可选择X、Y、Z方向</li>
                    <li><strong>分析按钮</strong> - 执行振动分析计算</li>
                    <li><strong>导出按钮</strong> - 导出分析结果到Excel</li>
                </ul>
            </div>
            
            <div class="ui-description">
                <h3>3. 时域信号图</h3>
                <p>位于界面右上方，显示原始时域振动信号。</p>
                <ul>
                    <li>X轴表示时间(s)</li>
                    <li>Y轴表示加速度(m/s²)</li>
                    <li>支持缩放和平移操作</li>
                    <li>显示数据模式标识</li>
                </ul>
            </div>

            <div class="ui-description">
                <h3>4. 频谱图区域</h3>
                <p>位于界面右中方，显示振动数据的频谱分析结果。</p>
                <ul>
                    <li>X轴表示频率(Hz)</li>
                    <li>Y轴表示振幅(dB)</li>
                    <li>支持缩放和平移操作</li>
                    <li>可显示峰值标记</li>
                </ul>
            </div>

            <div class="ui-description">
                <h3>5. 1/3倍频程图区域</h3>
                <p>位于界面右下方，显示1/3倍频程分析结果。</p>
                <ul>
                    <li>X轴表示中心频率</li>
                    <li>Y轴表示A计权声级(dBA)</li>
                    <li>使用条形图显示不同频段的能量分布</li>
                    <li>图表标题显示当前频段范围和数据模式</li>
                </ul>
            </div>

            <div class="ui-description">
                <h3>6. 结果数据区</h3>
                <p>位于界面底部，以表格形式显示详细的分析数据。</p>
                <ul>
                    <li>频率、振幅和相位数据</li>
                    <li>各频段的声级值</li>
                    <li>总振动加速度级</li>
                    <li>组合模式下的分量信息</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>数据模式与频段范围</h2>

            <div class="mode-description">
                <h3>数据模式说明</h3>
                <div class="mode-grid">
                    <div class="mode-card">
                        <h4>🌊 流体数据模式</h4>
                        <p>仅加载流体激振力数据时的分析模式</p>
                        <ul>
                            <li>支持X、Y、Z三个方向分析</li>
                            <li>文件格式：.txt文本文件</li>
                            <li>数据列：时间、X加速度、Y加速度、Z加速度</li>
                        </ul>
                    </div>

                    <div class="mode-card">
                        <h4>⚡ 电机数据模式</h4>
                        <p>仅加载电机数据时的分析模式</p>
                        <ul>
                            <li>主要分析Z方向数据</li>
                            <li>文件格式：.xlsx/.xls Excel文件</li>
                            <li>自动识别数据列结构</li>
                        </ul>
                    </div>

                    <div class="mode-card">
                        <h4>🔄 组合数据模式</h4>
                        <p>同时加载流体和电机数据时的分析模式</p>
                        <ul>
                            <li>Z方向：流体+电机数据组合</li>
                            <li>X、Y方向：仅流体数据</li>
                            <li>使用对数加法公式组合</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="frequency-description">
                <h3>频段范围配置</h3>
                <table class="frequency-table">
                    <thead>
                        <tr>
                            <th>频段类型</th>
                            <th>频率范围</th>
                            <th>频段数量</th>
                            <th>应用场景</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>低频段</strong></td>
                            <td>10-315 Hz</td>
                            <td>16个1/3倍频程</td>
                            <td>结构振动、低频分析（默认）</td>
                        </tr>
                        <tr>
                            <td><strong>总频段</strong></td>
                            <td>10-10000 Hz</td>
                            <td>31个1/3倍频程</td>
                            <td>全频段分析、高频噪声</td>
                        </tr>
                    </tbody>
                </table>

                <div class="combination-formula">
                    <h4>数据组合算法</h4>
                    <p><strong>对数加法公式：</strong></p>
                    <code>L_total = 10 × lg(10^(L_a1/10) + 10^(L_a2/10))</code>
                    <ul>
                        <li>L_a1：流体激振力总振动级</li>
                        <li>L_a2：电机数据总振动级</li>
                        <li>L_total：组合后总振动级</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>操作指南</h2>
            
            <div class="step-guide">
                <h3>基本操作流程</h3>
                <ol>
                    <li>
                        <strong>加载数据</strong>
                        <p>根据数据类型选择相应的加载方式：</p>
                        <ul>
                            <li><strong>流体数据：</strong>点击"流体数据"区域的"浏览"按钮，选择.txt格式文件</li>
                            <li><strong>电机数据：</strong>点击"电机数据"区域的"浏览"按钮，选择.xlsx/.xls格式文件</li>
                            <li><strong>组合分析：</strong>可同时加载两种数据，系统自动进入组合模式</li>
                        </ul>
                    </li>
                    <li>
                        <strong>选择频段范围</strong>
                        <p>从频段范围下拉菜单中选择：</p>
                        <ul>
                            <li><strong>低频段 (10-315 Hz)：</strong>适用于结构振动分析（默认）</li>
                            <li><strong>总频段 (10-10k Hz)：</strong>适用于全频段分析</li>
                        </ul>
                    </li>
                    <li>
                        <strong>选择分析方向</strong>
                        <p>从方向下拉菜单中选择要分析的方向(X、Y或Z)。注意：</p>
                        <ul>
                            <li>流体数据模式：支持X、Y、Z三个方向</li>
                            <li>电机数据模式：主要分析Z方向</li>
                            <li>组合模式：Z方向为组合数据，X、Y方向为流体数据</li>
                        </ul>
                    </li>
                    <li>
                        <strong>执行分析</strong>
                        <p>点击"分析数据"按钮开始处理。软件将：</p>
                        <ul>
                            <li>自动执行FFT分析和1/3倍频程分析</li>
                            <li>根据当前频段范围计算结果</li>
                            <li>在组合模式下应用对数加法公式</li>
                        </ul>
                    </li>
                    <li>
                        <strong>查看结果</strong>
                        <p>分析完成后，结果将显示在各个图表中：</p>
                        <ul>
                            <li>时域信号图显示原始数据</li>
                            <li>频谱图显示频域分析结果</li>
                            <li>1/3倍频程图显示频段分析结果</li>
                            <li>图表标题显示当前数据模式和频段信息</li>
                        </ul>
                    </li>
                    <li>
                        <strong>导出结果</strong>
                        <p>点击"导出到Excel"按钮，将分析结果保存为Excel文件，包括：</p>
                        <ul>
                            <li>原始时域数据</li>
                            <li>频谱分析数据</li>
                            <li>1/3倍频程数据</li>
                            <li>数据模式和频段信息</li>
                            <li>组合模式下的分量详情</li>
                        </ul>
                    </li>
                </ol>
            </div>
            
            <div class="tip">
                <h3>高级操作技巧</h3>
                <ul>
                    <li><strong>频段范围切换</strong> - 可以在分析过程中实时切换频段范围，所有数据模式都支持此功能，便于对比不同频段的分析结果</li>
                    <li><strong>数据模式管理</strong> - 使用独立的清除按钮可以精确控制数据状态，清除特定数据会自动切换到相应的分析模式</li>
                    <li><strong>组合分析优化</strong> - 在组合模式下，可以先分别查看流体和电机数据的单独分析结果，再查看组合结果进行对比</li>
                    <li><strong>多方向分析</strong> - 在组合模式下，Z方向显示组合结果，X、Y方向显示流体数据结果，可以全面了解振动特性</li>
                    <li><strong>结果验证</strong> - 导出的Excel文件包含详细的计算过程和分量信息，可用于验证组合算法的正确性</li>
                    <li><strong>自定义显示</strong> - 右键点击图表区域可以访问上下文菜单，调整显示选项，如网格线、坐标轴范围等</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>结果解读</h2>
            
            <div class="interpretation">
                <h3>振动级别指示</h3>
                <p>软件使用颜色编码系统来直观地表示振动严重程度：</p>
                <ul>
                    <li><span style="color: #2ecc71; font-weight: bold;">绿色</span> - 低振动级别 (&lt; 80 dB)，通常表示正常工作状态</li>
                    <li><span style="color: #f39c12; font-weight: bold;">黄色</span> - 中等振动级别 (80-100 dB)，可能需要关注</li>
                    <li><span style="color: #e74c3c; font-weight: bold;">红色</span> - 高振动级别 (&gt; 100 dB)，可能存在问题，需要进一步分析</li>
                </ul>
            </div>
            
            <div class="interpretation">
                <h3>频谱图分析</h3>
                <p>频谱图显示了振动能量在不同频率上的分布：</p>
                <ul>
                    <li><strong>峰值</strong> - 频谱图中的峰值表示在该频率处有显著的振动能量，可能对应于系统的自然频率或激励频率</li>
                    <li><strong>宽频带</strong> - 宽频带能量可能表示随机振动或噪声</li>
                    <li><strong>谐波</strong> - 在基频的整数倍频率处出现的峰值，可能表示非线性振动或结构问题</li>
                </ul>
            </div>
            
            <div class="interpretation">
                <h3>1/3倍频程分析</h3>
                <p>1/3倍频程分析将频谱分为多个频段，每个频段的能量用条形图表示：</p>
                <ul>
                    <li><strong>低频段</strong> (20-200 Hz) - 通常与结构基础振动相关</li>
                    <li><strong>中频段</strong> (200-2000 Hz) - 通常与机械部件振动相关</li>
                    <li><strong>高频段</strong> (&gt; 2000 Hz) - 通常与摩擦、气流或电气噪声相关</li>
                </ul>
            </div>
            
            <div class="warning">
                <h3>注意事项</h3>
                <p>在解读振动分析结果时，请注意以下几点：</p>
                <ul>
                    <li>振动数据的采集质量直接影响分析结果的准确性</li>
                    <li>不同方向的振动特性可能有显著差异，应综合考虑</li>
                    <li>振动级别的评估应结合具体应用场景和相关标准</li>
                    <li>单次测量可能不具有代表性，建议进行多次测量并取平均值</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>常见问题</h2>
            
            <div class="faq">
                <h3>Q: 如何选择合适的频段范围？</h3>
                <p>A: <strong>低频段 (10-315 Hz)</strong> 适用于大多数结构振动分析，<strong>总频段 (10-10k Hz)</strong> 适用于需要分析高频噪声或全频段特性的场合。可以通过切换频段范围对比分析结果。</p>
            </div>

            <div class="faq">
                <h3>Q: 什么时候使用组合数据模式？</h3>
                <p>A: 当需要同时考虑流体激振力和电机振动的综合影响时使用组合模式。系统会自动使用对数加法公式计算Z方向的组合振动级，X、Y方向仍显示流体数据结果。</p>
            </div>

            <div class="faq">
                <h3>Q: 为什么电机数据只分析Z方向？</h3>
                <p>A: 电机数据通常主要关注垂直方向（Z方向）的振动特性。如果需要分析其他方向，可以加载相应的流体数据进行组合分析。</p>
            </div>

            <div class="faq">
                <h3>Q: 组合模式下的计算公式是什么？</h3>
                <p>A: 使用对数加法公式：<code>L_total = 10 × lg(10^(L_a1/10) + 10^(L_a2/10))</code>，其中L_a1为流体数据振动级，L_a2为电机数据振动级。</p>
            </div>

            <div class="faq">
                <h3>Q: 如何验证分析结果的正确性？</h3>
                <p>A: 可以通过以下方式验证：1) 导出Excel文件查看详细计算过程；2) 在组合模式下分别查看流体和电机的单独结果；3) 切换频段范围对比分析结果的一致性。</p>
            </div>

            <div class="faq">
                <h3>Q: 频段切换后为什么结果会变化？</h3>
                <p>A: 这是正常现象。不同频段范围包含的频率成分不同，低频段主要反映结构振动特性，总频段还包含高频噪声成分，因此总振动级会有所差异。</p>
            </div>

            <div class="faq">
                <h3>Q: 导出的Excel文件包含哪些信息？</h3>
                <p>A: 导出文件包含：原始时域数据、频谱数据、1/3倍频程数据、当前频段范围信息、数据模式标识，以及组合模式下的流体和电机分量详情。</p>
            </div>
        </div>

        <a href="index.html" class="back-link">返回主页</a>
    </div>

    <div class="footer">
        <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
    </div>
</body>
</html> 