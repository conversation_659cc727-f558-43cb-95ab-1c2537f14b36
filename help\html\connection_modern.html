<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接设置界面 - 智能化连接定义系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-purple-600 to-indigo-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-purple-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🔗 连接设置界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-purple-100">
                    智能化连接定义系统 | 多种连接类型与高级配置
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-purple-500 bg-opacity-20 text-purple-100 text-sm font-semibold px-4 py-2 rounded-full border border-purple-400">
                        🔗 连接类型 | ⚙️ 参数配置 | 🎯 智能识别 | 📊 状态监控
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                连接设置界面用于定义模型中不同部件之间的连接关系。正确的连接设置对于准确模拟实际工程结构至关重要。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">连接类型选择</h3>
                    </div>
                    <p class="text-sm text-blue-600">选择合适的连接类型，如绑定、接触、焊接等</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">参数配置</h3>
                    </div>
                    <p class="text-sm text-green-600">设置连接的详细参数，如刚度、阻尼等</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">智能识别</h3>
                    </div>
                    <p class="text-sm text-purple-600">自动识别可能的连接区域和连接类型</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">状态监控</h3>
                    </div>
                    <p class="text-sm text-orange-600">显示连接状态和验证结果</p>
                </div>
            </div>
        </section>

        <!-- Connection Types -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🔗 连接类型</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">支持多种连接类型以满足不同的工程需求</p>
            </div>
            
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔗</span>绑定连接 (Bonded)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 完全刚性连接</li>
                                    <li>• 不允许相对运动</li>
                                    <li>• 传递所有力和力矩</li>
                                    <li>• 适用于焊接、胶接等永久连接</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">应用场景</h4>
                                <p class="text-sm text-blue-600">焊接结构、胶接部件、一体化设计的组件连接</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">👆</span>接触连接 (Contact)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 允许分离和滑动</li>
                                    <li>• 可设置摩擦系数</li>
                                    <li>• 支持非线性分析</li>
                                    <li>• 适用于可拆卸连接</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">应用场景</h4>
                                <p class="text-sm text-green-600">螺栓连接、压配合、机械接触面</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌸</span>弹簧连接 (Spring)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 可设置刚度和阻尼</li>
                                    <li>• 支持线性和非线性特性</li>
                                    <li>• 可定义预载荷</li>
                                    <li>• 适用于弹性连接</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">应用场景</h4>
                                <p class="text-sm text-purple-600">减震器、弹簧支撑、柔性连接</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔩</span>螺栓连接 (Bolt)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">特点</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 模拟真实螺栓行为</li>
                                    <li>• 可设置预紧力</li>
                                    <li>• 考虑螺栓材料属性</li>
                                    <li>• 支持螺栓失效分析</li>
                                </ul>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">应用场景</h4>
                                <p class="text-sm text-orange-600">法兰连接、机械装配、结构连接</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Operation Guide -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📋 操作指南</h2>

            <div class="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🔄 连接设置流程</h3>

                <div class="space-y-4">
                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">1</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🎯 选择连接面</h4>
                            <p class="text-gray-600 text-sm">在模型中选择需要建立连接的面。可以使用点击、框选或过滤器来选择面。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">2</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔗 选择连接类型</h4>
                            <p class="text-gray-600 text-sm">根据实际工程情况选择合适的连接类型：绑定、接触、弹簧或螺栓连接。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">3</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">⚙️ 配置参数</h4>
                            <p class="text-gray-600 text-sm">设置连接的详细参数，如刚度、阻尼、摩擦系数、预紧力等。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">4</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">✅ 验证连接</h4>
                            <p class="text-gray-600 text-sm">检查连接设置是否正确，确认连接状态和参数设置。</p>
                        </div>
                    </div>

                    <div class="flex items-start p-4 bg-white rounded-lg shadow-sm">
                        <span class="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">5</span>
                        <div>
                            <h4 class="font-semibold text-gray-800 mb-2">🔄 应用设置</h4>
                            <p class="text-gray-600 text-sm">确认所有连接设置后，应用到模型中并进入下一步分析设置。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Parameter Configuration -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">⚙️ 参数配置详解</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔗 绑定连接参数</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-blue-800 mb-2">容差设置</h4>
                            <p class="text-sm text-blue-600">控制连接面之间的最大允许间隙</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-green-800 mb-2">行为类型</h4>
                            <p class="text-sm text-green-600">选择对称或非对称连接行为</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-purple-800 mb-2">界面处理</h4>
                            <p class="text-sm text-purple-600">设置界面处理方法和算法</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">👆 接触连接参数</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">摩擦系数</h4>
                                <p class="text-sm text-green-600">设置静摩擦和动摩擦系数</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                                <h4 class="font-semibold text-blue-800 mb-2">法向刚度</h4>
                                <p class="text-sm text-blue-600">控制接触面的法向刚度</p>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                                <h4 class="font-semibold text-purple-800 mb-2">穿透容差</h4>
                                <p class="text-sm text-purple-600">允许的最大穿透深度</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                                <h4 class="font-semibold text-orange-800 mb-2">接触算法</h4>
                                <p class="text-sm text-orange-600">选择接触检测和求解算法</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🌸 弹簧连接参数</h3>
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                            <h4 class="font-semibold text-purple-800 mb-2">刚度矩阵</h4>
                            <p class="text-sm text-purple-600">设置6个自由度的刚度值</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-pink-400">
                            <h4 class="font-semibold text-pink-800 mb-2">阻尼系数</h4>
                            <p class="text-sm text-pink-600">设置各方向的阻尼系数</p>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-indigo-400">
                            <h4 class="font-semibold text-indigo-800 mb-2">预载荷</h4>
                            <p class="text-sm text-indigo-600">设置初始预载荷和预变形</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-orange-50 to-red-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔩 螺栓连接参数</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                                <h4 class="font-semibold text-orange-800 mb-2">螺栓规格</h4>
                                <p class="text-sm text-orange-600">选择螺栓直径、长度和材料</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-red-400">
                                <h4 class="font-semibold text-red-800 mb-2">预紧力</h4>
                                <p class="text-sm text-red-600">设置螺栓的预紧力大小</p>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-yellow-400">
                                <h4 class="font-semibold text-yellow-800 mb-2">螺纹参数</h4>
                                <p class="text-sm text-yellow-600">设置螺纹类型和参数</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">失效准则</h4>
                                <p class="text-sm text-green-600">选择螺栓失效判断准则</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Smart Recognition -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 智能识别功能</h2>

            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🤖 自动连接识别</h3>
                <p class="text-gray-600 mb-4">系统可以自动识别模型中可能的连接区域和推荐的连接类型：</p>

                <div class="grid md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <span class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                            <h4 class="font-semibold text-blue-800">几何分析</h4>
                        </div>
                        <p class="text-sm text-blue-600">分析几何特征，识别相邻面和接触区域</p>
                    </div>

                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <span class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                            <h4 class="font-semibold text-green-800">距离检测</h4>
                        </div>
                        <p class="text-sm text-green-600">检测面之间的距离，判断连接可能性</p>
                    </div>

                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3">
                            <span class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                            <h4 class="font-semibold text-purple-800">类型推荐</h4>
                        </div>
                        <p class="text-sm text-purple-600">基于几何特征推荐合适的连接类型</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Connection Validation -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">✅ 连接验证</h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔍 验证检查项目</h3>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-green-600 mr-3">✓</span>
                            <span class="text-sm">连接面的几何兼容性</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-green-600 mr-3">✓</span>
                            <span class="text-sm">参数设置的合理性</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-green-600 mr-3">✓</span>
                            <span class="text-sm">连接类型的适用性</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-green-600 mr-3">✓</span>
                            <span class="text-sm">网格兼容性检查</span>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">⚠️ 常见问题提示</h3>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-yellow-600 mr-3">⚠️</span>
                            <span class="text-sm">面间距过大导致连接失效</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-orange-600 mr-3">⚠️</span>
                            <span class="text-sm">参数设置超出合理范围</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-red-600 mr-3">⚠️</span>
                            <span class="text-sm">连接类型与应用场景不匹配</span>
                        </div>
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <span class="text-purple-600 mr-3">⚠️</span>
                            <span class="text-sm">网格不兼容可能影响计算</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔗</span>如何选择合适的连接类型？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">连接类型的选择应基于实际工程情况和分析需求。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">选择指南：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• <strong>绑定连接</strong> - 用于永久性连接，如焊接、胶接</li>
                                <li>• <strong>接触连接</strong> - 用于可分离的连接，如螺栓、压配合</li>
                                <li>• <strong>弹簧连接</strong> - 用于弹性连接，如减震器、支撑</li>
                                <li>• <strong>螺栓连接</strong> - 用于真实螺栓连接的精确模拟</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚙️</span>连接参数如何设置？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">参数设置应参考实际材料属性和工程经验。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">设置建议：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 参考材料手册获取基本参数</li>
                                <li>• 根据实验数据调整参数</li>
                                <li>• 进行敏感性分析验证参数影响</li>
                                <li>• 使用默认值作为初始设置</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">❌</span>连接设置失败怎么办？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">连接设置失败通常由几何问题或参数设置不当引起。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">排查步骤：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 检查连接面的几何完整性</li>
                                <li>• 确认面间距是否在合理范围内</li>
                                <li>• 验证参数设置是否正确</li>
                                <li>• 尝试调整容差设置</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>如何优化连接性能？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">连接性能优化可以提高计算效率和结果精度。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">优化方法：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 使用合适的连接算法</li>
                                <li>• 优化网格质量和密度</li>
                                <li>• 调整求解器设置</li>
                                <li>• 简化不必要的连接细节</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 连接面之间的距离不能过大</li>
                    <li>• 参数设置必须符合物理实际</li>
                    <li>• 不同连接类型不能随意混用</li>
                    <li>• 螺栓预紧力不能超过材料强度</li>
                    <li>• 接触连接需要考虑收敛性问题</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 优先使用智能识别功能提高效率</li>
                    <li>• 定期验证连接设置的正确性</li>
                    <li>• 保存常用的连接配置模板</li>
                    <li>• 参考工程手册设置参数值</li>
                    <li>• 进行敏感性分析验证参数影响</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">智能化连接定义系统 - 多种连接类型与高级配置</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
