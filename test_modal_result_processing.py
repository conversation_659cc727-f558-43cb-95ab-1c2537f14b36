#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模态结果处理功能

此脚本用于测试新实现的模态计算结果读取和界面更新功能
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_modal_result_files(output_dir: str, mesh_size: float = 0.012):
    """创建测试用的模态结果文件"""
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建模态频率结果文件
    modal_freq_data = {
        "calculation_time_s": 298.21,
        "element_size_m": mesh_size,
        "frequencies_Hz": [11.93, 63.85, 96.01, 107.8, 324.5, 420.8, 444.2, 461.7, 493.3, 599.2],
        "element_count": 421601,
        "node_count": 699608
    }
    
    modal_freq_file = os.path.join(output_dir, f"modal_freq_{mesh_size}.json")
    with open(modal_freq_file, 'w', encoding='utf-8') as f:
        json.dump(modal_freq_data, f, indent=4)
    
    # 创建网格信息文件
    mesh_info_data = {
        "Elements": 421601,
        "Nodes": 699608,
        "ElementSize": mesh_size
    }
    
    mesh_info_file = os.path.join(output_dir, f"mesh_info_{mesh_size}.json")
    with open(mesh_info_file, 'w', encoding='utf-8') as f:
        json.dump(mesh_info_data, f, indent=4)
    
    print(f"✅ 测试文件创建成功:")
    print(f"   模态结果文件: {modal_freq_file}")
    print(f"   网格信息文件: {mesh_info_file}")
    
    return modal_freq_file, mesh_info_file

def test_read_modal_calculation_results():
    """测试模态计算结果读取函数"""
    print("\n🧪 测试模态计算结果读取函数...")
    
    try:
        # 导入测试函数
        from views.mesh_window_merged import read_modal_calculation_results
        
        # 创建临时测试文件
        with tempfile.TemporaryDirectory() as temp_dir:
            mesh_size = 12.0  # 12mm
            modal_file, mesh_file = create_test_modal_result_files(temp_dir, 0.012)
            
            # 测试读取功能
            result = read_modal_calculation_results(temp_dir, mesh_size)
            
            # 验证结果
            assert result['success'] == True, "读取应该成功"
            assert len(result['frequencies']) == 10, "应该有10个频率"
            assert result['element_count'] == 421601, "单元数应该正确"
            assert result['node_count'] == 699608, "节点数应该正确"
            assert result['calculation_time'] > 0, "计算时间应该大于0"
            
            print("✅ 模态计算结果读取测试通过")
            print(f"   读取到 {len(result['frequencies'])} 个频率")
            print(f"   单元数: {result['element_count']:,}")
            print(f"   节点数: {result['node_count']:,}")
            print(f"   计算时间: {result['calculation_time']:.1f} 秒")
            
            return True
            
    except Exception as e:
        print(f"❌ 模态计算结果读取测试失败: {str(e)}")
        return False

def test_update_mesh_data_from_results():
    """测试网格数据更新函数"""
    print("\n🧪 测试网格数据更新函数...")
    
    try:
        # 导入必要的类和函数
        from views.mesh_window_merged import update_mesh_data_from_results
        from core.mesh_manager import MeshParameter, ElementType
        
        # 创建测试网格参数
        mesh = MeshParameter(
            name="测试网格",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        # 创建测试结果数据
        result_data = {
            'success': True,
            'frequencies': [11.93, 63.85, 96.01, 107.8, 324.5],
            'element_count': 421601,
            'node_count': 699608,
            'calculation_time': 298.21,
            'element_size': 0.012,
            'error_message': ''
        }
        
        # 测试更新功能
        success = update_mesh_data_from_results(mesh, result_data)
        
        # 验证结果
        assert success == True, "更新应该成功"
        assert len(mesh.modal_results.frequencies) == 5, "频率数量应该正确"
        assert mesh.statistics.element_count == 421601, "单元数应该更新"
        assert mesh.statistics.node_count == 699608, "节点数应该更新"
        assert mesh.modal_results.calculation_time == 298.21, "计算时间应该更新"
        
        print("✅ 网格数据更新测试通过")
        print(f"   更新了 {len(mesh.modal_results.frequencies)} 个频率")
        print(f"   单元数: {mesh.statistics.element_count:,}")
        print(f"   节点数: {mesh.statistics.node_count:,}")
        print(f"   计算时间: {mesh.modal_results.calculation_time:.1f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 网格数据更新测试失败: {str(e)}")
        return False

def test_file_not_found_handling():
    """测试文件不存在时的错误处理"""
    print("\n🧪 测试文件不存在时的错误处理...")
    
    try:
        from views.mesh_window_merged import read_modal_calculation_results
        
        # 测试不存在的目录
        result = read_modal_calculation_results("/nonexistent/directory", 12.0)
        
        # 验证错误处理
        assert result['success'] == False, "应该返回失败状态"
        assert 'error_message' in result, "应该包含错误消息"
        
        print("✅ 文件不存在错误处理测试通过")
        print(f"   错误消息: {result['error_message']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件不存在错误处理测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试模态结果处理功能...")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(test_read_modal_calculation_results())
    test_results.append(test_update_mesh_data_from_results())
    test_results.append(test_file_not_found_handling())
    
    # 汇总测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ 所有测试通过 ({passed}/{total})")
        print("\n🎉 模态结果处理功能实现成功！")
        return True
    else:
        print(f"❌ 部分测试失败 ({passed}/{total})")
        print("\n⚠️  请检查失败的测试并修复相关问题。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
