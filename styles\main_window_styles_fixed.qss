
/* 现代化主界面样式表 - 修复版 */

/* 主窗口样式 */
QMainWindow {
    background-color: #f5f5f5;
    font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
}

/* 标题区域样式 */
QLabel#titleLabel {
    background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                stop: 0 #0078d4, stop: 1 #106ebe);
    color: #ffffff;
    font-size: 24px;
    font-weight: bold;
    padding: 20px 30px;
    border-radius: 8px;
    margin: 15px;
}

QLabel#subtitleLabel {
    color: #666666;
    font-size: 14px;
    font-weight: normal;
    padding: 8px 30px;
    margin: 5px 15px 15px 15px;
}

/* 功能卡片样式 */
QFrame[class="functionCard"] {
    background-color: #ffffff;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 15px;
    margin: 5px;
}

QFrame[class="functionCard"]:hover {
    border-color: #0078d4;
    background-color: #fafafa;
    border-width: 3px;
}

/* 卡片图标样式 */
QLabel[class="cardIcon"] {
    font-size: 32px;
    color: #0078d4;
    background-color: #deecf9;
    border-radius: 30px;
    padding: 12px;
    margin: 5px;
    min-width: 60px;
    max-width: 60px;
    min-height: 60px;
    max-height: 60px;
}

/* 卡片标题样式 */
QLabel[class="cardTitle"] {
    font-size: 14px;
    font-weight: bold;
    color: #333333;
    margin: 5px;
}

/* 卡片描述样式 */
QLabel[class="cardDescription"] {
    font-size: 11px;
    color: #666666;
    margin: 5px;
}

/* 滚动区域样式 */
QScrollArea {
    border: none;
    background-color: transparent;
}

QScrollArea > QWidget > QWidget {
    background-color: transparent;
}
