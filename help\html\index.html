<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>振动传递计算软件帮助文档 v0.2.1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.2);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        @keyframes text-fade-in-up {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .animate-title {
            animation: text-fade-in-up 0.8s ease-out forwards;
        }
        .animate-subtitle {
            opacity: 0;
            animation: text-fade-in-up 0.8s ease-out 0.3s forwards;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-blue-600 to-blue-800 text-white py-20 md:py-32">
        <div class="container mx-auto px-6 text-center relative z-10">
            <h1 class="text-4xl md:text-6xl font-extrabold tracking-tighter leading-tight animate-title">
                振动传递计算软件
            </h1>
            <p class="mt-4 text-lg md:text-xl max-w-3xl mx-auto text-blue-100 animate-subtitle">
                专业的ANSYS Workbench自动化平台 | v0.2.1 核心功能指南
            </p>
            <div class="mt-8 animate-subtitle">
                <span class="inline-block bg-blue-500 bg-opacity-20 text-blue-100 text-sm font-semibold px-4 py-2 rounded-full border border-blue-400">
                    🚀 四合一自动化脚本 | 🌐 多编码处理 | 📍 监控点管理
                </span>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">

        <!-- Software Interface Navigation -->
        <section id="interfaces" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">🖥️ 软件界面导航</h2>
            <p class="text-center text-gray-600 mb-6">选择现代化界面体验更好的视觉效果和交互设计</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                <div class="relative group">
                    <a href="main_modern.html" class="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-blue-500 text-white rounded-full mb-3 group-hover:bg-blue-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-blue-800 group-hover:text-blue-900">主界面</h4>
                        <p class="text-sm text-blue-600">软件核心操作中心</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-blue-200 text-blue-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="main.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="mesh_modern.html" class="text-center p-4 bg-gradient-to-br from-emerald-50 to-teal-100 hover:from-emerald-100 hover:to-teal-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-emerald-500 text-white rounded-full mb-3 group-hover:bg-emerald-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-emerald-800 group-hover:text-emerald-900">网格划分</h4>
                        <p class="text-sm text-emerald-600">智能网格生成系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-emerald-200 text-emerald-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="mesh.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="pre_modern.html" class="text-center p-4 bg-gradient-to-br from-purple-50 to-indigo-100 hover:from-purple-100 hover:to-indigo-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-purple-500 text-white rounded-full mb-3 group-hover:bg-purple-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-purple-800 group-hover:text-purple-900">前处理</h4>
                        <p class="text-sm text-purple-600">ANSYS四合一自动化</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-purple-200 text-purple-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="pre.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="connection_modern.html" class="text-center p-4 bg-gradient-to-br from-orange-50 to-amber-100 hover:from-orange-100 hover:to-amber-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-orange-500 text-white rounded-full mb-3 group-hover:bg-orange-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-orange-800 group-hover:text-orange-900">连接设置</h4>
                        <p class="text-sm text-orange-600">智能连接识别系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="connection.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="analysis_modern.html" class="text-center p-4 bg-gradient-to-br from-red-50 to-pink-100 hover:from-red-100 hover:to-pink-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-red-500 text-white rounded-full mb-3 group-hover:bg-red-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-red-800 group-hover:text-red-900">分析设置</h4>
                        <p class="text-sm text-red-600">高级分析配置系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-red-200 text-red-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="analysis.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="constrain_modern.html" class="text-center p-4 bg-gradient-to-br from-indigo-50 to-blue-100 hover:from-indigo-100 hover:to-blue-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-indigo-500 text-white rounded-full mb-3 group-hover:bg-indigo-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 11V7a4 4 0 118 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-indigo-800 group-hover:text-indigo-900">约束设置</h4>
                        <p class="text-sm text-indigo-600">监控点管理系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-indigo-200 text-indigo-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="constrain.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="result_modern.html" class="text-center p-4 bg-gradient-to-br from-teal-50 to-cyan-100 hover:from-teal-100 hover:to-cyan-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-teal-500 text-white rounded-full mb-3 group-hover:bg-teal-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-teal-800 group-hover:text-teal-900">结果查看</h4>
                        <p class="text-sm text-teal-600">高级结果可视化系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-teal-200 text-teal-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="result.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="vibration_modern.html" class="text-center p-4 bg-gradient-to-br from-pink-50 to-purple-100 hover:from-pink-100 hover:to-purple-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-pink-500 text-white rounded-full mb-3 group-hover:bg-pink-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-pink-800 group-hover:text-pink-900">振动分析</h4>
                        <p class="text-sm text-pink-600">专业振动数据处理系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-pink-200 text-pink-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="vibration.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>
            </div>

            <div class="mt-8 text-center">
                <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    <span class="text-sm font-medium text-gray-700">现代版界面提供更好的用户体验和视觉效果</span>
                </div>
            </div>
        </section>

        <!-- Quick Start Guide -->
        <section id="quick-start" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">🚀 快速入门指南</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">按照以下步骤快速开始使用振动传递计算软件</p>
            </div>
            <div class="grid md:grid-cols-3 gap-6">
                <div class="space-y-4">
                    <div class="flex items-center p-4 bg-blue-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">1</div>
                        <div>
                            <h4 class="font-semibold text-blue-800">创建项目</h4>
                            <p class="text-sm text-blue-600">在主界面创建新项目或打开现有项目</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-green-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">2</div>
                        <div>
                            <h4 class="font-semibold text-green-800">网格设置</h4>
                            <p class="text-sm text-green-600">通过网格划分界面设置网格参数</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-purple-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">3</div>
                        <div>
                            <h4 class="font-semibold text-purple-800">导入模型</h4>
                            <p class="text-sm text-purple-600">在前处理界面导入几何模型</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center p-4 bg-orange-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">4</div>
                        <div>
                            <h4 class="font-semibold text-orange-800">连接设置</h4>
                            <p class="text-sm text-orange-600">设置模型的连接参数</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-red-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">5</div>
                        <div>
                            <h4 class="font-semibold text-red-800">分析配置</h4>
                            <p class="text-sm text-red-600">配置分析参数</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-indigo-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">6</div>
                        <div>
                            <h4 class="font-semibold text-indigo-800">约束定义</h4>
                            <p class="text-sm text-indigo-600">定义约束条件</p>
                        </div>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center p-4 bg-teal-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-teal-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">7</div>
                        <div>
                            <h4 class="font-semibold text-teal-800">运行分析</h4>
                            <p class="text-sm text-teal-600">运行分析</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-pink-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-pink-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">8</div>
                        <div>
                            <h4 class="font-semibold text-pink-800">查看结果</h4>
                            <p class="text-sm text-pink-600">查看结果并进行振动分析</p>
                        </div>
                    </div>
                    <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0 w-8 h-8 bg-gray-600 text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">9</div>
                        <div>
                            <h4 class="font-semibold text-gray-800">导出报告</h4>
                            <p class="text-sm text-gray-600">导出分析报告</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- New Features v0.2.1 -->
        <section id="new-features" class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <span class="inline-block bg-blue-100 text-blue-800 text-sm font-semibold px-4 py-1 rounded-full">v0.2.1 版本</span>
                <h2 class="text-3xl font-bold text-gray-800 mt-2">🚀 最新功能亮点</h2>
                <p class="text-gray-600 mt-2 max-w-2xl mx-auto">探索旨在简化工作流程并提高精度的最新增强功能</p>
            </div>
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>ANSYS四合一自动化脚本
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">全新的智能化ANSYS Workbench自动化处理系统，集成四大核心功能：</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">🧹 任务1: 清理数字命名选择</h4>
                                <p class="text-sm">自动扫描并清理数字命名的选择，智能识别需要清理的命名模式</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">🔧 任务2: 创建/更新命名选择</h4>
                                <p class="text-sm">根据几何体名称自动创建命名选择，支持ROTOR等关键组件识别</p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">📝 任务3: 统一命名规范</h4>
                                <p class="text-sm">将所有命名选择统一为小写格式，智能处理命名冲突</p>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">📤 任务4: 导出面命名选择</h4>
                                <p class="text-sm">自动识别并导出指定的面命名选择到JSON格式</p>
                            </div>
                        </div>
                        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                            <p class="text-sm"><strong>✨ 特色功能：</strong>7步骤实时进度监控、智能关键词匹配和状态更新</p>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌐</span>多编码处理系统
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">完整的编码兼容性和中文支持，解决所有编码显示问题：</p>
                        <div class="grid md:grid-cols-3 gap-4 mb-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl mb-2">🔤</div>
                                <h4 class="font-semibold text-blue-800">UTF-8</h4>
                                <p class="text-sm">统一编码标准</p>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl mb-2">🔄</div>
                                <h4 class="font-semibold text-green-800">智能转换</h4>
                                <p class="text-sm">自动检测GBK、CP936</p>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-2xl mb-2">🛡️</div>
                                <h4 class="font-semibold text-purple-800">错误恢复</h4>
                                <p class="text-sm">智能编码错误修复</p>
                            </div>
                        </div>
                        <p class="text-sm bg-green-50 p-3 rounded-lg">✅ 中文日志完美显示，实时监控多编码日志文件</p>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📍</span>监控点管理系统
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">完整的监控点管理和文件处理解决方案：</p>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <span class="text-blue-600 mr-3">📁</span>
                                <span><strong>文件格式支持：</strong>JSON和TXT格式监控点文件</span>
                            </div>
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <span class="text-green-600 mr-3">✅</span>
                                <span><strong>数据验证：</strong>自动数据验证和格式检查</span>
                            </div>
                            <div class="flex items-center p-3 bg-purple-50 rounded-lg">
                                <span class="text-purple-600 mr-3">🧹</span>
                                <span><strong>智能清理：</strong>智能文件清理机制</span>
                            </div>
                            <div class="flex items-center p-3 bg-orange-50 rounded-lg">
                                <span class="text-orange-600 mr-3">👁️</span>
                                <span><strong>实时预览：</strong>监控点数据实时预览和版本化管理</span>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🛡️</span>线程安全架构
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">增强的多线程处理和安全机制，确保系统稳定运行：</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <div class="flex items-center p-2 bg-blue-50 rounded">
                                    <span class="text-blue-600 mr-2">🧵</span>
                                    <span class="text-sm">LogMonitorThread实时日志监控</span>
                                </div>
                                <div class="flex items-center p-2 bg-green-50 rounded">
                                    <span class="text-green-600 mr-2">📊</span>
                                    <span class="text-sm">ANSYS日志文件监控线程</span>
                                </div>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center p-2 bg-purple-50 rounded">
                                    <span class="text-purple-600 mr-2">⚡</span>
                                    <span class="text-sm">进程输出捕获线程</span>
                                </div>
                                <div class="flex items-center p-2 bg-orange-50 rounded">
                                    <span class="text-orange-600 mr-2">🔒</span>
                                    <span class="text-sm">线程安全的UI更新机制</span>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm bg-gray-50 p-3 rounded-lg mt-4">🔧 完整的跨线程错误处理，消除数据竞争风险</p>
                    </div>
                </details>
            </div>

            <div class="mt-8 text-center">
                <div class="inline-flex flex-wrap gap-3">
                    <a href="pre_modern.html" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">四合一脚本详情</a>
                    <a href="troubleshooting_modern.html" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">故障排查指南</a>
                    <a href="constrain_modern.html" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">监控点管理</a>
                    <a href="encoding_guide.html" class="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300">编码处理技术</a>
                </div>
            </div>
        </section>

        <!-- Detailed Quick Guide -->
        <section id="quick-guide" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">📋 ANSYS四合一自动化快速指南</h2>
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">🔧</span>四合一自动化脚本
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-start p-3 bg-blue-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                            <div>
                                <h4 class="font-semibold text-blue-800">启动前处理模块</h4>
                                <p class="text-sm text-blue-600">在主界面选择"前处理"模块</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-green-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                            <div>
                                <h4 class="font-semibold text-green-800">配置ANSYS路径</h4>
                                <p class="text-sm text-green-600">确认ANSYS Workbench路径配置正确</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-purple-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                            <div>
                                <h4 class="font-semibold text-purple-800">执行自动化脚本</h4>
                                <p class="text-sm text-purple-600">点击"执行脚本"按钮启动四合一自动化</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-orange-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-orange-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                            <div>
                                <h4 class="font-semibold text-orange-800">监控执行进度</h4>
                                <p class="text-sm text-orange-600">实时查看7步骤进度状态和中文日志</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-red-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
                            <div>
                                <h4 class="font-semibold text-red-800">查看执行结果</h4>
                                <p class="text-sm text-red-600">检查生成的JSON面选择文件和ANSYS模型</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                        <span class="text-2xl mr-3">📍</span>监控点管理
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-start p-3 bg-indigo-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</span>
                            <div>
                                <h4 class="font-semibold text-indigo-800">访问约束设置界面</h4>
                                <p class="text-sm text-indigo-600">在主界面选择"约束设置"模块</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-teal-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-teal-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</span>
                            <div>
                                <h4 class="font-semibold text-teal-800">导入监控点文件</h4>
                                <p class="text-sm text-teal-600">点击"导入"按钮选择JSON或TXT格式文件</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-pink-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-pink-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</span>
                            <div>
                                <h4 class="font-semibold text-pink-800">验证数据格式</h4>
                                <p class="text-sm text-pink-600">系统自动验证坐标数据和格式</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-yellow-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-yellow-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</span>
                            <div>
                                <h4 class="font-semibold text-yellow-800">预览监控点</h4>
                                <p class="text-sm text-yellow-600">在表格中查看导入的监控点数据</p>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-gray-50 rounded-lg">
                            <span class="flex-shrink-0 w-6 h-6 bg-gray-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">5</span>
                            <div>
                                <h4 class="font-semibold text-gray-800">应用设置</h4>
                                <p class="text-sm text-gray-600">确认数据无误后应用监控点设置</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="text-xl mr-3">🌐</span>编码处理提示
                </h3>
                <p class="text-gray-600 mb-4">系统已内置多编码处理机制，自动处理中文显示问题：</p>
                <div class="grid md:grid-cols-4 gap-4">
                    <div class="text-center p-3 bg-white rounded-lg">
                        <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-sm font-bold">UTF</span>
                        </div>
                        <h4 class="font-semibold text-green-800 text-sm">UTF-8</h4>
                        <p class="text-xs text-green-600">统一编码标准</p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-sm font-bold">GBK</span>
                        </div>
                        <h4 class="font-semibold text-blue-800 text-sm">GBK</h4>
                        <p class="text-xs text-blue-600">自动检测转换</p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-sm font-bold">936</span>
                        </div>
                        <h4 class="font-semibold text-purple-800 text-sm">CP936</h4>
                        <p class="text-xs text-purple-600">Windows兼容</p>
                    </div>
                    <div class="text-center p-3 bg-white rounded-lg">
                        <div class="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">
                            <span class="text-sm font-bold">🔧</span>
                        </div>
                        <h4 class="font-semibold text-orange-800 text-sm">自动恢复</h4>
                        <p class="text-xs text-orange-600">错误修复机制</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Documentation -->
        <section id="technical-docs" class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6 text-center">📚 技术文档</h2>
            <p class="text-center text-gray-600 mb-6">深入了解软件的技术特性和高级功能</p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="relative group">
                    <a href="api_modern.html" class="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-100 hover:from-green-100 hover:to-emerald-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-green-500 text-white rounded-full mb-3 group-hover:bg-green-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-green-800 group-hover:text-green-900">API接口文档</h4>
                        <p class="text-sm text-green-600">RESTful API集成系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-green-200 text-green-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="api.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="i18n_modern.html" class="text-center p-4 bg-gradient-to-br from-indigo-50 to-purple-100 hover:from-indigo-100 hover:to-purple-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-indigo-500 text-white rounded-full mb-3 group-hover:bg-indigo-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-indigo-800 group-hover:text-indigo-900">国际化支持</h4>
                        <p class="text-sm text-indigo-600">多语言本地化系统</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-indigo-200 text-indigo-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="i18n.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="thread_safety_modern.html" class="text-center p-4 bg-gradient-to-br from-violet-50 to-purple-100 hover:from-violet-100 hover:to-purple-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-violet-500 text-white rounded-full mb-3 group-hover:bg-violet-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-violet-800 group-hover:text-violet-900">线程安全架构</h4>
                        <p class="text-sm text-violet-600">高并发稳定性保障</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-violet-200 text-violet-800 text-xs rounded-full">现代版</span>
                    </a>
                    <a href="thread_safety.html" class="absolute top-2 right-2 text-xs text-gray-500 hover:text-gray-700 bg-white px-2 py-1 rounded shadow">经典版</a>
                </div>

                <div class="relative group">
                    <a href="encoding_guide.html" class="text-center p-4 bg-gradient-to-br from-orange-50 to-amber-100 hover:from-orange-100 hover:to-amber-200 rounded-lg transition duration-300 ease-in-out hover:shadow-lg group block">
                        <div class="flex items-center justify-center h-16 w-16 mx-auto bg-orange-500 text-white rounded-full mb-3 group-hover:bg-orange-600 transition duration-300 shadow-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-orange-800 group-hover:text-orange-900">编码处理技术</h4>
                        <p class="text-sm text-orange-600">多编码兼容解决方案</p>
                        <span class="inline-block mt-2 px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded-full">现代版</span>
                    </a>
                </div>
            </div>

            <div class="mt-8 text-center">
                <div class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                    <span class="text-sm font-medium text-gray-700">现代版技术文档提供更详细的说明和示例</span>
                </div>
            </div>
        </section>

        <!-- Important Notes and Tips -->
        <section class="grid md:grid-cols-3 gap-6 mb-12 scroll-reveal">
            <div class="section-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">重要提示</h3>
                </div>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                        <span class="text-blue-500 mr-2 mt-1">•</span>
                        <span>使用软件前请确保已正确安装所有必要的组件</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-500 mr-2 mt-1">•</span>
                        <span>定期保存项目文件，避免数据丢失</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-500 mr-2 mt-1">•</span>
                        <span>参数设置时注意单位的一致性</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-blue-500 mr-2 mt-1">•</span>
                        <span>如遇问题，请参考各界面的帮助文档或联系技术支持</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">✨</span>
                        <span><strong>新功能</strong>：线程安全机制自动启用，无需手动配置</span>
                    </li>
                </ul>
            </div>

            <div class="section-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">常见问题</h3>
                </div>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                        <span class="text-orange-500 mr-2 mt-1">⚠️</span>
                        <span>工作目录不支持中文路径，请使用英文路径</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-orange-500 mr-2 mt-1">⚠️</span>
                        <span>确保ANSYS Workbench已正确安装并配置</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-orange-500 mr-2 mt-1">⚠️</span>
                        <span>程序运行前需完成ANSYS路径配置</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-orange-500 mr-2 mt-1">⚠️</span>
                        <span>首次使用时可能需要管理员权限</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-orange-500 mr-2 mt-1">⚠️</span>
                        <span>API服务器默认使用8000端口，确保该端口未被占用</span>
                    </li>
                </ul>
            </div>

            <div class="section-card p-6">
                <div class="flex items-center mb-4">
                    <div class="w-10 h-10 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">新版本特性</h3>
                </div>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">🛡️</span>
                        <span><strong>线程安全</strong>：消除API线程与UI线程数据竞争</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">🌐</span>
                        <span><strong>多编码处理</strong>：完美支持中文显示</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">🎨</span>
                        <span><strong>现代化UI</strong>：全新的帮助系统界面设计</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">📊</span>
                        <span><strong>增强分析</strong>：更强大的振动数据分析功能</span>
                    </li>
                    <li class="flex items-start">
                        <span class="text-green-500 mr-2 mt-1">🔧</span>
                        <span><strong>四合一脚本</strong>：智能化ANSYS自动化处理</span>
                    </li>
                </ul>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">基于现代化技术栈构建的专业ANSYS Workbench自动化平台</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html> 