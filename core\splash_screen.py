"""
启动画面模块

此模块提供了一个现代化的启动画面实现，支持：
1. 自定义背景和logo
2. 进度条和状态文本显示
3. 淡入淡出动画效果
4. 高DPI显示支持
5. 旋转动画效果

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
from typing import Optional, Dict, Any
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Signal, QRect
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QPen, QBrush, QLinearGradient
from PySide6.QtWidgets import QSplashScreen, QApplication


class CustomSplashScreen(QSplashScreen):
    """自定义启动画面类
    
    提供现代化的启动画面，包含logo、进度条、状态文本和动画效果
    """
    
    # 信号定义
    progress_updated = Signal(int, str)  # 进度值, 状态文本
    
    def __init__(self, pixmap: Optional[QPixmap] = None, config: Optional[Dict[str, Any]] = None, parent=None):
        """初始化启动画面

        Args:
            pixmap: 背景图片，如果为None则使用默认背景
            config: 配置字典
            parent: 父窗口
        """
        # 加载配置
        self.config = config or self._get_default_config()

        # 如果没有提供背景图片，创建默认背景
        if pixmap is None:
            pixmap = self._create_default_background()

        super().__init__(pixmap, Qt.WindowStaysOnTopHint)

        # 启动画面属性
        self.progress = 0
        self.status_text = "正在启动应用程序..."
        self.rotation_angle = 0

        # 动画相关
        self.fade_animation = None
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self._update_rotation)

        # 从配置加载设置
        self._load_settings_from_config()

        # 设置窗口属性
        layout_config = self.config.get("layout", {})
        width = layout_config.get("width", 480)
        height = layout_config.get("height", 320)
        self.setFixedSize(width, height)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 连接信号
        self.progress_updated.connect(self._on_progress_updated)

        # 启动旋转动画（如果启用）
        if self.config.get("show_rotation_animation", True):
            self.start_rotation_animation()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置

        Returns:
            Dict[str, Any]: 默认配置
        """
        return {
            "colors": {
                "primary": "#3498db",
                "secondary": "#2ecc71",
                "text": "#34495e",
                "progress_background": "#ecf0f1"
            },
            "fonts": {
                "title_size": 16,
                "version_size": 10,
                "status_size": 9,
                "font_family": "Arial"
            },
            "layout": {
                "width": 480,
                "height": 320
            },
            "show_rotation_animation": True,
            "show_company_info": True
        }

    def _load_settings_from_config(self):
        """从配置加载设置"""
        # 字体设置
        font_config = self.config.get("fonts", {})
        font_family = font_config.get("font_family", "Arial")
        title_size = font_config.get("title_size", 16)
        version_size = font_config.get("version_size", 10)
        status_size = font_config.get("status_size", 9)

        self.title_font = QFont(font_family, title_size, QFont.Bold)
        self.version_font = QFont(font_family, version_size)
        self.status_font = QFont(font_family, status_size)

        # 颜色设置
        color_config = self.config.get("colors", {})
        self.primary_color = QColor(color_config.get("primary", "#3498db"))
        self.secondary_color = QColor(color_config.get("secondary", "#2ecc71"))
        self.text_color = QColor(color_config.get("text", "#34495e"))
        self.progress_bg_color = QColor(color_config.get("progress_background", "#ecf0f1"))

        # 应用程序信息
        self.app_name = "振动传递计算软件"
        self.app_version = "v1.2.0"
        self.company_name = "振动传递计算软件开发团队"
    
    def _create_default_background(self) -> QPixmap:
        """创建默认背景图片
        
        Returns:
            QPixmap: 默认背景图片
        """
        pixmap = QPixmap(480, 320)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建渐变背景
        gradient = QLinearGradient(0, 0, 480, 320)
        gradient.setColorAt(0, QColor(52, 152, 219, 240))  # 蓝色，半透明
        gradient.setColorAt(1, QColor(46, 204, 113, 240))  # 绿色，半透明
        
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, 480, 320, 15, 15)
        
        painter.end()
        return pixmap
    
    def _on_progress_updated(self, progress: int, status: str):
        """处理进度更新信号
        
        Args:
            progress: 进度值 (0-100)
            status: 状态文本
        """
        self.progress = progress
        self.status_text = status
        self.repaint()
    
    def update_progress(self, progress: int, status: str = ""):
        """更新进度和状态文本
        
        Args:
            progress: 进度值 (0-100)
            status: 状态文本
        """
        if status:
            self.status_text = status
        self.progress = max(0, min(100, progress))
        self.progress_updated.emit(self.progress, self.status_text)
        QApplication.processEvents()  # 确保界面更新
    
    def start_rotation_animation(self):
        """启动旋转动画"""
        self.rotation_timer.start(50)  # 每50ms更新一次
    
    def stop_rotation_animation(self):
        """停止旋转动画"""
        self.rotation_timer.stop()
    
    def _update_rotation(self):
        """更新旋转角度"""
        self.rotation_angle = (self.rotation_angle + 3) % 360
        self.repaint()
    
    def paintEvent(self, event):
        """重写绘制事件"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制logo区域
        self._draw_logo(painter)
        
        # 绘制应用信息
        self._draw_app_info(painter)
        
        # 绘制进度条
        self._draw_progress_bar(painter)
        
        # 绘制状态文本
        self._draw_status_text(painter)
        
        # 绘制旋转动画
        self._draw_rotation_animation(painter)
    
    def _draw_logo(self, painter: QPainter):
        """绘制logo"""
        # 尝试加载应用图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                "assets", "icons", "vibration_transfer_icon_alt.ico")
        
        if os.path.exists(icon_path):
            logo_pixmap = QPixmap(icon_path)
            if not logo_pixmap.isNull():
                # 缩放logo到合适大小
                logo_size = 64
                logo_pixmap = logo_pixmap.scaled(logo_size, logo_size, 
                                               Qt.KeepAspectRatio, Qt.SmoothTransformation)
                
                # 居中绘制logo
                logo_x = (self.width() - logo_size) // 2
                logo_y = 40
                painter.drawPixmap(logo_x, logo_y, logo_pixmap)
                return
        
        # 如果没有图标文件，绘制默认logo
        painter.setPen(QPen(self.primary_color, 3))
        painter.setBrush(QBrush(self.primary_color))
        
        # 绘制一个简单的圆形logo
        logo_rect = QRect(self.width()//2 - 32, 40, 64, 64)
        painter.drawEllipse(logo_rect)
        
        # 在圆形中绘制文字
        painter.setPen(QPen(Qt.white))
        painter.setFont(QFont("Arial", 20, QFont.Bold))
        painter.drawText(logo_rect, Qt.AlignCenter, "VT")
    
    def _draw_app_info(self, painter: QPainter):
        """绘制应用程序信息"""
        # 应用名称
        painter.setFont(self.title_font)
        painter.setPen(QPen(self.text_color))
        title_rect = QRect(0, 120, self.width(), 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.app_name)
        
        # 版本信息
        painter.setFont(self.version_font)
        painter.setPen(QPen(self.text_color.lighter(120)))
        version_rect = QRect(0, 150, self.width(), 20)
        painter.drawText(version_rect, Qt.AlignCenter, self.app_version)
        
        # 公司信息
        company_rect = QRect(0, 170, self.width(), 20)
        painter.drawText(company_rect, Qt.AlignCenter, self.company_name)
    
    def _draw_progress_bar(self, painter: QPainter):
        """绘制进度条"""
        # 进度条背景
        progress_rect = QRect(60, 220, self.width() - 120, 8)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(self.progress_bg_color))
        painter.drawRoundedRect(progress_rect, 4, 4)
        
        # 进度条前景
        if self.progress > 0:
            progress_width = int((self.width() - 120) * self.progress / 100)
            progress_fill_rect = QRect(60, 220, progress_width, 8)
            
            # 创建进度条渐变
            gradient = QLinearGradient(60, 220, 60 + progress_width, 220)
            gradient.setColorAt(0, self.primary_color)
            gradient.setColorAt(1, self.secondary_color)
            
            painter.setBrush(QBrush(gradient))
            painter.drawRoundedRect(progress_fill_rect, 4, 4)
        
        # 进度百分比
        painter.setFont(self.status_font)
        painter.setPen(QPen(self.text_color))
        percent_rect = QRect(0, 235, self.width(), 15)
        painter.drawText(percent_rect, Qt.AlignCenter, f"{self.progress}%")
    
    def _draw_status_text(self, painter: QPainter):
        """绘制状态文本"""
        painter.setFont(self.status_font)
        painter.setPen(QPen(self.text_color.lighter(130)))
        status_rect = QRect(20, 260, self.width() - 40, 20)
        painter.drawText(status_rect, Qt.AlignCenter, self.status_text)
    
    def _draw_rotation_animation(self, painter: QPainter):
        """绘制旋转动画"""
        # 在右下角绘制一个小的旋转指示器
        center_x = self.width() - 30
        center_y = self.height() - 30
        radius = 8
        
        painter.save()
        painter.translate(center_x, center_y)
        painter.rotate(self.rotation_angle)
        
        # 绘制旋转的圆弧
        painter.setPen(QPen(self.primary_color, 2))
        painter.drawArc(-radius, -radius, radius*2, radius*2, 0, 270*16)
        
        painter.restore()
    
    def show_with_fade_in(self, duration: int = 500):
        """显示启动画面并淡入
        
        Args:
            duration: 淡入持续时间（毫秒）
        """
        self.setWindowOpacity(0.0)
        self.show()
        
        # 创建淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(duration)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()
    
    def hide_with_fade_out(self, duration: int = 300):
        """隐藏启动画面并淡出
        
        Args:
            duration: 淡出持续时间（毫秒）
        """
        self.stop_rotation_animation()
        
        # 创建淡出动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(duration)
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.setEasingCurve(QEasingCurve.InCubic)
        self.fade_animation.finished.connect(self.close)
        self.fade_animation.start()


def create_splash_screen(config: Optional[Dict[str, Any]] = None) -> CustomSplashScreen:
    """创建启动画面实例

    Args:
        config: 配置字典

    Returns:
        CustomSplashScreen: 启动画面实例
    """
    return CustomSplashScreen(config=config)


class SplashScreenManager:
    """启动画面管理器

    负责管理启动画面的生命周期和进度更新
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.splash = None
        self.start_time = None
        self.config = config or {}
        self.minimum_display_time = self.config.get("minimum_display_time", 2000)  # 最小显示时间（毫秒）
        self.progress_steps = []
        self.current_step = 0

        # 定义初始化步骤和对应的进度
        self.init_steps = [
            (5, "初始化日志系统..."),
            (10, "创建应用程序实例..."),
            (15, "设置应用程序图标..."),
            (20, "配置异常处理..."),
            (30, "加载关键样式..."),
            (35, "初始化字体管理器..."),
            (40, "加载应用程序样式..."),
            (50, "加载初始配置数据..."),
            (60, "创建窗口管理器..."),
            (70, "创建应用程序窗口..."),
            (80, "注册窗口工厂..."),
            (85, "应用初始数据..."),
            (90, "启动API服务器..."),
            (95, "初始化槽函数..."),
            (100, "启动完成！")
        ]

    def show_splash(self) -> CustomSplashScreen:
        """显示启动画面

        Returns:
            CustomSplashScreen: 启动画面实例
        """
        import time
        self.start_time = time.time()

        self.splash = create_splash_screen(self.config)

        # 根据配置决定是否使用淡入效果
        if self.config.get("show_fade_in", True):
            fade_duration = self.config.get("fade_in_duration", 500)
            self.splash.show_with_fade_in(fade_duration)
        else:
            self.splash.show()

        return self.splash

    def update_progress(self, step_name: str):
        """根据步骤名称更新进度

        Args:
            step_name: 步骤名称
        """
        if not self.splash:
            return

        # 查找匹配的步骤
        for progress, status in self.init_steps:
            if step_name in status or any(keyword in step_name.lower() for keyword in
                                        ["日志", "应用", "图标", "异常", "样式", "字体", "配置",
                                         "窗口", "工厂", "数据", "api", "槽"]):
                self.splash.update_progress(progress, status)
                break

    def update_progress_by_percentage(self, percentage: int, status: str = ""):
        """直接通过百分比更新进度

        Args:
            percentage: 进度百分比 (0-100)
            status: 状态文本
        """
        if self.splash:
            self.splash.update_progress(percentage, status)

    def hide_splash(self):
        """隐藏启动画面"""
        if not self.splash:
            return

        import time

        # 确保最小显示时间
        elapsed_time = (time.time() - self.start_time) * 1000
        if elapsed_time < self.minimum_display_time:
            remaining_time = self.minimum_display_time - elapsed_time
            QTimer.singleShot(int(remaining_time), self._do_hide_splash)
        else:
            self._do_hide_splash()

    def _do_hide_splash(self):
        """执行隐藏启动画面"""
        if self.splash:
            self.splash.hide_with_fade_out()
            self.splash = None


# 全局启动画面管理器实例
_splash_manager = None


def get_splash_manager() -> SplashScreenManager:
    """获取全局启动画面管理器实例

    Returns:
        SplashScreenManager: 启动画面管理器实例
    """
    global _splash_manager
    if _splash_manager is None:
        _splash_manager = SplashScreenManager()
    return _splash_manager
