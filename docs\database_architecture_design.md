# 数据库架构设计文档

## 🎯 架构概述

### **混合数据库架构**
采用PostgreSQL + Redis + 文件存储的混合架构，充分发挥各种存储技术的优势。

```
应用层 (PySide6/Qt)
    ↓
业务逻辑层 (Python Services)
    ↓
数据访问层 (SQLAlchemy ORM + Repository)
    ↓
数据存储层
├── PostgreSQL (结构化数据)
├── Redis (缓存和会话)
└── 文件系统 (大文件存储)
```

## 🗄️ 数据库选型决策

### **PostgreSQL - 主数据库**
**选择理由**:
- 优秀的数值计算支持（NUMERIC类型精度高）
- 强大的JSON/JSONB支持，适合工程数据
- 丰富的索引类型（B-tree、GiST、GIN等）
- 成熟的Python生态系统支持

**配置建议**:
```sql
-- 针对工程计算优化的PostgreSQL配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 16MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

### **Redis - 缓存层**
**使用场景**:
- 会话管理
- 计算结果缓存
- 实时数据交换
- 任务队列

## 📊 数据模型设计

### **核心实体关系图**
```
Projects (项目)
    ├── Analyses (分析)
    │   ├── Configurations (配置)
    │   ├── Results (结果)
    │   └── Files (文件)
    ├── Meshes (网格)
    │   ├── Nodes (节点)
    │   └── Elements (单元)
    └── Materials (材料)
```

### **详细表结构设计**

#### **项目管理表**
```sql
-- 项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    metadata JSONB,
    CONSTRAINT unique_project_name_user UNIQUE(name, created_by)
);

-- 分析表
CREATE TABLE analyses (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL, -- 'modal', 'harmonic', 'transient'
    status VARCHAR(50) DEFAULT 'pending',
    configuration JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_analyses_project_type (project_id, type),
    INDEX idx_analyses_status (status)
);
```

#### **网格数据表**
```sql
-- 网格表
CREATE TABLE meshes (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    element_type VARCHAR(50), -- 'SOLID186', 'SHELL181', etc.
    node_count INTEGER,
    element_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    file_path VARCHAR(500),
    metadata JSONB
);

-- 节点表（大数据量，考虑分区）
CREATE TABLE mesh_nodes (
    id BIGSERIAL PRIMARY KEY,
    mesh_id INTEGER REFERENCES meshes(id) ON DELETE CASCADE,
    node_id INTEGER NOT NULL,
    x DOUBLE PRECISION NOT NULL,
    y DOUBLE PRECISION NOT NULL,
    z DOUBLE PRECISION NOT NULL,
    CONSTRAINT unique_mesh_node UNIQUE(mesh_id, node_id)
) PARTITION BY HASH (mesh_id);

-- 创建分区表
CREATE TABLE mesh_nodes_p0 PARTITION OF mesh_nodes FOR VALUES WITH (modulus 4, remainder 0);
CREATE TABLE mesh_nodes_p1 PARTITION OF mesh_nodes FOR VALUES WITH (modulus 4, remainder 1);
CREATE TABLE mesh_nodes_p2 PARTITION OF mesh_nodes FOR VALUES WITH (modulus 4, remainder 2);
CREATE TABLE mesh_nodes_p3 PARTITION OF mesh_nodes FOR VALUES WITH (modulus 4, remainder 3);
```

#### **计算结果表**
```sql
-- 分析结果表
CREATE TABLE analysis_results (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES analyses(id) ON DELETE CASCADE,
    result_type VARCHAR(100) NOT NULL, -- 'frequency', 'displacement', 'stress'
    step_number INTEGER,
    frequency DOUBLE PRECISION,
    data_summary JSONB, -- 统计信息
    file_path VARCHAR(500), -- 大数据文件路径
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_results_analysis_type (analysis_id, result_type),
    INDEX idx_results_frequency (frequency)
);

-- 节点结果表（时间序列数据）
CREATE TABLE node_results (
    id BIGSERIAL PRIMARY KEY,
    result_id INTEGER REFERENCES analysis_results(id) ON DELETE CASCADE,
    node_id INTEGER NOT NULL,
    time_step DOUBLE PRECISION,
    displacement_x DOUBLE PRECISION,
    displacement_y DOUBLE PRECISION,
    displacement_z DOUBLE PRECISION,
    stress_von_mises DOUBLE PRECISION,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);

-- 按月分区
CREATE TABLE node_results_2024_01 PARTITION OF node_results 
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### **索引策略**
```sql
-- 复合索引优化查询
CREATE INDEX idx_analyses_project_status_type ON analyses(project_id, status, type);
CREATE INDEX idx_results_analysis_step ON analysis_results(analysis_id, step_number);
CREATE INDEX idx_node_results_result_node ON node_results(result_id, node_id);

-- 部分索引优化存储
CREATE INDEX idx_active_projects ON projects(id) WHERE status = 'active';
CREATE INDEX idx_completed_analyses ON analyses(id) WHERE status = 'completed';

-- GIN索引支持JSONB查询
CREATE INDEX idx_project_metadata_gin ON projects USING GIN (metadata);
CREATE INDEX idx_analysis_config_gin ON analyses USING GIN (configuration);
```

## 🔧 数据访问层架构

### **Repository模式实现**
```python
# core/database/repositories/base.py
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Optional
from sqlalchemy.orm import Session

T = TypeVar('T')

class BaseRepository(Generic[T], ABC):
    def __init__(self, session: Session, model_class: type):
        self.session = session
        self.model_class = model_class
    
    def create(self, entity: T) -> T:
        self.session.add(entity)
        self.session.commit()
        self.session.refresh(entity)
        return entity
    
    def get_by_id(self, id: int) -> Optional[T]:
        return self.session.query(self.model_class).filter(
            self.model_class.id == id
        ).first()
    
    def get_all(self, limit: int = 100, offset: int = 0) -> List[T]:
        return self.session.query(self.model_class)\
            .offset(offset).limit(limit).all()
    
    def update(self, entity: T) -> T:
        self.session.commit()
        self.session.refresh(entity)
        return entity
    
    def delete(self, entity: T) -> None:
        self.session.delete(entity)
        self.session.commit()

# core/database/repositories/project_repository.py
class ProjectRepository(BaseRepository[Project]):
    def __init__(self, session: Session):
        super().__init__(session, Project)
    
    def get_by_user(self, user_id: int) -> List[Project]:
        return self.session.query(Project)\
            .filter(Project.created_by == user_id)\
            .order_by(Project.updated_at.desc()).all()
    
    def search_by_name(self, name_pattern: str) -> List[Project]:
        return self.session.query(Project)\
            .filter(Project.name.ilike(f'%{name_pattern}%')).all()
```

### **服务层设计**
```python
# core/services/project_service.py
from typing import List, Optional
from core.database.repositories.project_repository import ProjectRepository
from core.database.models import Project
from core.cache.redis_client import RedisClient

class ProjectService:
    def __init__(self, project_repo: ProjectRepository, cache: RedisClient):
        self.project_repo = project_repo
        self.cache = cache
    
    def create_project(self, name: str, description: str, user_id: int) -> Project:
        """创建新项目"""
        project = Project(
            name=name,
            description=description,
            created_by=user_id
        )
        
        # 保存到数据库
        created_project = self.project_repo.create(project)
        
        # 更新缓存
        cache_key = f"user_projects:{user_id}"
        self.cache.delete(cache_key)
        
        return created_project
    
    def get_user_projects(self, user_id: int) -> List[Project]:
        """获取用户项目列表（带缓存）"""
        cache_key = f"user_projects:{user_id}"
        
        # 尝试从缓存获取
        cached_projects = self.cache.get_json(cache_key)
        if cached_projects:
            return [Project.from_dict(p) for p in cached_projects]
        
        # 从数据库获取
        projects = self.project_repo.get_by_user(user_id)
        
        # 缓存结果
        self.cache.set_json(
            cache_key, 
            [p.to_dict() for p in projects],
            expire=300  # 5分钟过期
        )
        
        return projects
```

## 🚀 缓存策略设计

### **多级缓存架构**
```python
# core/cache/cache_manager.py
from typing import Any, Optional
import json
import redis
from functools import wraps

class CacheManager:
    def __init__(self, redis_client: redis.Redis):
        self.redis = redis_client
        self.local_cache = {}  # 进程内缓存
        self.local_cache_size = 1000
    
    def get(self, key: str) -> Optional[Any]:
        """多级缓存获取"""
        # 1. 检查本地缓存
        if key in self.local_cache:
            return self.local_cache[key]
        
        # 2. 检查Redis缓存
        value = self.redis.get(key)
        if value:
            decoded_value = json.loads(value)
            # 更新本地缓存
            self._update_local_cache(key, decoded_value)
            return decoded_value
        
        return None
    
    def set(self, key: str, value: Any, expire: int = 3600):
        """设置缓存"""
        # 更新Redis
        self.redis.setex(key, expire, json.dumps(value))
        # 更新本地缓存
        self._update_local_cache(key, value)
    
    def _update_local_cache(self, key: str, value: Any):
        """更新本地缓存（LRU策略）"""
        if len(self.local_cache) >= self.local_cache_size:
            # 删除最旧的项
            oldest_key = next(iter(self.local_cache))
            del self.local_cache[oldest_key]
        
        self.local_cache[key] = value

# 缓存装饰器
def cached(expire: int = 3600, key_prefix: str = ""):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试获取缓存
            cache_manager = get_cache_manager()
            result = cache_manager.get(cache_key)
            
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, expire)
            
            return result
        return wrapper
    return decorator
```

## 📈 性能优化策略

### **数据库连接池配置**
```python
# core/database/connection.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from sqlalchemy.orm import sessionmaker

class DatabaseManager:
    def __init__(self, database_url: str):
        self.engine = create_engine(
            database_url,
            poolclass=QueuePool,
            pool_size=20,          # 连接池大小
            max_overflow=30,       # 最大溢出连接
            pool_pre_ping=True,    # 连接前检查
            pool_recycle=3600,     # 连接回收时间
            echo=False             # 生产环境关闭SQL日志
        )
        
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def get_session(self):
        """获取数据库会话"""
        session = self.SessionLocal()
        try:
            yield session
        finally:
            session.close()
```

### **批量操作优化**
```python
# core/database/bulk_operations.py
from sqlalchemy.dialects.postgresql import insert
from typing import List, Dict, Any

class BulkOperations:
    def __init__(self, session):
        self.session = session
    
    def bulk_insert_nodes(self, mesh_id: int, nodes_data: List[Dict[str, Any]]):
        """批量插入节点数据"""
        # 准备数据
        insert_data = [
            {
                'mesh_id': mesh_id,
                'node_id': node['id'],
                'x': node['x'],
                'y': node['y'],
                'z': node['z']
            }
            for node in nodes_data
        ]
        
        # 使用PostgreSQL的COPY命令进行批量插入
        stmt = insert(MeshNode).values(insert_data)
        
        # 处理冲突（如果需要）
        stmt = stmt.on_conflict_do_update(
            index_elements=['mesh_id', 'node_id'],
            set_=dict(x=stmt.excluded.x, y=stmt.excluded.y, z=stmt.excluded.z)
        )
        
        self.session.execute(stmt)
        self.session.commit()
    
    def bulk_insert_results(self, result_id: int, results_data: List[Dict[str, Any]]):
        """批量插入计算结果"""
        # 分批处理大数据量
        batch_size = 10000
        
        for i in range(0, len(results_data), batch_size):
            batch = results_data[i:i + batch_size]
            
            insert_data = [
                {
                    'result_id': result_id,
                    'node_id': result['node_id'],
                    'time_step': result['time_step'],
                    'displacement_x': result.get('displacement_x'),
                    'displacement_y': result.get('displacement_y'),
                    'displacement_z': result.get('displacement_z'),
                    'stress_von_mises': result.get('stress_von_mises')
                }
                for result in batch
            ]
            
            stmt = insert(NodeResult).values(insert_data)
            self.session.execute(stmt)
        
        self.session.commit()
```

## 🔄 数据同步和备份

### **实时数据同步**
```python
# core/sync/data_synchronizer.py
import asyncio
from typing import List
from core.database.models import Project, Analysis

class DataSynchronizer:
    def __init__(self, primary_db, backup_db):
        self.primary_db = primary_db
        self.backup_db = backup_db
    
    async def sync_project_changes(self, project_ids: List[int]):
        """同步项目变更"""
        for project_id in project_ids:
            # 获取主库数据
            project = self.primary_db.get_project(project_id)
            
            if project:
                # 同步到备份库
                await self.backup_db.upsert_project(project)
                
                # 同步相关分析数据
                analyses = self.primary_db.get_project_analyses(project_id)
                for analysis in analyses:
                    await self.backup_db.upsert_analysis(analysis)
    
    async def sync_analysis_results(self, analysis_id: int):
        """同步分析结果"""
        results = self.primary_db.get_analysis_results(analysis_id)
        
        # 分批同步大数据量
        batch_size = 1000
        for i in range(0, len(results), batch_size):
            batch = results[i:i + batch_size]
            await self.backup_db.bulk_insert_results(batch)
```

### **自动备份策略**
```python
# scripts/backup_manager.py
import subprocess
import datetime
import os
from pathlib import Path

class BackupManager:
    def __init__(self, db_config: dict, backup_dir: str):
        self.db_config = db_config
        self.backup_dir = Path(backup_dir)
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_full_backup(self):
        """创建完整数据库备份"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = self.backup_dir / f"full_backup_{timestamp}.sql"
        
        cmd = [
            "pg_dump",
            "-h", self.db_config["host"],
            "-p", str(self.db_config["port"]),
            "-U", self.db_config["username"],
            "-d", self.db_config["database"],
            "-f", str(backup_file),
            "--verbose"
        ]
        
        env = os.environ.copy()
        env["PGPASSWORD"] = self.db_config["password"]
        
        result = subprocess.run(cmd, env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"备份成功: {backup_file}")
            self._compress_backup(backup_file)
        else:
            print(f"备份失败: {result.stderr}")
    
    def _compress_backup(self, backup_file: Path):
        """压缩备份文件"""
        compressed_file = backup_file.with_suffix(".sql.gz")
        
        cmd = ["gzip", str(backup_file)]
        subprocess.run(cmd)
        
        print(f"备份已压缩: {compressed_file}")
    
    def cleanup_old_backups(self, keep_days: int = 30):
        """清理旧备份文件"""
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=keep_days)
        
        for backup_file in self.backup_dir.glob("*.sql.gz"):
            if backup_file.stat().st_mtime < cutoff_date.timestamp():
                backup_file.unlink()
                print(f"删除旧备份: {backup_file}")