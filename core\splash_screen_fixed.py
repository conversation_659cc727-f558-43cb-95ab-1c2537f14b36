"""
启动画面模块 - 修复版本

此模块提供了一个现代化的启动画面实现，支持：
1. 自定义背景和logo
2. 进度条和状态文本显示
3. 淡入淡出动画效果
4. 高DPI显示支持
5. 旋转动画效果

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import time
from typing import Optional, Dict, Any
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, Signal, QRect
from PySide6.QtGui import QPixmap, QPainter, QFont, QColor, QPen, QBrush, QLinearGradient
from PySide6.QtWidgets import QSplashScreen, QApplication


class CustomSplashScreen(QSplashScreen):
    """自定义启动画面类
    
    提供现代化的启动画面，包含logo、进度条、状态文本和动画效果
    """
    
    # 信号定义
    progress_updated = Signal(int, str)  # 进度值, 状态文本
    
    def __init__(self, pixmap: Optional[QPixmap] = None, config: Optional[Dict[str, Any]] = None, parent=None):
        """初始化启动画面
        
        Args:
            pixmap: 背景图片，如果为None则使用默认背景
            config: 配置字典
            parent: 父窗口
        """
        # 加载配置
        self.config = config or self._get_default_config()
        
        # 如果没有提供背景图片，创建默认背景
        if pixmap is None:
            pixmap = self._create_default_background()
            
        super().__init__(pixmap, Qt.WindowStaysOnTopHint)
        
        # 启动画面属性
        self.progress = 0
        self.status_text = "正在启动应用程序..."
        self.rotation_angle = 0
        
        # 动画相关
        self.fade_animation = None
        self.rotation_timer = QTimer()
        self.rotation_timer.timeout.connect(self._update_rotation)
        
        # 从配置加载设置
        self._load_settings_from_config()
        
        # 设置窗口属性
        layout_config = self.config.get("layout", {})
        width = layout_config.get("width", 480)
        height = layout_config.get("height", 320)
        self.setFixedSize(width, height)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 连接信号
        self.progress_updated.connect(self._on_progress_updated)
        
        # 启动旋转动画（如果启用）
        if self.config.get("show_rotation_animation", True):
            self.start_rotation_animation()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置
        
        Returns:
            Dict[str, Any]: 默认配置
        """
        return {
            "colors": {
                "primary": "#3498db",
                "secondary": "#2ecc71",
                "text": "#34495e",
                "progress_background": "#ecf0f1"
            },
            "fonts": {
                "title_size": 16,
                "version_size": 10,
                "status_size": 9,
                "font_family": "Arial"
            },
            "layout": {
                "width": 480,
                "height": 320
            },
            "show_rotation_animation": True,
            "show_company_info": True
        }
    
    def _load_settings_from_config(self):
        """从配置加载设置"""
        # 字体设置
        font_config = self.config.get("fonts", {})
        font_family = font_config.get("font_family", "Arial")
        title_size = font_config.get("title_size", 16)
        version_size = font_config.get("version_size", 10)
        status_size = font_config.get("status_size", 9)
        
        self.title_font = QFont(font_family, title_size, QFont.Bold)
        self.version_font = QFont(font_family, version_size)
        self.status_font = QFont(font_family, status_size)
        
        # 颜色设置
        color_config = self.config.get("colors", {})
        self.primary_color = QColor(color_config.get("primary", "#3498db"))
        self.secondary_color = QColor(color_config.get("secondary", "#2ecc71"))
        self.text_color = QColor(color_config.get("text", "#34495e"))
        self.progress_bg_color = QColor(color_config.get("progress_background", "#ecf0f1"))
        
        # 应用程序信息
        self.app_name = "振动传递计算软件"
        self.app_version = "v1.2.0"
        self.company_name = "振动传递计算软件开发团队"
    
    def _create_default_background(self) -> QPixmap:
        """创建默认背景图片

        Returns:
            QPixmap: 默认背景图片
        """
        layout_config = self.config.get("layout", {})
        width = layout_config.get("width", 480)
        height = layout_config.get("height", 320)

        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)

        painter = QPainter()
        if not painter.begin(pixmap):
            # 如果无法开始绘制，返回简单背景
            return self._create_simple_background(width, height)

        try:
            painter.setRenderHint(QPainter.Antialiasing)

            # 创建现代化的多层渐变背景
            self._draw_modern_gradient_background(painter, width, height)

            # 添加装饰性几何元素
            self._draw_decorative_elements(painter, width, height)

        except Exception as e:
            print(f"绘制背景时发生错误: {e}")
        finally:
            painter.end()

        return pixmap

    def _create_simple_background(self, width: int, height: int) -> QPixmap:
        """创建简单的备用背景

        Args:
            width: 宽度
            height: 高度

        Returns:
            QPixmap: 简单背景图片
        """
        pixmap = QPixmap(width, height)
        pixmap.fill(Qt.transparent)

        painter = QPainter()
        if painter.begin(pixmap):
            try:
                painter.setRenderHint(QPainter.Antialiasing)

                # 创建简单的渐变背景
                gradient = QLinearGradient(0, 0, width, height)
                gradient.setColorAt(0, QColor(52, 152, 219, 240))  # 蓝色
                gradient.setColorAt(1, QColor(46, 204, 113, 240))  # 绿色

                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.NoPen)
                painter.drawRoundedRect(0, 0, width, height, 15, 15)

            finally:
                painter.end()

        return pixmap

    def _draw_modern_gradient_background(self, painter: QPainter, width: int, height: int):
        """绘制现代化的渐变背景

        Args:
            painter: 绘制器
            width: 宽度
            height: 高度
        """
        # 主背景 - 深色到亮色的对角线渐变
        main_gradient = QLinearGradient(0, 0, width, height)

        # 使用深蓝到亮蓝的专业渐变
        main_gradient.setColorAt(0, QColor(44, 62, 80, 250))    # 深蓝灰色
        main_gradient.setColorAt(0.3, QColor(52, 73, 94, 245))  # 中等蓝灰
        main_gradient.setColorAt(0.7, QColor(52, 152, 219, 240)) # 亮蓝色
        main_gradient.setColorAt(1, QColor(155, 207, 255, 235))  # 浅蓝色

        painter.setBrush(QBrush(main_gradient))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, width, height, 20, 20)

        # 添加第二层渐变 - 增强视觉深度
        overlay_gradient = QLinearGradient(width * 0.2, 0, width * 0.8, height)
        overlay_gradient.setColorAt(0, QColor(46, 204, 113, 60))   # 绿色高光
        overlay_gradient.setColorAt(0.5, QColor(52, 152, 219, 40)) # 蓝色中间
        overlay_gradient.setColorAt(1, QColor(155, 89, 182, 50))   # 紫色阴影

        painter.setBrush(QBrush(overlay_gradient))
        painter.drawRoundedRect(0, 0, width, height, 20, 20)

    def _draw_decorative_elements(self, painter: QPainter, width: int, height: int):
        """绘制装饰性几何元素

        Args:
            painter: 绘制器
            width: 宽度
            height: 高度
        """
        # 保存当前状态
        painter.save()

        # 设置混合模式以创建更好的视觉效果（兼容不同PySide6版本）
        try:
            painter.setCompositionMode(QPainter.CompositionMode_Overlay)
        except AttributeError:
            # 如果不支持混合模式，跳过此设置
            pass

        # 绘制大圆形装饰 - 右上角
        circle_gradient = QLinearGradient(width * 0.7, 0, width, height * 0.4)
        circle_gradient.setColorAt(0, QColor(255, 255, 255, 25))
        circle_gradient.setColorAt(1, QColor(255, 255, 255, 5))

        painter.setBrush(QBrush(circle_gradient))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(int(width * 0.6), -50, 200, 200)

        # 绘制小圆形装饰 - 左下角
        small_circle_gradient = QLinearGradient(0, height * 0.6, width * 0.4, height)
        small_circle_gradient.setColorAt(0, QColor(46, 204, 113, 30))
        small_circle_gradient.setColorAt(1, QColor(46, 204, 113, 8))

        painter.setBrush(QBrush(small_circle_gradient))
        painter.drawEllipse(-60, int(height * 0.7), 150, 150)

        # 绘制几何线条装饰
        try:
            painter.setCompositionMode(QPainter.CompositionMode_Normal)
        except AttributeError:
            # 如果不支持混合模式，跳过此设置
            pass

        # 顶部装饰线
        line_gradient = QLinearGradient(0, 0, width, 0)
        line_gradient.setColorAt(0, QColor(255, 255, 255, 0))
        line_gradient.setColorAt(0.3, QColor(255, 255, 255, 40))
        line_gradient.setColorAt(0.7, QColor(255, 255, 255, 40))
        line_gradient.setColorAt(1, QColor(255, 255, 255, 0))

        painter.setPen(QPen(QBrush(line_gradient), 2))
        painter.drawLine(0, 25, width, 25)

        # 底部装饰线
        bottom_gradient = QLinearGradient(0, height - 25, width, height - 25)
        bottom_gradient.setColorAt(0, QColor(255, 255, 255, 0))
        bottom_gradient.setColorAt(0.2, QColor(255, 255, 255, 20))
        bottom_gradient.setColorAt(0.8, QColor(255, 255, 255, 20))
        bottom_gradient.setColorAt(1, QColor(255, 255, 255, 0))

        painter.setPen(QPen(QBrush(bottom_gradient), 1))
        painter.drawLine(0, height - 25, width, height - 25)

        # 恢复状态
        painter.restore()
    
    def _on_progress_updated(self, progress: int, status: str):
        """处理进度更新信号
        
        Args:
            progress: 进度值 (0-100)
            status: 状态文本
        """
        self.progress = progress
        self.status_text = status
        self.repaint()
    
    def update_progress(self, progress: int, status: str = ""):
        """更新进度和状态文本
        
        Args:
            progress: 进度值 (0-100)
            status: 状态文本
        """
        if status:
            self.status_text = status
        self.progress = max(0, min(100, progress))
        self.progress_updated.emit(self.progress, self.status_text)
        QApplication.processEvents()  # 确保界面更新
    
    def start_rotation_animation(self):
        """启动旋转动画"""
        self.rotation_timer.start(50)  # 每50ms更新一次
    
    def stop_rotation_animation(self):
        """停止旋转动画"""
        self.rotation_timer.stop()
    
    def _update_rotation(self):
        """更新旋转角度"""
        self.rotation_angle = (self.rotation_angle + 3) % 360
        self.repaint()
    
    def paintEvent(self, event):
        """重写绘制事件"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制logo区域
        self._draw_logo(painter)
        
        # 绘制应用信息
        self._draw_app_info(painter)
        
        # 绘制进度条
        self._draw_progress_bar(painter)
        
        # 绘制状态文本
        self._draw_status_text(painter)
        
        # 绘制旋转动画
        self._draw_rotation_animation(painter)
    
    def _draw_logo(self, painter: QPainter):
        """绘制logo - 现代化效果"""
        logo_size = 72  # 稍微增大logo尺寸
        logo_x = (self.width() - logo_size) // 2
        logo_y = 35

        # 尝试加载应用图标
        icon_path = os.path.join(os.path.dirname(os.path.dirname(__file__)),
                                "assets", "icons", "vibration_transfer_icon_alt.ico")

        if os.path.exists(icon_path):
            logo_pixmap = QPixmap(icon_path)
            if not logo_pixmap.isNull():
                # 缩放logo到合适大小
                logo_pixmap = logo_pixmap.scaled(logo_size, logo_size,
                                               Qt.KeepAspectRatio, Qt.SmoothTransformation)

                # 绘制logo阴影
                shadow_offset = 3
                shadow_pixmap = logo_pixmap.copy()
                shadow_painter = QPainter(shadow_pixmap)
                shadow_painter.setCompositionMode(QPainter.CompositionMode_SourceIn)
                shadow_painter.fillRect(shadow_pixmap.rect(), QColor(0, 0, 0, 60))
                shadow_painter.end()

                painter.drawPixmap(logo_x + shadow_offset, logo_y + shadow_offset, shadow_pixmap)

                # 绘制主logo
                painter.drawPixmap(logo_x, logo_y, logo_pixmap)
                return

        # 如果没有图标文件，绘制现代化的默认logo
        logo_rect = QRect(logo_x, logo_y, logo_size, logo_size)

        # 绘制logo阴影
        shadow_rect = QRect(logo_x + 3, logo_y + 3, logo_size, logo_size)
        shadow_gradient = QLinearGradient(logo_x + 3, logo_y + 3, logo_x + 3, logo_y + 3 + logo_size)
        shadow_gradient.setColorAt(0, QColor(0, 0, 0, 80))
        shadow_gradient.setColorAt(1, QColor(0, 0, 0, 20))

        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(shadow_gradient))
        painter.drawEllipse(shadow_rect)

        # 绘制主logo背景 - 现代化渐变
        logo_gradient = QLinearGradient(logo_x, logo_y, logo_x, logo_y + logo_size)
        logo_gradient.setColorAt(0, QColor(52, 152, 219, 255))   # 亮蓝色
        logo_gradient.setColorAt(0.5, QColor(46, 204, 113, 255)) # 绿色
        logo_gradient.setColorAt(1, QColor(155, 89, 182, 255))   # 紫色

        painter.setBrush(QBrush(logo_gradient))
        painter.drawEllipse(logo_rect)

        # 绘制logo高光
        highlight_gradient = QLinearGradient(logo_x, logo_y, logo_x, logo_y + logo_size * 0.6)
        highlight_gradient.setColorAt(0, QColor(255, 255, 255, 100))
        highlight_gradient.setColorAt(1, QColor(255, 255, 255, 0))

        highlight_rect = QRect(logo_x, logo_y, logo_size, int(logo_size * 0.6))
        painter.setBrush(QBrush(highlight_gradient))
        painter.drawEllipse(highlight_rect)

        # 在圆形中绘制文字 - 添加阴影效果
        font = QFont("Arial", 24, QFont.Bold)
        painter.setFont(font)

        # 文字阴影
        painter.setPen(QPen(QColor(0, 0, 0, 120)))
        text_shadow_rect = QRect(logo_x + 2, logo_y + 2, logo_size, logo_size)
        painter.drawText(text_shadow_rect, Qt.AlignCenter, "VT")

        # 主文字
        painter.setPen(QPen(QColor(255, 255, 255, 240)))
        painter.drawText(logo_rect, Qt.AlignCenter, "VT")
    
    def _draw_app_info(self, painter: QPainter):
        """绘制应用程序信息 - 现代化文字效果"""
        # 应用名称 - 添加阴影和渐变效果
        painter.setFont(self.title_font)

        # 标题阴影
        shadow_color = QColor(0, 0, 0, 120)
        painter.setPen(QPen(shadow_color))
        title_shadow_rect = QRect(1, 121, self.width(), 30)
        painter.drawText(title_shadow_rect, Qt.AlignCenter, self.app_name)

        # 标题主文字 - 使用白色高亮
        title_color = QColor(255, 255, 255, 240)
        painter.setPen(QPen(title_color))
        title_rect = QRect(0, 120, self.width(), 30)
        painter.drawText(title_rect, Qt.AlignCenter, self.app_name)

        # 版本信息 - 柔和的高亮色
        painter.setFont(self.version_font)

        # 版本阴影
        painter.setPen(QPen(QColor(0, 0, 0, 80)))
        version_shadow_rect = QRect(1, 151, self.width(), 20)
        painter.drawText(version_shadow_rect, Qt.AlignCenter, self.app_version)

        # 版本主文字
        version_color = QColor(200, 230, 255, 200)
        painter.setPen(QPen(version_color))
        version_rect = QRect(0, 150, self.width(), 20)
        painter.drawText(version_rect, Qt.AlignCenter, self.app_version)

        # 公司信息 - 更柔和的颜色
        if self.config.get("show_company_info", True):
            # 公司信息阴影
            painter.setPen(QPen(QColor(0, 0, 0, 60)))
            company_shadow_rect = QRect(1, 171, self.width(), 20)
            painter.drawText(company_shadow_rect, Qt.AlignCenter, self.company_name)

            # 公司信息主文字
            company_color = QColor(180, 210, 240, 160)
            painter.setPen(QPen(company_color))
            company_rect = QRect(0, 170, self.width(), 20)
            painter.drawText(company_rect, Qt.AlignCenter, self.company_name)
    
    def _draw_progress_bar(self, painter: QPainter):
        """绘制现代化进度条"""
        # 进度条背景 - 使用更现代的设计
        progress_bg_rect = QRect(50, 220, self.width() - 100, 12)

        # 背景渐变
        bg_gradient = QLinearGradient(50, 220, 50, 232)
        bg_gradient.setColorAt(0, QColor(255, 255, 255, 30))
        bg_gradient.setColorAt(1, QColor(0, 0, 0, 20))

        painter.setPen(Qt.NoPen)
        painter.setBrush(QBrush(bg_gradient))
        painter.drawRoundedRect(progress_bg_rect, 6, 6)

        # 进度条前景
        if self.progress > 0:
            progress_width = int((self.width() - 100) * self.progress / 100)
            progress_fill_rect = QRect(50, 220, progress_width, 12)

            # 创建现代化的进度条渐变
            progress_gradient = QLinearGradient(50, 220, 50 + progress_width, 220)
            progress_gradient.setColorAt(0, QColor(46, 204, 113, 255))   # 鲜绿色
            progress_gradient.setColorAt(0.5, QColor(52, 152, 219, 255)) # 亮蓝色
            progress_gradient.setColorAt(1, QColor(155, 89, 182, 255))   # 紫色

            painter.setBrush(QBrush(progress_gradient))
            painter.drawRoundedRect(progress_fill_rect, 6, 6)

            # 添加进度条高光效果
            highlight_gradient = QLinearGradient(50, 220, 50, 226)
            highlight_gradient.setColorAt(0, QColor(255, 255, 255, 80))
            highlight_gradient.setColorAt(1, QColor(255, 255, 255, 0))

            highlight_rect = QRect(50, 220, progress_width, 6)
            painter.setBrush(QBrush(highlight_gradient))
            painter.drawRoundedRect(highlight_rect, 3, 3)

        # 进度百分比 - 使用更好的文字效果
        painter.setFont(self.status_font)

        # 文字阴影效果
        shadow_color = QColor(0, 0, 0, 100)
        painter.setPen(QPen(shadow_color))
        shadow_rect = QRect(1, 241, self.width(), 15)
        painter.drawText(shadow_rect, Qt.AlignCenter, f"{self.progress}%")

        # 主文字
        text_color = QColor(255, 255, 255, 220)
        painter.setPen(QPen(text_color))
        percent_rect = QRect(0, 240, self.width(), 15)
        painter.drawText(percent_rect, Qt.AlignCenter, f"{self.progress}%")
    
    def _draw_status_text(self, painter: QPainter):
        """绘制状态文本 - 现代化效果"""
        painter.setFont(self.status_font)

        # 状态文本阴影
        shadow_color = QColor(0, 0, 0, 100)
        painter.setPen(QPen(shadow_color))
        status_shadow_rect = QRect(21, 271, self.width() - 40, 20)
        painter.drawText(status_shadow_rect, Qt.AlignCenter, self.status_text)

        # 状态文本主体 - 使用柔和的白色
        status_color = QColor(220, 240, 255, 180)
        painter.setPen(QPen(status_color))
        status_rect = QRect(20, 270, self.width() - 40, 20)
        painter.drawText(status_rect, Qt.AlignCenter, self.status_text)
    
    def _draw_rotation_animation(self, painter: QPainter):
        """绘制现代化旋转动画"""
        if not self.config.get("show_rotation_animation", True):
            return

        # 在右下角绘制现代化的旋转指示器
        center_x = self.width() - 35
        center_y = self.height() - 35
        radius = 12

        painter.save()
        painter.setRenderHint(QPainter.Antialiasing)
        painter.translate(center_x, center_y)
        painter.rotate(self.rotation_angle)

        # 绘制多层旋转圆弧 - 创建更丰富的视觉效果

        # 外层圆弧 - 主要动画
        outer_gradient = QLinearGradient(-radius, -radius, radius, radius)
        outer_gradient.setColorAt(0, QColor(46, 204, 113, 200))   # 绿色
        outer_gradient.setColorAt(0.5, QColor(52, 152, 219, 200)) # 蓝色
        outer_gradient.setColorAt(1, QColor(155, 89, 182, 100))   # 紫色，渐隐

        painter.setPen(QPen(QBrush(outer_gradient), 3, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(-radius, -radius, radius*2, radius*2, 0, 240*16)

        # 内层圆弧 - 反向旋转效果
        painter.rotate(-self.rotation_angle * 1.5)  # 反向且稍快的旋转

        inner_radius = radius - 4
        inner_gradient = QLinearGradient(-inner_radius, -inner_radius, inner_radius, inner_radius)
        inner_gradient.setColorAt(0, QColor(255, 255, 255, 150))
        inner_gradient.setColorAt(1, QColor(255, 255, 255, 50))

        painter.setPen(QPen(QBrush(inner_gradient), 2, Qt.SolidLine, Qt.RoundCap))
        painter.drawArc(-inner_radius, -inner_radius, inner_radius*2, inner_radius*2, 0, 180*16)

        # 中心点高光
        painter.setBrush(QBrush(QColor(255, 255, 255, 100)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(-2, -2, 4, 4)

        painter.restore()
    
    def show_with_fade_in(self, duration: int = 500):
        """显示启动画面并淡入
        
        Args:
            duration: 淡入持续时间（毫秒）
        """
        self.setWindowOpacity(0.0)
        self.show()
        
        # 创建淡入动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(duration)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()
    
    def hide_with_fade_out(self, duration: int = 300):
        """隐藏启动画面并淡出
        
        Args:
            duration: 淡出持续时间（毫秒）
        """
        self.stop_rotation_animation()
        
        # 创建淡出动画
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(duration)
        self.fade_animation.setStartValue(1.0)
        self.fade_animation.setEndValue(0.0)
        self.fade_animation.setEasingCurve(QEasingCurve.InCubic)
        self.fade_animation.finished.connect(self.close)
        self.fade_animation.start()


class SplashScreenManager:
    """启动画面管理器
    
    负责管理启动画面的生命周期和进度更新
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.splash = None
        self.start_time = None
        self.config = config or {}
        self.minimum_display_time = self.config.get("minimum_display_time", 2000)  # 最小显示时间（毫秒）
    
    def show_splash(self) -> CustomSplashScreen:
        """显示启动画面
        
        Returns:
            CustomSplashScreen: 启动画面实例
        """
        self.start_time = time.time()
        
        self.splash = CustomSplashScreen(config=self.config)
        
        # 根据配置决定是否使用淡入效果
        if self.config.get("show_fade_in", True):
            fade_duration = self.config.get("fade_in_duration", 500)
            self.splash.show_with_fade_in(fade_duration)
        else:
            self.splash.show()
        
        return self.splash
    
    def update_progress_by_percentage(self, percentage: int, status: str = ""):
        """直接通过百分比更新进度
        
        Args:
            percentage: 进度百分比 (0-100)
            status: 状态文本
        """
        if self.splash:
            self.splash.update_progress(percentage, status)
    
    def hide_splash(self):
        """隐藏启动画面"""
        if not self.splash:
            return
        
        # 确保最小显示时间
        elapsed_time = (time.time() - self.start_time) * 1000
        if elapsed_time < self.minimum_display_time:
            remaining_time = self.minimum_display_time - elapsed_time
            QTimer.singleShot(int(remaining_time), self._do_hide_splash)
        else:
            self._do_hide_splash()
    
    def _do_hide_splash(self):
        """执行隐藏启动画面"""
        if self.splash:
            self.splash.hide_with_fade_out()
            self.splash = None


# 全局启动画面管理器实例
_splash_manager = None


def get_splash_manager() -> SplashScreenManager:
    """获取全局启动画面管理器实例
    
    Returns:
        SplashScreenManager: 启动画面管理器实例
    """
    global _splash_manager
    if _splash_manager is None:
        _splash_manager = SplashScreenManager()
    return _splash_manager


def create_splash_screen(config: Optional[Dict[str, Any]] = None) -> CustomSplashScreen:
    """创建启动画面实例
    
    Args:
        config: 配置字典
        
    Returns:
        CustomSplashScreen: 启动画面实例
    """
    return CustomSplashScreen(config=config)
