"""
约束设置界面控制器

此模块负责处理约束设置界面的所有操作，主要包括：
1. 界面跳转控制
2. 约束类型设置
3. 约束参数配置
4. 边界条件设置

工作流程：
1. 初始化约束设置界面
2. 配置约束类型和参数
3. 设置边界条件
4. 界面跳转管理

作者: [作者名]
日期: [日期]
"""

from typing import Optional, Dict, Any
from PySide6.QtWidgets import QWidget, QFileDialog
from window_manager import WindowManager, WindowType
from error_handler import ErrorHandler, AppError, ErrorSeverity, ValidationError, FileOperationError
from resource_manager import ResourceManager
import re
import json
import os
import shutil
from datetime import datetime

# 约束类型定义
CONSTRAINT_TYPES = {
    "remote": ["X", "Y", "Z", "XRot", "YRot", "ZRot"],
    "displacement": ["X", "Y", "Z"]
}

# 旋转轴选项
ROTATION_AXES = ["x", "y", "z"]

def validate_rotation_speed(speed: str) -> float:
    """验证旋转速度值
    
    Args:
        speed: 旋转速度字符串
        
    Returns:
        float: 验证后的旋转速度值
        
    Raises:
        ValidationError: 当速度值无效时抛出
    """
    try:
        value = float(speed)
        if value < 0:
            raise ValidationError("旋转速度不能为负值")
        if value > 1e6:
            raise ValidationError("旋转速度不能超过1,000,000 r/min")
        return value
    except ValueError:
        raise ValidationError("旋转速度必须是有效的数值")

# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口

def validate_force_file_content(file_path: str) -> bool:
    """验证力文件内容格式
    
    Args:
        file_path: 力文件路径
        
    Returns:
        bool: 文件格式是否有效
    """
    try:
        with open(file_path, 'r') as f:
            # 检查文件头
            lines = f.readlines()
            if len(lines) < 2:
                return False
                
            # 检查标题行
            if not any('"Time Step"' in line for line in lines[:3]):
                return False
                
            # 检查数据行格式
            for line in lines[3:]:
                parts = line.strip().split()
                if len(parts) != 3:  # 应该有3列：步数、力值、时间
                    return False
                try:
                    float(parts[0])  # 步数
                    float(parts[1])  # 力值
                    float(parts[2])  # 时间
                except ValueError:
                    return False
                    
        return True
    except Exception:
        return False

def get_force_file_path(constrain_window: QWidget) -> None:
    """获取力文件路径并验证文件有效性
    
    Args:
        constrain_window: 约束设置窗口实例
    """
    error_handler = ErrorHandler()
    status_text = []
    
    def log_status(msg: str):
        """记录状态信息并更新显示"""
        status_text.append(msg)
        constrain_window.ui.plainTextEdit_force.setPlainText("\n".join(status_text))
        constrain_window.ui.plainTextEdit_force.repaint()
    
    constrain_window.ui.lineEdit_force.clear()

    # 清空状态显示
    constrain_window.ui.plainTextEdit_force.clear()
    log_status("开始处理力文件...")
    
    # 获取力文件路径
    force_dir = QFileDialog.getExistingDirectory(constrain_window, "选择力文件夹")
    if not force_dir:
        log_status("已取消选择文件夹")
        return
    
    log_status(f"选择的文件夹: {force_dir}")
    log_status("开始验证文件...")
        
    # 定义必需的文件 - 更新为新的命名规范
    required_files = {
        "yl-wall": ["yl-fx.out", "yl-fy.out", "yl-fz.out"],
        "wk-wall": ["wk-fx.out", "wk-fy.out", "wk-fz.out"],
        # 保留注释的其他文件格式供将来扩展
        # "qbq-qbg": ["qbq-qbg-fx.out", "qbq-qbg-fy.out", "qbq-qbg-fz.out"],
        # "hbq-hbg": ["hbq-hbg-fx.out", "hbq-hbg-fy.out", "hbq-hbg-fz.out"],
        # "qbq-wall": ["QBQ-WALL-fx.out", "QBQ-WALL-fy.out", "QBQ-WALL-fz.out"],
        # "hbq-wall": ["HBQ-WALL-fx.out", "HBQ-WALL-fy.out", "HBQ-WALL-fz.out"]
    }
    
    # 检查文件存在性和格式
    missing_files = []
    invalid_files = []
    
    for group, files in required_files.items():
        log_status(f"\n检查 {group} 组文件...")
        for filename in files:
            file_path = os.path.join(force_dir, filename)
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                log_status(f"  ✗ {filename} - 文件不存在")
                missing_files.append(filename)
                continue
                
            # 检查文件扩展名
            if not filename.endswith('.out'):
                log_status(f"  ✗ {filename} - 不是.out文件")
                invalid_files.append(f"{filename} (不是.out文件)")
                continue
                
            # 检查文件内容格式
            if not validate_force_file_content(file_path):
                log_status(f"  ✗ {filename} - 格式错误")
                invalid_files.append(f"{filename} (格式错误)")
                continue
                
            log_status(f"  ✓ {filename} - 验证通过")
    
    # 处理验证结果
    if missing_files or invalid_files:
        error_msg = "力文件验证失败:\n"
        if missing_files:
            error_msg += "\n缺少以下文件:\n" + "\n".join(missing_files)
        if invalid_files:
            error_msg += "\n\n以下文件无效:\n" + "\n".join(invalid_files)
            
        log_status("\n验证结果: 失败")
        log_status(error_msg)
            
        error_handler.handle_error(
            AppError(error_msg, ErrorSeverity.ERROR),
            constrain_window
        )
        return
        
    # 验证通过，保存路径
    constrain_window.ui.lineEdit_force.setText(force_dir)
    log_status("\n验证结果: 全部通过 ✓")
    log_status(f"力文件路径已保存: {force_dir}")
    
    error_handler.handle_error(
        AppError("力文件验证通过", ErrorSeverity.INFO),
        constrain_window
    )

def get_point_file_path(constrain_window: QWidget) -> None:
    """获取点文件路径并验证文件有效性

    Args:
        constrain_window: 约束设置窗口实例
    """
    error_handler = ErrorHandler()
    status_text = []
    
    def log_status(msg: str):
        """记录状态信息并更新显示"""
        status_text.append(msg)
        constrain_window.ui.plainTextEdit_point.setPlainText("\n".join(status_text))
        constrain_window.ui.plainTextEdit_point.repaint()
    
    constrain_window.ui.lineEdit_point.clear()

    # 清空状态显示
    constrain_window.ui.plainTextEdit_point.clear()
    log_status("开始处理监测点文件...")
    
    # 获取监测点文件路径
    point_file, _ = QFileDialog.getOpenFileName(constrain_window, "选择监测点文件", "", "JSON文件 (*.json);;文本文件 (*.txt);;所有文件 (*)")
    if not point_file:
        log_status("已取消选择文件")
        return
    
    log_status(f"选择的文件: {point_file}")
    log_status("开始验证文件...")
    
    try:
        # 检查文件扩展名
        if not (point_file.endswith('.json') or point_file.endswith('.txt')):
            log_status(f"  ✗ 文件格式不支持，请选择.json或.txt文件")
            error_handler.handle_error(
                AppError("监测点文件格式不支持，请选择.json或.txt文件", ErrorSeverity.ERROR),
                constrain_window
            )
            return
        
        # 读取并解析文件
        points = []
        if point_file.endswith('.json'):
            # 解析JSON文件
            with open(point_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 检查JSON格式
            if 'monitor_points' not in data:
                log_status(f"  ✗ JSON文件格式错误，缺少'monitor_points'字段")
                error_handler.handle_error(
                    AppError("JSON文件格式错误，缺少'monitor_points'字段", ErrorSeverity.ERROR),
                    constrain_window
                )
                return
                
            points = data['monitor_points']
            
            # 验证每个点的格式
            for i, point in enumerate(points):
                if not isinstance(point, list) or len(point) != 3:
                    log_status(f"  ✗ 点 #{i+1} 格式错误，应为包含3个坐标值的数组")
                    error_handler.handle_error(
                        AppError(f"点 #{i+1} 格式错误，应为包含3个坐标值的数组", ErrorSeverity.ERROR),
                        constrain_window
                    )
                    return
                    
                # 验证坐标值为数值
                for j, coord in enumerate(point):
                    if not isinstance(coord, (int, float)):
                        log_status(f"  ✗ 点 #{i+1} 的坐标 #{j+1} 不是有效的数值")
                        error_handler.handle_error(
                            AppError(f"点 #{i+1} 的坐标 #{j+1} 不是有效的数值", ErrorSeverity.ERROR),
                            constrain_window
                        )
                        return
        else:
            # 解析TXT文件 - 使用UTF-8编码打开
            try:
                with open(point_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            except UnicodeDecodeError:
                # 如果UTF-8解码失败，尝试使用其他编码
                try:
                    with open(point_file, 'r', encoding='latin-1') as f:
                        lines = f.readlines()
                    log_status("注意: 文件不是UTF-8编码，使用latin-1编码读取")
                except Exception as e:
                    log_status(f"  ✗ 文件编码错误: {str(e)}")
                    error_handler.handle_error(
                        AppError(f"文件编码错误: {str(e)}", ErrorSeverity.ERROR),
                        constrain_window
                    )
                    return
                
            for i, line in enumerate(lines):
                line = line.strip()
                if not line or line.startswith('#'):  # 跳过空行和注释
                    continue
                    
                # 分割坐标值
                coords = re.split(r'[,\s]+', line)
                if len(coords) != 3:
                    log_status(f"  ✗ 第 {i+1} 行格式错误，应包含3个坐标值")
                    error_handler.handle_error(
                        AppError(f"第 {i+1} 行格式错误，应包含3个坐标值", ErrorSeverity.ERROR),
                        constrain_window
                    )
                    return
                    
                # 验证坐标值为数值
                try:
                    point = [float(coord) for coord in coords]
                    points.append(point)
                except ValueError:
                    log_status(f"  ✗ 第 {i+1} 行包含非数值坐标")
                    error_handler.handle_error(
                        AppError(f"第 {i+1} 行包含非数值坐标", ErrorSeverity.ERROR),
                        constrain_window
                    )
                    return
        
        # 检查是否有监测点
        if not points:
            log_status(f"  ✗ 文件中未找到有效的监测点")
            error_handler.handle_error(
                AppError("文件中未找到有效的监测点", ErrorSeverity.ERROR),
                constrain_window
            )
            return
            
        # 显示监测点信息
        log_status(f"\n找到 {len(points)} 个监测点:")
        for i, point in enumerate(points):
            log_status(f"  ✓ 点 #{i+1}: [{point[0]:.3f}, {point[1]:.3f}, {point[2]:.3f}]")
            
        # 验证通过，保存路径
        constrain_window.ui.lineEdit_point.setText(point_file)
        log_status("\n验证结果: 全部通过 ✓")
        log_status(f"监测点文件路径已保存: {point_file}")
        
        error_handler.handle_error(
            AppError("监测点文件验证通过", ErrorSeverity.INFO),
            constrain_window
        )
        
    except json.JSONDecodeError:
        log_status(f"  ✗ JSON文件解析失败，请检查格式")
        error_handler.handle_error(
            AppError("JSON文件解析失败，请检查格式", ErrorSeverity.ERROR),
            constrain_window
        )
    except Exception as e:
        log_status(f"  ✗ 文件验证失败: {str(e)}")
        error_handler.handle_error(
            AppError(f"文件验证失败: {str(e)}", ErrorSeverity.ERROR),
            constrain_window
        )

def get_constrain_json(window_manager: WindowManager) -> None:
    """获取约束设置界面的配置信息并保存为JSON文件
    
    此函数完成以下任务：
    1. 获取界面上的约束参数
    2. 验证参数有效性
    3. 生成配置JSON
    4. 保存到文件
    5. 更新window_manager中的initial_data
    
    Args:
        window_manager: 窗口管理器实例
    """
    constrain_window = window_manager.get_window(WindowType.CONSTRAIN)
    error_handler = ErrorHandler()
    
    if not constrain_window:
        error_handler.handle_error(
            AppError("无法获取约束设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    try:
        print("=" * 50)
        print("开始执行get_constrain_json函数")
        print("=" * 50)

        # 获取力文件路径
        force_dir = constrain_window.ui.lineEdit_force.text()
        print(f"力文件路径: {force_dir}")
        if not force_dir:
            raise ValidationError("请先选择力文件路径")
        
        # 获取监控点数据 - 优先使用tab_5中创建的监控点
        monitor_points = []
        monitor_point_file = ""

        # 首先尝试从tab_5监控点管理界面获取监控点
        try:
            print("开始获取监控点数据...")
            if hasattr(constrain_window, 'get_monitor_points'):
                print("约束窗口具有get_monitor_points方法")
                tab5_monitor_points = constrain_window.get_monitor_points()
                print(f"从tab_5获取到原始监控点数据: {tab5_monitor_points}")
                if tab5_monitor_points:
                    # 转换tab_5监控点数据格式为配置文件格式
                    monitor_points = []
                    for point in tab5_monitor_points:
                        # tab_5格式: {'id': 1, 'name': 'point1', 'x': 1.0, 'y': 2.0, 'z': 3.0, 'created_time': '...'}
                        # 配置文件格式: {'name': 'point1', 'coordinates': [x, y, z]}
                        try:
                            # 安全地获取坐标数据
                            x = point.get('x', 0.0)
                            y = point.get('y', 0.0)
                            z = point.get('z', 0.0)

                            # 验证坐标数据类型
                            if not all(isinstance(coord, (int, float)) for coord in [x, y, z]):
                                print(f"警告: 监控点坐标数据类型错误: {point}")
                                continue

                            monitor_points.append({
                                'name': point.get('name', f"Point_{point.get('id', len(monitor_points)+1)}"),
                                'coordinates': [float(x), float(y), float(z)],
                                'id': point.get('id'),
                                'created_time': point.get('created_time')
                            })
                        except (KeyError, TypeError, ValueError) as e:
                            print(f"警告: 处理监控点数据时出错: {point}, 错误: {str(e)}")
                            continue
                    print(f"从tab_5监控点管理界面获取到 {len(monitor_points)} 个监控点")
                    print(f"转换后的监控点数据: {monitor_points}")
                else:
                    print("tab_5中没有监控点数据")
            else:
                print("约束窗口没有get_monitor_points方法")
        except Exception as e:
            print(f"警告: 从tab_5获取监控点失败: {str(e)}")
            import traceback
            traceback.print_exc()

        # 如果tab_5中没有监控点，则尝试从原有的文件路径获取
        if not monitor_points:
            monitor_point_file = constrain_window.ui.lineEdit_point.text()
            if monitor_point_file:
                try:
                    # 读取监测点文件
                    if monitor_point_file.endswith('.json'):
                        with open(monitor_point_file, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        file_monitor_points = data.get('monitor_points', [])
                    else:
                        # 假设是文本文件
                        file_monitor_points = []
                        try:
                            with open(monitor_point_file, 'r', encoding='utf-8') as f:
                                for line in f:
                                    line = line.strip()
                                    if not line or line.startswith('#'):
                                        continue
                                    coords = re.split(r'[,\s]+', line)
                                    if len(coords) == 3:
                                        try:
                                            point = [float(coord) for coord in coords]
                                            file_monitor_points.append(point)
                                        except ValueError:
                                            pass
                        except UnicodeDecodeError:
                            # 如果UTF-8解码失败，尝试使用其他编码
                            try:
                                with open(monitor_point_file, 'r', encoding='latin-1') as f:
                                    for line in f:
                                        line = line.strip()
                                        if not line or line.startswith('#'):
                                            continue
                                        coords = re.split(r'[,\s]+', line)
                                        if len(coords) == 3:
                                            try:
                                                point = [float(coord) for coord in coords]
                                                file_monitor_points.append(point)
                                            except ValueError:
                                                pass
                            except Exception as e:
                                print(f"警告: 读取监测点文件编码错误: {str(e)}")
                                # 继续执行，不阻止其他配置的保存

                    # 转换文件监控点为统一格式
                    for i, point in enumerate(file_monitor_points):
                        if isinstance(point, list) and len(point) == 3:
                            monitor_points.append({
                                'name': f"导入点_{i+1}",
                                'coordinates': point,
                                'id': i+1,
                                'created_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'source': 'file_import'
                            })

                    print(f"从文件 {monitor_point_file} 获取到 {len(monitor_points)} 个监控点")

                except Exception as e:
                    print(f"警告: 读取监测点文件失败: {str(e)}")
                    # 继续执行，不阻止其他配置的保存
            
        # 获取远程位移约束
        remote_displacement1 = {
            "type": "remote",
            "values": get_constraint_values(constrain_window.ui, "bearing1", "remote")
        }
        validate_constraint_values(remote_displacement1["values"], "remote", "轴承1远程位移")
        
        remote_displacement2 = {
            "type": "remote",
            "values": get_constraint_values(constrain_window.ui, "bearing2", "remote")
        }
        validate_constraint_values(remote_displacement2["values"], "remote", "轴承2远程位移")
        
        # 获取位移约束
        flange1 = {
            "type": "displacement",
            "values": get_constraint_values(constrain_window.ui, "flange1", "displacement")
        }
        validate_constraint_values(flange1["values"], "displacement", "法兰1位移")
        
        flange2 = {
            "type": "displacement",
            "values": get_constraint_values(constrain_window.ui, "flange2", "displacement")
        }
        validate_constraint_values(flange2["values"], "displacement", "法兰2位移")
        
        # 获取旋转参数
        rotation_speed = constrain_window.ui.lineEdit_rotation_speed.text()
        if not rotation_speed:
            raise ValidationError("请输入旋转速度")
        speed_value = validate_rotation_speed(rotation_speed)
        
        rotation_axis = constrain_window.ui.rotation_axis.currentText()
        if rotation_axis not in ROTATION_AXES:
            raise ValidationError("请选择有效的旋转轴")
            
        # 生成配置数据
        constrain_config = {
            "force_file_path": force_dir,
            "bearing_1": remote_displacement1,
            "bearing_2": remote_displacement2,
            "flange1": flange1,
            "flange2": flange2,
            "rotation_speed": speed_value,
            "rotation_axis": rotation_axis
        }
        
        # 添加监控点配置
        if monitor_points:
            constrain_config["monitor_points"] = monitor_points
            constrain_config["monitor_points_count"] = len(monitor_points)
            constrain_config["monitor_points_source"] = "tab5_interface" if not monitor_point_file else "file_import"
            if monitor_point_file:
                constrain_config["monitor_point_file"] = monitor_point_file
        
        # 保存配置文件
        main_window = window_manager.get_window(WindowType.MAIN)
        resource_manager = ResourceManager()
        resource_manager.initialize(main_window.ANSYS_Work_Dir)
        
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)
            
            # 清理旧的配置文件
            cleanup_old_configs(resource_manager.json_dir, "constrain_config_")
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"constrain_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)
            
            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(constrain_config, f, indent=4)
                
            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")
                    
            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
            
            # 更新window_manager中的initial_data
            if hasattr(window_manager, 'initial_data'):
                window_manager.initial_data.update({
                    'forceOutputFolder': force_dir,
                    'rotationSpeed': speed_value,
                    'rotationAxis': rotation_axis
                })
                print(f"已更新window_manager中的initial_data: forceOutputFolder={force_dir}, rotationSpeed={speed_value}")
            
        except Exception as e:
            raise FileOperationError(
                "保存配置文件失败",
                details={'file': config_file}
            ) from e
        
        # 设置完成状态
        window_manager.process_status.set_completed(WindowType.CONSTRAIN)
        
        # 显示成功消息
        print("=" * 50)
        print("约束设置配置保存成功！")
        print(f"配置文件: {config_file}")
        print(f"监控点数量: {len(monitor_points)}")
        print("=" * 50)

        error_handler.handle_error(
            AppError(
                "约束设置已保存",
                ErrorSeverity.INFO
            ),
            constrain_window
        )

        # 调用完成后处理函数
        finish_constrain(window_manager)

    except Exception as e:
        print("=" * 50)
        print(f"get_constrain_json函数执行失败: {str(e)}")
        print("=" * 50)
        import traceback
        traceback.print_exc()
        error_handler.handle_exception(e, constrain_window)

def finish_constrain(window_manager: WindowManager) -> None:
    """完成约束设置界面的配置

    此函数完成以下任务：
    1. 显示完成确认消息
    2. 可选择跳转到下一个界面（分析设置）

    Args:
        window_manager: 窗口管理器实例
    """
    constrain_window = window_manager.get_window(WindowType.CONSTRAIN)
    error_handler = ErrorHandler()

    if not constrain_window:
        error_handler.handle_error(
            AppError("无法获取约束设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    try:
        print("=" * 50)
        print("约束设置完成后处理开始")
        print("=" * 50)

        # 显示完成确认对话框
        from PySide6.QtWidgets import QMessageBox

        msg_box = QMessageBox(constrain_window)
        msg_box.setWindowTitle("约束设置完成")
        msg_box.setText("约束设置配置已成功保存！")
        msg_box.setInformativeText("是否要继续进行下一步（分析设置）？")
        msg_box.setStandardButtons(QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        msg_box.setDefaultButton(QMessageBox.StandardButton.Yes)

        # 设置按钮文本
        yes_button = msg_box.button(QMessageBox.StandardButton.Yes)
        no_button = msg_box.button(QMessageBox.StandardButton.No)
        yes_button.setText("继续下一步")
        no_button.setText("留在当前界面")

        # 显示对话框并获取用户选择
        result = msg_box.exec()

        if result == QMessageBox.StandardButton.Yes:
            # 用户选择继续，跳转到分析设置界面
            print("用户选择继续，跳转到分析设置界面")
            window_manager.switch_to(WindowType.ANALYSIS)
        else:
            # 用户选择留在当前界面
            print("用户选择留在约束设置界面")

        print("=" * 50)
        print("约束设置完成后处理结束")
        print("=" * 50)

    except Exception as e:
        print(f"完成后处理时出错: {str(e)}")
        error_handler.handle_exception(e, constrain_window)

def validate_constraint_values(values: Dict[str, Any], constraint_type: str, name: str) -> None:
    """验证约束值的有效性
    
    Args:
        values: 约束值字典
        constraint_type: 约束类型
        name: 约束名称(用于错误提示)
        
    Raises:
        ValidationError: 当约束值无效时抛出
    """
    if constraint_type not in CONSTRAINT_TYPES:
        raise ValidationError(f"无效的约束类型: {constraint_type}")
        
    required_fields = CONSTRAINT_TYPES[constraint_type]
    for field in required_fields:
        if field not in values:
            raise ValidationError(f"{name}缺少{field}分量")
        value = values[field]
        if value != "Free" and not isinstance(value, (int, float)):
            raise ValidationError(f"{name}的{field}分量必须是数值或'Free'")

def get_constraint_values(ui, prefix: str, constraint_type: str) -> Dict[str, Any]:
    """从UI获取约束值

    Args:
        ui: UI实例
        prefix: UI控件前缀
        constraint_type: 约束类型

    Returns:
        Dict[str, Any]: 约束值字典
    """
    values = {}
    print(f"获取约束值: prefix={prefix}, constraint_type={constraint_type}")

    for field in CONSTRAINT_TYPES[constraint_type]:
        # 构建控件名称
        if field.startswith(("X", "Y", "Z")):
            if "Rot" in field:
                widget_name = f"{field[0].lower()}rotation_{prefix}"
            else:
                widget_name = f"{field[0].lower()}component_{prefix}"
        else:
            widget_name = f"{field.lower()}component_{prefix}"

        print(f"尝试获取控件: {widget_name}")

        try:
            # 安全地获取UI控件
            if hasattr(ui, widget_name):
                widget = getattr(ui, widget_name)
                value = widget.currentText()
                values[field] = "Free" if value == "Free" else 0.0
                print(f"✓ 控件 {widget_name} 值: {value}")
            else:
                # 如果控件不存在，使用默认值
                print(f"⚠ 控件 {widget_name} 不存在，使用默认值 'Free'")
                values[field] = "Free"
        except Exception as e:
            print(f"✗ 获取控件 {widget_name} 时出错: {str(e)}")
            # 使用默认值继续执行
            values[field] = "Free"

    print(f"约束值获取完成: {values}")
    return values

def cleanup_old_configs(directory: str, prefix: str, max_files: int = 5) -> None:
    """清理旧的配置文件，只保留最新的几个文件
    
    Args:
        directory: 配置文件目录
        prefix: 配置文件前缀
        max_files: 保留的最大文件数量
    """
    files = [f for f in os.listdir(directory) if f.startswith(prefix) and f.endswith('.json')]
    files.sort(reverse=True)  # 按文件名降序排序（因为包含时间戳）
    
    # 删除超出数量限制的旧文件
    for old_file in files[max_files:]:
        try:
            os.remove(os.path.join(directory, old_file))
        except Exception as e:
            print(f"警告: 无法删除旧配置文件 {old_file}: {str(e)}")
            
def constrain_slot(window_manager: WindowManager) -> None:
    """初始化约束设置界面的所有槽函数连接

    此函数负责将约束设置界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮、约束设置功能按钮和监控点管理功能按钮。

    Args:
        window_manager: 窗口管理器实例
    """
    constrain_window = window_manager.get_window(WindowType.CONSTRAIN)
    if not constrain_window:
        ErrorHandler().handle_error(
            AppError("无法获取约束设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step

    constrain_window.ui.push_mainui.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 约束设置的上一步应该是分析设置
    constrain_window.ui.push_analysisui.clicked.connect(
        lambda: navigate_to_previous_step(window_manager, WindowType.CONSTRAIN))

    # 约束设置的下一步应该是网格无关性验证
    if hasattr(constrain_window.ui, 'push_meshui'):
        constrain_window.ui.push_meshui.clicked.connect(
            lambda: navigate_to_next_step(window_manager, WindowType.CONSTRAIN))

    # 保留原有的结果跳转（可能用于快速跳转）
    if hasattr(constrain_window.ui, 'push_resultui'):
        constrain_window.ui.push_resultui.clicked.connect(
            lambda: navigate_to_next_step(window_manager, WindowType.CONSTRAIN))

    # 原有约束设置功能按钮连接
    constrain_window.ui.pushButton_force.clicked.connect(lambda: get_force_file_path(constrain_window))
    constrain_window.ui.push_finish.clicked.connect(
        lambda: get_constrain_json(window_manager))

def connect_monitor_point_signals(constrain_window) -> None:
    """验证监控点管理功能的信号槽连接

    此函数负责验证约束设置界面tab_5中监控点管理功能的信号槽连接是否正常。
    由于信号槽连接已在 views/constrain_window.py 的 _connect_monitor_signals() 中完成，
    这里主要进行验证和确保连接的完整性。

    Args:
        constrain_window: 约束设置窗口实例
    """
    error_handler = ErrorHandler()

    try:
        # 检查监控点管理UI组件是否存在
        monitor_ui_components = [
            'monitor_add_point_btn',
            'monitor_import_file_btn',
            'monitor_clear_all_btn',
            'monitor_name_input',
            'monitor_x_input',
            'monitor_y_input',
            'monitor_z_input',
            'monitor_points_table',
            'monitor_create_import_group',
            'monitor_points_list_group'
        ]

        missing_components = []
        for component in monitor_ui_components:
            if not hasattr(constrain_window.ui, component):
                missing_components.append(component)

        if missing_components:
            print(f"警告: 以下监控点管理UI组件不存在: {', '.join(missing_components)}")
            return

        # 检查监控点管理方法是否存在
        monitor_methods = [
            '_add_single_point',
            '_import_from_file',
            '_clear_all_points',
            '_update_monitor_table_display',
            '_delete_monitor_point',
            'get_monitor_points',
            'set_monitor_points'
        ]

        missing_methods = []
        for method in monitor_methods:
            if not hasattr(constrain_window, method):
                missing_methods.append(method)

        if missing_methods:
            print(f"警告: 以下监控点管理方法不存在: {', '.join(missing_methods)}")
            return

        # 验证信号槽连接（通过检查是否已经连接了信号）
        # 注意：这里不重复连接，因为连接已在 constrain_window.__init__ 中完成

        print("监控点管理功能验证完成 - 所有组件和方法都存在")
        print("信号槽连接已在 views/constrain_window.py 中完成")

        # 可选：测试一个简单的功能来验证连接是否正常工作
        try:
            # 获取当前监控点数量作为连接验证
            current_points = constrain_window.get_monitor_points()
            print(f"当前监控点数量: {len(current_points)}")
        except Exception as e:
            print(f"监控点管理功能测试失败: {str(e)}")

    except Exception as e:
        error_handler.handle_error(
            AppError(f"监控点管理功能验证失败: {str(e)}", ErrorSeverity.WARNING),
            constrain_window
        )