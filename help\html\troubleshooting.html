<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排除指南 - 振动传递计算软件</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>故障排除指南</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔧 常见问题解决</h2>
            <p>本指南提供了振动传递计算软件常见问题的解决方案，包括安装、配置、运行和功能使用等方面的问题。</p>
        </div>

        <div class="section">
            <h2>🚀 启动和安装问题</h2>
            
            <div class="faq">
                <h3>Q: 程序无法启动，提示缺少依赖库</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>确保已安装Python 3.12或更高版本</li>
                    <li>安装所需的依赖库：
                        <pre><code>pip install -r requirements.txt</code></pre>
                    </li>
                    <li>如果仍有问题，尝试重新创建虚拟环境：
                        <pre><code>
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
                        </code></pre>
                    </li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: 启动时出现QKeySequence导入错误</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确保从PySide6.QtGui而不是PySide6.QtCore导入QKeySequence</li>
                    <li>检查PySide6版本是否为6.5.0或更高版本</li>
                    <li>重新安装PySide6：<code>pip install --upgrade PySide6</code></li>
                </ul>
            </div>

            <div class="faq">
                <h3>Q: 程序启动后界面显示异常</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>检查显示器分辨率和DPI设置</li>
                    <li>尝试以管理员权限运行程序</li>
                    <li>清除配置文件：删除config/settings.json</li>
                    <li>重启计算机后再次尝试</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>⚙️ ANSYS配置问题</h2>
            
            <div class="faq">
                <h3>Q: 程序无法找到ANSYS Workbench启动文件</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>确保ANSYS Workbench已正确安装</li>
                    <li>通过主界面的"设置" → "ANSYS启动文件"菜单重新配置路径</li>
                    <li>典型的ANSYS路径：
                        <ul>
                            <li>Windows: <code>C:\Program Files\ANSYS Inc\v232\Framework\bin\Win64\RunWB2.exe</code></li>
                            <li>Linux: <code>/usr/ansys_inc/v232/Framework/bin/runwb2</code></li>
                        </ul>
                    </li>
                    <li>确保路径中不包含中文字符</li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: ANSYS启动失败或无响应</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>检查ANSYS许可证是否有效</li>
                    <li>确保有足够的系统内存（建议16GB以上）</li>
                    <li>关闭防火墙或添加ANSYS到白名单</li>
                    <li>以管理员权限运行程序</li>
                    <li>检查ANSYS服务是否正常运行</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🌐 API和网络问题</h2>
            
            <div class="faq">
                <h3>Q: API服务器启动失败或端口被占用</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>检查端口8000是否被其他程序占用：
                        <pre><code>
# Windows
netstat -ano | findstr :8000

# Linux/Mac
lsof -i :8000
                        </code></pre>
                    </li>
                    <li>终止占用端口的进程或更改API端口配置</li>
                    <li>确认已安装fastapi和uvicorn依赖</li>
                    <li>检查防火墙设置，允许端口8000的通信</li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: 外部应用无法连接到API</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确认API服务器已启动（查看控制台输出）</li>
                    <li>测试API连接：<code>curl http://127.0.0.1:8000/health</code></li>
                    <li>检查请求格式是否正确（JSON格式，正确的Content-Type）</li>
                    <li>查看API服务器日志获取详细错误信息</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🌍 国际化和语言问题</h2>
            
            <div class="faq">
                <h3>Q: 语言切换功能不工作</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>确认翻译文件存在：
                        <ul>
                            <li>translations/app_zh_CN.qm</li>
                            <li>translations/app_en_US.qm</li>
                            <li>translations/app_ja_JP.qm</li>
                        </ul>
                    </li>
                    <li>重新生成翻译文件：
                        <pre><code>python tools/generate_translations.py --compile</code></pre>
                    </li>
                    <li>重启应用程序</li>
                    <li>检查配置文件中的语言设置</li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: 界面显示乱码或字体问题</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确保系统已安装相应语言的字体</li>
                    <li>检查系统区域和语言设置</li>
                    <li>尝试切换到其他语言再切换回来</li>
                    <li>重新安装系统字体包</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📊 振动分析问题</h2>
            
            <div class="faq">
                <h3>Q: 振动分析模块加载失败</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>确认已安装科学计算依赖：
                        <pre><code>pip install numpy scipy matplotlib pandas</code></pre>
                    </li>
                    <li>检查数据文件格式是否正确</li>
                    <li>确保数据文件路径不包含中文字符</li>
                    <li>查看错误日志获取详细信息</li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: 振动数据导入失败</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>检查数据文件格式（支持CSV、TXT、Excel格式）</li>
                    <li>确保数据文件包含时间和振动数据列</li>
                    <li>检查数据文件编码（建议使用UTF-8）</li>
                    <li>验证数据文件大小（避免过大的文件）</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🛡️ 线程安全和性能问题</h2>
            
            <div class="faq">
                <h3>Q: 程序运行时出现崩溃或卡死</h3>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>检查系统资源使用情况（CPU、内存）</li>
                    <li>查看应用程序日志文件</li>
                    <li>尝试重启应用程序</li>
                    <li>如果问题持续，请联系技术支持并提供日志文件</li>
                </ol>
            </div>

            <div class="faq">
                <h3>Q: API调用响应缓慢</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>检查系统负载和可用内存</li>
                    <li>减少并发API调用数量</li>
                    <li>优化请求数据大小</li>
                    <li>监控网络连接状态</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🎨 界面和显示问题</h2>
            
            <div class="faq">
                <h3>Q: 界面样式显示不正常</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>检查样式表文件是否完整</li>
                    <li>确保QSS语法正确</li>
                    <li>尝试重启应用程序</li>
                    <li>重置界面设置到默认值</li>
                </ul>
            </div>

            <div class="faq">
                <h3>Q: 帮助文档显示异常</h3>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确认help/html目录下的文件完整</li>
                    <li>检查浏览器组件是否正常安装</li>
                    <li>尝试使用外部浏览器打开帮助文件</li>
                    <li>重新安装应用程序</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📝 日志和调试</h2>
            
            <div class="note">
                <h3>日志文件位置</h3>
                <ul>
                    <li><strong>应用程序日志</strong>: logs/app.log</li>
                    <li><strong>API服务器日志</strong>: logs/api.log</li>
                    <li><strong>错误日志</strong>: logs/error.log</li>
                    <li><strong>调试日志</strong>: logs/debug.log</li>
                </ul>
            </div>

            <div class="tip">
                <h3>启用调试模式</h3>
                <p>在配置文件中设置调试级别：</p>
                <pre><code>
{
    "logging": {
        "level": "DEBUG",
        "console_output": true,
        "file_output": true
    }
}
                </code></pre>
            </div>
        </div>

        <div class="section">
            <h2>📞 获取技术支持</h2>
            
            <div class="warning">
                <h3>联系技术支持前请准备</h3>
                <ul>
                    <li>详细的问题描述和重现步骤</li>
                    <li>错误消息的完整截图</li>
                    <li>相关的日志文件</li>
                    <li>系统配置信息（操作系统、Python版本等）</li>
                    <li>软件版本信息</li>
                </ul>
            </div>

            <div class="interpretation">
                <h3>联系方式</h3>
                <ul>
                    <li><strong>邮箱</strong>: <EMAIL></li>
                    <li><strong>技术论坛</strong>: https://forum.example.com</li>
                    <li><strong>GitHub Issues</strong>: https://github.com/example/qtproject/issues</li>
                    <li><strong>在线文档</strong>: https://docs.example.com</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
            <p><a href="index.html" class="back-link">← 返回主页</a></p>
        </div>
    </div>
</body>
</html>
