"""
新建项目功能控制器

此模块负责处理新建项目的所有操作，主要包括：
1. 新建项目对话框管理
2. 脚本参数替换和生成
3. 批处理文件执行
4. 进度监控和日志处理
5. 文件清理和错误处理

工作流程：
1. 显示新建项目对话框
2. 获取用户输入并验证
3. 替换脚本模板参数
4. 创建批处理文件并执行
5. 监控执行进度
6. 清理临时文件

作者: [作者名]
日期: [日期]
"""

import os
import logging
import subprocess
import tempfile
from typing import Optional
from datetime import datetime
from PySide6.QtWidgets import QMessageBox, QDialog
from PySide6.QtCore import QCoreApplication

from window_manager import WindowManager, WindowType
from error_handler import (
    ErrorHandler, AppError, ErrorSeverity,
    ValidationError, FileOperationError, AnsysError
)
from resource_manager import ResourceManager
from views.new_project_dialog import NewProjectDialog
from views.project_progress_dialog import ProjectProgressDialog

logger = logging.getLogger(__name__)

def tr(text: str) -> str:
    """翻译函数的便捷包装"""
    return QCoreApplication.translate("NewProjectSlot", text)

def create_new_project(window_manager: WindowManager) -> None:
    """创建新的Workbench项目
    
    此函数执行完整的新项目创建流程：
    1. 显示新建项目对话框
    2. 获取用户配置
    3. 生成脚本文件
    4. 执行Workbench
    5. 监控进度
    6. 清理临时文件
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 获取主窗口实例和错误处理器
    main_window = window_manager.get_window(WindowType.MAIN)
    error_handler = ErrorHandler()
    
    if not main_window:
        error_handler.handle_error(
            AppError("无法获取主窗口实例", ErrorSeverity.CRITICAL)
        )
        return
    
    # 用于存储需要清理的临时文件
    temp_files = []
    
    try:
        # 1. 显示新建项目对话框
        dialog = NewProjectDialog(main_window)
        if dialog.exec() != QDialog.Accepted:
            logger.info("用户取消了新建项目操作")
            return
        
        # 2. 获取项目配置
        model_file, project_name, save_directory = dialog.get_project_config()
        logger.info(f"获取项目配置: 模型={model_file}, 名称={project_name}, 路径={save_directory}")
        
        # 3. 初始化资源管理器
        resource_manager = ResourceManager()
        try:
            resource_manager.initialize(main_window.ANSYS_Work_Dir)
        except Exception as e:
            raise FileOperationError(
                "初始化资源管理器失败",
                details={'work_dir': main_window.ANSYS_Work_Dir}
            ) from e
        
        # 4. 生成项目脚本
        script_content = generate_project_script(
            model_file, project_name, save_directory, resource_manager
        )
        
        # 5. 创建临时脚本文件
        script_file = resource_manager.create_temp_file(prefix="newproject", suffix=".py")
        temp_files.append(script_file)
        
        with open(script_file, "w", encoding="utf-8") as f:
            f.write(script_content)
        
        logger.info(f"创建项目脚本文件: {script_file}")
        
        # 6. 创建批处理文件
        bat_file = create_batch_file(main_window, script_file, resource_manager)
        temp_files.append(bat_file)
        
        # 7. 获取日志文件路径
        log_file_path = os.path.join(save_directory, f"{project_name}_creation.log")
        
        # 8. 显示进度对话框并执行（新建项目使用UTF-8编码）
        progress_dialog = ProjectProgressDialog(log_file_path, main_window, encoding='utf-8')

        # 设置新建项目的进度项目和关键词映射
        progress_dialog.setWindowTitle("新建项目进度")
        progress_dialog.title_label.setText("正在创建ANSYS Workbench项目...")

        # 配置进度项目（中文）
        progress_dialog.progress_items = [
            ("脚本开始执行", False),
            ("创建分析系统", False),
            ("导入几何文件", False),
            ("编辑几何", False),
            ("刷新模型组件", False),
            ("编辑模型", False),
            ("保存项目", False),
            ("脚本执行完毕", False)
        ]

        # 配置进度关键词映射（与脚本英文输出匹配，但界面显示中文）
        progress_dialog.progress_keywords = {
            "Script execution started...": 0,
            "Creating 'Transient Structural' analysis system...": 1,
            "Preparing to import geometry file:": 2,
            "Editing geometry (SpaceClaim)...": 3,
            "Refreshing model component...": 4,
            "Editing model (Mechanical)...": 5,
            "Preparing to save project to:": 6,
            "Script execution completed.": 7
        }

        # 启动批处理文件
        try:
            process = subprocess.Popen(
                f'"{bat_file}"',
                shell=True,
                cwd=os.path.dirname(bat_file)
            )
            
            logger.info(f"启动批处理文件: {bat_file}")
            
            # 显示进度对话框
            result = progress_dialog.exec()
            
            # 等待进程完成
            if process.poll() is None:
                process.wait()
            
            # 检查结果
            if progress_dialog.is_operation_completed():
                # 成功完成
                project_file_path = os.path.join(save_directory, f"{project_name}.wbpj")
                
                QMessageBox.information(
                    main_window,
                    tr("创建成功"),
                    tr(f"项目创建成功！\n\n项目文件: {project_file_path}")
                )
                
                logger.info(f"项目创建成功: {project_file_path}")
                
                # 询问是否设置为当前项目
                reply = QMessageBox.question(
                    main_window,
                    tr("设置当前项目"),
                    tr("是否将新创建的项目设置为当前工作项目？"),
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    main_window.WORKBENCH_Project_File = project_file_path
                    main_window.config_manager.set("ansys.project_file", project_file_path)
                    logger.info(f"设置当前项目文件: {project_file_path}")
                
            elif progress_dialog.is_operation_cancelled():
                # 用户取消
                logger.info("用户取消了项目创建操作")
                
                # 终止进程
                if process.poll() is None:
                    process.terminate()
                    process.wait()
                
            else:
                # 执行失败
                raise AnsysError(
                    "项目创建过程中出现错误",
                    details={'log_file': log_file_path}
                )
                
        except subprocess.SubprocessError as e:
            raise AnsysError(
                "执行ANSYS Workbench失败",
                details={'error': str(e), 'bat_file': bat_file}
            ) from e
        
        # 9. 清理日志文件（如果成功完成）
        if progress_dialog.is_operation_completed():
            try:
                if os.path.exists(log_file_path):
                    os.remove(log_file_path)
                    logger.info(f"清理日志文件: {log_file_path}")
            except Exception as e:
                logger.warning(f"清理日志文件失败: {e}")
        
    except AppError as e:
        # 处理应用程序异常
        error_handler.handle_error(e, main_window)
    except Exception as e:
        # 处理其他未预期的异常
        error_handler.handle_exception(e, main_window)
    finally:
        # 清理临时文件
        cleanup_temp_files(temp_files, error_handler)

def generate_project_script(model_file: str, project_name: str, save_directory: str, 
                          resource_manager: ResourceManager) -> str:
    """生成项目创建脚本
    
    Args:
        model_file: 模型文件路径
        project_name: 项目名称
        save_directory: 保存目录
        resource_manager: 资源管理器实例
        
    Returns:
        生成的脚本内容
    """
    try:
        # 读取原始脚本模板
        template_file = resource_manager.get_resource_path("originscript", "newfile.py")

        # 检查模板文件是否存在
        if not os.path.exists(template_file):
            raise FileOperationError(
                f"脚本模板文件不存在: {template_file}",
                details={
                    'template_file': template_file,
                    'base_dir': resource_manager.base_dir,
                    'originscript_dir': os.path.join(resource_manager.base_dir, "originscript")
                }
            )

        with open(template_file, "r", encoding="utf-8") as f:
            script_content = f.read()

        # 验证模板文件内容
        if not script_content or len(script_content.strip()) == 0:
            raise FileOperationError(
                "脚本模板文件内容为空",
                details={'template_file': template_file}
            )
        
        # 生成日志文件路径
        log_file_path = os.path.join(save_directory, f"{project_name}_creation.log")
        
        # 生成项目文件路径
        project_file_path = os.path.join(save_directory, f"{project_name}.wbpj")
        
        # 替换参数
        replacements = {
            'log_file_path = "D:/data/cs2425_run.log"': f'log_file_path = r"{log_file_path}"',
            'geometry_file = "C:/Users/<USER>/Desktop/tempPart.stp"': f'geometry_file = r"{model_file}"',
            'save_path = "D:/data/cs2425.wbpj"': f'save_path = r"{project_file_path}"'
        }
        
        # 执行替换
        replacements_made = 0
        for old_text, new_text in replacements.items():
            if old_text in script_content:
                script_content = script_content.replace(old_text, new_text)
                replacements_made += 1
                logger.debug(f"替换参数: {old_text[:50]}... -> {new_text[:50]}...")
            else:
                logger.warning(f"未找到要替换的文本: {old_text[:50]}...")

        logger.info(f"生成项目脚本，参数替换完成 ({replacements_made}/{len(replacements)} 项替换成功)")
        return script_content
        
    except Exception as e:
        raise FileOperationError(
            "生成项目脚本失败",
            details={'template_file': template_file}
        ) from e

def create_batch_file(main_window, script_file: str, resource_manager: ResourceManager) -> str:
    """创建批处理文件
    
    Args:
        main_window: 主窗口实例
        script_file: 脚本文件路径
        resource_manager: 资源管理器实例
        
    Returns:
        批处理文件路径
    """
    try:
        # 创建批处理文件
        ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
        script_file = script_file.replace("\\", "/")
        
        bat_content = f'"{ansys_path}" -B -R "{script_file}"'
        
        bat_file = resource_manager.create_temp_file(prefix="newproject", suffix=".bat")
        with open(bat_file, "w", encoding="utf-8") as f:
            f.write(bat_content)
        
        logger.info(f"创建批处理文件: {bat_file}")
        return bat_file
        
    except Exception as e:
        raise FileOperationError(
            "创建批处理文件失败",
            details={'script_file': script_file}
        ) from e

def cleanup_temp_files(temp_files: list, error_handler: ErrorHandler) -> None:
    """清理临时文件
    
    Args:
        temp_files: 临时文件列表
        error_handler: 错误处理器
    """
    for file_path in temp_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"清理临时文件: {file_path}")
        except Exception as e:
            error_handler.handle_error(
                AppError(
                    f"清理临时文件失败: {file_path}",
                    ErrorSeverity.WARNING,
                    details={'file': file_path, 'error': str(e)}
                )
            )
