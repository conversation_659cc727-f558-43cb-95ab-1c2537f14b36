"""
材料创建对话框模块

此模块定义了材料创建对话框类，负责：
1. 显示材料创建界面
2. 处理用户输入验证
3. 创建新的材料对象

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Optional
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QDoubleValidator
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QDoubleSpinBox, QPushButton,
    QMessageBox, QGroupBox
)

from core.material_manager import Material, MaterialCategory

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MaterialCreateDialog(QDialog):
    """材料创建对话框类"""
    
    # 自定义信号
    material_created = Signal(object)  # 材料创建信号，传递Material对象
    
    def __init__(self, parent=None, existing_names=None):
        """初始化对话框

        Args:
            parent: 父窗口
            existing_names: 已存在的材料名称列表，用于验证重复
        """
        super().__init__(parent)

        self.existing_names = existing_names or []
        self.created_material = None

        # 设置对话框属性
        self.setModal(True)
        self.setWindowTitle("创建新材料")
        self.setFixedSize(480, 380)

        # 设置窗口图标
        self._set_window_icon()

        # 设置UI
        self._setup_ui()

        # 应用样式
        self._apply_styles()

        # 连接信号槽
        self._connect_signals()

        # 设置默认值
        self._set_default_values()

        logger.debug("材料创建对话框初始化完成")

    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            from utils.icon_manager import get_operation_icon
            icon = get_operation_icon('new')
            if not icon.isNull():
                self.setWindowIcon(icon)
        except ImportError:
            # 如果图标管理器不可用，使用默认图标
            pass
    
    def _setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # 标题区域
        title_layout = QHBoxLayout()

        # 标题图标
        try:
            from utils.icon_manager import get_operation_icon
            icon_label = QLabel()
            icon = get_operation_icon('new')
            if not icon.isNull():
                pixmap = icon.pixmap(24, 24)
                icon_label.setPixmap(pixmap)
            title_layout.addWidget(icon_label)
        except ImportError:
            pass

        # 标题文本
        title_label = QLabel("创建新材料")
        title_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        title_label.setObjectName("dialog_title")
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        main_layout.addLayout(title_layout)

        # 材料属性组
        properties_group = QGroupBox("材料属性")
        properties_group.setObjectName("properties_group")
        properties_layout = QFormLayout(properties_group)
        properties_layout.setSpacing(15)
        properties_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # 材料名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入材料名称")
        self.name_edit.setObjectName("name_edit")
        name_label = QLabel("材料名称:")
        name_label.setObjectName("form_label")
        properties_layout.addRow(name_label, self.name_edit)

        # 杨氏模量
        self.young_modulus_spinbox = QDoubleSpinBox()
        self.young_modulus_spinbox.setRange(0.01, 999999.0)
        self.young_modulus_spinbox.setDecimals(2)
        self.young_modulus_spinbox.setSuffix(" GPa")
        self.young_modulus_spinbox.setValue(200.0)
        self.young_modulus_spinbox.setObjectName("young_modulus_spinbox")
        young_label = QLabel("杨氏模量:")
        young_label.setObjectName("form_label")
        properties_layout.addRow(young_label, self.young_modulus_spinbox)

        # 密度
        self.density_spinbox = QDoubleSpinBox()
        self.density_spinbox.setRange(0.01, 99999.0)
        self.density_spinbox.setDecimals(2)
        self.density_spinbox.setSuffix(" kg/m³")
        self.density_spinbox.setValue(7850.0)
        self.density_spinbox.setObjectName("density_spinbox")
        density_label = QLabel("密度:")
        density_label.setObjectName("form_label")
        properties_layout.addRow(density_label, self.density_spinbox)

        # 泊松比
        self.poisson_ratio_spinbox = QDoubleSpinBox()
        self.poisson_ratio_spinbox.setRange(0.0, 0.5)
        self.poisson_ratio_spinbox.setDecimals(3)
        self.poisson_ratio_spinbox.setSingleStep(0.01)
        self.poisson_ratio_spinbox.setValue(0.3)
        self.poisson_ratio_spinbox.setObjectName("poisson_ratio_spinbox")
        poisson_label = QLabel("泊松比:")
        poisson_label.setObjectName("form_label")
        properties_layout.addRow(poisson_label, self.poisson_ratio_spinbox)

        main_layout.addWidget(properties_group)

        # 添加弹簧
        main_layout.addStretch()

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setFixedSize(100, 40)
        self.cancel_button.setObjectName("cancel_button")
        button_layout.addWidget(self.cancel_button)

        # 弹簧
        button_layout.addStretch()

        # 创建按钮
        self.create_button = QPushButton("创建")
        self.create_button.setFixedSize(100, 40)
        self.create_button.setDefault(True)
        self.create_button.setObjectName("create_button")
        button_layout.addWidget(self.create_button)

        main_layout.addLayout(button_layout)

    def _apply_styles(self):
        """应用样式表"""
        style = """
        /* 对话框整体样式 */
        QDialog {
            background-color: #fafafa;
            border-radius: 12px;
        }

        /* 标题样式 */
        QLabel#dialog_title {
            font-size: 18px;
            font-weight: 600;
            color: #333333;
            margin-left: 8px;
        }

        /* 分组框样式 */
        QGroupBox#properties_group {
            font-weight: 600;
            font-size: 11pt;
            border: 2px solid #4caf50;
            border-radius: 10px;
            margin-top: 12px;
            padding-top: 8px;
            background-color: #ffffff;
        }

        QGroupBox#properties_group::title {
            subcontrol-origin: margin;
            subcontrol-position: top left;
            padding: 4px 12px;
            background-color: #4caf50;
            color: white;
            border-radius: 6px;
            margin-left: 10px;
            font-weight: 600;
        }

        /* 表单标签样式 */
        QLabel#form_label {
            color: #333333;
            font-weight: 500;
            font-size: 10pt;
            padding: 2px 0;
        }

        /* 输入控件样式 */
        QLineEdit#name_edit, QDoubleSpinBox {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 10pt;
            background-color: #ffffff;
            selection-background-color: #4caf50;
        }

        QLineEdit#name_edit:focus, QDoubleSpinBox:focus {
            border-color: #4caf50;
            background-color: #ffffff;
        }

        QLineEdit#name_edit:hover, QDoubleSpinBox:hover {
            border-color: #66bb6a;
        }

        /* 数值输入框按钮样式 */
        QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
            width: 20px;
            border: none;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
            background-color: #e8f5e8;
        }

        /* 按钮样式 */
        QPushButton#create_button {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #4caf50, stop: 1 #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 11pt;
        }

        QPushButton#create_button:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #45a049, stop: 1 #3d8b40);
        }

        QPushButton#create_button:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #3d8b40, stop: 1 #357a38);
        }

        QPushButton#cancel_button {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #f5f5f5, stop: 1 #e0e0e0);
            color: #333333;
            border: 2px solid #d0d0d0;
            border-radius: 8px;
            font-weight: 500;
            font-size: 11pt;
        }

        QPushButton#cancel_button:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #e8e8e8, stop: 1 #d0d0d0);
            border-color: #b0b0b0;
        }

        QPushButton#cancel_button:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                        stop: 0 #d0d0d0, stop: 1 #c0c0c0);
        }
        """

        self.setStyleSheet(style)

    def _connect_signals(self):
        """连接信号槽"""
        self.cancel_button.clicked.connect(self.reject)
        self.create_button.clicked.connect(self._on_create_clicked)
        self.name_edit.textChanged.connect(self._on_name_changed)
    
    def _set_default_values(self):
        """设置默认值"""
        self.name_edit.setFocus()
        self._update_create_button_state()
    
    def _on_name_changed(self):
        """材料名称变化处理"""
        self._update_create_button_state()
    
    def _update_create_button_state(self):
        """更新创建按钮状态"""
        name = self.name_edit.text().strip()
        self.create_button.setEnabled(bool(name))
    
    def _on_create_clicked(self):
        """创建按钮点击处理"""
        try:
            # 验证输入
            if not self._validate_input():
                return
            
            # 创建材料对象
            material = Material(
                name=self.name_edit.text().strip(),
                young_modulus=self.young_modulus_spinbox.value(),
                density=self.density_spinbox.value(),
                poisson_ratio=self.poisson_ratio_spinbox.value(),
                category=MaterialCategory.CUSTOM
            )
            
            self.created_material = material
            self.material_created.emit(material)
            
            logger.info(f"创建新材料: {material.name}")
            self.accept()
            
        except Exception as e:
            logger.error(f"创建材料失败: {e}")
            QMessageBox.critical(
                self,
                "创建失败",
                f"创建材料时发生错误：\n{str(e)}"
            )
    
    def _validate_input(self) -> bool:
        """验证用户输入
        
        Returns:
            bool: 验证是否通过
        """
        name = self.name_edit.text().strip()
        
        # 检查名称是否为空
        if not name:
            QMessageBox.warning(
                self,
                "输入错误",
                "请输入材料名称。"
            )
            self.name_edit.setFocus()
            return False
        
        # 检查名称是否重复
        if name in self.existing_names:
            QMessageBox.warning(
                self,
                "名称重复",
                f"材料名称 '{name}' 已存在，请使用其他名称。"
            )
            self.name_edit.setFocus()
            self.name_edit.selectAll()
            return False
        
        # 检查属性值范围
        if self.young_modulus_spinbox.value() <= 0:
            QMessageBox.warning(
                self,
                "输入错误",
                "杨氏模量必须大于0。"
            )
            self.young_modulus_spinbox.setFocus()
            return False
        
        if self.density_spinbox.value() <= 0:
            QMessageBox.warning(
                self,
                "输入错误",
                "密度必须大于0。"
            )
            self.density_spinbox.setFocus()
            return False
        
        if not (0 <= self.poisson_ratio_spinbox.value() <= 0.5):
            QMessageBox.warning(
                self,
                "输入错误",
                "泊松比必须在0到0.5之间。"
            )
            self.poisson_ratio_spinbox.setFocus()
            return False
        
        return True
    
    def get_created_material(self) -> Optional[Material]:
        """获取创建的材料对象
        
        Returns:
            Material: 创建的材料对象，如果没有创建返回None
        """
        return self.created_material
    
    @staticmethod
    def create_material(parent=None, existing_names=None) -> Optional[Material]:
        """静态方法：显示材料创建对话框
        
        Args:
            parent: 父窗口
            existing_names: 已存在的材料名称列表
            
        Returns:
            Material: 创建的材料对象，如果取消则返回None
        """
        try:
            dialog = MaterialCreateDialog(parent, existing_names)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                return dialog.get_created_material()
            return None
            
        except Exception as e:
            logger.error(f"显示材料创建对话框失败: {e}")
            return None
