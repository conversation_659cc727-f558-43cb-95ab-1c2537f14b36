"""
模态分析槽函数模块

此模块提供模态分析相关的槽函数，包括：
1. 单个模态计算功能
2. 配置文件验证
3. ANSYS Workbench集成
4. 错误处理和用户反馈

作者: 振动传递计算软件开发团队
日期: 2025-01-29
"""
import glob
import shutil
import os
import json
import subprocess
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from PySide6.QtWidgets import QMessageBox
from window_manager import WindowManager, WindowType
from resource_manager import ResourceManager
from error_handler import (
    ErrorHandler, AppError, AnsysError, ValidationError,
    FileOperationError, ConfigurationError, ErrorSeverity
)

logger = logging.getLogger(__name__)


# 简化的模态结果跟踪器
class ModalResultTracker:
    """模态分析结果跟踪器"""

    def __init__(self):
        self.current_result = None

    def update_result(self, result_info):
        """更新结果信息"""
        self.current_result = result_info

    def clear_result(self):
        """清除结果信息"""
        self.current_result = None


class ModalResultInfo:
    """模态分析结果信息"""

    def __init__(self, output_dir, timestamp, mesh_name, element_size, config_files):
        self.output_dir = output_dir
        self.timestamp = timestamp
        self.mesh_name = mesh_name
        self.element_size = element_size
        self.config_files = config_files


def cleanup_modal_temp_files(resource_manager, error_handler, mesh_window, exclude_paths=None):
    """清理模态分析临时文件

    Args:
        resource_manager: 资源管理器实例
        error_handler: 错误处理器
        mesh_window: 网格窗口实例
        exclude_paths: 需要排除的文件路径列表
    """
    try:
        if hasattr(resource_manager, 'cleanup_temp_files'):
            resource_manager.cleanup_temp_files(exclude_paths or [])
        logger.info("模态分析临时文件清理完成")
    except Exception as e:
        logger.warning(f"清理模态分析临时文件失败: {str(e)}")
        if error_handler and mesh_window:
            error_handler.handle_error(
                AppError(f"清理临时文件失败: {str(e)}", ErrorSeverity.WARNING),
                mesh_window
            )


def execute_single_modal_calculation(window_manager: WindowManager, mesh_parameter, calc_params: Dict[str, Any]) -> None:
    """执行单个模态计算的完整流程

    此函数按顺序执行以下步骤：
    1. 初始化资源管理器
    2. 创建临时输出目录
    3. 验证和获取配置文件路径
    4. 准备模态分析脚本
    5. 修改脚本内容
    6. 创建新版本的脚本
    7. 创建Workbench控制脚本
    8. 创建批处理文件
    9. 执行批处理文件

    Args:
        window_manager: 窗口管理器实例
        mesh_parameter: 网格参数对象
        calc_params: 计算参数字典
    """
    logger.info("=== execute_single_modal_calculation 开始执行 ===")
    print("DEBUG: execute_single_modal_calculation 开始执行")
    print(f"DEBUG: mesh_parameter.name = {mesh_parameter.name}")
    print(f"DEBUG: calc_params = {calc_params}")

    # 获取窗口实例和错误处理器
    try:
        mesh_window = window_manager.get_window(WindowType.MESH)
        main_window = window_manager.get_window(WindowType.MAIN)
        error_handler = ErrorHandler()
        modal_tracker = ModalResultTracker()

        logger.info(f"窗口实例获取成功: mesh_window={mesh_window is not None}, main_window={main_window is not None}")
        print(f"DEBUG: 窗口实例获取成功: mesh_window={mesh_window is not None}, main_window={main_window is not None}")

    except Exception as e:
        logger.error(f"获取窗口实例失败: {str(e)}", exc_info=True)
        print(f"DEBUG: 获取窗口实例失败: {str(e)}")
        raise

    if not mesh_window or not main_window:
        error_handler.handle_error(
            AppError("无法获取窗口实例", ErrorSeverity.CRITICAL)
        )
        return

    # 用于存储需要保护的文件路径
    active_files = []

    try:
        # 1. 初始化资源管理器
        resource_manager = ResourceManager()
        try:
            resource_manager.initialize(main_window.ANSYS_Work_Dir)

            # 首先清理旧的临时文件
            cleanup_modal_temp_files(resource_manager, error_handler, mesh_window)

        except Exception as e:
            raise ConfigurationError(
                "初始化资源管理器失败",
                details={'work_dir': main_window.ANSYS_Work_Dir}
            ) from e

        # 2. 创建临时输出目录
        try:
            temp_dir_name = resource_manager.create_temp_file(prefix="modal_output_")
            active_files.append(temp_dir_name)
            os.remove(temp_dir_name)  # 删除生成的文件
            temp_output_dir = temp_dir_name  # 使用这个名字作为目录名
            os.makedirs(temp_output_dir, exist_ok=True)

            # 创建新的模态结果跟踪记录
            modal_tracker.update_result(ModalResultInfo(
                output_dir=temp_output_dir,
                timestamp=datetime.now(),
                mesh_name=mesh_parameter.name,
                element_size=mesh_parameter.size,
                config_files=[]
            ))

        except Exception as e:
            raise FileOperationError(
                "创建临时输出目录失败",
                details={'error': str(e)}
            ) from e

         # 3.生成模态分析配置
        try:
            modal_config = _generate_modal_config(mesh_parameter, resource_manager, calc_params, temp_output_dir)
            active_files.append(modal_config['config_path'])
            logger.info(f"模态分析配置生成成功: {modal_config['config_path']}")
        except Exception as e:
            raise ConfigurationError(
                "生成模态分析配置失败",
                details={'mesh': mesh_parameter.name}
            ) from e
        
        # 4. 获取配置文件路径
        try:
            ansys_result_path = os.path.join(resource_manager.json_dir, "analysis_modal_config_latest.json")
            constrain_result_path = os.path.join(resource_manager.json_dir, "constrain_config_latest.json")
            connection_result_path = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            cfg_path = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")

            # 检查文件是否存在
            for path in [ansys_result_path, constrain_result_path, connection_result_path]:
                if not os.path.exists(path):
                    raise FileOperationError(
                        "配置文件不存在",
                        details={'missing_file': path}
                    )
                # 存在则读取，并显示在mesh_window的状态显示中
                else:
                    with open(path, "r", encoding="utf-8") as f:
                        content = f.read()
                        if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                            current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                            mesh_window.ui.textEdit_calc_stats.setPlainText(
                                current_text + f"\n✅ 配置文件验证通过: {os.path.basename(path)}"
                            )

        except Exception as e:
            raise FileOperationError(
                "获取配置文件路径失败",
                details={'error': str(e)}
            ) from e

       

        # 5. 准备源脚本
        try:
            source_script_path = os.path.join(resource_manager.base_dir, "originscript", "modal.py")
            with open(source_script_path, "r", encoding="utf-8") as f:
                script_content = f.read()

        except Exception as e:
            raise FileOperationError(
                "读取源脚本文件失败",
                details={'source_file': source_script_path}
            ) from e

        # 6. 修改脚本内容
        target_dir = main_window.ANSYS_Work_Dir.replace("\\", "/")
        old_target_dir = r'target_directory = r"D:/data/all-XM/autoworkbench/csdaima"'
        new_target_dir = f'target_directory = r"{target_dir}"'
        script_content = script_content.replace(old_target_dir, new_target_dir)

        # 替换配置文件路径
        replacements = {
            r'ansys_result_path = r"D:/data/all-XM/autoworkbench/csdaima/analysis_config_latest.json"':
                f'ansys_result_path = r"{ansys_result_path}"',
            r'constrain_result_path = r"D:/data/all-XM/autoworkbench/csdaima/2.json"':
                f'constrain_result_path = r"{constrain_result_path}"',
            r'connection_result_path = r"D:/data/all-XM/autoworkbench/csdaima/connection_result.json"':
                f'connection_result_path = r"{connection_result_path}"',
            r'cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"':
                f'cfg_path = r"{cfg_path}"'
        }

        for old, new in replacements.items():
            script_content = script_content.replace(old, new)

        # 7. 创建新版本的脚本
        try:
            script_file = resource_manager.create_script_version("modal_analysis.py", script_content)
            active_files.append(script_file)

        except Exception as e:
            raise FileOperationError(
                "创建脚本文件失败",
                details={'script_name': "modal_analysis.py"}
            ) from e

        # 8. 清理旧版本
        resource_manager.clean_old_versions("modal_analysis.py")

        # 9. 创建Workbench控制脚本
        wb_script_content = f'''
# encoding: utf-8
Open(FilePath=r"{main_window.WORKBENCH_Project_File}")
system1 = GetSystem(Name="SYS")
model1 = system1.GetContainer(ComponentName="Model")
model1.Edit()
'''

        # 10. 获取最新的脚本路径
        script_file = script_file.replace("\\", "/")

        # 11. 添加执行命令
        wb_script_content += f'''
model1.SendCommand(Command=r'WB.AppletList.Applet("DSApplet").App.Script.doToolsRunMacro("{script_file}")')
model1.Exit()
Save(Overwrite=True)
'''

        # 12. 创建Workbench脚本文件
        try:
            wb_script_file = resource_manager.create_temp_file(prefix="modal_wb", suffix=".py")
            active_files.append(wb_script_file)
            with open(wb_script_file, "w", encoding="utf-8") as f:
                f.write(wb_script_content)

        except Exception as e:
            raise FileOperationError(
                "创建Workbench脚本文件失败",
                details={'script_file': wb_script_file}
            ) from e

        # 13. 创建批处理文件
        try:
            ansys_path = main_window.ANSYS_Start_File.replace("\\", "/")
            wb_script_file = wb_script_file.replace("\\", "/")
            bat_content = (
                f'"{ansys_path}" '
                f'-B -R "{wb_script_file}"'
            )
            bat_file = resource_manager.create_temp_file(prefix="modal", suffix=".bat")
            active_files.append(bat_file)
            with open(bat_file, "w", encoding="utf-8") as f:
                f.write(bat_content)

        except Exception as e:
            raise FileOperationError(
                "创建批处理文件失败",
                details={'bat_file': bat_file}
            ) from e

        # 14. 执行批处理文件
        try:
            # 更新界面状态
            if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                mesh_window.ui.textEdit_calc_stats.setPlainText(
                    current_text + f"\n🚀 开始执行ANSYS模态分析..."
                )

            result = subprocess.run(
                f'"{bat_file}"',
                shell=True,
                check=True,
                text=True,
                capture_output=True
            )

            # 检查ANSYS输出中的错误信息
            if "error" in result.stdout.lower() or "error" in result.stderr.lower():
                raise AnsysError(
                    "ANSYS执行过程中出现错误",
                    details={
                        'stdout': result.stdout,
                        'stderr': result.stderr
                    }
                )

            # 显示成功消息
            error_handler.handle_error(
                AppError(
                    f"模态计算完成: {mesh_parameter.name}",
                    ErrorSeverity.INFO
                ),
                mesh_window
            )

            # 更新界面状态
            if hasattr(mesh_window, 'ui') and hasattr(mesh_window.ui, 'textEdit_calc_stats'):
                current_text = mesh_window.ui.textEdit_calc_stats.toPlainText()
                mesh_window.ui.textEdit_calc_stats.setPlainText(
                    current_text + f"\n✅ 模态计算成功完成!"
                )

            logger.info(f"模态计算成功完成: {mesh_parameter.name}")

        except subprocess.CalledProcessError as e:
            modal_tracker.clear_result()  # 清除失败的结果记录
            raise AnsysError(
                "ANSYS执行失败",
                details={
                    'return_code': e.returncode,
                    'stdout': e.stdout,
                    'stderr': e.stderr
                }
            ) from e

    except AppError as e:
        # 处理应用程序异常
        modal_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_error(e, mesh_window)
    except Exception as e:
        # 处理其他未预期的异常
        modal_tracker.clear_result()  # 清除失败的结果记录
        error_handler.handle_exception(e, mesh_window)
    finally:
        # 清理临时文件，但排除当前正在使用的文件
        cleanup_modal_temp_files(
            resource_manager,
            error_handler,
            mesh_window,
            exclude_paths=active_files
        )


def _generate_modal_config(mesh_parameter, resource_manager: ResourceManager,calc_params: Dict[str, Any], temp_output_dir: str) -> Dict[str, Any]:
    """生成模态分析网格配置文件

    参考 ctrl/result_slot.py 中的配置文件生成模式，
    生成与 originscript/modal.py 兼容的网格配置文件。

    Args:
        mesh_parameter: 网格参数对象，包含以下属性：
            - name: 网格名称 (str)
            - size: 网格尺寸，单位毫米 (float)
        calc_params: 计算参数字典
        temp_output_dir: 临时输出目录路径 (str)

    Returns:
        Dict[str, Any]: 包含以下键值的配置字典：
            - config_path: 配置文件路径 (str)
            - output_directory: 输出目录路径 (str)
            - element_size: 网格尺寸，单位米 (float)

    Raises:
        ValueError: 当网格参数无效时
        FileOperationError: 当文件操作失败时
    """
    try:
        # 1. 参数验证
        if not hasattr(mesh_parameter, 'name') or not hasattr(mesh_parameter, 'size'):
            raise ValueError("网格参数对象缺少必需的属性 (name, size)")

        mesh_name = str(mesh_parameter.name)
        mesh_size_mm = float(mesh_parameter.size)

        if mesh_size_mm <= 0:
            raise ValueError(f"网格尺寸必须大于0，当前值: {mesh_size_mm} mm")

        if not temp_output_dir or not isinstance(temp_output_dir, str):
            raise ValueError("临时输出目录路径无效")

        # 2. 单位转换：毫米转米
        element_size_m = mesh_size_mm / 1000.0

        # 3. 构建输出目录路径
        output_directory = os.path.join(temp_output_dir, f"modal_result_{mesh_name}").replace("\\", "/")

        # 4. 创建输出目录
        try:
            os.makedirs(output_directory, exist_ok=True)
            logger.info(f"输出目录创建成功: {output_directory}")
        except Exception as e:
            raise FileOperationError(
                "创建输出目录失败",
                details={'directory': output_directory, 'error': str(e)}
            ) from e

        # 5. 构建网格配置数据（与 originscript/modal.py 兼容）
        modal_config = {
            "element_size": [element_size_m],  # 数组格式，单位：米
            "output_directory": output_directory
        }

        # 6. 保存网格配置文件
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)
            
            # 清理旧的配置文件，只保留最新的5个
            pattern = os.path.join(resource_manager.json_dir, "mesh_config_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            for old_file in json_files[5:]:  # 保留最新的5个文件
                try:
                    os.remove(old_file)
                    print(f"清理旧配置文件: {old_file}")
                except Exception as e:
                    print(f"警告: 无法删除旧的配置文件 {old_file}: {str(e)}")
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"mesh_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)
            
            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(modal_config, f, indent=4, ensure_ascii=False)
                
            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "mesh_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")
                    
            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
        except Exception as e:
            raise FileOperationError(
                "保存网格配置文件失败",
                details={'file': config_file, 'error': str(e)}
            ) from e
        
        

        # 7. 生成分析设置配置文件
        try:
            _generate_analysis_config(resource_manager, calc_params)
        except Exception as e:
            logger.error(f"生成分析设置配置失败: {str(e)}")
            # 不抛出异常，因为网格配置已经成功生成

        # 8. 记录成功日志
        logger.info(f"网格配置已保存: {config_file}")
        logger.info(f"网格参数: 名称={mesh_name}, 尺寸={mesh_size_mm}mm ({element_size_m}m)")
        logger.info(f"输出目录: {output_directory}")

        # 9. 返回配置信息
        return {
            'config_path': config_file,
            'output_directory': output_directory,
            'element_size': element_size_m
        }

    except (ValueError, TypeError) as e:
        logger.error(f"网格配置参数验证失败: {str(e)}")
        raise ValueError(f"网格配置参数验证失败: {str(e)}") from e
    except Exception as e:
        logger.error(f"生成网格配置失败: {str(e)}", exc_info=True)
        raise FileOperationError(
            "生成网格配置失败",
            details={'error': str(e)}
        ) from e


def _generate_analysis_config(resource_manager: ResourceManager, calc_params: Dict[str, Any]) -> None:
    """生成分析设置配置文件

    参考 ctrl/analysis_slot.py 中的 get_analysis_json() 函数实现模式，
    生成与 originscript/modal.py 兼容的分析配置文件。

    Args:
        calc_params: 计算参数字典，包含以下键值：
            - modal_count: 模态数量 (int)
            - limit_freq: 是否限制频率范围 (bool)
            - freq_min: 最小频率 (float)
            - freq_max: 最大频率 (float)

    Raises:
        ValueError: 当参数类型或值不正确时
        FileOperationError: 当文件写入失败时
    """
    try:
        # 1. 参数验证和类型转换
        modal_count = int(calc_params.get('modal_count', 12))
        limit_freq = bool(calc_params.get('limit_freq', False))
        freq_min = float(calc_params.get('freq_min', 0.0))
        freq_max = float(calc_params.get('freq_max', 1000.0))

        # 验证参数有效性
        if modal_count <= 0:
            raise ValueError(f"模态数量必须大于0，当前值: {modal_count}")
        if freq_min < 0:
            raise ValueError(f"最小频率不能为负数，当前值: {freq_min}")
        if freq_max <= freq_min:
            raise ValueError(f"最大频率({freq_max})必须大于最小频率({freq_min})")

        # 2. 构建配置数据结构（与 originscript/modal.py 兼容）
        analysis_config = {
            "analysis_settings": {
                "MaximumModesToFind": modal_count,
                "LimitSearchToRange": limit_freq,
                "MinimumFrequency": freq_min,
                "MaximumFrequency": freq_max
            }
        }

        # 3. 保存分析配置文件
        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)
            
            # 清理旧的配置文件，只保留最新的5个
            pattern = os.path.join(resource_manager.json_dir, "analysis_config_*.json")
            json_files = sorted(glob.glob(pattern), key=os.path.getctime, reverse=True)
            for old_file in json_files[5:]:  # 保留最新的5个文件
                try:
                    os.remove(old_file)
                    print(f"清理旧配置文件: {old_file}")
                except Exception as e:
                    print(f"警告: 无法删除旧的配置文件 {old_file}: {str(e)}")
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"analysis_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)
            
            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(analysis_config, f, indent=4, ensure_ascii=False)
                
            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "analysis_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")
                    
            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
        except Exception as e:
            raise FileOperationError(
                "保存分析配置文件失败",
                details={'file': config_file, 'error': str(e)}
            ) from e
        

        # 4. 记录成功日志
        logger.info(f"分析配置已保存: {config_file}")
        logger.info(f"配置参数: 模态数={modal_count}, 频率限制={limit_freq}, 频率范围=[{freq_min}, {freq_max}]")

    except (ValueError, TypeError) as e:
        logger.error(f"分析配置参数验证失败: {str(e)}")
        raise ValueError(f"分析配置参数验证失败: {str(e)}") from e
    except Exception as e:
        logger.error(f"生成分析配置失败: {str(e)}", exc_info=True)
        raise FileOperationError(
            "生成分析配置失败",
            details={'error': str(e)}
        ) from e

