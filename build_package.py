#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
振动传递计算软件打包脚本

此脚本用于使用PyInstaller打包振动传递计算软件，包括：
1. 环境检查和依赖验证
2. 清理旧的构建文件
3. 执行PyInstaller打包
4. 验证打包结果
5. 创建安装包

作者: [作者名]
日期: [日期]
"""

import os
import sys
import shutil
import subprocess
import time
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('build_package.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PackageBuilder:
    """打包构建器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.spec_file = self.project_root / "qt_new.spec"
        
    def check_environment(self):
        """检查打包环境"""
        logger.info("🔍 检查打包环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        logger.info(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 8):
            logger.error("❌ Python版本过低，需要Python 3.8或更高版本")
            return False
            
        # 检查PyInstaller
        try:
            import PyInstaller
            logger.info(f"PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            logger.error("❌ PyInstaller未安装，请运行: pip install pyinstaller")
            return False
            
        # 检查关键依赖
        required_packages = [
            'PySide6', 'numpy', 'matplotlib', 'pandas', 
            'fastapi', 'uvicorn', 'openpyxl'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
                logger.info(f"✅ {package} - 已安装")
            except ImportError:
                missing_packages.append(package)
                logger.error(f"❌ {package} - 未安装")
        
        if missing_packages:
            logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
            logger.error("请运行: pip install -r requirements.txt")
            return False
            
        # 检查spec文件
        if not self.spec_file.exists():
            logger.error(f"❌ 找不到spec文件: {self.spec_file}")
            return False
            
        logger.info("✅ 环境检查通过")
        return True
    
    def clean_build_files(self):
        """清理旧的构建文件"""
        logger.info("🧹 清理旧的构建文件...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        
        for dir_path in dirs_to_clean:
            if dir_path.exists():
                try:
                    shutil.rmtree(dir_path)
                    logger.info(f"✅ 已清理: {dir_path}")
                except Exception as e:
                    logger.warning(f"⚠️ 清理失败 {dir_path}: {e}")
            else:
                logger.info(f"📁 目录不存在: {dir_path}")
    
    def build_package(self):
        """执行PyInstaller打包"""
        logger.info("📦 开始PyInstaller打包...")
        
        # 构建命令
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",  # 清理缓存
            "--noconfirm",  # 不询问覆盖
            str(self.spec_file)
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 执行打包
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            
            # 记录结束时间
            end_time = time.time()
            build_time = end_time - start_time
            
            if result.returncode == 0:
                logger.info(f"✅ 打包成功! 耗时: {build_time:.2f}秒")
                logger.info("标准输出:")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"❌ 打包失败! 返回码: {result.returncode}")
                logger.error("标准错误:")
                logger.error(result.stderr)
                logger.error("标准输出:")
                logger.error(result.stdout)
                return False
                
        except Exception as e:
            logger.error(f"❌ 打包过程中发生异常: {e}")
            return False
    
    def verify_package(self):
        """验证打包结果"""
        logger.info("🔍 验证打包结果...")
        
        # 检查输出目录
        output_dir = self.dist_dir / "vibration_transfer"
        if not output_dir.exists():
            logger.error(f"❌ 输出目录不存在: {output_dir}")
            return False
            
        # 检查主执行文件
        exe_file = output_dir / "振动传递计算软件.exe"
        if not exe_file.exists():
            logger.error(f"❌ 主执行文件不存在: {exe_file}")
            return False
            
        # 检查文件大小
        exe_size = exe_file.stat().st_size / (1024 * 1024)  # MB
        logger.info(f"📊 主执行文件大小: {exe_size:.2f} MB")
        
        # 检查关键目录
        required_dirs = [
            "assets", "help", "config", "translations"
        ]
        
        for dir_name in required_dirs:
            dir_path = output_dir / dir_name
            if dir_path.exists():
                logger.info(f"✅ 目录存在: {dir_name}")
            else:
                logger.warning(f"⚠️ 目录缺失: {dir_name}")
        
        # 统计总大小
        total_size = sum(f.stat().st_size for f in output_dir.rglob('*') if f.is_file())
        total_size_mb = total_size / (1024 * 1024)
        logger.info(f"📊 总包大小: {total_size_mb:.2f} MB")
        
        logger.info("✅ 打包结果验证完成")
        return True
    
    def create_installer(self):
        """创建安装包（可选）"""
        logger.info("📦 创建安装包...")
        
        # 这里可以添加创建安装包的逻辑
        # 例如使用NSIS、Inno Setup等工具
        
        logger.info("ℹ️ 安装包创建功能待实现")
        logger.info("您可以手动使用NSIS或Inno Setup创建安装包")
    
    def run(self):
        """运行完整的打包流程"""
        logger.info("🚀 开始振动传递计算软件打包流程")
        logger.info("=" * 50)
        
        try:
            # 1. 环境检查
            if not self.check_environment():
                logger.error("❌ 环境检查失败，打包终止")
                return False
            
            # 2. 清理旧文件
            self.clean_build_files()
            
            # 3. 执行打包
            if not self.build_package():
                logger.error("❌ 打包失败，流程终止")
                return False
            
            # 4. 验证结果
            if not self.verify_package():
                logger.error("❌ 打包验证失败")
                return False
            
            # 5. 创建安装包（可选）
            self.create_installer()
            
            logger.info("=" * 50)
            logger.info("🎉 打包流程完成!")
            logger.info(f"📁 输出目录: {self.dist_dir / 'vibration_transfer'}")
            logger.info("💡 您可以在dist/vibration_transfer目录中找到打包好的程序")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 打包流程发生异常: {e}")
            return False

def main():
    """主函数"""
    builder = PackageBuilder()
    success = builder.run()
    
    if success:
        print("\n🎉 打包成功完成!")
        print("📁 请查看 dist/vibration_transfer 目录")
    else:
        print("\n❌ 打包失败!")
        print("📋 请查看 build_package.log 了解详细信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
