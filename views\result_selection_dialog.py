"""
计算结果选择对话框

此模块定义了计算结果选择对话框，用于：
1. 显示所有已完成模态计算的网格
2. 显示每个网格的计算结果摘要
3. 提供结果预览功能
4. 允许用户选择结果用于后续振动计算

作者: AI Assistant
日期: 2025-07-25
"""

import logging
from typing import List, Dict, Any, Optional
from PySide6.QtCore import Qt, Signal
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QTextEdit, QHeaderView, QMessageBox,
                               QSplitter, QListWidget, QListWidgetItem)
from .custom_message_box import CustomMessageBox
from PySide6.QtGui import QFont, QColor

from core.mesh_manager import MeshParameter, MeshStatus

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class ResultSelectionDialog(QDialog):
    """计算结果选择对话框"""
    
    # 信号定义
    results_selected = Signal(list)  # 结果选择信号：(选中的网格列表)
    
    def __init__(self, parent=None, completed_meshes: List[MeshParameter] = None):
        super().__init__(parent)
        
        self.completed_meshes = completed_meshes or []
        self.selected_meshes = []
        
        self._setup_ui()
        self._setup_connections()
        self._populate_data()
        
        logger.debug(f"计算结果选择对话框初始化完成，可用结果数量: {len(self.completed_meshes)}")
    
    def _setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("选择计算结果")
        self.setModal(True)
        self.resize(1000, 700)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("选择计算结果用于后续振动分析")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # 添加单选提示
        hint_label = QLabel("请选择一个网格结果用于后续振动分析")
        hint_label.setStyleSheet("color: #666; font-style: italic; margin: 5px;")
        hint_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(hint_label)
        
        # 主分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧：结果列表
        left_widget = QGroupBox("可用计算结果")
        left_layout = QVBoxLayout(left_widget)
        
        # 选择状态显示 - 单选模式不需要全选/全不选按钮
        selection_layout = QHBoxLayout()

        selection_layout.addStretch()

        self.label_selection_count = QLabel("已选择: 0 个结果")
        self.label_selection_count.setStyleSheet("font-weight: bold; color: #0078d4;")
        selection_layout.addWidget(self.label_selection_count)

        left_layout.addLayout(selection_layout)
        
        # 结果列表 - 支持多选模式
        self.result_list = QListWidget()
        self.result_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)
        left_layout.addWidget(self.result_list)
        
        splitter.addWidget(left_widget)
        
        # 右侧：结果预览
        right_widget = QGroupBox("结果预览")
        right_layout = QVBoxLayout(right_widget)
        
        # 网格信息表格
        self.info_table = QTableWidget()
        self.info_table.setColumnCount(2)
        self.info_table.setHorizontalHeaderLabels(["属性", "值"])
        self.info_table.verticalHeader().setVisible(False)
        self.info_table.setMaximumHeight(200)
        
        # 设置表格列宽
        header = self.info_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        right_layout.addWidget(self.info_table)
        
        # 频率结果显示
        freq_label = QLabel("模态频率 (Hz)")
        freq_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        right_layout.addWidget(freq_label)
        
        self.freq_text = QTextEdit()
        self.freq_text.setMaximumHeight(150)
        self.freq_text.setReadOnly(True)
        right_layout.addWidget(self.freq_text)
        
        # 计算统计信息
        stats_label = QLabel("计算统计")
        stats_label.setStyleSheet("font-weight: bold; margin-top: 10px;")
        right_layout.addWidget(stats_label)
        
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(100)
        self.stats_text.setReadOnly(True)
        right_layout.addWidget(self.stats_text)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        layout.addWidget(splitter)
        
        # 使用说明
        help_label = QLabel("""
使用说明:
• 左侧列表显示所有已完成模态计算的网格
• 点击网格名称可在右侧预览计算结果
• 可以选择一个或多个结果用于后续振动分析
• 选中的结果将用于振动响应计算和对比分析
        """.strip())
        help_label.setStyleSheet("""
            QLabel {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
                color: #666;
            }
        """)
        layout.addWidget(help_label)
        
        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setMinimumSize(100, 35)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
                border: 1px solid #34495e;
            }
            QPushButton:pressed {
                background-color: #34495e;
            }
        """)
        button_layout.addWidget(self.cancel_button)

        self.confirm_button = QPushButton("确认选择")
        self.confirm_button.setMinimumSize(100, 35)
        self.confirm_button.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #27ae60;
                border: 1px solid #1e8449;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        button_layout.addWidget(self.confirm_button)
        
        layout.addLayout(button_layout)
    
    def _setup_connections(self):
        """设置信号连接"""
        self.cancel_button.clicked.connect(self.reject)
        self.confirm_button.clicked.connect(self._on_confirm)
        self.result_list.itemSelectionChanged.connect(self._on_selection_changed)
        self.result_list.currentItemChanged.connect(self._on_current_item_changed)
    
    def _populate_data(self):
        """填充数据"""
        try:
            # 填充结果列表
            for mesh in self.completed_meshes:
                item = QListWidgetItem()
                
                # 设置显示文本
                freq_count = len(mesh.modal_results.frequencies)
                calc_time = mesh.modal_results.calculation_time
                
                display_text = f"{mesh.name}\n"
                display_text += f"尺寸: {mesh.size:.2f}mm | "
                display_text += f"频率数: {freq_count} | "
                display_text += f"计算时间: {calc_time:.1f}s"
                
                item.setText(display_text)
                item.setData(Qt.ItemDataRole.UserRole, mesh)
                
                # 设置颜色
                if freq_count > 0:
                    item.setBackground(QColor(240, 255, 240))  # 浅绿色
                else:
                    item.setBackground(QColor(255, 240, 240))  # 浅红色
                
                self.result_list.addItem(item)
            
            # 更新选择计数
            self._update_selection_count()
            
            # 如果有结果，默认选择第一个
            if self.completed_meshes:
                self.result_list.setCurrentRow(0)
                
        except Exception as e:
            logger.error(f"填充结果列表失败: {str(e)}", exc_info=True)
            QMessageBox.critical(self, "错误", f"填充结果列表失败: {str(e)}")
    

    
    def _on_selection_changed(self):
        """选择变化处理"""
        self._update_selection_count()
    
    def _on_current_item_changed(self, current, previous):
        """当前项变化处理"""
        if current:
            mesh = current.data(Qt.ItemDataRole.UserRole)
            self._update_preview(mesh)
    
    def _update_selection_count(self):
        """更新选择计数"""
        selected_count = len(self.result_list.selectedItems())
        if selected_count == 0:
            self.label_selection_count.setText("请选择一个结果")
            self.label_selection_count.setStyleSheet("font-weight: bold; color: #ff6b6b;")
        else:
            self.label_selection_count.setText(f"已选择: {selected_count} 个结果")
            self.label_selection_count.setStyleSheet("font-weight: bold; color: #0078d4;")
    
    def _update_preview(self, mesh: MeshParameter):
        """更新预览信息"""
        try:
            # 更新网格信息表格
            info_data = [
                ("网格名称", mesh.name),
                ("网格尺寸", f"{mesh.size:.2f} mm"),
                ("单元类型", mesh.element_type.value),
                ("节点数", str(mesh.statistics.node_count)),
                ("单元数", str(mesh.statistics.element_count)),
                ("平均质量", f"{mesh.statistics.avg_quality:.3f}"),
                ("生成时间", f"{mesh.statistics.generation_time:.1f} s"),
                ("计算时间", f"{mesh.modal_results.calculation_time:.1f} s")
            ]
            
            self.info_table.setRowCount(len(info_data))
            for row, (key, value) in enumerate(info_data):
                self.info_table.setItem(row, 0, QTableWidgetItem(key))
                self.info_table.setItem(row, 1, QTableWidgetItem(value))
            
            # 更新频率结果
            frequencies = mesh.modal_results.frequencies
            if frequencies:
                freq_text = "模态频率列表:\n"
                for i, freq in enumerate(frequencies, 1):
                    freq_text += f"模态 {i:2d}: {freq:8.2f} Hz\n"
            else:
                freq_text = "暂无频率数据"
            
            self.freq_text.setPlainText(freq_text)
            
            # 更新统计信息
            stats_text = f"""
计算完成时间: {mesh.modal_results.calculation_time:.1f} 秒
模态数量: {len(frequencies)} 个
频率范围: {min(frequencies):.2f} ~ {max(frequencies):.2f} Hz
平均频率间隔: {(max(frequencies) - min(frequencies)) / max(1, len(frequencies) - 1):.2f} Hz
            """.strip() if frequencies else "暂无统计数据"
            
            self.stats_text.setPlainText(stats_text)
            
        except Exception as e:
            logger.error(f"更新预览失败: {str(e)}", exc_info=True)
    
    def _on_confirm(self):
        """确认按钮点击处理"""
        try:
            selected_items = self.result_list.selectedItems()
            if not selected_items:
                CustomMessageBox.warning(self, "选择提示",
                    "⚠️ 请选择计算结果\n\n"
                    "请在列表中选择一个或多个计算结果用于后续分析。\n\n"
                    "💡 提示: 可以按住 Ctrl 键选择多个结果进行对比分析。")
                return

            # 获取选中的网格
            self.selected_meshes = []
            for item in selected_items:
                mesh = item.data(Qt.ItemDataRole.UserRole)
                self.selected_meshes.append(mesh)

            # 构建选择摘要信息
            total_modals = sum(len(mesh.modal_results.frequencies) for mesh in self.selected_meshes)
            mesh_names = [mesh.name for mesh in self.selected_meshes]
            size_range = f"{min(mesh.size for mesh in self.selected_meshes):.2f} - {max(mesh.size for mesh in self.selected_meshes):.2f} mm"

            # 显示选择完成确认消息
            confirmation_message = f"""
✅ 结果选择完成

📊 选择摘要:
• 选择结果数量: {len(self.selected_meshes)} 个
• 网格尺寸范围: {size_range}
• 总模态数量: {total_modals} 个

📋 选择的结果:
{chr(10).join([f"• {mesh.name} ({mesh.size:.2f}mm, {len(mesh.modal_results.frequencies)} 个模态)" for mesh in self.selected_meshes])}

💾 选择状态: 已保存

🎯 后续操作:
• 可以在"结果对比"标签页查看详细对比分析
• 可以导出选择的结果数据
• 可以重新选择其他结果进行对比

选择已成功保存，您可以继续其他操作。
            """.strip()

            CustomMessageBox.information(self, "选择完成", confirmation_message)

            # 发射选择信号（不自动导航）
            self.results_selected.emit(self.selected_meshes)

            # 关闭对话框
            self.accept()

            logger.info(f"用户选择了 {len(self.selected_meshes)} 个计算结果，总模态数: {total_modals}")

        except Exception as e:
            logger.error(f"确认选择失败: {str(e)}", exc_info=True)
            CustomMessageBox.critical(self, "错误", f"确认选择失败: {str(e)}")
    
    def get_selected_meshes(self) -> List[MeshParameter]:
        """获取选中的网格列表"""
        return self.selected_meshes
