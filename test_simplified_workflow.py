"""
测试简化的网格与模态分析工作流程

此脚本验证简化后的工作流程：
1. 移除了批量网格生成步骤
2. 模态计算自动处理网格生成
3. 状态直接从"待处理"跳转到"完成"

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_modifications():
    """测试UI修改"""
    print("🧪 测试UI修改...")
    
    try:
        from ui.ui_mesh_merged import Ui_MainWindow
        from PySide6.QtWidgets import QApplication, QMainWindow
        
        app = QApplication([])
        window = QMainWindow()
        ui = Ui_MainWindow()
        ui.setupUi(window)
        
        print("✅ UI文件导入成功")
        
        # 检查批量生成相关组件是否已移除
        removed_components = [
            'btn_batch_generate',
            'btn_stop_generation', 
            'progressBar_generation',
            'label_generation_progress'
        ]
        
        missing_components = []
        for component in removed_components:
            if not hasattr(ui, component):
                missing_components.append(component)
        
        if len(missing_components) == len(removed_components):
            print("✅ 所有批量生成相关组件已成功移除")
        else:
            print(f"⚠️ 部分组件未移除: {[c for c in removed_components if c not in missing_components]}")
        
        # 检查新增的说明标签
        if hasattr(ui, 'label_mesh_selection_info'):
            print("✅ 新增的网格选择说明标签存在")
        else:
            print("⚠️ 网格选择说明标签未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ UI测试失败: {e}")
        return False

def test_window_class_modifications():
    """测试窗口类修改"""
    print("\n🧪 测试窗口类修改...")
    
    try:
        from views.mesh_window_merged import MeshWindow
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        
        print("✅ 窗口类导入成功")
        
        # 检查是否移除了批量生成相关方法
        removed_methods = [
            '_on_batch_generate',
            '_on_stop_generation',
            '_start_batch_generation',
            '_process_next_generation',
            '_finish_batch_generation'
        ]
        
        window = MeshWindow()
        missing_methods = []
        for method in removed_methods:
            if not hasattr(window, method):
                missing_methods.append(method)
        
        if len(missing_methods) == len(removed_methods):
            print("✅ 所有批量生成相关方法已成功移除")
        else:
            print(f"⚠️ 部分方法未移除: {[m for m in removed_methods if m not in missing_methods]}")
        
        # 检查新增的自动网格生成方法
        new_methods = [
            '_auto_generate_mesh',
            '_auto_generate_meshes_for_batch'
        ]
        
        existing_methods = []
        for method in new_methods:
            if hasattr(window, method):
                existing_methods.append(method)
        
        if len(existing_methods) == len(new_methods):
            print("✅ 所有自动网格生成方法已成功添加")
        else:
            print(f"⚠️ 部分方法未添加: {[m for m in new_methods if m not in existing_methods]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 窗口类测试失败: {e}")
        return False

def test_validation_logic():
    """测试验证逻辑修改"""
    print("\n🧪 测试验证逻辑修改...")
    
    try:
        from views.mesh_window_merged import MeshWindow
        from models.mesh_parameter import MeshParameter, MeshStatus
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        window = MeshWindow()
        
        # 创建测试网格
        test_mesh = MeshParameter(
            name="测试网格",
            size=1.0,
            element_type="四面体"
        )
        test_mesh.update_status(MeshStatus.PENDING)  # 待处理状态
        
        # 测试验证逻辑
        result = window._validate_mesh_for_modal_calculation(test_mesh)
        
        if result['valid']:
            print("✅ 验证逻辑修改成功 - 待处理状态的网格可以进行模态计算")
        else:
            print(f"❌ 验证逻辑修改失败: {result['message']}")
            return False
        
        # 测试无效参数的网格
        invalid_mesh = MeshParameter(
            name="",  # 无效名称
            size=0,   # 无效尺寸
            element_type="四面体"
        )
        
        result = window._validate_mesh_for_modal_calculation(invalid_mesh)
        
        if not result['valid']:
            print("✅ 验证逻辑正确 - 无效参数的网格被正确拒绝")
        else:
            print("⚠️ 验证逻辑可能过于宽松")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证逻辑测试失败: {e}")
        return False

def test_auto_mesh_generation():
    """测试自动网格生成功能"""
    print("\n🧪 测试自动网格生成功能...")
    
    try:
        from views.mesh_window_merged import MeshWindow
        from models.mesh_parameter import MeshParameter, MeshStatus
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        window = MeshWindow()
        
        # 创建测试网格
        test_mesh = MeshParameter(
            name="自动生成测试网格",
            size=2.0,
            element_type="四面体"
        )
        test_mesh.update_status(MeshStatus.PENDING)
        
        print(f"测试网格初始状态: {test_mesh.status.value}")
        
        # 测试自动网格生成
        try:
            window._auto_generate_mesh(test_mesh)
            
            if test_mesh.status == MeshStatus.GENERATED:
                print("✅ 自动网格生成成功")
                print(f"  - 节点数: {test_mesh.statistics.node_count}")
                print(f"  - 单元数: {test_mesh.statistics.element_count}")
                print(f"  - 网格质量: {test_mesh.statistics.avg_quality:.3f}")
            else:
                print(f"❌ 自动网格生成失败 - 状态: {test_mesh.status.value}")
                return False
                
        except Exception as gen_error:
            print(f"❌ 自动网格生成异常: {gen_error}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 自动网格生成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 简化网格与模态分析工作流程测试")
    print("=" * 70)
    print("\n测试目标：")
    print("✨ 验证批量网格生成组件已移除")
    print("✨ 验证模态计算前置条件已简化")
    print("✨ 验证自动网格生成功能正常")
    print("✨ 验证状态管理逻辑正确")
    print("=" * 70)
    
    success_count = 0
    total_tests = 4
    
    # 测试UI修改
    print("\n1. 测试UI修改")
    if test_ui_modifications():
        success_count += 1
    
    # 测试窗口类修改
    print("\n2. 测试窗口类修改")
    if test_window_class_modifications():
        success_count += 1
    
    # 测试验证逻辑
    print("\n3. 测试验证逻辑修改")
    if test_validation_logic():
        success_count += 1
    
    # 测试自动网格生成
    print("\n4. 测试自动网格生成功能")
    if test_auto_mesh_generation():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！")
        print("✅ 批量网格生成组件已成功移除")
        print("✅ 模态计算前置条件已简化")
        print("✅ 自动网格生成功能正常")
        print("✅ 工作流程已成功简化")
        print("\n🎯 新的工作流程：")
        print("1. 用户设置网格参数")
        print("2. 直接点击模态计算按钮")
        print("3. 系统自动生成网格并进行模态计算")
        print("4. 状态直接从'待处理'跳转到'完成'")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
        print("请检查修改是否完整")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
