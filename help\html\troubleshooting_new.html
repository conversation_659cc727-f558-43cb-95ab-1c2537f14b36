<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排查指南 - 振动传递计算软件</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>故障排查指南</h1>
        <nav>
            <a href="index.html">返回首页</a>
        </nav>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔧 编码处理与中文显示问题</h2>
            
            <div class="problem-section">
                <h3>🌐 多编码处理系统</h3>
                
                <div class="problem-item">
                    <h4>中文显示乱码问题</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>命令行窗口或日志中中文显示为乱码</p>
                        <p><strong>技术原因：</strong>编码不一致导致字符转换错误</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>系统编码设置：</strong>
                                <ul>
                                    <li>Windows: 控制面板 → 区域 → 管理 → 更改系统区域设置</li>
                                    <li>确保设置为"中文(简体，中国)"</li>
                                </ul>
                            </li>
                            <li><strong>命令行编码：</strong>
                                <code>chcp 65001</code> (设置为UTF-8)
                            </li>
                            <li><strong>应用程序编码：</strong>
                                <ul>
                                    <li>系统已内置多编码支持</li>
                                    <li>自动检测UTF-8、GBK、CP936格式</li>
                                    <li>智能编码转换和错误恢复</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h4>进度监控不更新问题</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>ANSYS脚本执行时进度条停滞，关键词匹配失败</p>
                        <p><strong>技术原因：</strong>编码问题导致关键词匹配失败</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>检查关键词映射：</strong>
                                <ul>
                                    <li>确认脚本输出包含预期的关键词</li>
                                    <li>验证关键词映射与实际输出匹配</li>
                                </ul>
                            </li>
                            <li><strong>编码问题排查：</strong>
                                <ul>
                                    <li>检查日志文件编码格式</li>
                                    <li>确认中文字符正确显示</li>
                                    <li>验证LogMonitorThread正常运行</li>
                                </ul>
                            </li>
                            <li><strong>调试方法：</strong>
                                <ul>
                                    <li>启用详细调试日志</li>
                                    <li>使用进度监控测试工具</li>
                                    <li>检查关键词匹配算法</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 ANSYS四合一自动化脚本问题</h2>
            
            <div class="problem-section">
                <h3>📋 脚本执行问题</h3>
                
                <div class="problem-item">
                    <h4>脚本执行失败</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>四合一自动化脚本无法正常执行</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>ANSYS配置检查：</strong>
                                <ul>
                                    <li>确认ANSYS Workbench路径配置正确</li>
                                    <li>验证ANSYS许可证服务正常运行</li>
                                    <li>检查项目文件是否正确加载</li>
                                </ul>
                            </li>
                            <li><strong>脚本文件检查：</strong>
                                <ul>
                                    <li>确认脚本文件存在且可读</li>
                                    <li>检查脚本文件编码为UTF-8</li>
                                    <li>验证脚本语法正确性</li>
                                </ul>
                            </li>
                            <li><strong>权限问题：</strong>
                                <ul>
                                    <li>以管理员权限运行应用程序</li>
                                    <li>确保输出目录有写入权限</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h4>JSON文件生成失败</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>面命名选择JSON文件未生成或格式错误</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li>检查输出目录权限</li>
                            <li>确认面命名选择存在</li>
                            <li>验证ANSYS模型状态</li>
                            <li>查看详细错误日志</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📍 监控点管理问题</h2>
            
            <div class="problem-section">
                <h3>📁 文件格式问题</h3>
                
                <div class="problem-item">
                    <h4>监控点文件格式错误</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>导入监控点文件时格式验证失败</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>JSON格式检查：</strong>
                                <ul>
                                    <li>确保语法正确，包含必需字段</li>
                                    <li>验证坐标数据为数值类型</li>
                                    <li>检查文件编码为UTF-8</li>
                                </ul>
                            </li>
                            <li><strong>TXT格式检查：</strong>
                                <ul>
                                    <li>确保坐标格式为 "X, Y, Z"</li>
                                    <li>每行一个监控点</li>
                                    <li>避免使用特殊字符</li>
                                </ul>
                            </li>
                            <li><strong>使用示例文件：</strong>
                                <ul>
                                    <li>参考项目中的示例文件格式</li>
                                    <li>复制示例格式修改数据</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 性能和稳定性问题</h2>

            <div class="problem-section">
                <h3>💾 资源管理问题</h3>

                <div class="problem-item">
                    <h4>内存占用过高</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>长时间运行后内存占用持续增长</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>自动清理机制：</strong>
                                <ul>
                                    <li>系统已启用自动清理机制</li>
                                    <li>监控点文件自动清理</li>
                                    <li>临时文件定期清理</li>
                                </ul>
                            </li>
                            <li><strong>手动清理：</strong>
                                <ul>
                                    <li>清理temp/目录下的临时文件</li>
                                    <li>清理output/目录下的旧文件</li>
                                </ul>
                            </li>
                            <li><strong>应用程序重启：</strong>
                                <ul>
                                    <li>长时间运行后建议重启应用</li>
                                    <li>确保所有资源正确释放</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h4>界面响应缓慢</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>界面操作响应缓慢或卡顿</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>后台任务检查：</strong>
                                <ul>
                                    <li>确认ANSYS脚本是否在执行</li>
                                    <li>检查日志监控线程状态</li>
                                    <li>验证API服务器状态</li>
                                </ul>
                            </li>
                            <li><strong>系统资源检查：</strong>
                                <ul>
                                    <li>确保系统有足够的可用内存</li>
                                    <li>关闭不必要的其他应用程序</li>
                                    <li>检查CPU使用率</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🌐 API和集成问题</h2>

            <div class="problem-section">
                <h3>🔗 API服务问题</h3>

                <div class="problem-item">
                    <h4>API服务启动失败</h4>
                    <div class="solution">
                        <p><strong>问题现象：</strong>FastAPI服务无法启动或端口被占用</p>
                        <p><strong>解决方案：</strong></p>
                        <ol>
                            <li><strong>端口检查：</strong>
                                <code>netstat -ano | findstr :8000</code>
                            </li>
                            <li><strong>更改端口：</strong>
                                <ul>
                                    <li>修改配置文件中的端口设置</li>
                                    <li>使用其他可用端口</li>
                                </ul>
                            </li>
                            <li><strong>防火墙设置：</strong>
                                <ul>
                                    <li>确保防火墙允许应用程序网络访问</li>
                                    <li>添加端口例外规则</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📞 技术支持</h2>
            <div class="support-info">
                <p>如果以上解决方案无法解决您的问题，请联系技术支持：</p>
                <ul>
                    <li>📧 邮箱：<EMAIL></li>
                    <li>📞 电话：400-123-4567</li>
                    <li>🌐 在线支持：访问官方网站获取更多帮助</li>
                    <li>📋 GitHub Issues：提交问题报告和功能请求</li>
                </ul>
                
                <h3>🔍 问题报告清单</h3>
                <p>提供问题描述时，请包含以下信息：</p>
                <ul>
                    <li>操作系统版本和区域设置</li>
                    <li>Python版本和虚拟环境信息</li>
                    <li>ANSYS版本和许可证状态</li>
                    <li>详细的错误日志内容</li>
                    <li>问题复现的具体步骤</li>
                    <li>相关配置文件内容</li>
                    <li>系统编码设置信息</li>
                </ul>
                
                <h3>🛠️ 调试工具</h3>
                <p>系统提供以下调试工具帮助排查问题：</p>
                <ul>
                    <li><strong>编码测试脚本：</strong>测试多编码处理功能</li>
                    <li><strong>进度监控测试：</strong>验证关键词匹配机制</li>
                    <li><strong>日志分析工具：</strong>分析日志文件编码和内容</li>
                    <li><strong>配置验证工具：</strong>检查配置文件完整性</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
