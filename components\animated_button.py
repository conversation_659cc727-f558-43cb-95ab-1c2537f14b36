"""
可复用的动画按钮组件

此模块提供了带有3D浮动效果的按钮组件，可以在各个界面中复用。
包含统一的动画参数、样式和行为。

作者: AI Assistant
日期: 2025-06-23
"""

from PySide6.QtWidgets import QPushButton, QGraphicsDropShadowEffect
from PySide6.QtCore import QPropertyAnimation, QEasingCurve, Property
from PySide6.QtGui import QColor


class AnimatedButton(QPushButton):
    """带有3D浮动效果的按钮，鼠标悬停时按钮会抬升起来（性能优化版）"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # 设置初始属性 - 简化属性数量
        self._elevation = 0        # 按钮抬升高度
        
        # 创建阴影效果，使用固定值减少计算
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setColor(QColor(0, 0, 0, 100))
        self.shadow.setOffset(0, 3)
        self.setGraphicsEffect(self.shadow)
        
        # 优化动画性能 - 预先定义两个状态的动画对象
        self.hover_anim = QPropertyAnimation(self, b"elevation")
        self.hover_anim.setDuration(100)  # 更短的动画时间
        self.hover_anim.setStartValue(0)
        self.hover_anim.setEndValue(-8)
        self.hover_anim.setEasingCurve(QEasingCurve.OutQuad)  # 更简单的曲线
        
        self.reset_anim = QPropertyAnimation(self, b"elevation")
        self.reset_anim.setDuration(70)  # 非常短的恢复时间
        self.reset_anim.setStartValue(-8)
        self.reset_anim.setEndValue(0)
        self.reset_anim.setEasingCurve(QEasingCurve.InQuad)
        
        # 设置基础样式 - 可以被子类或外部样式覆盖
        self._apply_base_style()
    
    def _apply_base_style(self):
        """应用基础动画按钮样式"""
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #4f46e5;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }
            
            QPushButton:hover {
                background-color: #4338ca;
            }
            
            QPushButton:pressed {
                background-color: #312e81;
            }
        """)
    
    # 简化属性设置，减少运行时计算
    def get_elevation(self):
        return self._elevation
        
    def set_elevation(self, elevation):
        if self._elevation == elevation:
            return  # 避免不必要的更新
            
        self._elevation = elevation
        
        # 仅修改必要的视觉属性
        if elevation < 0:  # 向上抬升
            # 阴影大小固定，只调整位置
            self.shadow.setOffset(0, 3 - elevation / 2)
            self.setContentsMargins(0, elevation, 0, -elevation)
        else:  # 平面状态
            self.shadow.setOffset(0, 3)
            self.setContentsMargins(0, 0, 0, 0)
        
    elevation = Property(float, get_elevation, set_elevation)
    
    def enterEvent(self, event):
        """鼠标悬停时抬升按钮"""
        # 立即停止任何正在进行的动画
        self.reset_anim.stop()
        self.hover_anim.start()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开时恢复按钮"""
        # 立即停止任何正在进行的动画
        self.hover_anim.stop()
        self.reset_anim.start()
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """按下时按钮直接设置状态，不使用动画"""
        self.hover_anim.stop() 
        self.reset_anim.stop()
        self.set_elevation(0)  # 直接设置为平面状态
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """释放时立即恢复状态"""
        if self.rect().contains(event.position().toPoint()):
            # 如果鼠标仍在按钮上，立即恢复悬停状态
            self.set_elevation(-8)
        else:
            # 鼠标已移开，恢复正常状态
            self.set_elevation(0)
        super().mouseReleaseEvent(event)


class PrimaryAnimatedButton(AnimatedButton):
    """主要操作按钮 - 蓝色主题"""
    
    def _apply_base_style(self):
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #3498db;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #1f6aa6;
            }
        """)


class SuccessAnimatedButton(AnimatedButton):
    """成功/完成操作按钮 - 绿色主题"""
    
    def _apply_base_style(self):
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #2ecc71;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }
            
            QPushButton:hover {
                background-color: #27ae60;
            }
            
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)


class SecondaryAnimatedButton(AnimatedButton):
    """次要操作按钮 - 灰色主题"""
    
    def _apply_base_style(self):
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #7f8c8d;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }
            
            QPushButton:hover {
                background-color: #6c7a7d;
            }
            
            QPushButton:pressed {
                background-color: #5a6769;
            }
        """)


class WarningAnimatedButton(AnimatedButton):
    """警告/危险操作按钮 - 红色主题"""
    
    def _apply_base_style(self):
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }
            
            QPushButton:hover {
                background-color: #c0392b;
            }
            
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)


def create_animated_button(button_type="primary", text="", parent=None):
    """
    工厂函数：创建指定类型的动画按钮
    
    Args:
        button_type: 按钮类型 ("primary", "success", "secondary", "warning")
        text: 按钮文本
        parent: 父组件
        
    Returns:
        AnimatedButton: 对应类型的动画按钮实例
    """
    button_classes = {
        "primary": PrimaryAnimatedButton,
        "success": SuccessAnimatedButton,
        "secondary": SecondaryAnimatedButton,
        "warning": WarningAnimatedButton,
        "default": AnimatedButton
    }
    
    button_class = button_classes.get(button_type, AnimatedButton)
    return button_class(text, parent)


def apply_animation_to_button(original_button, button_type="primary"):
    """
    将现有按钮替换为动画按钮
    
    Args:
        original_button: 原始按钮对象
        button_type: 动画按钮类型
        
    Returns:
        AnimatedButton: 新的动画按钮实例
    """
    # 获取原始按钮的属性
    text = original_button.text()
    font = original_button.font()
    size_policy = original_button.sizePolicy()
    min_size = original_button.minimumSize()
    object_name = original_button.objectName()
    parent = original_button.parent()
    
    # 创建新的动画按钮
    new_button = create_animated_button(button_type, text, parent)
    new_button.setObjectName(object_name)
    new_button.setFont(font)
    new_button.setSizePolicy(size_policy)
    new_button.setMinimumSize(min_size)
    
    return new_button
