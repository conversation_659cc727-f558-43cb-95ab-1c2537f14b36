# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'connection.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QComboBox, QGridLayout, QGroupBox,
    QHBoxLayout, QHeaderView, QLabel, QLineEdit,
    QMainWindow, QPushButton, QSizePolicy, QStatusBar,
    QTabWidget, QTableWidget, QTableWidgetItem, QVBoxLayout,
    QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1500, 800)
        MainWindow.setMinimumSize(QSize(1500, 800))
        MainWindow.setIconSize(QSize(30, 30))
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName(u"tabWidget")
        self.tabWidget.setMinimumSize(QSize(1500, 530))
        font = QFont()
        font.setFamilies([u"\u5b8b\u4f53"])
        font.setPointSize(12)
        self.tabWidget.setFont(font)
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.verticalLayout_2 = QVBoxLayout(self.tab)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.groupBox = QGroupBox(self.tab)
        self.groupBox.setObjectName(u"groupBox")
        self.groupBox.setMinimumSize(QSize(600, 500))
        self.verticalLayout_6 = QVBoxLayout(self.groupBox)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_19 = QLabel(self.groupBox)
        self.label_19.setObjectName(u"label_19")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.label_19.sizePolicy().hasHeightForWidth())
        self.label_19.setSizePolicy(sizePolicy)
        self.label_19.setMinimumSize(QSize(250, 50))
        self.label_19.setMaximumSize(QSize(180, 16777215))
        font1 = QFont()
        font1.setFamilies([u"Times New Roman"])
        font1.setPointSize(12)
        self.label_19.setFont(font1)

        self.horizontalLayout_3.addWidget(self.label_19)

        self.connection_zhou1 = QComboBox(self.groupBox)
        self.connection_zhou1.addItem("")
        self.connection_zhou1.setObjectName(u"connection_zhou1")
        self.connection_zhou1.setMinimumSize(QSize(0, 40))
        self.connection_zhou1.setFont(font1)

        self.horizontalLayout_3.addWidget(self.connection_zhou1)


        self.verticalLayout_6.addLayout(self.horizontalLayout_3)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_20 = QLabel(self.groupBox)
        self.label_20.setObjectName(u"label_20")
        sizePolicy.setHeightForWidth(self.label_20.sizePolicy().hasHeightForWidth())
        self.label_20.setSizePolicy(sizePolicy)
        self.label_20.setMinimumSize(QSize(250, 50))
        self.label_20.setMaximumSize(QSize(180, 16777215))
        self.label_20.setFont(font1)

        self.horizontalLayout_4.addWidget(self.label_20)

        self.rotation_zhou1 = QComboBox(self.groupBox)
        self.rotation_zhou1.addItem("")
        self.rotation_zhou1.addItem("")
        self.rotation_zhou1.addItem("")
        self.rotation_zhou1.setObjectName(u"rotation_zhou1")
        self.rotation_zhou1.setMinimumSize(QSize(0, 40))
        self.rotation_zhou1.setFont(font1)

        self.horizontalLayout_4.addWidget(self.rotation_zhou1)


        self.verticalLayout_6.addLayout(self.horizontalLayout_4)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_16 = QLabel(self.groupBox)
        self.label_16.setObjectName(u"label_16")
        sizePolicy.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy)
        self.label_16.setMinimumSize(QSize(250, 50))
        self.label_16.setMaximumSize(QSize(180, 16777215))
        self.label_16.setFont(font1)
        self.label_16.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignVCenter)

        self.horizontalLayout_5.addWidget(self.label_16)

        self.stiffness_zhou1 = QLineEdit(self.groupBox)
        self.stiffness_zhou1.setObjectName(u"stiffness_zhou1")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.stiffness_zhou1.sizePolicy().hasHeightForWidth())
        self.stiffness_zhou1.setSizePolicy(sizePolicy1)
        self.stiffness_zhou1.setMinimumSize(QSize(250, 40))
        self.stiffness_zhou1.setMaximumSize(QSize(16777215, 16777215))
        self.stiffness_zhou1.setFont(font1)

        self.horizontalLayout_5.addWidget(self.stiffness_zhou1)


        self.verticalLayout_6.addLayout(self.horizontalLayout_5)

        self.horizontalLayout_6 = QHBoxLayout()
        self.horizontalLayout_6.setObjectName(u"horizontalLayout_6")
        self.label_18 = QLabel(self.groupBox)
        self.label_18.setObjectName(u"label_18")
        sizePolicy.setHeightForWidth(self.label_18.sizePolicy().hasHeightForWidth())
        self.label_18.setSizePolicy(sizePolicy)
        self.label_18.setMinimumSize(QSize(250, 50))
        self.label_18.setMaximumSize(QSize(180, 16777215))
        self.label_18.setFont(font1)

        self.horizontalLayout_6.addWidget(self.label_18)

        self.damping_zhou1 = QLineEdit(self.groupBox)
        self.damping_zhou1.setObjectName(u"damping_zhou1")
        self.damping_zhou1.setMinimumSize(QSize(250, 40))
        self.damping_zhou1.setMaximumSize(QSize(16777215, 16777215))
        self.damping_zhou1.setFont(font1)

        self.horizontalLayout_6.addWidget(self.damping_zhou1)


        self.verticalLayout_6.addLayout(self.horizontalLayout_6)

        self.horizontalLayout_11 = QHBoxLayout()
        self.horizontalLayout_11.setObjectName(u"horizontalLayout_11")
        self.groupBox_3 = QGroupBox(self.groupBox)
        self.groupBox_3.setObjectName(u"groupBox_3")
        self.groupBox_3.setMinimumSize(QSize(300, 100))
        self.verticalLayout_5 = QVBoxLayout(self.groupBox_3)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.horizontalLayout_12 = QHBoxLayout()
        self.horizontalLayout_12.setObjectName(u"horizontalLayout_12")
        self.label_24 = QLabel(self.groupBox_3)
        self.label_24.setObjectName(u"label_24")
        sizePolicy.setHeightForWidth(self.label_24.sizePolicy().hasHeightForWidth())
        self.label_24.setSizePolicy(sizePolicy)
        self.label_24.setMinimumSize(QSize(150, 50))
        self.label_24.setMaximumSize(QSize(16777215, 16777215))
        self.label_24.setFont(font1)

        self.horizontalLayout_12.addWidget(self.label_24)

        self.reference_zhou1 = QComboBox(self.groupBox_3)
        self.reference_zhou1.addItem("")
        self.reference_zhou1.addItem("")
        self.reference_zhou1.setObjectName(u"reference_zhou1")
        self.reference_zhou1.setMinimumSize(QSize(150, 40))
        self.reference_zhou1.setFont(font1)

        self.horizontalLayout_12.addWidget(self.reference_zhou1)


        self.verticalLayout_5.addLayout(self.horizontalLayout_12)

        self.horizontalLayout_13 = QHBoxLayout()
        self.horizontalLayout_13.setObjectName(u"horizontalLayout_13")
        self.label_25 = QLabel(self.groupBox_3)
        self.label_25.setObjectName(u"label_25")
        sizePolicy.setHeightForWidth(self.label_25.sizePolicy().hasHeightForWidth())
        self.label_25.setSizePolicy(sizePolicy)
        self.label_25.setMinimumSize(QSize(150, 50))
        self.label_25.setMaximumSize(QSize(16777215, 16777215))
        self.label_25.setFont(font1)

        self.horizontalLayout_13.addWidget(self.label_25)

        self.referencebehavior_zhou1 = QComboBox(self.groupBox_3)
        self.referencebehavior_zhou1.addItem("")
        self.referencebehavior_zhou1.addItem("")
        self.referencebehavior_zhou1.addItem("")
        self.referencebehavior_zhou1.setObjectName(u"referencebehavior_zhou1")
        self.referencebehavior_zhou1.setMinimumSize(QSize(150, 40))
        self.referencebehavior_zhou1.setFont(font1)

        self.horizontalLayout_13.addWidget(self.referencebehavior_zhou1)


        self.verticalLayout_5.addLayout(self.horizontalLayout_13)


        self.horizontalLayout_11.addWidget(self.groupBox_3)

        self.groupBox_4 = QGroupBox(self.groupBox)
        self.groupBox_4.setObjectName(u"groupBox_4")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.groupBox_4.sizePolicy().hasHeightForWidth())
        self.groupBox_4.setSizePolicy(sizePolicy2)
        self.groupBox_4.setMinimumSize(QSize(300, 100))
        self.verticalLayout_3 = QVBoxLayout(self.groupBox_4)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.horizontalLayout_14 = QHBoxLayout()
        self.horizontalLayout_14.setObjectName(u"horizontalLayout_14")
        self.label_27 = QLabel(self.groupBox_4)
        self.label_27.setObjectName(u"label_27")
        sizePolicy.setHeightForWidth(self.label_27.sizePolicy().hasHeightForWidth())
        self.label_27.setSizePolicy(sizePolicy)
        self.label_27.setMinimumSize(QSize(150, 50))
        self.label_27.setMaximumSize(QSize(16777215, 16777215))
        self.label_27.setFont(font1)

        self.horizontalLayout_14.addWidget(self.label_27)

        self.mobile_zhou1 = QComboBox(self.groupBox_4)
        self.mobile_zhou1.addItem("")
        self.mobile_zhou1.addItem("")
        self.mobile_zhou1.setObjectName(u"mobile_zhou1")
        self.mobile_zhou1.setMinimumSize(QSize(150, 40))
        self.mobile_zhou1.setFont(font1)

        self.horizontalLayout_14.addWidget(self.mobile_zhou1)


        self.verticalLayout_3.addLayout(self.horizontalLayout_14)

        self.horizontalLayout_15 = QHBoxLayout()
        self.horizontalLayout_15.setObjectName(u"horizontalLayout_15")
        self.label_26 = QLabel(self.groupBox_4)
        self.label_26.setObjectName(u"label_26")
        sizePolicy.setHeightForWidth(self.label_26.sizePolicy().hasHeightForWidth())
        self.label_26.setSizePolicy(sizePolicy)
        self.label_26.setMinimumSize(QSize(150, 50))
        self.label_26.setMaximumSize(QSize(16777215, 16777215))
        self.label_26.setFont(font1)

        self.horizontalLayout_15.addWidget(self.label_26)

        self.mobilebehavior_zhou1 = QComboBox(self.groupBox_4)
        self.mobilebehavior_zhou1.addItem("")
        self.mobilebehavior_zhou1.addItem("")
        self.mobilebehavior_zhou1.addItem("")
        self.mobilebehavior_zhou1.setObjectName(u"mobilebehavior_zhou1")
        self.mobilebehavior_zhou1.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_zhou1.setFont(font1)

        self.horizontalLayout_15.addWidget(self.mobilebehavior_zhou1)


        self.verticalLayout_3.addLayout(self.horizontalLayout_15)


        self.horizontalLayout_11.addWidget(self.groupBox_4)


        self.verticalLayout_6.addLayout(self.horizontalLayout_11)


        self.horizontalLayout_2.addWidget(self.groupBox)

        self.groupBox_2 = QGroupBox(self.tab)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.groupBox_2.setMinimumSize(QSize(600, 500))
        self.verticalLayout_4 = QVBoxLayout(self.groupBox_2)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.horizontalLayout_7 = QHBoxLayout()
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.label_22 = QLabel(self.groupBox_2)
        self.label_22.setObjectName(u"label_22")
        sizePolicy.setHeightForWidth(self.label_22.sizePolicy().hasHeightForWidth())
        self.label_22.setSizePolicy(sizePolicy)
        self.label_22.setMinimumSize(QSize(250, 50))
        self.label_22.setMaximumSize(QSize(180, 16777215))
        self.label_22.setFont(font1)

        self.horizontalLayout_7.addWidget(self.label_22)

        self.connection_zhou2 = QComboBox(self.groupBox_2)
        self.connection_zhou2.addItem("")
        self.connection_zhou2.setObjectName(u"connection_zhou2")
        self.connection_zhou2.setMinimumSize(QSize(0, 40))
        self.connection_zhou2.setFont(font1)

        self.horizontalLayout_7.addWidget(self.connection_zhou2)


        self.verticalLayout_4.addLayout(self.horizontalLayout_7)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.label_21 = QLabel(self.groupBox_2)
        self.label_21.setObjectName(u"label_21")
        sizePolicy.setHeightForWidth(self.label_21.sizePolicy().hasHeightForWidth())
        self.label_21.setSizePolicy(sizePolicy)
        self.label_21.setMinimumSize(QSize(250, 50))
        self.label_21.setMaximumSize(QSize(180, 16777215))
        self.label_21.setFont(font1)

        self.horizontalLayout_8.addWidget(self.label_21)

        self.rotation_zhou2 = QComboBox(self.groupBox_2)
        self.rotation_zhou2.addItem("")
        self.rotation_zhou2.addItem("")
        self.rotation_zhou2.addItem("")
        self.rotation_zhou2.setObjectName(u"rotation_zhou2")
        self.rotation_zhou2.setMinimumSize(QSize(0, 40))
        self.rotation_zhou2.setFont(font1)

        self.horizontalLayout_8.addWidget(self.rotation_zhou2)


        self.verticalLayout_4.addLayout(self.horizontalLayout_8)

        self.horizontalLayout_9 = QHBoxLayout()
        self.horizontalLayout_9.setObjectName(u"horizontalLayout_9")
        self.label_17 = QLabel(self.groupBox_2)
        self.label_17.setObjectName(u"label_17")
        sizePolicy.setHeightForWidth(self.label_17.sizePolicy().hasHeightForWidth())
        self.label_17.setSizePolicy(sizePolicy)
        self.label_17.setMinimumSize(QSize(250, 50))
        self.label_17.setMaximumSize(QSize(180, 16777215))
        self.label_17.setFont(font1)
        self.label_17.setAlignment(Qt.AlignmentFlag.AlignLeading|Qt.AlignmentFlag.AlignLeft|Qt.AlignmentFlag.AlignVCenter)

        self.horizontalLayout_9.addWidget(self.label_17)

        self.stiffness_zhou2 = QLineEdit(self.groupBox_2)
        self.stiffness_zhou2.setObjectName(u"stiffness_zhou2")
        self.stiffness_zhou2.setMinimumSize(QSize(250, 40))
        self.stiffness_zhou2.setMaximumSize(QSize(16777215, 16777215))
        self.stiffness_zhou2.setFont(font1)

        self.horizontalLayout_9.addWidget(self.stiffness_zhou2)


        self.verticalLayout_4.addLayout(self.horizontalLayout_9)

        self.horizontalLayout_10 = QHBoxLayout()
        self.horizontalLayout_10.setObjectName(u"horizontalLayout_10")
        self.label_23 = QLabel(self.groupBox_2)
        self.label_23.setObjectName(u"label_23")
        sizePolicy.setHeightForWidth(self.label_23.sizePolicy().hasHeightForWidth())
        self.label_23.setSizePolicy(sizePolicy)
        self.label_23.setMinimumSize(QSize(250, 50))
        self.label_23.setMaximumSize(QSize(180, 16777215))
        self.label_23.setFont(font1)

        self.horizontalLayout_10.addWidget(self.label_23)

        self.damping_zhou2 = QLineEdit(self.groupBox_2)
        self.damping_zhou2.setObjectName(u"damping_zhou2")
        self.damping_zhou2.setMinimumSize(QSize(250, 40))
        self.damping_zhou2.setMaximumSize(QSize(16777215, 16777215))
        self.damping_zhou2.setFont(font1)

        self.horizontalLayout_10.addWidget(self.damping_zhou2)


        self.verticalLayout_4.addLayout(self.horizontalLayout_10)

        self.horizontalLayout_20 = QHBoxLayout()
        self.horizontalLayout_20.setObjectName(u"horizontalLayout_20")
        self.groupBox_5 = QGroupBox(self.groupBox_2)
        self.groupBox_5.setObjectName(u"groupBox_5")
        self.groupBox_5.setMinimumSize(QSize(250, 100))
        self.verticalLayout_7 = QVBoxLayout(self.groupBox_5)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.horizontalLayout_16 = QHBoxLayout()
        self.horizontalLayout_16.setObjectName(u"horizontalLayout_16")
        self.label_28 = QLabel(self.groupBox_5)
        self.label_28.setObjectName(u"label_28")
        sizePolicy.setHeightForWidth(self.label_28.sizePolicy().hasHeightForWidth())
        self.label_28.setSizePolicy(sizePolicy)
        self.label_28.setMinimumSize(QSize(150, 50))
        self.label_28.setMaximumSize(QSize(16777215, 16777215))
        self.label_28.setFont(font1)

        self.horizontalLayout_16.addWidget(self.label_28)

        self.reference_zhou2 = QComboBox(self.groupBox_5)
        self.reference_zhou2.addItem("")
        self.reference_zhou2.addItem("")
        self.reference_zhou2.setObjectName(u"reference_zhou2")
        self.reference_zhou2.setMinimumSize(QSize(150, 40))
        self.reference_zhou2.setFont(font1)

        self.horizontalLayout_16.addWidget(self.reference_zhou2)


        self.verticalLayout_7.addLayout(self.horizontalLayout_16)

        self.horizontalLayout_17 = QHBoxLayout()
        self.horizontalLayout_17.setObjectName(u"horizontalLayout_17")
        self.label_29 = QLabel(self.groupBox_5)
        self.label_29.setObjectName(u"label_29")
        sizePolicy.setHeightForWidth(self.label_29.sizePolicy().hasHeightForWidth())
        self.label_29.setSizePolicy(sizePolicy)
        self.label_29.setMinimumSize(QSize(150, 50))
        self.label_29.setMaximumSize(QSize(16777215, 16777215))
        self.label_29.setFont(font1)

        self.horizontalLayout_17.addWidget(self.label_29)

        self.referencebehavior_zhou2 = QComboBox(self.groupBox_5)
        self.referencebehavior_zhou2.addItem("")
        self.referencebehavior_zhou2.addItem("")
        self.referencebehavior_zhou2.addItem("")
        self.referencebehavior_zhou2.setObjectName(u"referencebehavior_zhou2")
        self.referencebehavior_zhou2.setMinimumSize(QSize(150, 40))
        self.referencebehavior_zhou2.setFont(font1)

        self.horizontalLayout_17.addWidget(self.referencebehavior_zhou2)


        self.verticalLayout_7.addLayout(self.horizontalLayout_17)


        self.horizontalLayout_20.addWidget(self.groupBox_5)

        self.groupBox_6 = QGroupBox(self.groupBox_2)
        self.groupBox_6.setObjectName(u"groupBox_6")
        self.groupBox_6.setMinimumSize(QSize(250, 100))
        self.verticalLayout_8 = QVBoxLayout(self.groupBox_6)
        self.verticalLayout_8.setObjectName(u"verticalLayout_8")
        self.horizontalLayout_18 = QHBoxLayout()
        self.horizontalLayout_18.setObjectName(u"horizontalLayout_18")
        self.label_30 = QLabel(self.groupBox_6)
        self.label_30.setObjectName(u"label_30")
        sizePolicy.setHeightForWidth(self.label_30.sizePolicy().hasHeightForWidth())
        self.label_30.setSizePolicy(sizePolicy)
        self.label_30.setMinimumSize(QSize(150, 50))
        self.label_30.setMaximumSize(QSize(16777215, 16777215))
        self.label_30.setFont(font1)

        self.horizontalLayout_18.addWidget(self.label_30)

        self.mobile_zhou2 = QComboBox(self.groupBox_6)
        self.mobile_zhou2.addItem("")
        self.mobile_zhou2.addItem("")
        self.mobile_zhou2.setObjectName(u"mobile_zhou2")
        self.mobile_zhou2.setMinimumSize(QSize(150, 40))
        self.mobile_zhou2.setFont(font1)

        self.horizontalLayout_18.addWidget(self.mobile_zhou2)


        self.verticalLayout_8.addLayout(self.horizontalLayout_18)

        self.horizontalLayout_19 = QHBoxLayout()
        self.horizontalLayout_19.setObjectName(u"horizontalLayout_19")
        self.label_31 = QLabel(self.groupBox_6)
        self.label_31.setObjectName(u"label_31")
        sizePolicy.setHeightForWidth(self.label_31.sizePolicy().hasHeightForWidth())
        self.label_31.setSizePolicy(sizePolicy)
        self.label_31.setMinimumSize(QSize(150, 50))
        self.label_31.setMaximumSize(QSize(16777215, 16777215))
        self.label_31.setFont(font1)

        self.horizontalLayout_19.addWidget(self.label_31)

        self.mobilebehavior_zhou2 = QComboBox(self.groupBox_6)
        self.mobilebehavior_zhou2.addItem("")
        self.mobilebehavior_zhou2.addItem("")
        self.mobilebehavior_zhou2.addItem("")
        self.mobilebehavior_zhou2.setObjectName(u"mobilebehavior_zhou2")
        self.mobilebehavior_zhou2.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_zhou2.setFont(font1)

        self.horizontalLayout_19.addWidget(self.mobilebehavior_zhou2)


        self.verticalLayout_8.addLayout(self.horizontalLayout_19)


        self.horizontalLayout_20.addWidget(self.groupBox_6)


        self.verticalLayout_4.addLayout(self.horizontalLayout_20)


        self.horizontalLayout_2.addWidget(self.groupBox_2)


        self.verticalLayout_2.addLayout(self.horizontalLayout_2)

        self.tabWidget.addTab(self.tab, "")
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        self.gridLayout = QGridLayout(self.tab_2)
        self.gridLayout.setObjectName(u"gridLayout")
        self.tabWidget_2 = QTabWidget(self.tab_2)
        self.tabWidget_2.setObjectName(u"tabWidget_2")
        self.tab_3 = QWidget()
        self.tab_3.setObjectName(u"tab_3")
        self.horizontalLayout_48 = QHBoxLayout(self.tab_3)
        self.horizontalLayout_48.setObjectName(u"horizontalLayout_48")
        self.groupBox_7 = QGroupBox(self.tab_3)
        self.groupBox_7.setObjectName(u"groupBox_7")
        self.groupBox_7.setMinimumSize(QSize(600, 500))
        self.verticalLayout_9 = QVBoxLayout(self.groupBox_7)
        self.verticalLayout_9.setObjectName(u"verticalLayout_9")
        self.horizontalLayout_21 = QHBoxLayout()
        self.horizontalLayout_21.setObjectName(u"horizontalLayout_21")
        self.label_32 = QLabel(self.groupBox_7)
        self.label_32.setObjectName(u"label_32")
        sizePolicy.setHeightForWidth(self.label_32.sizePolicy().hasHeightForWidth())
        self.label_32.setSizePolicy(sizePolicy)
        self.label_32.setMinimumSize(QSize(250, 50))
        self.label_32.setMaximumSize(QSize(180, 16777215))
        self.label_32.setFont(font1)

        self.horizontalLayout_21.addWidget(self.label_32)

        self.connection_bushing1 = QComboBox(self.groupBox_7)
        self.connection_bushing1.addItem("")
        self.connection_bushing1.setObjectName(u"connection_bushing1")
        self.connection_bushing1.setMinimumSize(QSize(0, 40))
        self.connection_bushing1.setFont(font1)

        self.horizontalLayout_21.addWidget(self.connection_bushing1)


        self.verticalLayout_9.addLayout(self.horizontalLayout_21)

        self.horizontalLayout_22 = QHBoxLayout()
        self.horizontalLayout_22.setObjectName(u"horizontalLayout_22")

        self.verticalLayout_9.addLayout(self.horizontalLayout_22)

        self.horizontalLayout_25 = QHBoxLayout()
        self.horizontalLayout_25.setObjectName(u"horizontalLayout_25")
        self.groupBox_8 = QGroupBox(self.groupBox_7)
        self.groupBox_8.setObjectName(u"groupBox_8")
        self.groupBox_8.setMinimumSize(QSize(300, 100))
        self.verticalLayout_10 = QVBoxLayout(self.groupBox_8)
        self.verticalLayout_10.setObjectName(u"verticalLayout_10")
        self.horizontalLayout_26 = QHBoxLayout()
        self.horizontalLayout_26.setObjectName(u"horizontalLayout_26")
        self.label_36 = QLabel(self.groupBox_8)
        self.label_36.setObjectName(u"label_36")
        sizePolicy.setHeightForWidth(self.label_36.sizePolicy().hasHeightForWidth())
        self.label_36.setSizePolicy(sizePolicy)
        self.label_36.setMinimumSize(QSize(150, 50))
        self.label_36.setMaximumSize(QSize(16777215, 16777215))
        self.label_36.setFont(font1)

        self.horizontalLayout_26.addWidget(self.label_36)

        self.mobile_bushing1 = QComboBox(self.groupBox_8)
        self.mobile_bushing1.addItem("")
        self.mobile_bushing1.addItem("")
        self.mobile_bushing1.setObjectName(u"mobile_bushing1")
        self.mobile_bushing1.setMinimumSize(QSize(150, 40))
        self.mobile_bushing1.setFont(font1)

        self.horizontalLayout_26.addWidget(self.mobile_bushing1)


        self.verticalLayout_10.addLayout(self.horizontalLayout_26)

        self.horizontalLayout_27 = QHBoxLayout()
        self.horizontalLayout_27.setObjectName(u"horizontalLayout_27")
        self.label_37 = QLabel(self.groupBox_8)
        self.label_37.setObjectName(u"label_37")
        sizePolicy.setHeightForWidth(self.label_37.sizePolicy().hasHeightForWidth())
        self.label_37.setSizePolicy(sizePolicy)
        self.label_37.setMinimumSize(QSize(150, 50))
        self.label_37.setMaximumSize(QSize(16777215, 16777215))
        self.label_37.setFont(font1)

        self.horizontalLayout_27.addWidget(self.label_37)

        self.mobilebehavior_bushing1 = QComboBox(self.groupBox_8)
        self.mobilebehavior_bushing1.addItem("")
        self.mobilebehavior_bushing1.addItem("")
        self.mobilebehavior_bushing1.addItem("")
        self.mobilebehavior_bushing1.setObjectName(u"mobilebehavior_bushing1")
        self.mobilebehavior_bushing1.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_bushing1.setFont(font1)

        self.horizontalLayout_27.addWidget(self.mobilebehavior_bushing1)


        self.verticalLayout_10.addLayout(self.horizontalLayout_27)


        self.horizontalLayout_25.addWidget(self.groupBox_8)


        self.verticalLayout_9.addLayout(self.horizontalLayout_25)

        self.horizontalLayout_23 = QHBoxLayout()
        self.horizontalLayout_23.setObjectName(u"horizontalLayout_23")
        self.label = QLabel(self.groupBox_7)
        self.label.setObjectName(u"label")
        font2 = QFont()
        font2.setFamilies([u"Times New Roman"])
        font2.setPointSize(15)
        self.label.setFont(font2)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_23.addWidget(self.label)

        self.tableWidget_stiffness_bushing1 = QTableWidget(self.groupBox_7)
        if (self.tableWidget_stiffness_bushing1.columnCount() < 3):
            self.tableWidget_stiffness_bushing1.setColumnCount(3)
        __qtablewidgetitem = QTableWidgetItem()
        __qtablewidgetitem.setFont(font2);
        self.tableWidget_stiffness_bushing1.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        __qtablewidgetitem1.setFont(font2);
        self.tableWidget_stiffness_bushing1.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        font3 = QFont()
        font3.setFamilies([u"Times New Roman"])
        font3.setPointSize(15)
        font3.setUnderline(False)
        font3.setStrikeOut(False)
        __qtablewidgetitem2 = QTableWidgetItem()
        __qtablewidgetitem2.setFont(font3);
        self.tableWidget_stiffness_bushing1.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        if (self.tableWidget_stiffness_bushing1.rowCount() < 3):
            self.tableWidget_stiffness_bushing1.setRowCount(3)
        __qtablewidgetitem3 = QTableWidgetItem()
        __qtablewidgetitem3.setFont(font2);
        self.tableWidget_stiffness_bushing1.setVerticalHeaderItem(0, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        __qtablewidgetitem4.setFont(font2);
        self.tableWidget_stiffness_bushing1.setVerticalHeaderItem(1, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        __qtablewidgetitem5.setFont(font2);
        self.tableWidget_stiffness_bushing1.setVerticalHeaderItem(2, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(0, 0, __qtablewidgetitem6)
        font4 = QFont()
        font4.setStrikeOut(False)
        __qtablewidgetitem7 = QTableWidgetItem()
        __qtablewidgetitem7.setFont(font4);
        __qtablewidgetitem7.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing1.setItem(0, 1, __qtablewidgetitem7)
        font5 = QFont()
        font5.setPointSize(15)
        __qtablewidgetitem8 = QTableWidgetItem()
        __qtablewidgetitem8.setFont(font5);
        __qtablewidgetitem8.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing1.setItem(0, 2, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(1, 0, __qtablewidgetitem9)
        __qtablewidgetitem10 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(1, 1, __qtablewidgetitem10)
        __qtablewidgetitem11 = QTableWidgetItem()
        __qtablewidgetitem11.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing1.setItem(1, 2, __qtablewidgetitem11)
        __qtablewidgetitem12 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(2, 0, __qtablewidgetitem12)
        __qtablewidgetitem13 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(2, 1, __qtablewidgetitem13)
        __qtablewidgetitem14 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing1.setItem(2, 2, __qtablewidgetitem14)
        self.tableWidget_stiffness_bushing1.setObjectName(u"tableWidget_stiffness_bushing1")
        self.tableWidget_stiffness_bushing1.setMinimumSize(QSize(561, 140))
        self.tableWidget_stiffness_bushing1.setMaximumSize(QSize(561, 141))
        self.tableWidget_stiffness_bushing1.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_stiffness_bushing1.setRowCount(3)
        self.tableWidget_stiffness_bushing1.horizontalHeader().setMinimumSectionSize(150)
        self.tableWidget_stiffness_bushing1.horizontalHeader().setDefaultSectionSize(150)
        self.tableWidget_stiffness_bushing1.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_stiffness_bushing1.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_23.addWidget(self.tableWidget_stiffness_bushing1)


        self.verticalLayout_9.addLayout(self.horizontalLayout_23)

        self.horizontalLayout_24 = QHBoxLayout()
        self.horizontalLayout_24.setObjectName(u"horizontalLayout_24")
        self.label_2 = QLabel(self.groupBox_7)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setFont(font2)
        self.label_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_24.addWidget(self.label_2)

        self.tableWidget_damping_bushing1 = QTableWidget(self.groupBox_7)
        if (self.tableWidget_damping_bushing1.columnCount() < 3):
            self.tableWidget_damping_bushing1.setColumnCount(3)
        __qtablewidgetitem15 = QTableWidgetItem()
        __qtablewidgetitem15.setFont(font2);
        self.tableWidget_damping_bushing1.setHorizontalHeaderItem(0, __qtablewidgetitem15)
        __qtablewidgetitem16 = QTableWidgetItem()
        __qtablewidgetitem16.setFont(font2);
        self.tableWidget_damping_bushing1.setHorizontalHeaderItem(1, __qtablewidgetitem16)
        __qtablewidgetitem17 = QTableWidgetItem()
        __qtablewidgetitem17.setFont(font3);
        self.tableWidget_damping_bushing1.setHorizontalHeaderItem(2, __qtablewidgetitem17)
        if (self.tableWidget_damping_bushing1.rowCount() < 3):
            self.tableWidget_damping_bushing1.setRowCount(3)
        __qtablewidgetitem18 = QTableWidgetItem()
        __qtablewidgetitem18.setFont(font2);
        self.tableWidget_damping_bushing1.setVerticalHeaderItem(0, __qtablewidgetitem18)
        __qtablewidgetitem19 = QTableWidgetItem()
        __qtablewidgetitem19.setFont(font2);
        self.tableWidget_damping_bushing1.setVerticalHeaderItem(1, __qtablewidgetitem19)
        __qtablewidgetitem20 = QTableWidgetItem()
        __qtablewidgetitem20.setFont(font2);
        self.tableWidget_damping_bushing1.setVerticalHeaderItem(2, __qtablewidgetitem20)
        __qtablewidgetitem21 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(0, 0, __qtablewidgetitem21)
        __qtablewidgetitem22 = QTableWidgetItem()
        __qtablewidgetitem22.setFont(font4);
        __qtablewidgetitem22.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing1.setItem(0, 1, __qtablewidgetitem22)
        __qtablewidgetitem23 = QTableWidgetItem()
        __qtablewidgetitem23.setFont(font5);
        __qtablewidgetitem23.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing1.setItem(0, 2, __qtablewidgetitem23)
        __qtablewidgetitem24 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(1, 0, __qtablewidgetitem24)
        __qtablewidgetitem25 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(1, 1, __qtablewidgetitem25)
        __qtablewidgetitem26 = QTableWidgetItem()
        __qtablewidgetitem26.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing1.setItem(1, 2, __qtablewidgetitem26)
        __qtablewidgetitem27 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(2, 0, __qtablewidgetitem27)
        __qtablewidgetitem28 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(2, 1, __qtablewidgetitem28)
        __qtablewidgetitem29 = QTableWidgetItem()
        self.tableWidget_damping_bushing1.setItem(2, 2, __qtablewidgetitem29)
        self.tableWidget_damping_bushing1.setObjectName(u"tableWidget_damping_bushing1")
        self.tableWidget_damping_bushing1.setMinimumSize(QSize(561, 140))
        self.tableWidget_damping_bushing1.setMaximumSize(QSize(561, 141))
        self.tableWidget_damping_bushing1.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_damping_bushing1.setRowCount(3)
        self.tableWidget_damping_bushing1.horizontalHeader().setMinimumSectionSize(125)
        self.tableWidget_damping_bushing1.horizontalHeader().setDefaultSectionSize(125)
        self.tableWidget_damping_bushing1.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_damping_bushing1.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_24.addWidget(self.tableWidget_damping_bushing1)


        self.verticalLayout_9.addLayout(self.horizontalLayout_24)


        self.horizontalLayout_48.addWidget(self.groupBox_7)

        self.groupBox_10 = QGroupBox(self.tab_3)
        self.groupBox_10.setObjectName(u"groupBox_10")
        self.groupBox_10.setMinimumSize(QSize(600, 500))
        self.verticalLayout_15 = QVBoxLayout(self.groupBox_10)
        self.verticalLayout_15.setObjectName(u"verticalLayout_15")
        self.horizontalLayout_39 = QHBoxLayout()
        self.horizontalLayout_39.setObjectName(u"horizontalLayout_39")
        self.label_44 = QLabel(self.groupBox_10)
        self.label_44.setObjectName(u"label_44")
        sizePolicy.setHeightForWidth(self.label_44.sizePolicy().hasHeightForWidth())
        self.label_44.setSizePolicy(sizePolicy)
        self.label_44.setMinimumSize(QSize(250, 50))
        self.label_44.setMaximumSize(QSize(180, 16777215))
        self.label_44.setFont(font1)

        self.horizontalLayout_39.addWidget(self.label_44)

        self.connection_bushing2 = QComboBox(self.groupBox_10)
        self.connection_bushing2.addItem("")
        self.connection_bushing2.setObjectName(u"connection_bushing2")
        self.connection_bushing2.setMinimumSize(QSize(0, 40))
        self.connection_bushing2.setFont(font1)

        self.horizontalLayout_39.addWidget(self.connection_bushing2)


        self.verticalLayout_15.addLayout(self.horizontalLayout_39)

        self.horizontalLayout_40 = QHBoxLayout()
        self.horizontalLayout_40.setObjectName(u"horizontalLayout_40")

        self.verticalLayout_15.addLayout(self.horizontalLayout_40)

        self.horizontalLayout_41 = QHBoxLayout()
        self.horizontalLayout_41.setObjectName(u"horizontalLayout_41")
        self.groupBox_13 = QGroupBox(self.groupBox_10)
        self.groupBox_13.setObjectName(u"groupBox_13")
        self.groupBox_13.setMinimumSize(QSize(300, 100))
        self.verticalLayout_16 = QVBoxLayout(self.groupBox_13)
        self.verticalLayout_16.setObjectName(u"verticalLayout_16")
        self.horizontalLayout_42 = QHBoxLayout()
        self.horizontalLayout_42.setObjectName(u"horizontalLayout_42")
        self.label_46 = QLabel(self.groupBox_13)
        self.label_46.setObjectName(u"label_46")
        sizePolicy.setHeightForWidth(self.label_46.sizePolicy().hasHeightForWidth())
        self.label_46.setSizePolicy(sizePolicy)
        self.label_46.setMinimumSize(QSize(150, 50))
        self.label_46.setMaximumSize(QSize(16777215, 16777215))
        self.label_46.setFont(font1)

        self.horizontalLayout_42.addWidget(self.label_46)

        self.mobile_bushing2 = QComboBox(self.groupBox_13)
        self.mobile_bushing2.addItem("")
        self.mobile_bushing2.addItem("")
        self.mobile_bushing2.setObjectName(u"mobile_bushing2")
        self.mobile_bushing2.setMinimumSize(QSize(150, 40))
        self.mobile_bushing2.setFont(font1)

        self.horizontalLayout_42.addWidget(self.mobile_bushing2)


        self.verticalLayout_16.addLayout(self.horizontalLayout_42)

        self.horizontalLayout_43 = QHBoxLayout()
        self.horizontalLayout_43.setObjectName(u"horizontalLayout_43")
        self.label_47 = QLabel(self.groupBox_13)
        self.label_47.setObjectName(u"label_47")
        sizePolicy.setHeightForWidth(self.label_47.sizePolicy().hasHeightForWidth())
        self.label_47.setSizePolicy(sizePolicy)
        self.label_47.setMinimumSize(QSize(150, 50))
        self.label_47.setMaximumSize(QSize(16777215, 16777215))
        self.label_47.setFont(font1)

        self.horizontalLayout_43.addWidget(self.label_47)

        self.mobilebehavior_bushing2 = QComboBox(self.groupBox_13)
        self.mobilebehavior_bushing2.addItem("")
        self.mobilebehavior_bushing2.addItem("")
        self.mobilebehavior_bushing2.addItem("")
        self.mobilebehavior_bushing2.setObjectName(u"mobilebehavior_bushing2")
        self.mobilebehavior_bushing2.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_bushing2.setFont(font1)

        self.horizontalLayout_43.addWidget(self.mobilebehavior_bushing2)


        self.verticalLayout_16.addLayout(self.horizontalLayout_43)


        self.horizontalLayout_41.addWidget(self.groupBox_13)


        self.verticalLayout_15.addLayout(self.horizontalLayout_41)

        self.horizontalLayout_46 = QHBoxLayout()
        self.horizontalLayout_46.setObjectName(u"horizontalLayout_46")
        self.label_5 = QLabel(self.groupBox_10)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setFont(font2)
        self.label_5.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_46.addWidget(self.label_5)

        self.tableWidget_stiffness_bushing2 = QTableWidget(self.groupBox_10)
        if (self.tableWidget_stiffness_bushing2.columnCount() < 3):
            self.tableWidget_stiffness_bushing2.setColumnCount(3)
        __qtablewidgetitem30 = QTableWidgetItem()
        __qtablewidgetitem30.setFont(font2);
        self.tableWidget_stiffness_bushing2.setHorizontalHeaderItem(0, __qtablewidgetitem30)
        __qtablewidgetitem31 = QTableWidgetItem()
        __qtablewidgetitem31.setFont(font2);
        self.tableWidget_stiffness_bushing2.setHorizontalHeaderItem(1, __qtablewidgetitem31)
        __qtablewidgetitem32 = QTableWidgetItem()
        __qtablewidgetitem32.setFont(font3);
        self.tableWidget_stiffness_bushing2.setHorizontalHeaderItem(2, __qtablewidgetitem32)
        if (self.tableWidget_stiffness_bushing2.rowCount() < 3):
            self.tableWidget_stiffness_bushing2.setRowCount(3)
        __qtablewidgetitem33 = QTableWidgetItem()
        __qtablewidgetitem33.setFont(font2);
        self.tableWidget_stiffness_bushing2.setVerticalHeaderItem(0, __qtablewidgetitem33)
        __qtablewidgetitem34 = QTableWidgetItem()
        __qtablewidgetitem34.setFont(font2);
        self.tableWidget_stiffness_bushing2.setVerticalHeaderItem(1, __qtablewidgetitem34)
        __qtablewidgetitem35 = QTableWidgetItem()
        __qtablewidgetitem35.setFont(font2);
        self.tableWidget_stiffness_bushing2.setVerticalHeaderItem(2, __qtablewidgetitem35)
        __qtablewidgetitem36 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(0, 0, __qtablewidgetitem36)
        __qtablewidgetitem37 = QTableWidgetItem()
        __qtablewidgetitem37.setFont(font4);
        __qtablewidgetitem37.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing2.setItem(0, 1, __qtablewidgetitem37)
        __qtablewidgetitem38 = QTableWidgetItem()
        __qtablewidgetitem38.setFont(font5);
        __qtablewidgetitem38.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing2.setItem(0, 2, __qtablewidgetitem38)
        __qtablewidgetitem39 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(1, 0, __qtablewidgetitem39)
        __qtablewidgetitem40 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(1, 1, __qtablewidgetitem40)
        __qtablewidgetitem41 = QTableWidgetItem()
        __qtablewidgetitem41.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing2.setItem(1, 2, __qtablewidgetitem41)
        __qtablewidgetitem42 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(2, 0, __qtablewidgetitem42)
        __qtablewidgetitem43 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(2, 1, __qtablewidgetitem43)
        __qtablewidgetitem44 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing2.setItem(2, 2, __qtablewidgetitem44)
        self.tableWidget_stiffness_bushing2.setObjectName(u"tableWidget_stiffness_bushing2")
        self.tableWidget_stiffness_bushing2.setMinimumSize(QSize(561, 140))
        self.tableWidget_stiffness_bushing2.setMaximumSize(QSize(561, 141))
        self.tableWidget_stiffness_bushing2.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_stiffness_bushing2.setRowCount(3)
        self.tableWidget_stiffness_bushing2.horizontalHeader().setMinimumSectionSize(150)
        self.tableWidget_stiffness_bushing2.horizontalHeader().setDefaultSectionSize(150)
        self.tableWidget_stiffness_bushing2.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_stiffness_bushing2.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_46.addWidget(self.tableWidget_stiffness_bushing2)


        self.verticalLayout_15.addLayout(self.horizontalLayout_46)

        self.horizontalLayout_47 = QHBoxLayout()
        self.horizontalLayout_47.setObjectName(u"horizontalLayout_47")
        self.label_6 = QLabel(self.groupBox_10)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setFont(font2)
        self.label_6.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_47.addWidget(self.label_6)

        self.tableWidget_damping_bushing2 = QTableWidget(self.groupBox_10)
        if (self.tableWidget_damping_bushing2.columnCount() < 3):
            self.tableWidget_damping_bushing2.setColumnCount(3)
        __qtablewidgetitem45 = QTableWidgetItem()
        __qtablewidgetitem45.setFont(font2);
        self.tableWidget_damping_bushing2.setHorizontalHeaderItem(0, __qtablewidgetitem45)
        __qtablewidgetitem46 = QTableWidgetItem()
        __qtablewidgetitem46.setFont(font2);
        self.tableWidget_damping_bushing2.setHorizontalHeaderItem(1, __qtablewidgetitem46)
        __qtablewidgetitem47 = QTableWidgetItem()
        __qtablewidgetitem47.setFont(font3);
        self.tableWidget_damping_bushing2.setHorizontalHeaderItem(2, __qtablewidgetitem47)
        if (self.tableWidget_damping_bushing2.rowCount() < 3):
            self.tableWidget_damping_bushing2.setRowCount(3)
        __qtablewidgetitem48 = QTableWidgetItem()
        __qtablewidgetitem48.setFont(font2);
        self.tableWidget_damping_bushing2.setVerticalHeaderItem(0, __qtablewidgetitem48)
        __qtablewidgetitem49 = QTableWidgetItem()
        __qtablewidgetitem49.setFont(font2);
        self.tableWidget_damping_bushing2.setVerticalHeaderItem(1, __qtablewidgetitem49)
        __qtablewidgetitem50 = QTableWidgetItem()
        __qtablewidgetitem50.setFont(font2);
        self.tableWidget_damping_bushing2.setVerticalHeaderItem(2, __qtablewidgetitem50)
        __qtablewidgetitem51 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(0, 0, __qtablewidgetitem51)
        __qtablewidgetitem52 = QTableWidgetItem()
        __qtablewidgetitem52.setFont(font4);
        __qtablewidgetitem52.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing2.setItem(0, 1, __qtablewidgetitem52)
        __qtablewidgetitem53 = QTableWidgetItem()
        __qtablewidgetitem53.setFont(font5);
        __qtablewidgetitem53.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing2.setItem(0, 2, __qtablewidgetitem53)
        __qtablewidgetitem54 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(1, 0, __qtablewidgetitem54)
        __qtablewidgetitem55 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(1, 1, __qtablewidgetitem55)
        __qtablewidgetitem56 = QTableWidgetItem()
        __qtablewidgetitem56.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing2.setItem(1, 2, __qtablewidgetitem56)
        __qtablewidgetitem57 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(2, 0, __qtablewidgetitem57)
        __qtablewidgetitem58 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(2, 1, __qtablewidgetitem58)
        __qtablewidgetitem59 = QTableWidgetItem()
        self.tableWidget_damping_bushing2.setItem(2, 2, __qtablewidgetitem59)
        self.tableWidget_damping_bushing2.setObjectName(u"tableWidget_damping_bushing2")
        self.tableWidget_damping_bushing2.setMinimumSize(QSize(561, 140))
        self.tableWidget_damping_bushing2.setMaximumSize(QSize(561, 141))
        self.tableWidget_damping_bushing2.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_damping_bushing2.setRowCount(3)
        self.tableWidget_damping_bushing2.horizontalHeader().setMinimumSectionSize(125)
        self.tableWidget_damping_bushing2.horizontalHeader().setDefaultSectionSize(125)
        self.tableWidget_damping_bushing2.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_damping_bushing2.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_47.addWidget(self.tableWidget_damping_bushing2)


        self.verticalLayout_15.addLayout(self.horizontalLayout_47)


        self.horizontalLayout_48.addWidget(self.groupBox_10)

        self.tabWidget_2.addTab(self.tab_3, "")
        self.tab_4 = QWidget()
        self.tab_4.setObjectName(u"tab_4")
        self.horizontalLayout_67 = QHBoxLayout(self.tab_4)
        self.horizontalLayout_67.setObjectName(u"horizontalLayout_67")
        self.groupBox_18 = QGroupBox(self.tab_4)
        self.groupBox_18.setObjectName(u"groupBox_18")
        self.groupBox_18.setMinimumSize(QSize(600, 500))
        self.verticalLayout_21 = QVBoxLayout(self.groupBox_18)
        self.verticalLayout_21.setObjectName(u"verticalLayout_21")
        self.horizontalLayout_58 = QHBoxLayout()
        self.horizontalLayout_58.setObjectName(u"horizontalLayout_58")
        self.label_56 = QLabel(self.groupBox_18)
        self.label_56.setObjectName(u"label_56")
        sizePolicy.setHeightForWidth(self.label_56.sizePolicy().hasHeightForWidth())
        self.label_56.setSizePolicy(sizePolicy)
        self.label_56.setMinimumSize(QSize(250, 50))
        self.label_56.setMaximumSize(QSize(180, 16777215))
        self.label_56.setFont(font1)

        self.horizontalLayout_58.addWidget(self.label_56)

        self.connection_bushing3 = QComboBox(self.groupBox_18)
        self.connection_bushing3.addItem("")
        self.connection_bushing3.setObjectName(u"connection_bushing3")
        self.connection_bushing3.setMinimumSize(QSize(0, 40))
        self.connection_bushing3.setFont(font1)

        self.horizontalLayout_58.addWidget(self.connection_bushing3)


        self.verticalLayout_21.addLayout(self.horizontalLayout_58)

        self.horizontalLayout_59 = QHBoxLayout()
        self.horizontalLayout_59.setObjectName(u"horizontalLayout_59")

        self.verticalLayout_21.addLayout(self.horizontalLayout_59)

        self.horizontalLayout_60 = QHBoxLayout()
        self.horizontalLayout_60.setObjectName(u"horizontalLayout_60")
        self.groupBox_19 = QGroupBox(self.groupBox_18)
        self.groupBox_19.setObjectName(u"groupBox_19")
        self.groupBox_19.setMinimumSize(QSize(300, 100))
        self.verticalLayout_22 = QVBoxLayout(self.groupBox_19)
        self.verticalLayout_22.setObjectName(u"verticalLayout_22")
        self.horizontalLayout_61 = QHBoxLayout()
        self.horizontalLayout_61.setObjectName(u"horizontalLayout_61")
        self.label_58 = QLabel(self.groupBox_19)
        self.label_58.setObjectName(u"label_58")
        sizePolicy.setHeightForWidth(self.label_58.sizePolicy().hasHeightForWidth())
        self.label_58.setSizePolicy(sizePolicy)
        self.label_58.setMinimumSize(QSize(150, 50))
        self.label_58.setMaximumSize(QSize(16777215, 16777215))
        self.label_58.setFont(font1)

        self.horizontalLayout_61.addWidget(self.label_58)

        self.mobile_bushing3 = QComboBox(self.groupBox_19)
        self.mobile_bushing3.addItem("")
        self.mobile_bushing3.addItem("")
        self.mobile_bushing3.setObjectName(u"mobile_bushing3")
        self.mobile_bushing3.setMinimumSize(QSize(150, 40))
        self.mobile_bushing3.setFont(font1)

        self.horizontalLayout_61.addWidget(self.mobile_bushing3)


        self.verticalLayout_22.addLayout(self.horizontalLayout_61)

        self.horizontalLayout_62 = QHBoxLayout()
        self.horizontalLayout_62.setObjectName(u"horizontalLayout_62")
        self.label_59 = QLabel(self.groupBox_19)
        self.label_59.setObjectName(u"label_59")
        sizePolicy.setHeightForWidth(self.label_59.sizePolicy().hasHeightForWidth())
        self.label_59.setSizePolicy(sizePolicy)
        self.label_59.setMinimumSize(QSize(150, 50))
        self.label_59.setMaximumSize(QSize(16777215, 16777215))
        self.label_59.setFont(font1)

        self.horizontalLayout_62.addWidget(self.label_59)

        self.mobilebehavior_bushing3 = QComboBox(self.groupBox_19)
        self.mobilebehavior_bushing3.addItem("")
        self.mobilebehavior_bushing3.addItem("")
        self.mobilebehavior_bushing3.addItem("")
        self.mobilebehavior_bushing3.setObjectName(u"mobilebehavior_bushing3")
        self.mobilebehavior_bushing3.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_bushing3.setFont(font1)

        self.horizontalLayout_62.addWidget(self.mobilebehavior_bushing3)


        self.verticalLayout_22.addLayout(self.horizontalLayout_62)


        self.horizontalLayout_60.addWidget(self.groupBox_19)


        self.verticalLayout_21.addLayout(self.horizontalLayout_60)

        self.horizontalLayout_65 = QHBoxLayout()
        self.horizontalLayout_65.setObjectName(u"horizontalLayout_65")
        self.label_9 = QLabel(self.groupBox_18)
        self.label_9.setObjectName(u"label_9")
        self.label_9.setFont(font2)
        self.label_9.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_65.addWidget(self.label_9)

        self.tableWidget_stiffness_bushing3 = QTableWidget(self.groupBox_18)
        if (self.tableWidget_stiffness_bushing3.columnCount() < 3):
            self.tableWidget_stiffness_bushing3.setColumnCount(3)
        __qtablewidgetitem60 = QTableWidgetItem()
        __qtablewidgetitem60.setFont(font2);
        self.tableWidget_stiffness_bushing3.setHorizontalHeaderItem(0, __qtablewidgetitem60)
        __qtablewidgetitem61 = QTableWidgetItem()
        __qtablewidgetitem61.setFont(font2);
        self.tableWidget_stiffness_bushing3.setHorizontalHeaderItem(1, __qtablewidgetitem61)
        __qtablewidgetitem62 = QTableWidgetItem()
        __qtablewidgetitem62.setFont(font3);
        self.tableWidget_stiffness_bushing3.setHorizontalHeaderItem(2, __qtablewidgetitem62)
        if (self.tableWidget_stiffness_bushing3.rowCount() < 3):
            self.tableWidget_stiffness_bushing3.setRowCount(3)
        __qtablewidgetitem63 = QTableWidgetItem()
        __qtablewidgetitem63.setFont(font2);
        self.tableWidget_stiffness_bushing3.setVerticalHeaderItem(0, __qtablewidgetitem63)
        __qtablewidgetitem64 = QTableWidgetItem()
        __qtablewidgetitem64.setFont(font2);
        self.tableWidget_stiffness_bushing3.setVerticalHeaderItem(1, __qtablewidgetitem64)
        __qtablewidgetitem65 = QTableWidgetItem()
        __qtablewidgetitem65.setFont(font2);
        self.tableWidget_stiffness_bushing3.setVerticalHeaderItem(2, __qtablewidgetitem65)
        __qtablewidgetitem66 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(0, 0, __qtablewidgetitem66)
        __qtablewidgetitem67 = QTableWidgetItem()
        __qtablewidgetitem67.setFont(font4);
        __qtablewidgetitem67.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing3.setItem(0, 1, __qtablewidgetitem67)
        __qtablewidgetitem68 = QTableWidgetItem()
        __qtablewidgetitem68.setFont(font5);
        __qtablewidgetitem68.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing3.setItem(0, 2, __qtablewidgetitem68)
        __qtablewidgetitem69 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(1, 0, __qtablewidgetitem69)
        __qtablewidgetitem70 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(1, 1, __qtablewidgetitem70)
        __qtablewidgetitem71 = QTableWidgetItem()
        __qtablewidgetitem71.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing3.setItem(1, 2, __qtablewidgetitem71)
        __qtablewidgetitem72 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(2, 0, __qtablewidgetitem72)
        __qtablewidgetitem73 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(2, 1, __qtablewidgetitem73)
        __qtablewidgetitem74 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing3.setItem(2, 2, __qtablewidgetitem74)
        self.tableWidget_stiffness_bushing3.setObjectName(u"tableWidget_stiffness_bushing3")
        self.tableWidget_stiffness_bushing3.setMinimumSize(QSize(561, 140))
        self.tableWidget_stiffness_bushing3.setMaximumSize(QSize(561, 141))
        self.tableWidget_stiffness_bushing3.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_stiffness_bushing3.setRowCount(3)
        self.tableWidget_stiffness_bushing3.horizontalHeader().setMinimumSectionSize(150)
        self.tableWidget_stiffness_bushing3.horizontalHeader().setDefaultSectionSize(150)
        self.tableWidget_stiffness_bushing3.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_stiffness_bushing3.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_65.addWidget(self.tableWidget_stiffness_bushing3)


        self.verticalLayout_21.addLayout(self.horizontalLayout_65)

        self.horizontalLayout_66 = QHBoxLayout()
        self.horizontalLayout_66.setObjectName(u"horizontalLayout_66")
        self.label_10 = QLabel(self.groupBox_18)
        self.label_10.setObjectName(u"label_10")
        self.label_10.setFont(font2)
        self.label_10.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_66.addWidget(self.label_10)

        self.tableWidget_damping_bushing3 = QTableWidget(self.groupBox_18)
        if (self.tableWidget_damping_bushing3.columnCount() < 3):
            self.tableWidget_damping_bushing3.setColumnCount(3)
        __qtablewidgetitem75 = QTableWidgetItem()
        __qtablewidgetitem75.setFont(font2);
        self.tableWidget_damping_bushing3.setHorizontalHeaderItem(0, __qtablewidgetitem75)
        __qtablewidgetitem76 = QTableWidgetItem()
        __qtablewidgetitem76.setFont(font2);
        self.tableWidget_damping_bushing3.setHorizontalHeaderItem(1, __qtablewidgetitem76)
        __qtablewidgetitem77 = QTableWidgetItem()
        __qtablewidgetitem77.setFont(font3);
        self.tableWidget_damping_bushing3.setHorizontalHeaderItem(2, __qtablewidgetitem77)
        if (self.tableWidget_damping_bushing3.rowCount() < 3):
            self.tableWidget_damping_bushing3.setRowCount(3)
        __qtablewidgetitem78 = QTableWidgetItem()
        __qtablewidgetitem78.setFont(font2);
        self.tableWidget_damping_bushing3.setVerticalHeaderItem(0, __qtablewidgetitem78)
        __qtablewidgetitem79 = QTableWidgetItem()
        __qtablewidgetitem79.setFont(font2);
        self.tableWidget_damping_bushing3.setVerticalHeaderItem(1, __qtablewidgetitem79)
        __qtablewidgetitem80 = QTableWidgetItem()
        __qtablewidgetitem80.setFont(font2);
        self.tableWidget_damping_bushing3.setVerticalHeaderItem(2, __qtablewidgetitem80)
        __qtablewidgetitem81 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(0, 0, __qtablewidgetitem81)
        __qtablewidgetitem82 = QTableWidgetItem()
        __qtablewidgetitem82.setFont(font4);
        __qtablewidgetitem82.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing3.setItem(0, 1, __qtablewidgetitem82)
        __qtablewidgetitem83 = QTableWidgetItem()
        __qtablewidgetitem83.setFont(font5);
        __qtablewidgetitem83.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing3.setItem(0, 2, __qtablewidgetitem83)
        __qtablewidgetitem84 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(1, 0, __qtablewidgetitem84)
        __qtablewidgetitem85 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(1, 1, __qtablewidgetitem85)
        __qtablewidgetitem86 = QTableWidgetItem()
        __qtablewidgetitem86.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing3.setItem(1, 2, __qtablewidgetitem86)
        __qtablewidgetitem87 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(2, 0, __qtablewidgetitem87)
        __qtablewidgetitem88 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(2, 1, __qtablewidgetitem88)
        __qtablewidgetitem89 = QTableWidgetItem()
        self.tableWidget_damping_bushing3.setItem(2, 2, __qtablewidgetitem89)
        self.tableWidget_damping_bushing3.setObjectName(u"tableWidget_damping_bushing3")
        self.tableWidget_damping_bushing3.setMinimumSize(QSize(561, 140))
        self.tableWidget_damping_bushing3.setMaximumSize(QSize(561, 141))
        self.tableWidget_damping_bushing3.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_damping_bushing3.setRowCount(3)
        self.tableWidget_damping_bushing3.horizontalHeader().setMinimumSectionSize(125)
        self.tableWidget_damping_bushing3.horizontalHeader().setDefaultSectionSize(125)
        self.tableWidget_damping_bushing3.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_damping_bushing3.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_66.addWidget(self.tableWidget_damping_bushing3)


        self.verticalLayout_21.addLayout(self.horizontalLayout_66)


        self.horizontalLayout_67.addWidget(self.groupBox_18)

        self.groupBox_15 = QGroupBox(self.tab_4)
        self.groupBox_15.setObjectName(u"groupBox_15")
        self.groupBox_15.setMinimumSize(QSize(600, 500))
        self.verticalLayout_18 = QVBoxLayout(self.groupBox_15)
        self.verticalLayout_18.setObjectName(u"verticalLayout_18")
        self.horizontalLayout_49 = QHBoxLayout()
        self.horizontalLayout_49.setObjectName(u"horizontalLayout_49")
        self.label_50 = QLabel(self.groupBox_15)
        self.label_50.setObjectName(u"label_50")
        sizePolicy.setHeightForWidth(self.label_50.sizePolicy().hasHeightForWidth())
        self.label_50.setSizePolicy(sizePolicy)
        self.label_50.setMinimumSize(QSize(250, 50))
        self.label_50.setMaximumSize(QSize(180, 16777215))
        self.label_50.setFont(font1)

        self.horizontalLayout_49.addWidget(self.label_50)

        self.connection_bushing4 = QComboBox(self.groupBox_15)
        self.connection_bushing4.addItem("")
        self.connection_bushing4.setObjectName(u"connection_bushing4")
        self.connection_bushing4.setMinimumSize(QSize(0, 40))
        self.connection_bushing4.setFont(font1)

        self.horizontalLayout_49.addWidget(self.connection_bushing4)


        self.verticalLayout_18.addLayout(self.horizontalLayout_49)

        self.horizontalLayout_50 = QHBoxLayout()
        self.horizontalLayout_50.setObjectName(u"horizontalLayout_50")

        self.verticalLayout_18.addLayout(self.horizontalLayout_50)

        self.horizontalLayout_51 = QHBoxLayout()
        self.horizontalLayout_51.setObjectName(u"horizontalLayout_51")
        self.groupBox_16 = QGroupBox(self.groupBox_15)
        self.groupBox_16.setObjectName(u"groupBox_16")
        self.groupBox_16.setMinimumSize(QSize(300, 100))
        self.verticalLayout_19 = QVBoxLayout(self.groupBox_16)
        self.verticalLayout_19.setObjectName(u"verticalLayout_19")
        self.horizontalLayout_52 = QHBoxLayout()
        self.horizontalLayout_52.setObjectName(u"horizontalLayout_52")
        self.label_52 = QLabel(self.groupBox_16)
        self.label_52.setObjectName(u"label_52")
        sizePolicy.setHeightForWidth(self.label_52.sizePolicy().hasHeightForWidth())
        self.label_52.setSizePolicy(sizePolicy)
        self.label_52.setMinimumSize(QSize(150, 50))
        self.label_52.setMaximumSize(QSize(16777215, 16777215))
        self.label_52.setFont(font1)

        self.horizontalLayout_52.addWidget(self.label_52)

        self.mobile_bushing4 = QComboBox(self.groupBox_16)
        self.mobile_bushing4.addItem("")
        self.mobile_bushing4.addItem("")
        self.mobile_bushing4.setObjectName(u"mobile_bushing4")
        self.mobile_bushing4.setMinimumSize(QSize(150, 40))
        self.mobile_bushing4.setFont(font1)

        self.horizontalLayout_52.addWidget(self.mobile_bushing4)


        self.verticalLayout_19.addLayout(self.horizontalLayout_52)

        self.horizontalLayout_53 = QHBoxLayout()
        self.horizontalLayout_53.setObjectName(u"horizontalLayout_53")
        self.label_53 = QLabel(self.groupBox_16)
        self.label_53.setObjectName(u"label_53")
        sizePolicy.setHeightForWidth(self.label_53.sizePolicy().hasHeightForWidth())
        self.label_53.setSizePolicy(sizePolicy)
        self.label_53.setMinimumSize(QSize(150, 50))
        self.label_53.setMaximumSize(QSize(16777215, 16777215))
        self.label_53.setFont(font1)

        self.horizontalLayout_53.addWidget(self.label_53)

        self.mobilebehavior_bushing4 = QComboBox(self.groupBox_16)
        self.mobilebehavior_bushing4.addItem("")
        self.mobilebehavior_bushing4.addItem("")
        self.mobilebehavior_bushing4.addItem("")
        self.mobilebehavior_bushing4.setObjectName(u"mobilebehavior_bushing4")
        self.mobilebehavior_bushing4.setMinimumSize(QSize(150, 40))
        self.mobilebehavior_bushing4.setFont(font1)

        self.horizontalLayout_53.addWidget(self.mobilebehavior_bushing4)


        self.verticalLayout_19.addLayout(self.horizontalLayout_53)


        self.horizontalLayout_51.addWidget(self.groupBox_16)


        self.verticalLayout_18.addLayout(self.horizontalLayout_51)

        self.horizontalLayout_56 = QHBoxLayout()
        self.horizontalLayout_56.setObjectName(u"horizontalLayout_56")
        self.label_7 = QLabel(self.groupBox_15)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setFont(font2)
        self.label_7.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_56.addWidget(self.label_7)

        self.tableWidget_stiffness_bushing4 = QTableWidget(self.groupBox_15)
        if (self.tableWidget_stiffness_bushing4.columnCount() < 3):
            self.tableWidget_stiffness_bushing4.setColumnCount(3)
        __qtablewidgetitem90 = QTableWidgetItem()
        __qtablewidgetitem90.setFont(font2);
        self.tableWidget_stiffness_bushing4.setHorizontalHeaderItem(0, __qtablewidgetitem90)
        __qtablewidgetitem91 = QTableWidgetItem()
        __qtablewidgetitem91.setFont(font2);
        self.tableWidget_stiffness_bushing4.setHorizontalHeaderItem(1, __qtablewidgetitem91)
        __qtablewidgetitem92 = QTableWidgetItem()
        __qtablewidgetitem92.setFont(font3);
        self.tableWidget_stiffness_bushing4.setHorizontalHeaderItem(2, __qtablewidgetitem92)
        if (self.tableWidget_stiffness_bushing4.rowCount() < 3):
            self.tableWidget_stiffness_bushing4.setRowCount(3)
        __qtablewidgetitem93 = QTableWidgetItem()
        __qtablewidgetitem93.setFont(font2);
        self.tableWidget_stiffness_bushing4.setVerticalHeaderItem(0, __qtablewidgetitem93)
        __qtablewidgetitem94 = QTableWidgetItem()
        __qtablewidgetitem94.setFont(font2);
        self.tableWidget_stiffness_bushing4.setVerticalHeaderItem(1, __qtablewidgetitem94)
        __qtablewidgetitem95 = QTableWidgetItem()
        __qtablewidgetitem95.setFont(font2);
        self.tableWidget_stiffness_bushing4.setVerticalHeaderItem(2, __qtablewidgetitem95)
        __qtablewidgetitem96 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(0, 0, __qtablewidgetitem96)
        __qtablewidgetitem97 = QTableWidgetItem()
        __qtablewidgetitem97.setFont(font4);
        __qtablewidgetitem97.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing4.setItem(0, 1, __qtablewidgetitem97)
        __qtablewidgetitem98 = QTableWidgetItem()
        __qtablewidgetitem98.setFont(font5);
        __qtablewidgetitem98.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing4.setItem(0, 2, __qtablewidgetitem98)
        __qtablewidgetitem99 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(1, 0, __qtablewidgetitem99)
        __qtablewidgetitem100 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(1, 1, __qtablewidgetitem100)
        __qtablewidgetitem101 = QTableWidgetItem()
        __qtablewidgetitem101.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_stiffness_bushing4.setItem(1, 2, __qtablewidgetitem101)
        __qtablewidgetitem102 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(2, 0, __qtablewidgetitem102)
        __qtablewidgetitem103 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(2, 1, __qtablewidgetitem103)
        __qtablewidgetitem104 = QTableWidgetItem()
        self.tableWidget_stiffness_bushing4.setItem(2, 2, __qtablewidgetitem104)
        self.tableWidget_stiffness_bushing4.setObjectName(u"tableWidget_stiffness_bushing4")
        self.tableWidget_stiffness_bushing4.setMinimumSize(QSize(561, 140))
        self.tableWidget_stiffness_bushing4.setMaximumSize(QSize(561, 141))
        self.tableWidget_stiffness_bushing4.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_stiffness_bushing4.setRowCount(3)
        self.tableWidget_stiffness_bushing4.horizontalHeader().setMinimumSectionSize(150)
        self.tableWidget_stiffness_bushing4.horizontalHeader().setDefaultSectionSize(150)
        self.tableWidget_stiffness_bushing4.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_stiffness_bushing4.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_56.addWidget(self.tableWidget_stiffness_bushing4)


        self.verticalLayout_18.addLayout(self.horizontalLayout_56)

        self.horizontalLayout_57 = QHBoxLayout()
        self.horizontalLayout_57.setObjectName(u"horizontalLayout_57")
        self.label_8 = QLabel(self.groupBox_15)
        self.label_8.setObjectName(u"label_8")
        self.label_8.setFont(font2)
        self.label_8.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_57.addWidget(self.label_8)

        self.tableWidget_damping_bushing4 = QTableWidget(self.groupBox_15)
        if (self.tableWidget_damping_bushing4.columnCount() < 3):
            self.tableWidget_damping_bushing4.setColumnCount(3)
        __qtablewidgetitem105 = QTableWidgetItem()
        __qtablewidgetitem105.setFont(font2);
        self.tableWidget_damping_bushing4.setHorizontalHeaderItem(0, __qtablewidgetitem105)
        __qtablewidgetitem106 = QTableWidgetItem()
        __qtablewidgetitem106.setFont(font2);
        self.tableWidget_damping_bushing4.setHorizontalHeaderItem(1, __qtablewidgetitem106)
        __qtablewidgetitem107 = QTableWidgetItem()
        __qtablewidgetitem107.setFont(font3);
        self.tableWidget_damping_bushing4.setHorizontalHeaderItem(2, __qtablewidgetitem107)
        if (self.tableWidget_damping_bushing4.rowCount() < 3):
            self.tableWidget_damping_bushing4.setRowCount(3)
        __qtablewidgetitem108 = QTableWidgetItem()
        __qtablewidgetitem108.setFont(font2);
        self.tableWidget_damping_bushing4.setVerticalHeaderItem(0, __qtablewidgetitem108)
        __qtablewidgetitem109 = QTableWidgetItem()
        __qtablewidgetitem109.setFont(font2);
        self.tableWidget_damping_bushing4.setVerticalHeaderItem(1, __qtablewidgetitem109)
        __qtablewidgetitem110 = QTableWidgetItem()
        __qtablewidgetitem110.setFont(font2);
        self.tableWidget_damping_bushing4.setVerticalHeaderItem(2, __qtablewidgetitem110)
        __qtablewidgetitem111 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(0, 0, __qtablewidgetitem111)
        __qtablewidgetitem112 = QTableWidgetItem()
        __qtablewidgetitem112.setFont(font4);
        __qtablewidgetitem112.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing4.setItem(0, 1, __qtablewidgetitem112)
        __qtablewidgetitem113 = QTableWidgetItem()
        __qtablewidgetitem113.setFont(font5);
        __qtablewidgetitem113.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing4.setItem(0, 2, __qtablewidgetitem113)
        __qtablewidgetitem114 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(1, 0, __qtablewidgetitem114)
        __qtablewidgetitem115 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(1, 1, __qtablewidgetitem115)
        __qtablewidgetitem116 = QTableWidgetItem()
        __qtablewidgetitem116.setFlags(Qt.ItemIsDragEnabled|Qt.ItemIsDropEnabled|Qt.ItemIsUserCheckable);
        self.tableWidget_damping_bushing4.setItem(1, 2, __qtablewidgetitem116)
        __qtablewidgetitem117 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(2, 0, __qtablewidgetitem117)
        __qtablewidgetitem118 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(2, 1, __qtablewidgetitem118)
        __qtablewidgetitem119 = QTableWidgetItem()
        self.tableWidget_damping_bushing4.setItem(2, 2, __qtablewidgetitem119)
        self.tableWidget_damping_bushing4.setObjectName(u"tableWidget_damping_bushing4")
        self.tableWidget_damping_bushing4.setMinimumSize(QSize(561, 140))
        self.tableWidget_damping_bushing4.setMaximumSize(QSize(561, 141))
        self.tableWidget_damping_bushing4.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_damping_bushing4.setRowCount(3)
        self.tableWidget_damping_bushing4.horizontalHeader().setMinimumSectionSize(125)
        self.tableWidget_damping_bushing4.horizontalHeader().setDefaultSectionSize(125)
        self.tableWidget_damping_bushing4.verticalHeader().setMinimumSectionSize(30)
        self.tableWidget_damping_bushing4.verticalHeader().setDefaultSectionSize(35)

        self.horizontalLayout_57.addWidget(self.tableWidget_damping_bushing4)


        self.verticalLayout_18.addLayout(self.horizontalLayout_57)


        self.horizontalLayout_67.addWidget(self.groupBox_15)

        self.tabWidget_2.addTab(self.tab_4, "")

        self.gridLayout.addWidget(self.tabWidget_2, 0, 1, 1, 1)

        self.tabWidget.addTab(self.tab_2, "")

        self.verticalLayout.addWidget(self.tabWidget)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.push_generateconnection = QPushButton(self.centralwidget)
        self.push_generateconnection.setObjectName(u"push_generateconnection")
        sizePolicy.setHeightForWidth(self.push_generateconnection.sizePolicy().hasHeightForWidth())
        self.push_generateconnection.setSizePolicy(sizePolicy)
        self.push_generateconnection.setMinimumSize(QSize(200, 65))
        font6 = QFont()
        font6.setFamilies([u"\u5b8b\u4f53"])
        font6.setPointSize(20)
        self.push_generateconnection.setFont(font6)

        self.horizontalLayout.addWidget(self.push_generateconnection)

        self.push_meshui = QPushButton(self.centralwidget)
        self.push_meshui.setObjectName(u"push_meshui")
        sizePolicy.setHeightForWidth(self.push_meshui.sizePolicy().hasHeightForWidth())
        self.push_meshui.setSizePolicy(sizePolicy)
        self.push_meshui.setMinimumSize(QSize(250, 65))
        self.push_meshui.setFont(font6)

        self.horizontalLayout.addWidget(self.push_meshui)

        self.push_analysisui = QPushButton(self.centralwidget)
        self.push_analysisui.setObjectName(u"push_analysisui")
        sizePolicy.setHeightForWidth(self.push_analysisui.sizePolicy().hasHeightForWidth())
        self.push_analysisui.setSizePolicy(sizePolicy)
        self.push_analysisui.setMinimumSize(QSize(250, 65))
        self.push_analysisui.setFont(font6)

        self.horizontalLayout.addWidget(self.push_analysisui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font6)

        self.horizontalLayout.addWidget(self.push_mainui)


        self.verticalLayout.addLayout(self.horizontalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        self.tabWidget.setCurrentIndex(0)
        self.tabWidget_2.setCurrentIndex(1)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.groupBox.setTitle(QCoreApplication.translate("MainWindow", u"\u8f74\u627f1", None))
        self.label_19.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Body", None))

        self.label_20.setText(QCoreApplication.translate("MainWindow", u"Rotation Plane", None))
        self.rotation_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"X-Y Plane", None))
        self.rotation_zhou1.setItemText(1, QCoreApplication.translate("MainWindow", u"Y-Z Plane", None))
        self.rotation_zhou1.setItemText(2, QCoreApplication.translate("MainWindow", u"Z-X Plane", None))

        self.label_16.setText(QCoreApplication.translate("MainWindow", u"\u521a\u5ea6\u7cfb\u6570(N/m)(K11 K22 K12 K21)", None))
        self.stiffness_zhou1.setText(QCoreApplication.translate("MainWindow", u"50000000 50000000 0 0", None))
        self.label_18.setText(QCoreApplication.translate("MainWindow", u"\u963b\u5c3c\u7cfb\u6570(N\u00b7s/m)(C11 C22 C12 C21)", None))
        self.damping_zhou1.setText(QCoreApplication.translate("MainWindow", u"100000 100000 0 0", None))
        self.groupBox_3.setTitle(QCoreApplication.translate("MainWindow", u"Reference", None))
        self.label_24.setText(QCoreApplication.translate("MainWindow", u"Reference Component", None))
        self.reference_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"bearing_1", None))
        self.reference_zhou1.setItemText(1, QCoreApplication.translate("MainWindow", u"qq", None))

        self.label_25.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.referencebehavior_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.referencebehavior_zhou1.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.referencebehavior_zhou1.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.groupBox_4.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_27.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"dianji_1", None))
        self.mobile_zhou1.setItemText(1, QCoreApplication.translate("MainWindow", u"qq", None))

        self.label_26.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_zhou1.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_zhou1.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_zhou1.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.groupBox_2.setTitle(QCoreApplication.translate("MainWindow", u"\u8f74\u627f2", None))
        self.label_22.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Body", None))

        self.label_21.setText(QCoreApplication.translate("MainWindow", u"Rotation Plane", None))
        self.rotation_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"X-Y Plane", None))
        self.rotation_zhou2.setItemText(1, QCoreApplication.translate("MainWindow", u"Y-Z Plane", None))
        self.rotation_zhou2.setItemText(2, QCoreApplication.translate("MainWindow", u"Z-X Plane", None))

        self.label_17.setText(QCoreApplication.translate("MainWindow", u"\u521a\u5ea6\u7cfb\u6570(N/mm)(K11 K22 K12 K21)", None))
        self.stiffness_zhou2.setText(QCoreApplication.translate("MainWindow", u"50000000 50000000 0 0", None))
        self.label_23.setText(QCoreApplication.translate("MainWindow", u"\u963b\u5c3c\u7cfb\u6570(N\u00b7s/m)(C11 C22 C12 C21)", None))
        self.damping_zhou2.setText(QCoreApplication.translate("MainWindow", u"100000 100000 0 0", None))
        self.groupBox_5.setTitle(QCoreApplication.translate("MainWindow", u"Reference", None))
        self.label_28.setText(QCoreApplication.translate("MainWindow", u"Reference Component", None))
        self.reference_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"bearing_2", None))
        self.reference_zhou2.setItemText(1, QCoreApplication.translate("MainWindow", u"qq", None))

        self.label_29.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.referencebehavior_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.referencebehavior_zhou2.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.referencebehavior_zhou2.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.groupBox_6.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_30.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"dianji_2", None))
        self.mobile_zhou2.setItemText(1, QCoreApplication.translate("MainWindow", u"qq", None))

        self.label_31.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_zhou2.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_zhou2.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_zhou2.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"Tab 1", None))
        self.groupBox_7.setTitle(QCoreApplication.translate("MainWindow", u"bushing1", None))
        self.label_32.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_bushing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Ground", None))

        self.groupBox_8.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_36.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_bushing1.setItemText(0, QCoreApplication.translate("MainWindow", u"bushing_1", None))
        self.mobile_bushing1.setItemText(1, QCoreApplication.translate("MainWindow", u"ww", None))

        self.label_37.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_bushing1.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_bushing1.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_bushing1.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.label.setText(QCoreApplication.translate("MainWindow", u"Stiffness", None))
        ___qtablewidgetitem = self.tableWidget_stiffness_bushing1.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem1 = self.tableWidget_stiffness_bushing1.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem2 = self.tableWidget_stiffness_bushing1.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem3 = self.tableWidget_stiffness_bushing1.verticalHeaderItem(0)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"Force X (N)", None));
        ___qtablewidgetitem4 = self.tableWidget_stiffness_bushing1.verticalHeaderItem(1)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"Force Y (N)", None));
        ___qtablewidgetitem5 = self.tableWidget_stiffness_bushing1.verticalHeaderItem(2)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"Force Z (N)", None));

        __sortingEnabled = self.tableWidget_stiffness_bushing1.isSortingEnabled()
        self.tableWidget_stiffness_bushing1.setSortingEnabled(False)
        ___qtablewidgetitem6 = self.tableWidget_stiffness_bushing1.item(0, 0)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("MainWindow", u"816", None));
        ___qtablewidgetitem7 = self.tableWidget_stiffness_bushing1.item(1, 0)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem8 = self.tableWidget_stiffness_bushing1.item(1, 1)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("MainWindow", u"2268", None));
        ___qtablewidgetitem9 = self.tableWidget_stiffness_bushing1.item(2, 0)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem10 = self.tableWidget_stiffness_bushing1.item(2, 1)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem11 = self.tableWidget_stiffness_bushing1.item(2, 2)
        ___qtablewidgetitem11.setText(QCoreApplication.translate("MainWindow", u"938.5", None));
        self.tableWidget_stiffness_bushing1.setSortingEnabled(__sortingEnabled)

        self.label_2.setText(QCoreApplication.translate("MainWindow", u"Damping", None))
        ___qtablewidgetitem12 = self.tableWidget_damping_bushing1.horizontalHeaderItem(0)
        ___qtablewidgetitem12.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem13 = self.tableWidget_damping_bushing1.horizontalHeaderItem(1)
        ___qtablewidgetitem13.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem14 = self.tableWidget_damping_bushing1.horizontalHeaderItem(2)
        ___qtablewidgetitem14.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem15 = self.tableWidget_damping_bushing1.verticalHeaderItem(0)
        ___qtablewidgetitem15.setText(QCoreApplication.translate("MainWindow", u"Force*Time X (N\u00b7s)", None));
        ___qtablewidgetitem16 = self.tableWidget_damping_bushing1.verticalHeaderItem(1)
        ___qtablewidgetitem16.setText(QCoreApplication.translate("MainWindow", u"Force*Time Y (N\u00b7s)", None));
        ___qtablewidgetitem17 = self.tableWidget_damping_bushing1.verticalHeaderItem(2)
        ___qtablewidgetitem17.setText(QCoreApplication.translate("MainWindow", u"Force*Time Z (N\u00b7s)", None));

        __sortingEnabled1 = self.tableWidget_damping_bushing1.isSortingEnabled()
        self.tableWidget_damping_bushing1.setSortingEnabled(False)
        ___qtablewidgetitem18 = self.tableWidget_damping_bushing1.item(0, 0)
        ___qtablewidgetitem18.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem19 = self.tableWidget_damping_bushing1.item(1, 0)
        ___qtablewidgetitem19.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem20 = self.tableWidget_damping_bushing1.item(1, 1)
        ___qtablewidgetitem20.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem21 = self.tableWidget_damping_bushing1.item(2, 0)
        ___qtablewidgetitem21.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem22 = self.tableWidget_damping_bushing1.item(2, 1)
        ___qtablewidgetitem22.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem23 = self.tableWidget_damping_bushing1.item(2, 2)
        ___qtablewidgetitem23.setText(QCoreApplication.translate("MainWindow", u"900", None));
        self.tableWidget_damping_bushing1.setSortingEnabled(__sortingEnabled1)

        self.groupBox_10.setTitle(QCoreApplication.translate("MainWindow", u"bushing2", None))
        self.label_44.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_bushing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Ground", None))

        self.groupBox_13.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_46.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_bushing2.setItemText(0, QCoreApplication.translate("MainWindow", u"bushing_2", None))
        self.mobile_bushing2.setItemText(1, QCoreApplication.translate("MainWindow", u"ww", None))

        self.label_47.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_bushing2.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_bushing2.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_bushing2.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.label_5.setText(QCoreApplication.translate("MainWindow", u"Stiffness", None))
        ___qtablewidgetitem24 = self.tableWidget_stiffness_bushing2.horizontalHeaderItem(0)
        ___qtablewidgetitem24.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem25 = self.tableWidget_stiffness_bushing2.horizontalHeaderItem(1)
        ___qtablewidgetitem25.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem26 = self.tableWidget_stiffness_bushing2.horizontalHeaderItem(2)
        ___qtablewidgetitem26.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem27 = self.tableWidget_stiffness_bushing2.verticalHeaderItem(0)
        ___qtablewidgetitem27.setText(QCoreApplication.translate("MainWindow", u"Force X (N)", None));
        ___qtablewidgetitem28 = self.tableWidget_stiffness_bushing2.verticalHeaderItem(1)
        ___qtablewidgetitem28.setText(QCoreApplication.translate("MainWindow", u"Force Y (N)", None));
        ___qtablewidgetitem29 = self.tableWidget_stiffness_bushing2.verticalHeaderItem(2)
        ___qtablewidgetitem29.setText(QCoreApplication.translate("MainWindow", u"Force Z (N)", None));

        __sortingEnabled2 = self.tableWidget_stiffness_bushing2.isSortingEnabled()
        self.tableWidget_stiffness_bushing2.setSortingEnabled(False)
        ___qtablewidgetitem30 = self.tableWidget_stiffness_bushing2.item(0, 0)
        ___qtablewidgetitem30.setText(QCoreApplication.translate("MainWindow", u"816", None));
        ___qtablewidgetitem31 = self.tableWidget_stiffness_bushing2.item(1, 0)
        ___qtablewidgetitem31.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem32 = self.tableWidget_stiffness_bushing2.item(1, 1)
        ___qtablewidgetitem32.setText(QCoreApplication.translate("MainWindow", u"2268", None));
        ___qtablewidgetitem33 = self.tableWidget_stiffness_bushing2.item(1, 2)
        ___qtablewidgetitem33.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem34 = self.tableWidget_stiffness_bushing2.item(2, 0)
        ___qtablewidgetitem34.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem35 = self.tableWidget_stiffness_bushing2.item(2, 1)
        ___qtablewidgetitem35.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem36 = self.tableWidget_stiffness_bushing2.item(2, 2)
        ___qtablewidgetitem36.setText(QCoreApplication.translate("MainWindow", u"938.5", None));
        self.tableWidget_stiffness_bushing2.setSortingEnabled(__sortingEnabled2)

        self.label_6.setText(QCoreApplication.translate("MainWindow", u"Damping", None))
        ___qtablewidgetitem37 = self.tableWidget_damping_bushing2.horizontalHeaderItem(0)
        ___qtablewidgetitem37.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem38 = self.tableWidget_damping_bushing2.horizontalHeaderItem(1)
        ___qtablewidgetitem38.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem39 = self.tableWidget_damping_bushing2.horizontalHeaderItem(2)
        ___qtablewidgetitem39.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem40 = self.tableWidget_damping_bushing2.verticalHeaderItem(0)
        ___qtablewidgetitem40.setText(QCoreApplication.translate("MainWindow", u"Force*Time X (N\u00b7s)", None));
        ___qtablewidgetitem41 = self.tableWidget_damping_bushing2.verticalHeaderItem(1)
        ___qtablewidgetitem41.setText(QCoreApplication.translate("MainWindow", u"Force*Time Y (N\u00b7s)", None));
        ___qtablewidgetitem42 = self.tableWidget_damping_bushing2.verticalHeaderItem(2)
        ___qtablewidgetitem42.setText(QCoreApplication.translate("MainWindow", u"Force*Time Z (N\u00b7s)", None));

        __sortingEnabled3 = self.tableWidget_damping_bushing2.isSortingEnabled()
        self.tableWidget_damping_bushing2.setSortingEnabled(False)
        ___qtablewidgetitem43 = self.tableWidget_damping_bushing2.item(0, 0)
        ___qtablewidgetitem43.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem44 = self.tableWidget_damping_bushing2.item(1, 0)
        ___qtablewidgetitem44.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem45 = self.tableWidget_damping_bushing2.item(1, 1)
        ___qtablewidgetitem45.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem46 = self.tableWidget_damping_bushing2.item(2, 0)
        ___qtablewidgetitem46.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem47 = self.tableWidget_damping_bushing2.item(2, 1)
        ___qtablewidgetitem47.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem48 = self.tableWidget_damping_bushing2.item(2, 2)
        ___qtablewidgetitem48.setText(QCoreApplication.translate("MainWindow", u"900", None));
        self.tableWidget_damping_bushing2.setSortingEnabled(__sortingEnabled3)

        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab_3), QCoreApplication.translate("MainWindow", u"Tab 1", None))
        self.groupBox_18.setTitle(QCoreApplication.translate("MainWindow", u"bushing3", None))
        self.label_56.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_bushing3.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Ground", None))

        self.groupBox_19.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_58.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_bushing3.setItemText(0, QCoreApplication.translate("MainWindow", u"bushing_3", None))
        self.mobile_bushing3.setItemText(1, QCoreApplication.translate("MainWindow", u"ww", None))

        self.label_59.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_bushing3.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_bushing3.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_bushing3.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.label_9.setText(QCoreApplication.translate("MainWindow", u"Stiffness", None))
        ___qtablewidgetitem49 = self.tableWidget_stiffness_bushing3.horizontalHeaderItem(0)
        ___qtablewidgetitem49.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem50 = self.tableWidget_stiffness_bushing3.horizontalHeaderItem(1)
        ___qtablewidgetitem50.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem51 = self.tableWidget_stiffness_bushing3.horizontalHeaderItem(2)
        ___qtablewidgetitem51.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem52 = self.tableWidget_stiffness_bushing3.verticalHeaderItem(0)
        ___qtablewidgetitem52.setText(QCoreApplication.translate("MainWindow", u"Force X (N)", None));
        ___qtablewidgetitem53 = self.tableWidget_stiffness_bushing3.verticalHeaderItem(1)
        ___qtablewidgetitem53.setText(QCoreApplication.translate("MainWindow", u"Force Y (N)", None));
        ___qtablewidgetitem54 = self.tableWidget_stiffness_bushing3.verticalHeaderItem(2)
        ___qtablewidgetitem54.setText(QCoreApplication.translate("MainWindow", u"Force Z (N)", None));

        __sortingEnabled4 = self.tableWidget_stiffness_bushing3.isSortingEnabled()
        self.tableWidget_stiffness_bushing3.setSortingEnabled(False)
        ___qtablewidgetitem55 = self.tableWidget_stiffness_bushing3.item(0, 0)
        ___qtablewidgetitem55.setText(QCoreApplication.translate("MainWindow", u"816", None));
        ___qtablewidgetitem56 = self.tableWidget_stiffness_bushing3.item(1, 0)
        ___qtablewidgetitem56.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem57 = self.tableWidget_stiffness_bushing3.item(1, 1)
        ___qtablewidgetitem57.setText(QCoreApplication.translate("MainWindow", u"2268", None));
        ___qtablewidgetitem58 = self.tableWidget_stiffness_bushing3.item(2, 0)
        ___qtablewidgetitem58.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem59 = self.tableWidget_stiffness_bushing3.item(2, 1)
        ___qtablewidgetitem59.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem60 = self.tableWidget_stiffness_bushing3.item(2, 2)
        ___qtablewidgetitem60.setText(QCoreApplication.translate("MainWindow", u"983.5", None));
        self.tableWidget_stiffness_bushing3.setSortingEnabled(__sortingEnabled4)

        self.label_10.setText(QCoreApplication.translate("MainWindow", u"Damping", None))
        ___qtablewidgetitem61 = self.tableWidget_damping_bushing3.horizontalHeaderItem(0)
        ___qtablewidgetitem61.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem62 = self.tableWidget_damping_bushing3.horizontalHeaderItem(1)
        ___qtablewidgetitem62.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem63 = self.tableWidget_damping_bushing3.horizontalHeaderItem(2)
        ___qtablewidgetitem63.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem64 = self.tableWidget_damping_bushing3.verticalHeaderItem(0)
        ___qtablewidgetitem64.setText(QCoreApplication.translate("MainWindow", u"Force*Time X (N\u00b7s)", None));
        ___qtablewidgetitem65 = self.tableWidget_damping_bushing3.verticalHeaderItem(1)
        ___qtablewidgetitem65.setText(QCoreApplication.translate("MainWindow", u"Force*Time Y (N\u00b7s)", None));
        ___qtablewidgetitem66 = self.tableWidget_damping_bushing3.verticalHeaderItem(2)
        ___qtablewidgetitem66.setText(QCoreApplication.translate("MainWindow", u"Force*Time Z (N\u00b7s)", None));

        __sortingEnabled5 = self.tableWidget_damping_bushing3.isSortingEnabled()
        self.tableWidget_damping_bushing3.setSortingEnabled(False)
        ___qtablewidgetitem67 = self.tableWidget_damping_bushing3.item(0, 0)
        ___qtablewidgetitem67.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem68 = self.tableWidget_damping_bushing3.item(1, 0)
        ___qtablewidgetitem68.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem69 = self.tableWidget_damping_bushing3.item(1, 1)
        ___qtablewidgetitem69.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem70 = self.tableWidget_damping_bushing3.item(2, 0)
        ___qtablewidgetitem70.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem71 = self.tableWidget_damping_bushing3.item(2, 1)
        ___qtablewidgetitem71.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem72 = self.tableWidget_damping_bushing3.item(2, 2)
        ___qtablewidgetitem72.setText(QCoreApplication.translate("MainWindow", u"900", None));
        self.tableWidget_damping_bushing3.setSortingEnabled(__sortingEnabled5)

        self.groupBox_15.setTitle(QCoreApplication.translate("MainWindow", u"bushing4", None))
        self.label_50.setText(QCoreApplication.translate("MainWindow", u"Connection Type", None))
        self.connection_bushing4.setItemText(0, QCoreApplication.translate("MainWindow", u"Body-Ground", None))

        self.groupBox_16.setTitle(QCoreApplication.translate("MainWindow", u"Mobile", None))
        self.label_52.setText(QCoreApplication.translate("MainWindow", u"Mobile Component", None))
        self.mobile_bushing4.setItemText(0, QCoreApplication.translate("MainWindow", u"bushing_4", None))
        self.mobile_bushing4.setItemText(1, QCoreApplication.translate("MainWindow", u"ww", None))

        self.label_53.setText(QCoreApplication.translate("MainWindow", u"Behavior", None))
        self.mobilebehavior_bushing4.setItemText(0, QCoreApplication.translate("MainWindow", u"Rigid", None))
        self.mobilebehavior_bushing4.setItemText(1, QCoreApplication.translate("MainWindow", u"Deformable", None))
        self.mobilebehavior_bushing4.setItemText(2, QCoreApplication.translate("MainWindow", u"Beam", None))

        self.label_7.setText(QCoreApplication.translate("MainWindow", u"Stiffness", None))
        ___qtablewidgetitem73 = self.tableWidget_stiffness_bushing4.horizontalHeaderItem(0)
        ___qtablewidgetitem73.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem74 = self.tableWidget_stiffness_bushing4.horizontalHeaderItem(1)
        ___qtablewidgetitem74.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem75 = self.tableWidget_stiffness_bushing4.horizontalHeaderItem(2)
        ___qtablewidgetitem75.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem76 = self.tableWidget_stiffness_bushing4.verticalHeaderItem(0)
        ___qtablewidgetitem76.setText(QCoreApplication.translate("MainWindow", u"Force X (N)", None));
        ___qtablewidgetitem77 = self.tableWidget_stiffness_bushing4.verticalHeaderItem(1)
        ___qtablewidgetitem77.setText(QCoreApplication.translate("MainWindow", u"Force Y (N)", None));
        ___qtablewidgetitem78 = self.tableWidget_stiffness_bushing4.verticalHeaderItem(2)
        ___qtablewidgetitem78.setText(QCoreApplication.translate("MainWindow", u"Force Z (N)", None));

        __sortingEnabled6 = self.tableWidget_stiffness_bushing4.isSortingEnabled()
        self.tableWidget_stiffness_bushing4.setSortingEnabled(False)
        ___qtablewidgetitem79 = self.tableWidget_stiffness_bushing4.item(0, 0)
        ___qtablewidgetitem79.setText(QCoreApplication.translate("MainWindow", u"816", None));
        ___qtablewidgetitem80 = self.tableWidget_stiffness_bushing4.item(1, 0)
        ___qtablewidgetitem80.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem81 = self.tableWidget_stiffness_bushing4.item(1, 1)
        ___qtablewidgetitem81.setText(QCoreApplication.translate("MainWindow", u"2268", None));
        ___qtablewidgetitem82 = self.tableWidget_stiffness_bushing4.item(2, 0)
        ___qtablewidgetitem82.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem83 = self.tableWidget_stiffness_bushing4.item(2, 1)
        ___qtablewidgetitem83.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem84 = self.tableWidget_stiffness_bushing4.item(2, 2)
        ___qtablewidgetitem84.setText(QCoreApplication.translate("MainWindow", u"983.5", None));
        self.tableWidget_stiffness_bushing4.setSortingEnabled(__sortingEnabled6)

        self.label_8.setText(QCoreApplication.translate("MainWindow", u"Damping", None))
        ___qtablewidgetitem85 = self.tableWidget_damping_bushing4.horizontalHeaderItem(0)
        ___qtablewidgetitem85.setText(QCoreApplication.translate("MainWindow", u"Per Unit X (m)", None));
        ___qtablewidgetitem86 = self.tableWidget_damping_bushing4.horizontalHeaderItem(1)
        ___qtablewidgetitem86.setText(QCoreApplication.translate("MainWindow", u"Per Unit Y (m)", None));
        ___qtablewidgetitem87 = self.tableWidget_damping_bushing4.horizontalHeaderItem(2)
        ___qtablewidgetitem87.setText(QCoreApplication.translate("MainWindow", u"Per Unit Z (m)", None));
        ___qtablewidgetitem88 = self.tableWidget_damping_bushing4.verticalHeaderItem(0)
        ___qtablewidgetitem88.setText(QCoreApplication.translate("MainWindow", u"Force*Time X (N\u00b7s)", None));
        ___qtablewidgetitem89 = self.tableWidget_damping_bushing4.verticalHeaderItem(1)
        ___qtablewidgetitem89.setText(QCoreApplication.translate("MainWindow", u"Force*Time Y (N\u00b7s)", None));
        ___qtablewidgetitem90 = self.tableWidget_damping_bushing4.verticalHeaderItem(2)
        ___qtablewidgetitem90.setText(QCoreApplication.translate("MainWindow", u"Force*Time Z (N\u00b7s)", None));

        __sortingEnabled7 = self.tableWidget_damping_bushing4.isSortingEnabled()
        self.tableWidget_damping_bushing4.setSortingEnabled(False)
        ___qtablewidgetitem91 = self.tableWidget_damping_bushing4.item(0, 0)
        ___qtablewidgetitem91.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem92 = self.tableWidget_damping_bushing4.item(1, 0)
        ___qtablewidgetitem92.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem93 = self.tableWidget_damping_bushing4.item(1, 1)
        ___qtablewidgetitem93.setText(QCoreApplication.translate("MainWindow", u"900", None));
        ___qtablewidgetitem94 = self.tableWidget_damping_bushing4.item(2, 0)
        ___qtablewidgetitem94.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem95 = self.tableWidget_damping_bushing4.item(2, 1)
        ___qtablewidgetitem95.setText(QCoreApplication.translate("MainWindow", u"0", None));
        ___qtablewidgetitem96 = self.tableWidget_damping_bushing4.item(2, 2)
        ___qtablewidgetitem96.setText(QCoreApplication.translate("MainWindow", u"900", None));
        self.tableWidget_damping_bushing4.setSortingEnabled(__sortingEnabled7)

        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab_4), QCoreApplication.translate("MainWindow", u"Tab 2", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), QCoreApplication.translate("MainWindow", u"\u9875", None))
        self.push_generateconnection.setText(QCoreApplication.translate("MainWindow", u"\u8bbe\u7f6e\u8fde\u63a5", None))
        self.push_meshui.setText(QCoreApplication.translate("MainWindow", u"\u4e0a\u4e00\u6b65(\u524d\u5904\u7406)", None))
        self.push_analysisui.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u5206\u6790\u8bbe\u7f6e)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

