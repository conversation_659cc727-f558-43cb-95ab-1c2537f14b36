#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单模态计算按钮调试脚本

此脚本用于诊断单模态计算按钮没有反应的问题。

作者: 振动传递计算软件开发团队
日期: 2025-01-29
"""

import sys
import os
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'debug_modal_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_imports():
    """检查关键模块的导入"""
    print("\n" + "="*60)
    print("检查模块导入")
    print("="*60)
    
    try:
        # 检查 ctrl.modal_slot 模块
        print("1. 检查 ctrl.modal_slot 模块...")
        from ctrl.modal_slot import execute_single_modal_calculation
        print("   ✅ ctrl.modal_slot.execute_single_modal_calculation 导入成功")
        
        # 检查 window_manager
        print("2. 检查 window_manager 模块...")
        from window_manager import WindowManager, WindowType
        print("   ✅ window_manager 导入成功")
        
        # 检查 views.mesh_window_merged
        print("3. 检查 views.mesh_window_merged 模块...")
        from views.mesh_window_merged import MeshWindow
        print("   ✅ views.mesh_window_merged 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 意外错误: {str(e)}")
        return False

def check_function_signature():
    """检查函数签名"""
    print("\n" + "="*60)
    print("检查函数签名")
    print("="*60)
    
    try:
        from ctrl.modal_slot import execute_single_modal_calculation
        import inspect
        
        sig = inspect.signature(execute_single_modal_calculation)
        print(f"execute_single_modal_calculation 函数签名: {sig}")
        
        # 检查参数
        params = list(sig.parameters.keys())
        expected_params = ['window_manager', 'mesh_parameter', 'calc_params']
        
        print(f"实际参数: {params}")
        print(f"期望参数: {expected_params}")
        
        if params == expected_params:
            print("✅ 函数签名正确")
            return True
        else:
            print("❌ 函数签名不匹配")
            return False
            
    except Exception as e:
        print(f"❌ 检查函数签名失败: {str(e)}")
        return False

def check_button_connection():
    """检查按钮连接（需要在Qt应用程序中运行）"""
    print("\n" + "="*60)
    print("检查按钮连接")
    print("="*60)
    
    try:
        # 这部分需要在实际的Qt应用程序中运行
        print("⚠️  此检查需要在实际的Qt应用程序中运行")
        print("   请在应用程序中查看以下内容:")
        print("   1. 按钮是否存在: self.ui.btn_single_modal")
        print("   2. 按钮是否已连接: self.ui.btn_single_modal.clicked.connect(self._on_single_modal)")
        print("   3. 按钮是否可见和可点击")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查按钮连接失败: {str(e)}")
        return False

def simulate_function_call():
    """模拟函数调用"""
    print("\n" + "="*60)
    print("模拟函数调用")
    print("="*60)
    
    try:
        # 创建模拟对象
        class MockMeshParameter:
            def __init__(self):
                self.name = "test_mesh"
                self.size = 2.0
                
        class MockWindowManager:
            def get_window(self, window_type):
                return None  # 模拟返回None
                
        # 模拟参数
        window_manager = MockWindowManager()
        mesh_parameter = MockMeshParameter()
        calc_params = {
            'modal_count': 12,
            'limit_freq': False,
            'freq_min': 0.0,
            'freq_max': 1000.0
        }
        
        print("准备调用 execute_single_modal_calculation...")
        print(f"参数: mesh_name={mesh_parameter.name}, calc_params={calc_params}")
        
        # 尝试调用函数
        from ctrl.modal_slot import execute_single_modal_calculation
        
        try:
            execute_single_modal_calculation(window_manager, mesh_parameter, calc_params)
            print("✅ 函数调用成功（可能因为窗口实例为None而提前返回）")
            
        except Exception as e:
            print(f"⚠️  函数调用出现异常: {str(e)}")
            print("   这可能是正常的，因为我们使用的是模拟对象")
            
        return True
        
    except Exception as e:
        print(f"❌ 模拟函数调用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_file_paths():
    """检查关键文件路径"""
    print("\n" + "="*60)
    print("检查关键文件路径")
    print("="*60)
    
    # 检查配置文件目录
    config_dir = r"D:/data/all-XM/autoworkbench/csdaima"
    print(f"配置文件目录: {config_dir}")
    
    if os.path.exists(config_dir):
        print("✅ 配置文件目录存在")
    else:
        print("❌ 配置文件目录不存在")
        try:
            os.makedirs(config_dir, exist_ok=True)
            print("✅ 配置文件目录已创建")
        except Exception as e:
            print(f"❌ 创建配置文件目录失败: {str(e)}")
    
    # 检查关键配置文件
    config_files = [
        "analysis_config_latest.json",
        "2.json",
        "connection_result.json"
    ]
    
    for filename in config_files:
        filepath = os.path.join(config_dir, filename)
        if os.path.exists(filepath):
            print(f"✅ {filename} 存在")
        else:
            print(f"⚠️  {filename} 不存在")
    
    return True

def main():
    """主函数"""
    print("单模态计算按钮调试脚本")
    print("="*60)
    print(f"开始时间: {datetime.now()}")
    
    # 添加项目路径到sys.path
    project_root = os.path.dirname(os.path.abspath(__file__))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    print(f"项目根目录: {project_root}")
    
    results = []
    
    # 执行各项检查
    results.append(("模块导入", check_imports()))
    results.append(("函数签名", check_function_signature()))
    results.append(("按钮连接", check_button_connection()))
    results.append(("文件路径", check_file_paths()))
    results.append(("函数调用", simulate_function_call()))
    
    # 显示总结
    print("\n" + "="*60)
    print("检查结果总结")
    print("="*60)
    
    for name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{name:12} : {status}")
    
    # 给出建议
    print("\n" + "="*60)
    print("调试建议")
    print("="*60)
    
    if all(result[1] for result in results):
        print("✅ 所有检查都通过了")
        print("\n可能的问题:")
        print("1. 界面上没有选中网格")
        print("2. 选中了多个网格")
        print("3. 网格状态不正确")
        print("4. 按钮被禁用")
        print("5. Qt事件循环问题")
        
        print("\n建议:")
        print("1. 在应用程序中查看控制台输出")
        print("2. 检查日志文件")
        print("3. 确保选中了单个已生成的网格")
        print("4. 重启应用程序")
        
    else:
        print("❌ 发现问题，请根据上述检查结果进行修复")
    
    print(f"\n结束时间: {datetime.now()}")

if __name__ == "__main__":
    main()
