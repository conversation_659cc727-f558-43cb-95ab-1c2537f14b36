"""
测试修复版本的启动画面
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    print("测试修复版本的启动画面...")
    
    try:
        # 导入必要的模块
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QIcon
        from core.splash_screen_fixed import CustomSplashScreen, SplashScreenManager
        
        print("✅ 所有模块导入成功")
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 设置应用程序图标
        icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
            print(f"✅ 应用图标加载成功")
        
        # 创建启动画面管理器
        splash_manager = SplashScreenManager()
        
        # 显示启动画面
        splash = splash_manager.show_splash()
        print("✅ 启动画面显示成功")
        
        # 模拟进度更新
        steps = [
            (20, "正在初始化系统..."),
            (40, "正在加载配置文件..."),
            (60, "正在创建用户界面..."),
            (80, "正在完成设置..."),
            (100, "启动完成！")
        ]
        
        for progress, status in steps:
            splash_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 进度更新: {progress}% - {status}")
            time.sleep(1)
            app.processEvents()
        
        # 等待一下再隐藏
        time.sleep(2)
        splash_manager.hide_splash()
        print("✅ 启动画面隐藏成功")
        
        print("\n🎉 启动画面测试完全成功！")
        print("修复版本工作正常。")
        
        # 测试自定义配置
        print("\n测试自定义配置...")
        custom_config = {
            "colors": {
                "primary": "#e74c3c",  # 红色主题
                "secondary": "#f39c12"
            },
            "layout": {
                "width": 520,
                "height": 360
            }
        }
        
        custom_manager = SplashScreenManager(custom_config)
        custom_splash = custom_manager.show_splash()
        
        custom_manager.update_progress_by_percentage(50, "测试自定义主题...")
        time.sleep(2)
        app.processEvents()
        
        custom_manager.hide_splash()
        print("✅ 自定义配置测试成功")
        
        print("\n所有测试完成！启动画面功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
