<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口文档 - 专业RESTful API集成系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }
        .code-inline {
            background: #f1f5f9;
            color: #475569;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-green-600 to-emerald-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-green-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🌐 API接口文档
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-green-100">
                    专业RESTful API集成系统 | 线程安全高并发调用支持
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-green-500 bg-opacity-20 text-green-100 text-sm font-semibold px-4 py-2 rounded-full border border-green-400">
                        🔗 RESTful设计 | 📊 JSON格式 | 🛡️ 线程安全 | 📚 完整文档
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- API Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🌐 API概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                振动传递计算软件提供了完整的RESTful API接口，支持外部应用程序与软件进行集成。API采用线程安全设计，支持高并发调用。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">RESTful设计</h3>
                    </div>
                    <p class="text-sm text-blue-600">标准的HTTP方法和状态码</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">JSON格式</h3>
                    </div>
                    <p class="text-sm text-green-600">统一的数据交换格式</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">线程安全</h3>
                    </div>
                    <p class="text-sm text-purple-600">支持高并发调用</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">错误处理</h3>
                    </div>
                    <p class="text-sm text-orange-600">详细的错误信息和状态码</p>
                </div>
                
                <div class="bg-teal-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-teal-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-teal-800">文档完整</h3>
                    </div>
                    <p class="text-sm text-teal-600">完整的API文档和示例</p>
                </div>
            </div>
        </section>

        <!-- Basic Information -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔧 基础信息</h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🖥️ 服务器配置</h3>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <span class="font-semibold text-blue-800 w-20">基础URL:</span>
                            <span class="code-inline">http://127.0.0.1:8000</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-semibold text-blue-800 w-20">协议:</span>
                            <span class="code-inline">HTTP/1.1</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-semibold text-blue-800 w-20">数据格式:</span>
                            <span class="code-inline">JSON</span>
                        </div>
                        <div class="flex items-center">
                            <span class="font-semibold text-blue-800 w-20">字符编码:</span>
                            <span class="code-inline">UTF-8</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔐 认证方式</h3>
                    <p class="text-gray-600 mb-4">当前版本使用本地服务，无需认证。未来版本将支持：</p>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                            <span class="text-sm">API密钥认证</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                            <span class="text-sm">JWT令牌认证</span>
                        </div>
                        <div class="flex items-center">
                            <span class="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                            <span class="text-sm">OAuth 2.0认证</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- API Endpoints -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📋 API端点</h2>

            <div class="space-y-6">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">💚</span>健康检查 - GET /health
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">检查API服务器状态，确保服务正常运行。</p>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-gray-800 mb-2">响应示例：</h4>
                            <div class="code-block">
{
    "status": "healthy",
    "timestamp": "2023-12-01T10:30:00Z",
    "version": "1.2.0",
    "uptime": 3600
}
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚙️</span>仿真参数更新 - POST /api/v1/simulation-params
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">更新仿真计算参数，支持网格大小、时间步长等配置。</p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">请求体示例：</h4>
                                <div class="code-block text-xs">
{
    "mesh_size": 0.01,
    "time_step": 0.001,
    "analysis_type": "transient",
    "solver_settings": {
        "max_iterations": 1000,
        "convergence_tolerance": 1e-6
    }
}
                                </div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">响应示例：</h4>
                                <div class="code-block text-xs">
{
    "success": true,
    "message": "参数更新成功",
    "updated_params": {
        "mesh_size": 0.01,
        "time_step": 0.001,
        "analysis_type": "transient"
    }
}
                                </div>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔄</span>窗口切换 - POST /api/v1/switch-window
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">切换到指定的功能窗口，支持参数传递。</p>
                        <div class="bg-purple-50 p-4 rounded-lg mb-4">
                            <h4 class="font-semibold text-purple-800 mb-2">请求体示例：</h4>
                            <div class="code-block">
{
    "window_name": "mesh",
    "params": {
        "auto_mesh": true,
        "element_size": 0.01
    }
}
                            </div>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-3">支持的窗口类型：</h4>
                            <div class="grid md:grid-cols-2 gap-2">
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">main</span>
                                    <span class="text-sm">主界面</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">mesh</span>
                                    <span class="text-sm">网格划分</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">pre</span>
                                    <span class="text-sm">前处理</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">connection</span>
                                    <span class="text-sm">连接设置</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">analysis</span>
                                    <span class="text-sm">分析设置</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">constrain</span>
                                    <span class="text-sm">约束设置</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">result</span>
                                    <span class="text-sm">结果显示</span>
                                </div>
                                <div class="flex items-center">
                                    <span class="code-inline mr-2">vibration</span>
                                    <span class="text-sm">振动分析</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Status Codes -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 状态码</h2>

            <div class="overflow-x-auto">
                <table class="w-full bg-white rounded-lg shadow-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">状态码</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">含义</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">说明</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">示例场景</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr class="hover:bg-green-50">
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    200
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">OK</td>
                            <td class="px-6 py-4 text-sm text-gray-600">请求成功</td>
                            <td class="px-6 py-4 text-sm text-gray-500">参数更新成功</td>
                        </tr>
                        <tr class="hover:bg-blue-50">
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    201
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Created</td>
                            <td class="px-6 py-4 text-sm text-gray-600">资源创建成功</td>
                            <td class="px-6 py-4 text-sm text-gray-500">新建项目成功</td>
                        </tr>
                        <tr class="hover:bg-yellow-50">
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    400
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Bad Request</td>
                            <td class="px-6 py-4 text-sm text-gray-600">请求参数错误</td>
                            <td class="px-6 py-4 text-sm text-gray-500">参数格式不正确</td>
                        </tr>
                        <tr class="hover:bg-orange-50">
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    404
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Not Found</td>
                            <td class="px-6 py-4 text-sm text-gray-600">资源不存在</td>
                            <td class="px-6 py-4 text-sm text-gray-500">指定窗口不存在</td>
                        </tr>
                        <tr class="hover:bg-red-50">
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    500
                                </span>
                            </td>
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">Internal Server Error</td>
                            <td class="px-6 py-4 text-sm text-gray-600">服务器内部错误</td>
                            <td class="px-6 py-4 text-sm text-gray-500">系统异常</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>如何启动API服务？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">API服务随软件主程序自动启动，无需手动配置。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">启动流程：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 启动软件主程序</li>
                                <li>• API服务自动在8000端口启动</li>
                                <li>• 通过 /health 端点检查服务状态</li>
                                <li>• 开始使用API接口</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🛡️</span>API是否线程安全？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">是的，API采用线程安全设计，支持高并发调用。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">安全特性：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 线程安全的数据访问</li>
                                <li>• 并发请求处理</li>
                                <li>• 资源锁定机制</li>
                                <li>• 异常处理和恢复</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📝</span>如何处理API错误？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">API提供详细的错误信息和标准HTTP状态码。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">错误处理建议：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 检查HTTP状态码</li>
                                <li>• 解析错误响应中的详细信息</li>
                                <li>• 实现重试机制</li>
                                <li>• 记录错误日志</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• API服务仅在本地运行，不支持远程访问</li>
                    <li>• 确保软件主程序正在运行</li>
                    <li>• 大量并发请求可能影响软件性能</li>
                    <li>• 参数更新会立即生效，请谨慎操作</li>
                    <li>• 定期检查API服务状态</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 使用健康检查端点监控服务状态</li>
                    <li>• 实现适当的错误处理和重试机制</li>
                    <li>• 合理控制API调用频率</li>
                    <li>• 保存重要的API响应数据</li>
                    <li>• 参考示例代码进行集成开发</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">专业RESTful API集成系统 - 线程安全高并发调用支持</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
