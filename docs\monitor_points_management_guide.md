# 监控点管理功能使用指南

## 概述

基于 `preuide.json` 设计规范，我们在Qt项目的约束设置界面 `ui/ui_constrain.py` 的第5个标签页（tab_5）中实现了完整的监控点管理功能。

## 功能特性

### 1. 单点创建功能
- **输入验证**: 支持坐标数值格式验证，允许6位小数精度
- **重复检查**: 自动检查点位名称是否重复
- **实时反馈**: 输入错误时提供明确的错误提示
- **快捷操作**: 支持回车键快速添加点位

### 2. 批量文件导入功能
- **多格式支持**: 支持 `.txt`, `.csv`, `.xlsx` 文件格式
- **智能解析**: 自动识别文件格式和分隔符
- **灵活格式**: 支持 "名称,X,Y,Z" 或 "X,Y,Z" 格式
- **错误处理**: 完善的文件解析错误处理机制

### 3. 监控点列表显示
- **实时更新**: 表格实时显示所有已创建的监控点
- **多列显示**: 序号、点位名称、X/Y/Z坐标、操作按钮
- **排序功能**: 支持按列排序
- **选择模式**: 支持多行选择

### 4. 点删除管理功能
- **单点删除**: 每行都有独立的删除按钮
- **批量清空**: 支持一键清空所有监控点
- **确认机制**: 删除前显示确认对话框
- **状态反馈**: 删除后显示操作结果

### 5. 界面设计特点
- **现代化UI**: 遵循Material Design设计原则
- **响应式布局**: 自适应窗口大小变化
- **状态栏显示**: 实时显示当前监控点数量和操作状态
- **一致性**: 与项目整体UI风格保持一致

## 文件结构

### 修改的文件

1. **ui/ui_constrain.py** - 约束设置界面文件（tab_5）
   - 在第5个标签页中添加监控点管理功能
   - 采用网格布局，左右分栏设计
   - 左侧：监控点创建与导入功能
   - 右侧：监控点列表显示
   - 与原有约束设置功能完美集成

2. **views/constrain_window.py** - 增强的约束设置窗口类
   - 在原有约束设置功能基础上添加监控点管理
   - 完整的监控点管理业务逻辑
   - 文件导入解析功能
   - 数据验证和错误处理
   - 表格管理和界面更新

## 技术实现要点

### 输入验证
```python
# 数值验证器设置
validator = QDoubleValidator()
validator.setDecimals(6)  # 允许6位小数
validator.setNotation(QDoubleValidator.Notation.StandardNotation)
```

### 文件解析支持
- **TXT文件**: 支持空格或逗号分隔的坐标数据
- **CSV文件**: 自动检测分隔符，支持标题行
- **Excel文件**: 使用openpyxl库解析，支持多种数据格式

### 数据结构
```python
point_data = {
    'id': self.point_counter,
    'name': name,
    'x': x,
    'y': y,
    'z': z,
    'created_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
}
```

## 使用方法

### 访问监控点管理功能
1. 打开约束设置窗口
2. 点击第5个标签页"监控点管理"
3. 即可看到完整的监控点管理界面

### 手动添加监控点
1. 在"点位名称"输入框中输入名称
2. 分别输入X、Y、Z坐标值
3. 点击"➕ 添加点位"按钮或按回车键

### 批量导入监控点
1. 点击"📁 选择文件导入"按钮
2. 选择支持的文件格式（.txt, .csv, .xlsx）
3. 系统自动解析并导入有效数据
4. 显示导入结果和重复项提示

### 管理监控点
- **删除单个点位**: 点击表格中对应行的"🗑️"按钮
- **清空所有点位**: 点击右下角的"清空全部"按钮
- **查看点位信息**: 在表格中查看所有点位的详细信息

## 错误处理

### 输入验证错误
- 空名称检查
- 坐标格式验证
- 重复名称检查

### 文件导入错误
- 文件格式不支持
- 文件解析失败
- 数据格式错误
- 缺少必要库（如openpyxl）

### 操作确认
- 删除单个点位确认
- 清空所有点位确认

## 样式设计

界面采用现代化的设计风格：
- **主色调**: Google Material Design 蓝色系
- **按钮样式**: 圆角设计，悬停效果
- **表格样式**: 斑马纹显示，清晰的边框
- **输入框**: 聚焦时的蓝色边框高亮

## 扩展功能

### 数据持久化
- `save_points_to_file()`: 保存监控点到JSON文件
- `load_points_from_file()`: 从JSON文件加载监控点

### API接口
- `get_monitor_points()`: 获取当前所有监控点数据
- `set_monitor_points()`: 设置监控点数据

## 性能优化

- 使用QDoubleValidator进行实时输入验证
- 表格数据按需更新，避免频繁重绘
- 文件解析采用流式读取，支持大文件
- 内存中维护轻量级数据结构

## 兼容性

- **Qt版本**: PySide6/Qt 6.x
- **Python版本**: 3.8+
- **依赖库**: openpyxl（Excel文件支持，可选）

## 后续改进建议

1. **导出功能**: 支持将监控点数据导出为各种格式
2. **坐标系转换**: 支持不同坐标系之间的转换
3. **可视化预览**: 添加3D坐标点预览功能
4. **模板管理**: 支持保存和加载监控点模板
5. **批量编辑**: 支持表格内直接编辑坐标值

## 总结

新的监控点管理界面完全按照 `preuide.json` 设计规范实现，提供了完整的监控点创建、导入、显示和管理功能。界面美观易用，功能完善，错误处理机制健全，为用户提供了良好的使用体验。
