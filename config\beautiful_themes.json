{"themes": {"default_modern": {"name": "现代默认主题", "description": "深色到浅色的现代化渐变，专业而优雅", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 900, "fade_out_duration": 700, "minimum_display_time": 4500, "show_progress": true, "show_rotation_animation": true, "show_company_info": true, "colors": {"primary": "#3498db", "secondary": "#2ecc71", "text": "#34495e", "progress_background": "#ecf0f1"}, "fonts": {"title_size": 16, "version_size": 10, "status_size": 9, "font_family": "<PERSON><PERSON>"}, "layout": {"width": 480, "height": 320}}}, "dark_professional": {"name": "深色专业主题", "description": "企业级深色设计，适合专业环境", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 600, "fade_out_duration": 400, "minimum_display_time": 2500, "show_rotation_animation": true, "colors": {"primary": "#2c3e50", "secondary": "#34495e", "text": "#ecf0f1", "progress_background": "#34495e"}, "fonts": {"title_size": 18, "version_size": 11, "status_size": 10, "font_family": "Microsoft YaHei"}, "layout": {"width": 520, "height": 360}}}, "vibrant_gradient": {"name": "活力渐变主题", "description": "鲜艳的彩虹渐变，充满活力和创意", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 700, "fade_out_duration": 500, "minimum_display_time": 3000, "show_rotation_animation": true, "colors": {"primary": "#e74c3c", "secondary": "#f39c12", "text": "#ffffff", "progress_background": "#34495e"}, "fonts": {"title_size": 17, "version_size": 11, "status_size": 10, "font_family": "<PERSON><PERSON>"}, "layout": {"width": 500, "height": 340}}}, "ocean_blue": {"name": "海洋蓝主题", "description": "深海蓝色渐变，宁静而深邃", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 800, "fade_out_duration": 600, "minimum_display_time": 2800, "show_rotation_animation": true, "colors": {"primary": "#2980b9", "secondary": "#3498db", "text": "#ffffff", "progress_background": "#34495e"}, "fonts": {"title_size": 16, "version_size": 10, "status_size": 9, "font_family": "Segoe UI"}, "layout": {"width": 480, "height": 320}}}, "sunset_glow": {"name": "日落余晖主题", "description": "温暖的日落色彩，橙红渐变", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 600, "fade_out_duration": 400, "minimum_display_time": 2200, "show_rotation_animation": true, "colors": {"primary": "#e67e22", "secondary": "#f39c12", "text": "#ffffff", "progress_background": "#d35400"}, "fonts": {"title_size": 16, "version_size": 10, "status_size": 9, "font_family": "<PERSON><PERSON>"}, "layout": {"width": 480, "height": 320}}}, "forest_green": {"name": "森林绿主题", "description": "自然的绿色渐变，清新而生机勃勃", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 500, "fade_out_duration": 300, "minimum_display_time": 2000, "show_rotation_animation": true, "colors": {"primary": "#27ae60", "secondary": "#2ecc71", "text": "#ffffff", "progress_background": "#1e8449"}, "fonts": {"title_size": 16, "version_size": 10, "status_size": 9, "font_family": "<PERSON><PERSON>"}, "layout": {"width": 480, "height": 320}}}, "purple_dream": {"name": "紫色梦幻主题", "description": "神秘的紫色渐变，优雅而梦幻", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 750, "fade_out_duration": 500, "minimum_display_time": 2500, "show_rotation_animation": true, "colors": {"primary": "#8e44ad", "secondary": "#9b59b6", "text": "#ffffff", "progress_background": "#6c3483"}, "fonts": {"title_size": 17, "version_size": 11, "status_size": 10, "font_family": "Segoe UI"}, "layout": {"width": 500, "height": 340}}}, "minimal_light": {"name": "简约浅色主题", "description": "简洁的浅色设计，清爽而现代", "config": {"enabled": true, "show_fade_in": true, "fade_in_duration": 400, "fade_out_duration": 250, "minimum_display_time": 1800, "show_rotation_animation": false, "colors": {"primary": "#95a5a6", "secondary": "#bdc3c7", "text": "#2c3e50", "progress_background": "#ecf0f1"}, "fonts": {"title_size": 15, "version_size": 9, "status_size": 8, "font_family": "Segoe UI"}, "layout": {"width": 460, "height": 300}}}}, "usage_instructions": {"how_to_use": "在代码中使用 SplashScreenManager(themes['theme_name']['config']) 来应用主题", "example": "splash_manager = SplashScreenManager(themes['dark_professional']['config'])", "customization": "可以基于这些主题进行进一步自定义，修改颜色、字体、布局等参数"}, "visual_features": {"gradient_background": "深色到浅色的现代化渐变背景", "decorative_elements": "多层装饰性几何元素增强视觉层次", "modern_progress_bar": "带高光效果的现代化进度条", "text_shadows": "文字阴影效果提升可读性", "enhanced_animations": "多层旋转动画和淡入淡出效果", "professional_colors": "精心调配的专业色彩方案"}}