"""
最小化启动画面测试
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    try:
        print("测试 PySide6 导入...")
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt, Signal
        from PySide6.QtGui import QPixmap
        print("✅ PySide6 导入成功")
        
        print("测试启动画面模块导入...")
        from core.splash_screen import CustomSplashScreen
        print("✅ 启动画面模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        from PySide6.QtWidgets import QApplication
        from core.splash_screen import CustomSplashScreen
        
        print("创建应用程序...")
        app = QApplication([])
        
        print("创建启动画面...")
        splash = CustomSplashScreen()
        
        print("显示启动画面...")
        splash.show()
        
        print("更新进度...")
        splash.update_progress(50, "测试中...")
        
        print("处理事件...")
        app.processEvents()
        
        print("隐藏启动画面...")
        splash.hide()
        
        print("✅ 基本功能测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("=" * 50)
    print("启动画面最小化测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("导入测试失败，退出")
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        print("基本功能测试失败")
        return
    
    print("\n🎉 所有测试通过！启动画面功能正常。")

if __name__ == "__main__":
    main()
