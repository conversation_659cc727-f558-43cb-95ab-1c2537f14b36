"""
振动传递计算软件启动器

此模块是应用程序的入口点，负责：
1. 初始化应用程序
2. 创建并注册窗口
3. 启动API服务器
4. 连接信号和槽
5. 显示主窗口

作者: [作者名]
日期: [日期]
"""

import os
import sys
import subprocess
import tempfile
import threading
import logging
import time
from PySide6.QtCore import QSettings, Qt, QUrl, QTimer
from PySide6.QtGui import QCloseEvent, QAction, QIcon, QDoubleValidator, QKeySequence, QShortcut
from PySide6.QtWidgets import (
    QMainWindow, QApplication, QMessageBox, QFileDialog, QStyle,
    QDialog, QLabel, QPushButton, QWidget, QTreeWidgetItem,
    QSpacerItem, QSizePolicy, QVBoxLayout, QHBoxLayout,
    QTextBrowser, QLineEdit
)
from PySide6.QtPrintSupport import QPrinter, QPrintDialog

# 导入优化相关模块
from core.performance_monitor import get_profiler, measure_performance, log_checkpoint, print_performance_summary
from core.optimization_config import get_optimization_manager, is_optimization_enabled
from core.style_manager import get_style_manager, apply_critical_styles, apply_full_styles
from core.font_manager import get_font_manager
try:
    from core.splash_screen_simple import get_simple_splash_manager as get_splash_manager
except ImportError:
    # 如果简化版本不可用，尝试其他版本
    try:
        from core.splash_screen_fixed import get_splash_manager
    except ImportError:
        try:
            from core.splash_screen import get_splash_manager
        except ImportError:
            # 如果都不可用，创建一个空的管理器
            class DummySplashManager:
                def show_splash(self): return None
                def update_progress_by_percentage(self, *args): pass
                def hide_splash(self): pass
            def get_splash_manager(): return DummySplashManager()

from window_manager import WindowManager, WindowType, WindowObserver
from ctrl.api_server import run_api_server, set_update_callback
from ctrl.startup_config import load_initial_data

# 延迟导入的模块（按需加载）
from views.main_window import MainWindow
from core.window_factories import register_all_window_factories


def _load_legacy_styles(app):
    """传统方式加载样式表"""
    style_path = os.path.join(os.path.dirname(__file__),"assets","styles","style.qss")
    try:
        with open(style_path, 'r', encoding='utf-8') as f:
            style = f.read()
        app.setStyleSheet(style)
        logging.debug("传统样式表加载成功")
    except Exception as e:
        logging.error(f"加载样式表失败: {e}")
        # 使用内联样式作为备用
        app.setStyleSheet("""
            QMainWindow { background-color: #f8fafc; }
            QPushButton {
                background-color: #3498db; color: white; border: none;
                border-radius: 4px; padding: 8px 16px; font-weight: 500;
            }
            QPushButton:hover { background-color: #2980b9; }
        """)


def _create_all_windows_legacy(window_manager, initial_data):
    """传统模式：创建所有窗口"""
    from views.mesh_window import MeshWindow
    from views.pre_window import PreWindow
    from views.connection_window import ConnectionWindow
    from views.analysis_window import AnalysisWindow
    from views.constrain_window import ConstrainWindow
    from views.result_window import ResultWindow
    from views.log_viewer import LogViewer
    from ctrl.vibration_analysis import VibrationAnalysisWindow

    main_window = MainWindow(window_manager=window_manager, initial_data=initial_data)
    mesh_window = MeshWindow(window_manager=window_manager)
    pre_window = PreWindow(window_manager=window_manager)
    connection_window = ConnectionWindow(window_manager=window_manager)
    analysis_window = AnalysisWindow(window_manager=window_manager)
    constrain_window = ConstrainWindow(window_manager=window_manager)
    result_window = ResultWindow(window_manager=window_manager)
    vibration_window = VibrationAnalysisWindow(window_manager=window_manager)
    log_viewer = LogViewer(window_manager=window_manager)

    # 存储所有窗口以便注册
    window_manager._legacy_windows = {
        'main': main_window,
        'mesh': mesh_window,
        'pre': pre_window,
        'connection': connection_window,
        'analysis': analysis_window,
        'constrain': constrain_window,
        'result': result_window,
        'vibration': vibration_window,
        'log': log_viewer
    }

    return main_window


def _register_all_windows_legacy(window_manager, main_window):
    """传统模式：注册所有窗口"""
    windows = window_manager._legacy_windows

    window_manager.register_window(WindowType.MAIN, windows['main'])
    window_manager.register_window(WindowType.MESH, windows['mesh'])
    window_manager.register_window(WindowType.PRE, windows['pre'])
    window_manager.register_window(WindowType.CONNECTION, windows['connection'])
    window_manager.register_window(WindowType.ANALYSIS, windows['analysis'])
    window_manager.register_window(WindowType.CONSTRAIN, windows['constrain'])
    window_manager.register_window(WindowType.RESULT, windows['result'])
    window_manager.register_window(WindowType.VIBRATION, windows['vibration'])
    window_manager.register_window(WindowType.LOG, windows['log'])
    

    # 清理临时存储
    delattr(window_manager, '_legacy_windows')


def _initialize_slots_lazy(window_manager):
    """延迟初始化槽函数"""
    from ctrl import main_slot, mesh_slot, connection_slot, analysis_slot, constrain_slot, pre_slot, result_slot

    pre_slot.pre_slot(window_manager)
    main_slot.main_slot(window_manager)
    mesh_slot.mesh_slot(window_manager)
    connection_slot.connection_slot(window_manager)
    analysis_slot.analysis_slot(window_manager)
    constrain_slot.constrain_slot(window_manager)
    result_slot.result_slot(window_manager)

# 导入全局异常处理和日志系统
try:
    from core import setup_exception_handling, exception_signaler, ErrorDialog, ConfigManager, setup_logging
    HAS_GLOBAL_EXCEPTION_HANDLER = True
except ImportError:
    HAS_GLOBAL_EXCEPTION_HANDLER = False
    print("警告: 未能导入全局异常处理模块，将使用默认异常处理")

# 尝试导入WebEngine，如果不可用则使用QTextBrowser备用方案
try:
    from PySide6.QtWebEngineWidgets import QWebEngineView
    HAS_WEB_ENGINE = True
    print("警告: 导入WebEngine模块成功")
except ImportError:
    HAS_WEB_ENGINE = False
    print("警告: 未能导入WebEngine模块，将使用QTextBrowser备用方案")


@measure_performance("initialize_application")
def initialize_application():
    """初始化应用程序（优化版本）"""
    log_checkpoint("应用程序初始化开始")

    # 设置日志系统
    setup_logging()

    # 创建应用实例
    app = QApplication([])
    app.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__),"assets","icons","vibration_transfer_icon_alt.ico")))

    # 加载启动画面配置并显示启动画面
    try:
        from core.config_manager import ConfigManager
        config_manager = ConfigManager()
        splash_config = config_manager.get("splash_screen", {})

        # 检查是否启用启动画面
        if splash_config.get("enabled", False):
            splash_manager = get_splash_manager()
            splash_manager.config = splash_config
            splash = splash_manager.show_splash()
            splash_manager.update_progress_by_percentage(5, "初始化日志系统...")
        else:
            splash_manager = None
    except Exception as e:
        logging.warning(f"启动画面初始化失败，将跳过: {e}")
        splash_manager = None

    # 记录应用程序启动信息
    logging.info("应用程序启动...")
    log_checkpoint("应用程序实例创建完成")
    if splash_manager:
        splash_manager.update_progress_by_percentage(10, "创建应用程序实例...")
        time.sleep(0.5)  # 让用户看到启动画面和进度

    # 设置全局异常处理
    if splash_manager:
        splash_manager.update_progress_by_percentage(15, "配置异常处理...")
        time.sleep(0.5)  # 显示异常处理配置状态
    if HAS_GLOBAL_EXCEPTION_HANDLER:
        exception_signaler = setup_exception_handling()
        logging.info("全局异常处理已启用")

    # 优化的样式表加载
    if splash_manager:
        splash_manager.update_progress_by_percentage(20, "加载关键样式...")
        time.sleep(0.7)  # 显示样式加载状态
    if is_optimization_enabled('style_cache'):
        # 使用样式管理器加载关键样式
        apply_critical_styles(app)
        log_checkpoint("关键样式已应用")
        if splash_manager:
            splash_manager.update_progress_by_percentage(30, "应用关键样式...")
            time.sleep(1)  # 显示样式应用状态

        # 延迟加载完整样式
        QTimer.singleShot(200, lambda: apply_full_styles(app))
    else:
        # 传统方式加载样式
        _load_legacy_styles(app)
        log_checkpoint("传统样式加载完成")

    # 初始化字体管理器并应用字体设置
    if splash_manager:
        splash_manager.update_progress_by_percentage(35, "初始化字体管理器...")
        time.sleep(1.0)  # 显示字体管理器初始化状态
    font_manager = get_font_manager()
    font_manager.apply_fonts_to_application(app)
    log_checkpoint("字体管理器初始化完成")

    # 应用内联关键样式（确保基本可用性）
    app.setStyleSheet(app.styleSheet() + """
        /* 主窗口样式 */
        QMainWindow {
            background-color: #f8fafc;
        }
        
        /* 按钮样式 */
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            min-width: 80px;
            font-weight: 500;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #f5f5f5;
        }
        
        /* 特殊按钮样式 */
        QPushButton#push_finish, QPushButton#result, QPushButton#post {
            background-color: #2ecc71;
        }
        
        QPushButton#push_finish:hover, QPushButton#result:hover, QPushButton#post:hover {
            background-color: #27ae60;
        }
        
        QPushButton#push_mainui, QPushButton#push_constrainui {
            background-color: #7f8c8d;
        }
        
        QPushButton#push_mainui:hover, QPushButton#push_constrainui:hover {
            background-color: #6c7a7d;
        }
        
        /* 标签样式 */
        QLabel {
            color: #34495e;
        }
        
        /* 组合框样式 */
        QComboBox {
            background-color: white;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 5px;
            min-height: 25px;
        }
        
        QComboBox:hover {
            border: 1px solid #3498db;
        }
        
        /* 输入框样式 */ 
        QLineEdit {
            background-color: white;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 6px 8px;
        }
        
        QLineEdit:focus {
            border: 2px solid #3498db;
        }
        
        /* 分组框样式 */
        QGroupBox {
            background-color: white;
            border: 1px solid #e9eaec;
            border-radius: 6px;
            margin-top: 12px;
            padding: 15px;
            font-weight: bold;
            color: #34495e;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px;
            background-color: white;
        }
        
        /* 标签页样式 */
        QTabWidget::pane {
            border: 1px solid #e9eaec;
            border-radius: 0 6px 6px 6px;
            background: white;
            top: -1px;
        }
        
        QTabBar::tab {
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            min-width: 80px;
            padding: 8px 15px;
            font-weight: 500;
        }
        
        QTabBar::tab:selected {
            background: white;
            border-bottom-color: white;
            color: #3498db;
        }
        
        QTabBar::tab:hover:!selected {
            background: #ecf0f1;
        }
        
        /* 表格样式 */
        QTableView, QTableWidget {
            border: 1px solid #dcdfe6;
            background-color: white;
            gridline-color: #e9eaec;
            selection-background-color: #3498db;
            selection-color: white;
            alternate-background-color: #f5f7fa;
        }
        
        /* 状态栏样式 */
        QStatusBar {
            background-color: #f5f7fa;
            color: #34495e;
            border-top: 1px solid #dcdfe6;
        }
        
        /* 菜单栏样式 */
        QMenuBar {
            background-color: #f8fafc;
            border-bottom: 1px solid #e9eaec;
        }
        
        QMenuBar::item {
            padding: 8px 15px;
            background: transparent;
        }
        
        QMenuBar::item:selected {
            background-color: #ecf0f1;
            border-radius: 4px;
        }
    """)
    
    # 1. 尝试加载初始数据
    if splash_manager:
        splash_manager.update_progress_by_percentage(50, "加载初始配置数据...")
        time.sleep(1.5)  # 显示数据加载状态
    try:
        initial_data = load_initial_data()
        if initial_data:
            logging.info(f"成功加载初始数据: {initial_data}")
        else:
            logging.info("未找到初始数据")
    except Exception as e:
        logging.error(f"加载初始数据失败: {e}")
        initial_data = None

    # 2. 创建窗口管理器 - 依赖注入的核心
    if splash_manager:
        splash_manager.update_progress_by_percentage(60, "创建窗口管理器...")
        time.sleep(1.3)  # 显示窗口管理器创建状态
    window_manager = WindowManager()
    logging.info("窗口管理器创建完成")
    log_checkpoint("窗口管理器创建完成")

    # 3. 根据优化配置选择窗口创建策略
    if splash_manager:
        splash_manager.update_progress_by_percentage(70, "创建应用程序窗口...")
        time.sleep(1.8)  # 显示窗口创建状态，这是一个重要步骤
    if is_optimization_enabled('lazy_loading'):
        # 懒加载模式：只创建主窗口，其他窗口注册工厂函数
        try:
            main_window = MainWindow(window_manager=window_manager, initial_data=initial_data)
            window_manager.register_window(WindowType.MAIN, main_window)
            log_checkpoint("主窗口创建完成")
            if splash_manager:
                splash_manager.update_progress_by_percentage(75, "主窗口创建完成...")

            # 注册其他窗口的工厂函数
            try:
                register_all_window_factories(window_manager)
                log_checkpoint("窗口工厂注册完成")
                logging.info("所有窗口工厂注册成功")
                if splash_manager:
                    splash_manager.update_progress_by_percentage(80, "注册窗口工厂...")
            except Exception as e:
                logging.error(f"窗口工厂注册失败: {e}", exc_info=True)
                raise

            # 启动预加载定时器
            window_manager.start_preload_timer()

            logging.info("懒加载模式：主窗口已创建，其他窗口将按需创建")
        except Exception as e:
            logging.critical(f"懒加载模式窗口创建失败: {e}", exc_info=True)
            raise
    else:
        # 传统模式：创建所有窗口
        try:
            main_window = _create_all_windows_legacy(window_manager, initial_data)
            _register_all_windows_legacy(window_manager, main_window)
            log_checkpoint("传统模式窗口创建完成")
            if splash_manager:
                splash_manager.update_progress_by_percentage(80, "所有窗口创建完成...")

            logging.info("传统模式：所有窗口已创建")
        except Exception as e:
            logging.critical(f"传统模式窗口创建失败: {e}", exc_info=True)
            raise
    
    # 5. 如果有初始数据，应用到相关窗口
    if initial_data:
        try:
            if splash_manager:
                splash_manager.update_progress_by_percentage(85, "应用初始数据...")
            apply_initial_data_to_windows(window_manager, initial_data)
            logging.info("初始数据已应用到窗口")
        except Exception as e:
            logging.error(f"应用初始数据到窗口失败: {e}")

    # 6. 设置API服务器的回调函数
    set_update_callback(main_window.update_ui_from_api)

    # 7. 在后台线程中启动API服务器
    if splash_manager:
        splash_manager.update_progress_by_percentage(90, "启动API服务器...")
        time.sleep(1.5)  # 显示API服务器启动状态
    try:
        api_thread = threading.Thread(target=run_api_server, daemon=True)
        api_thread.start()
        logging.info("API服务器启动成功")
    except Exception as e:
        logging.error(f"启动API服务器失败: {e}")

    # 8. 初始化槽函数
    if splash_manager:
        splash_manager.update_progress_by_percentage(95, "初始化槽函数...")
        time.sleep(1.2)  # 显示槽函数初始化状态
    try:
        _initialize_slots_lazy(window_manager)
        log_checkpoint("槽函数初始化完成")
        logging.info("槽函数初始化完成")
    except Exception as e:
        logging.error(f"初始化槽函数失败: {e}")

    # 9. 显示主窗口
    if splash_manager:
        splash_manager.update_progress_by_percentage(100, "启动完成！")
        time.sleep(2.0)  # 让用户看到启动完成状态
    try:
        window_manager.switch_to(WindowType.MAIN)
        logging.info("主窗口显示成功")

        # 隐藏启动画面
        if splash_manager:
            splash_manager.hide_splash()
    except Exception as e:
        logging.critical(f"显示主窗口失败: {e}", exc_info=True)
        raise

    return app


def _auto_validate_force_files(constrain_window, force_dir: str) -> None:
    """自动验证力文件并在显示框中输出结果

    Args:
        constrain_window: 约束窗口实例
        force_dir: 力文件夹路径
    """
    status_text = []

    def log_status(msg: str):
        """记录状态信息并更新显示"""
        status_text.append(msg)
        if hasattr(constrain_window.ui, 'plainTextEdit_force'):
            constrain_window.ui.plainTextEdit_force.setPlainText("\n".join(status_text))
            constrain_window.ui.plainTextEdit_force.repaint()

    # 清空状态显示并开始验证
    if hasattr(constrain_window.ui, 'plainTextEdit_force'):
        constrain_window.ui.plainTextEdit_force.clear()

    log_status("开始处理力文件...")
    log_status(f"选择的文件夹: {force_dir}")
    log_status("开始验证文件...")

    # 定义必需的文件 - 与constrain_slot.py中保持一致
    required_files = {
        "yl-wall": ["yl-fx.out", "yl-fy.out", "yl-fz.out"],
        "wk-wall": ["wk-fx.out", "wk-fy.out", "wk-fz.out"],
    }

    missing_files = []
    invalid_files = []

    # 导入验证函数
    try:
        from ctrl.constrain_slot import validate_force_file_content
    except ImportError:
        log_status("错误: 无法导入力文件验证函数")
        return

    for group, files in required_files.items():
        log_status(f"\n检查 {group} 组文件...")
        for filename in files:
            file_path = os.path.join(force_dir, filename)

            # 检查文件是否存在
            if not os.path.exists(file_path):
                log_status(f"  ✗ {filename} - 文件不存在")
                missing_files.append(filename)
                continue

            # 检查文件扩展名
            if not filename.endswith('.out'):
                log_status(f"  ✗ {filename} - 不是.out文件")
                invalid_files.append(f"{filename} (不是.out文件)")
                continue

            # 检查文件内容格式
            if not validate_force_file_content(file_path):
                log_status(f"  ✗ {filename} - 格式错误")
                invalid_files.append(f"{filename} (格式错误)")
                continue

            log_status(f"  [OK] {filename} - 验证通过")

    # 处理验证结果
    if missing_files or invalid_files:
        error_msg_parts = []
        if missing_files:
            error_msg_parts.append("缺少以下文件:\n" + "\n".join(missing_files))
        if invalid_files:
            error_msg_parts.append("以下文件无效:\n" + "\n".join(invalid_files))

        log_status("\n验证结果: 失败 ✗")
        log_status("\n" + "\n\n".join(error_msg_parts))
    else:
        # 验证通过
        log_status("\n验证结果: 全部通过 [SUCCESS]")
        log_status(f"力文件路径已保存: {force_dir}")


def apply_initial_data_to_windows(window_manager, data):
    """将初始数据应用到所有相关窗口
    
    Args:
        window_manager: 窗口管理器实例
        data: 初始数据
    """
    logger = logging.getLogger(__name__)
    
    # 应用到分析窗口
    analysis_window = window_manager.get_window(WindowType.ANALYSIS)
    if analysis_window and 'timeStep' in data:
        analysis_window.ui.lineEdit_timestep.setText(str(data['timeStep']))
        logger.debug(f"设置时间步长: {data['timeStep']}")
    if analysis_window and 'endTime' in data:
        analysis_window.ui.lineEdit_stependline.setText(str(data['endTime']))
        logger.debug(f"设置结束时间: {data['endTime']}")
    if analysis_window and 'stiffnessCoefficient' in data and data['stiffnessCoefficient'] is not None:
        analysis_window.ui.lineEdit_stiffness.setText(str(data['stiffnessCoefficient']))
        logger.debug(f"设置刚度系数: {data['stiffnessCoefficient']}")
    if analysis_window and 'massCoefficient' in data and data['massCoefficient'] is not None:
        analysis_window.ui.lineEdit_mass.setText(str(data['massCoefficient']))
        logger.debug(f"设置质量系数: {data['massCoefficient']}")
    
    # 应用到约束窗口
    constrain_window = window_manager.get_window(WindowType.CONSTRAIN)
    if constrain_window and 'forceOutputFolder' in data and data['forceOutputFolder']:
        force_dir = data['forceOutputFolder']
        if os.path.exists(force_dir):
            constrain_window.ui.lineEdit_force.setText(force_dir)
            logger.debug(f"设置力文件夹: {force_dir}")

            # 自动进行力文件验证并在显示框中输出结果
            _auto_validate_force_files(constrain_window, force_dir)
        else:
            logger.warning(f"力文件夹不存在: {force_dir}")
            # 在显示框中显示路径不存在的信息
            if hasattr(constrain_window.ui, 'plainTextEdit_force'):
                constrain_window.ui.plainTextEdit_force.setPlainText(
                    f"启动验证失败:\n力文件夹不存在: {force_dir}"
                )
    if constrain_window and 'rotationSpeed' in data and data['rotationSpeed'] is not None:
        constrain_window.ui.lineEdit_rotation_speed.setText(str(data['rotationSpeed']))
        logger.debug(f"设置旋转速度: {data['rotationSpeed']}")
    if constrain_window and 'rotationAxis' in data:
        axis = data['rotationAxis'].upper()
        if axis in ['X', 'Y', 'Z']:
            index = {'X': 0, 'Y': 1, 'Z': 2}.get(axis, 2)  # 默认为Z轴
            constrain_window.ui.comboBox_rotation_axis.setCurrentIndex(index)
            logger.debug(f"设置旋转轴: {axis}")
        else:
            logger.warning(f"无效的旋转轴: {axis}")


if __name__ == "__main__":
    try:
        # 启动性能监控
        profiler = get_profiler()
        log_checkpoint("程序启动")

        app = initialize_application()
        log_checkpoint("应用程序初始化完成")

        # 打印启动性能报告
        if is_optimization_enabled('performance_monitoring'):
            print_performance_summary()

        logging.info("应用程序初始化完成，进入主事件循环")
        exit_code = app.exec()
        logging.info(f"应用程序退出，退出码: {exit_code}")
        sys.exit(exit_code)
    except Exception as e:
        logging.critical(f"应用程序启动失败: {e}", exc_info=True)

        # 即使启动失败也打印性能信息（用于调试）
        try:
            print_performance_summary()
        except:
            pass

        sys.exit(1)