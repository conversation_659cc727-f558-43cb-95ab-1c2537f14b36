<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModalResultComparison</class>
 <widget class="QWidget" name="ModalResultComparison">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>模态分析结果对比</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_main">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>10</number>
   </property>
   <property name="topMargin">
    <number>10</number>
   </property>
   <property name="rightMargin">
    <number>10</number>
   </property>
   <property name="bottomMargin">
    <number>10</number>
   </property>
   <item>
    <!-- 左侧控制面板 -->
    <widget class="QGroupBox" name="groupBox_control_panel">
     <property name="title">
      <string>模态分析结果控制</string>
     </property>
     <property name="maximumSize">
      <size>
       <width>380</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_control">
      <property name="spacing">
       <number>15</number>
      </property>
      <item>
       <!-- 网格结果列表 -->
       <widget class="QGroupBox" name="groupBox_mesh_results">
        <property name="title">
         <string>网格结果列表</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_mesh_list">
         <item>
          <widget class="QLabel" name="label_mesh_info">
           <property name="text">
            <string>选择要对比的网格结果:</string>
           </property>
           <property name="styleSheet">
            <string>font-weight: normal; color: #666;</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QListWidget" name="listWidget_mesh_results">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>180</height>
            </size>
           </property>
           <property name="selectionMode">
            <enum>QAbstractItemView::MultiSelection</enum>
           </property>
           <property name="styleSheet">
            <string>QListWidget {
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fafafa;
}
QListWidget::item {
    padding: 8px;
    border-bottom: 1px solid #eee;
}
QListWidget::item:selected {
    background-color: #e3f2fd;
    color: #1976d2;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <!-- 模态分析选项 -->
       <widget class="QGroupBox" name="groupBox_modal_options">
        <property name="title">
         <string>模态分析选项</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_modal_options">
         <item>
          <widget class="QCheckBox" name="checkBox_show_frequencies">
           <property name="text">
            <string>显示频率数值</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_show_mode_shapes">
           <property name="text">
            <string>显示模态阶数</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QCheckBox" name="checkBox_show_mesh_info">
           <property name="text">
            <string>显示网格信息</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <!-- 图表类型选择 -->
       <widget class="QGroupBox" name="groupBox_chart_type">
        <property name="title">
         <string>图表类型</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_chart_type">
         <item>
          <widget class="QRadioButton" name="radioButton_frequency_comparison">
           <property name="text">
            <string>频率对比图</string>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QRadioButton" name="radioButton_mode_distribution">
           <property name="text">
            <string>模态分布图</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QRadioButton" name="radioButton_mesh_convergence">
           <property name="text">
            <string>网格收敛性分析</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <!-- 操作按钮 -->
       <layout class="QVBoxLayout" name="verticalLayout_buttons">
        <item>
         <widget class="QPushButton" name="btn_update_chart">
          <property name="text">
           <string>更新图表</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string>QPushButton {
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
}
QPushButton:hover {
    background-color: #1976d2;
}
QPushButton:pressed {
    background-color: #0d47a1;
}</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btn_export_results">
          <property name="text">
           <string>导出结果</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>40</height>
           </size>
          </property>
          <property name="styleSheet">
           <string>QPushButton {
    background-color: #4caf50;
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
}
QPushButton:hover {
    background-color: #388e3c;
}
QPushButton:pressed {
    background-color: #2e7d32;
}</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <!-- 右侧图表显示区域 -->
    <widget class="QGroupBox" name="groupBox_chart_display">
     <property name="title">
      <string>模态分析结果图表</string>
     </property>
     <property name="styleSheet">
      <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_chart">
      <property name="spacing">
       <number>10</number>
      </property>
      <item>
       <!-- 图表工具栏 -->
       <layout class="QHBoxLayout" name="horizontalLayout_chart_toolbar">
        <item>
         <widget class="QLabel" name="label_chart_title">
          <property name="text">
           <string>模态频率对比分析</string>
          </property>
          <property name="styleSheet">
           <string>font-size: 14px; font-weight: bold; color: #333;</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btn_save_chart">
          <property name="text">
           <string>保存图表</string>
          </property>
          <property name="maximumSize">
           <size>
            <width>100</width>
            <height>30</height>
           </size>
          </property>
          <property name="styleSheet">
           <string>QPushButton {
    background-color: #ff9800;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 11px;
}
QPushButton:hover {
    background-color: #f57c00;
}</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <!-- 图表显示区域 -->
       <widget class="QWidget" name="widget_chart_container">
        <property name="minimumSize">
         <size>
          <width>600</width>
          <height>500</height>
         </size>
        </property>
        <property name="styleSheet">
         <string>QWidget {
    border: 2px dashed #cccccc;
    border-radius: 8px;
    background-color: #fafafa;
}</string>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_chart_content">
         <item>
          <widget class="QLabel" name="label_chart_placeholder">
           <property name="text">
            <string>模态分析结果图表将在此处显示

📊 可用数据类型：
• 网格名称和尺寸信息
• 网格节点/单元数量
• 模态阶数 (1-N阶)
• 各阶模态频率结果

💡 选择左侧网格结果并点击"更新图表"开始分析</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="styleSheet">
            <string>color: #999999; 
font-size: 14px; 
line-height: 1.6;
border: none;</string>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
