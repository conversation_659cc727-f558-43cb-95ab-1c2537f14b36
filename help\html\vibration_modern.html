<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>振动分析界面 - 专业振动数据处理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-pink-600 to-purple-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-pink-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🌊 振动分析界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-pink-100">
                    专业振动数据处理系统 | 多源数据融合与智能频谱分析
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-pink-500 bg-opacity-20 text-pink-100 text-sm font-semibold px-4 py-2 rounded-full border border-pink-400">
                        📊 FFT分析 | 🎵 1/3倍频程 | 🔄 数据融合 | 📈 智能可视化
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Feature Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 功能概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                振动分析模块是本软件的核心功能之一，提供了强大的振动数据处理和可视化能力。支持多种数据源，可以帮助工程师分析结构在不同方向上的振动特性。
            </p>

            <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🎯 频段范围配置</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <h4 class="font-semibold text-purple-800 mb-2">📊 默认设置</h4>
                        <p class="text-sm text-purple-600">低频段 (10-315 Hz) - 16个1/3倍频程频段</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm">
                        <h4 class="font-semibold text-pink-800 mb-2">🔧 可选范围</h4>
                        <p class="text-sm text-pink-600">总频段 (10-10k Hz) - 31个1/3倍频程频段</p>
                    </div>
                </div>
                <p class="text-gray-600 mt-4 text-center">支持实时频段切换，所有数据模式同步更新</p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">多数据源</h3>
                    </div>
                    <p class="text-sm text-blue-600">流体激振力、电机数据、组合分析</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">频谱分析</h3>
                    </div>
                    <p class="text-sm text-green-600">FFT变换、1/3倍频程、A计权</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">智能可视化</h3>
                    </div>
                    <p class="text-sm text-purple-600">时域图、频谱图、条形图</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">数据导出</h3>
                    </div>
                    <p class="text-sm text-orange-600">Excel报告、图表导出</p>
                </div>
            </div>
        </section>

        <!-- Data Sources -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">📊 数据源管理</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">支持多种数据源的智能识别和处理</p>
            </div>
            
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌊</span>流体激振力数据模式
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">数据特征</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 支持X、Y、Z三个方向分析</li>
                                    <li>• 文件格式：.txt文本文件</li>
                                    <li>• 数据列：时间、X加速度、Y加速度、Z加速度</li>
                                    <li>• 自动数据验证和格式检查</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">应用场景</h4>
                                <p class="text-sm text-blue-600">适用于流体激励引起的结构振动分析，可以全面评估三个方向的振动特性</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚡</span>电机数据模式
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">数据特征</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 主要分析Z方向数据</li>
                                    <li>• 文件格式：.xlsx/.xls Excel文件</li>
                                    <li>• 自动识别数据列结构</li>
                                    <li>• 智能数据预处理</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">应用场景</h4>
                                <p class="text-sm text-green-600">专门用于电机振动分析，重点关注垂直方向的振动特性</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔄</span>组合数据模式
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">组合算法</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• Z方向：流体+电机数据组合</li>
                                    <li>• X、Y方向：仅流体数据</li>
                                    <li>• 使用对数加法公式组合</li>
                                    <li>• 自动模式切换和识别</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">计算公式</h4>
                                <p class="text-sm text-purple-600 mb-2">对数加法公式：</p>
                                <code class="text-xs bg-white p-2 rounded block">L_total = 10 × lg(10^(L_a1/10) + 10^(L_a2/10))</code>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🎛️</span>如何选择合适的频段范围？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">频段范围的选择应根据分析目标和应用场景确定。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">选择指南：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• <strong>低频段 (10-315 Hz)</strong> - 适用于大多数结构振动分析</li>
                                <li>• <strong>总频段 (10-10k Hz)</strong> - 适用于需要分析高频噪声的场合</li>
                                <li>• 可以通过切换频段范围对比分析结果</li>
                                <li>• 系统支持实时切换，便于结果对比</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔄</span>什么时候使用组合数据模式？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">组合模式用于同时考虑多种振动源的综合影响。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">使用场景：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 需要同时考虑流体激振力和电机振动的影响</li>
                                <li>• Z方向会显示组合结果，X、Y方向显示流体数据</li>
                                <li>• 系统自动使用对数加法公式计算组合振动级</li>
                                <li>• 可以分别查看各分量的单独分析结果</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📊</span>如何验证分析结果的正确性？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">结果验证是确保分析准确性的重要步骤。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">验证方法：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 导出Excel文件查看详细计算过程</li>
                                <li>• 在组合模式下分别查看流体和电机的单独结果</li>
                                <li>• 切换频段范围对比分析结果的一致性</li>
                                <li>• 检查时域信号的合理性和连续性</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📈</span>导出的Excel文件包含哪些信息？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">导出文件包含完整的分析数据和计算过程。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">文件内容：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 原始时域数据和预处理结果</li>
                                <li>• 频谱分析数据（频率、振幅、相位）</li>
                                <li>• 1/3倍频程数据和A计权声级</li>
                                <li>• 当前频段范围和数据模式信息</li>
                                <li>• 组合模式下的流体和电机分量详情</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 确保数据文件格式正确，避免分析错误</li>
                    <li>• 大数据文件的处理可能需要较长时间</li>
                    <li>• 频段切换后结果变化是正常现象</li>
                    <li>• 组合模式需要同时加载两种数据源</li>
                    <li>• 定期保存重要的分析结果</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 从单一数据源开始，逐步进行组合分析</li>
                    <li>• 充分利用频段切换功能对比结果</li>
                    <li>• 注意观察时域信号的质量和特征</li>
                    <li>• 使用导出功能保存详细的分析数据</li>
                    <li>• 结合工程经验解读分析结果</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">专业振动数据处理系统 - 多源数据融合与智能频谱分析</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
