"""
振动分析模块

此模块实现了振动分析界面和功能，主要包括：
1. 振动数据加载和处理
2. FFT分析和1/3倍频程分析
3. 结果可视化
4. 数据导出功能

作者: [作者名]
日期: [日期]
"""

import sys
import os
import numpy as np
import pandas as pd
import matplotlib
matplotlib.use('QtAgg')
# 配置matplotlib支持中文显示
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
from matplotlib.figure import Figure
from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.backends.backend_qtagg import NavigationToolbar2QT as NavigationToolbar
import matplotlib.pyplot as plt
from PySide6.QtCore import Qt, Signal, Slot, QSize, QTimer
from PySide6.QtGui import QIcon, QFont, QColor, QPalette, QAction, QPixmap
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QFileDialog, QTabWidget, QComboBox,
    QGroupBox, QGridLayout, QSplitter, QFrame, QTableWidget,
    QTableWidgetItem, QHeaderView, QMessageBox, QProgressBar,
    QStatusBar, QToolBar, QSizePolicy, QCheckBox, QStyle
)

from window_manager import WindowManager, WindowType
from pyma import load_and_preprocess_data, perform_fft, analyze_third_octave
import warnings


class MatplotlibCanvas(FigureCanvas):
    """Matplotlib画布类，用于嵌入到Qt界面"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi, tight_layout=True)
        self.axes = self.fig.add_subplot(111)
        super(MatplotlibCanvas, self).__init__(self.fig)
        self.setParent(parent)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.updateGeometry()


class ResultsTable(QTableWidget):
    """显示1/3倍频程结果的表格"""
    def __init__(self, parent=None):
        super(ResultsTable, self).__init__(parent)
        self.setColumnCount(2)
        self.setHorizontalHeaderLabels(["中心频率 (Hz)", "振动加速度级 L_A (dB)"])
        self.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.setAlternatingRowColors(True)
    
    def update_data(self, octave_centers, L_A, total_L_A):
        """更新表格数据"""
        self.setRowCount(len(octave_centers) + 1)
        
        # 填充频段数据
        for i, (freq, level) in enumerate(zip(octave_centers, L_A)):
            self.setItem(i, 0, QTableWidgetItem(f"{freq}"))
            self.setItem(i, 1, QTableWidgetItem(f"{level:.2f}"))
        
        # 添加总计行
        total_row = len(octave_centers)
        self.setItem(total_row, 0, QTableWidgetItem("总计"))
        total_item = QTableWidgetItem(f"{total_L_A:.2f}")
        total_item.setFont(QFont("Arial", 10, QFont.Bold))
        self.setItem(total_row, 1, total_item)


class VibrationAnalysisWindow(QMainWindow):
    """振动分析器主界面"""
    def __init__(self, parent=None, window_manager=None):
        super(VibrationAnalysisWindow, self).__init__(parent)
        self.window_manager = window_manager
        self.init_ui()
        
        # 数据存储
        self.time_data = None
        self.acc_data_dict = None
        self.motor_time_data = None
        self.motor_acc_data = None  # 只有Z方向数据
        self.results = {'X': None, 'Y': None, 'Z': None}
        self.data_combination_mode = False  # 数据组合模式标志
        self.current_frequency_range = 'standard'  # 默认使用低频段
        self.total_vibration_level = 0.0  # 总振级
        
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle("振动分析器")
        self.setWindowIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.setMinimumSize(1500, 800)
        
        # 创建状态栏
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("就绪")
        
        # 创建主部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部控制面板
        control_panel = QGroupBox("控制面板")
        control_layout = QHBoxLayout(control_panel)
        
        # 文件选择部分
        file_group = QGroupBox("数据文件")
        file_layout = QVBoxLayout(file_group)  # 改为垂直布局

        # 流体激振力数据选择
        fluid_group = QGroupBox("流体激振力数据")
        fluid_layout = QHBoxLayout(fluid_group)
        self.fluid_file_label = QLabel("未选择文件")
        self.fluid_file_label.setStyleSheet("color: gray; font-style: italic;")
        self.fluid_browse_button = QPushButton("浏览...")
        self.fluid_browse_button.clicked.connect(self.load_fluid_data)
        self.fluid_clear_button = QPushButton("清除数据")
        self.fluid_clear_button.setObjectName("fluid_clear_button")
        self.fluid_clear_button.clicked.connect(self.clear_fluid_data)
        self.fluid_clear_button.setEnabled(False)  # 初始状态禁用
        fluid_layout.addWidget(self.fluid_file_label, 3)
        fluid_layout.addWidget(self.fluid_browse_button, 1)
        fluid_layout.addWidget(self.fluid_clear_button, 1)

        # 电机数据选择
        motor_group = QGroupBox("电机数据")
        motor_layout = QHBoxLayout(motor_group)
        self.motor_file_label = QLabel("未选择文件")
        self.motor_file_label.setStyleSheet("color: gray; font-style: italic;")
        self.motor_browse_button = QPushButton("浏览...")
        self.motor_browse_button.clicked.connect(self.load_motor_data)
        self.motor_clear_button = QPushButton("清除数据")
        self.motor_clear_button.setObjectName("motor_clear_button")
        self.motor_clear_button.clicked.connect(self.clear_motor_data)
        self.motor_clear_button.setEnabled(False)  # 初始状态禁用
        motor_layout.addWidget(self.motor_file_label, 3)
        motor_layout.addWidget(self.motor_browse_button, 1)
        motor_layout.addWidget(self.motor_clear_button, 1)

        # 添加到文件组
        file_layout.addWidget(fluid_group)
        file_layout.addWidget(motor_group)
        
        # 分析控制部分
        analysis_group = QGroupBox("分析选项")
        analysis_layout = QGridLayout(analysis_group)  # 改为网格布局以容纳更多控件

        # 方向选择
        self.axis_combo = QComboBox()
        self.axis_combo.addItems(["Z 方向", "X 方向", "Y 方向"])  # 调整顺序，Z方向在前
        self.axis_combo.setCurrentIndex(0)  # 默认选择Z方向
        self.axis_combo.currentIndexChanged.connect(self.update_display)

        # 频段选择
        self.frequency_range_combo = QComboBox()
        self.frequency_range_combo.addItems([
            "低频段 (10-315 Hz)",
            "总频段 (10-10k Hz)"
        ])
        self.frequency_range_combo.setCurrentIndex(0)  # 默认选择低频段
        self.frequency_range_combo.currentIndexChanged.connect(self.on_frequency_range_changed)

        # 添加到网格布局
        analysis_layout.addWidget(QLabel("分析方向:"), 0, 0)
        analysis_layout.addWidget(self.axis_combo, 0, 1)
        analysis_layout.addWidget(QLabel("分析频段:"), 1, 0)
        analysis_layout.addWidget(self.frequency_range_combo, 1, 1)
        
        # 导出部分
        export_group = QGroupBox("导出结果")
        export_layout = QHBoxLayout(export_group)
        self.export_button = QPushButton("导出到Excel")
        self.export_button.clicked.connect(self.export_results)
        self.export_button.setEnabled(False)
        
        # 返回按钮
        self.return_button = QPushButton("返回结果界面")
        self.return_button.clicked.connect(self.return_to_result)
        self.return_button.setStyleSheet("""
            background-color: #2980b9;
            color: white;
        """)
        
        export_layout.addWidget(self.export_button)
        export_layout.addWidget(self.return_button)
        
        # 添加到控制面板
        control_layout.addWidget(file_group, 5)
        control_layout.addWidget(analysis_group, 3)
        control_layout.addWidget(export_group, 3)
        
        # 创建总振动加速度级显示区域
        total_level_group = QGroupBox("总振动加速度级 (dB)")
        total_level_layout = QHBoxLayout(total_level_group)

        # 为ZXY三个方向创建标签（调整显示顺序）
        self.total_level_labels = {}
        for axis, color in zip(['Z', 'X', 'Y'], ['#3498db', '#e74c3c', '#2ecc71']):  # 调整顺序为ZXY
            axis_group = QGroupBox(f"{axis} 方向")
            axis_layout = QVBoxLayout(axis_group)
            level_label = QLabel("--")
            level_label.setAlignment(Qt.AlignCenter)
            level_label.setFont(QFont("Arial", 16, QFont.Bold))
            level_label.setStyleSheet(f"color: {color}; background-color: #f8f9fa; border-radius: 4px; padding: 10px;")
            axis_layout.addWidget(level_label)
            total_level_layout.addWidget(axis_group)
            self.total_level_labels[axis] = level_label

        # 添加总振级显示框
        total_vibration_group = QGroupBox("总振级")
        total_vibration_layout = QVBoxLayout(total_vibration_group)
        self.total_vibration_label = QLabel("--")
        self.total_vibration_label.setAlignment(Qt.AlignCenter)
        self.total_vibration_label.setFont(QFont("Arial", 18, QFont.Bold))
        self.total_vibration_label.setStyleSheet("color: #8e44ad; background-color: #f8f9fa; border-radius: 4px; padding: 10px; border: 2px solid #8e44ad;")
        total_vibration_layout.addWidget(self.total_vibration_label)
        total_level_layout.addWidget(total_vibration_group)
        
        # 创建分析结果显示区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：表格结果
        table_group = QGroupBox("1/3倍频程分析结果")
        table_layout = QVBoxLayout(table_group)
        self.results_table = ResultsTable()
        table_layout.addWidget(self.results_table)
        
        # 右侧：图形结果
        plots_tab = QTabWidget()

        # 时域图
        self.time_canvas = MatplotlibCanvas(width=5, height=4)
        self.time_toolbar = NavigationToolbar(self.time_canvas, self)
        time_widget = QWidget()
        time_layout = QVBoxLayout(time_widget)
        time_layout.addWidget(self.time_toolbar)
        time_layout.addWidget(self.time_canvas)
        plots_tab.addTab(time_widget, "时域图")

        # 频谱图
        self.spectrum_canvas = MatplotlibCanvas(width=5, height=4)
        self.spectrum_toolbar = NavigationToolbar(self.spectrum_canvas, self)
        spectrum_widget = QWidget()
        spectrum_layout = QVBoxLayout(spectrum_widget)
        spectrum_layout.addWidget(self.spectrum_toolbar)
        spectrum_layout.addWidget(self.spectrum_canvas)
        plots_tab.addTab(spectrum_widget, "频谱图")

        # 1/3倍频程图
        self.octave_canvas = MatplotlibCanvas(width=5, height=4)
        self.octave_toolbar = NavigationToolbar(self.octave_canvas, self)
        octave_widget = QWidget()
        octave_layout = QVBoxLayout(octave_widget)
        octave_layout.addWidget(self.octave_toolbar)
        octave_layout.addWidget(self.octave_canvas)
        plots_tab.addTab(octave_widget, "1/3倍频程图")
        
        # 添加到分割器
        content_splitter.addWidget(table_group)
        content_splitter.addWidget(plots_tab)
        content_splitter.setStretchFactor(0, 1)
        content_splitter.setStretchFactor(1, 2)
        
        # 添加到主布局
        main_layout.addWidget(control_panel)
        main_layout.addWidget(total_level_group)
        main_layout.addWidget(content_splitter)
        
        # 设置样式
        self.apply_stylesheet()
        
    def apply_stylesheet(self):
        """应用自定义样式表"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8fafc;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #dcdfe6;
                border-radius: 6px;
                margin-top: 1ex;
                padding: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 8px;
                color: #2c3e50;
                font-weight: bold;
                background-color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 80px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #f5f5f5;
            }
            QPushButton#return_button {
                background-color: #7f8c8d;
            }
            QPushButton#return_button:hover {
                background-color: #6c7a7d;
            }
            QPushButton#export_button {
                background-color: #2ecc71;
            }
            QPushButton#export_button:hover {
                background-color: #27ae60;
            }
            QPushButton[objectName*="clear"] {
                background-color: #e74c3c;
                color: white;
            }
            QPushButton[objectName*="clear"]:hover {
                background-color: #c0392b;
            }
            QPushButton[objectName*="clear"]:disabled {
                background-color: #bdc3c7;
                color: #f5f5f5;
            }
            QTableWidget {
                border: 1px solid #dcdfe6;
                background-color: white;
                gridline-color: #e9eaec;
                selection-background-color: #3498db;
                selection-color: white;
                alternate-background-color: #f5f7fa;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #f5f7fa;
                border: 1px solid #dcdfe6;
                padding: 5px;
                font-weight: bold;
            }
            QComboBox {
                background-color: white;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 5px;
                min-height: 25px;
                min-width: 100px;
            }
            QComboBox:hover {
                border: 1px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QLabel {
                color: #34495e;
            }
            QSplitter::handle {
                background-color: #dcdfe6;
            }
            QTabWidget::pane {
                border: 1px solid #dcdfe6;
                border-radius: 6px;
                background: white;
                top: -1px;
            }
            QTabBar::tab {
                background: #f5f7fa;
                border: 1px solid #dcdfe6;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 80px;
                padding: 8px 15px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
                color: #3498db;
            }
            QTabBar::tab:hover:!selected {
                background: #ecf0f1;
            }
        """)
    
    def load_fluid_data(self):
        """加载流体激振力数据文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择流体激振力数据文件", "", "文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        self.statusBar.showMessage(f"正在加载流体激振力数据: {file_path}")
        self.fluid_file_label.setText(os.path.basename(file_path))
        self.fluid_file_label.setStyleSheet("color: black; font-style: normal;")
        self.fluid_clear_button.setEnabled(True)  # 启用清除按钮

        # 加载数据
        try:
            self.time_data, self.acc_data_dict = load_and_preprocess_data(file_path)
            if self.time_data is not None:
                self.export_button.setEnabled(True)
                self.statusBar.showMessage(f"流体激振力数据加载成功: {os.path.basename(file_path)}")

                # 自动分析数据
                self.analyze_data()
            else:
                self.statusBar.showMessage("流体激振力数据加载失败")
                QMessageBox.warning(self, "错误", "无法加载流体激振力数据文件，请检查文件格式。")
        except Exception as e:
            self.statusBar.showMessage(f"加载出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载流体激振力数据时发生错误:\n{str(e)}")

    def load_motor_data(self):
        """加载电机数据文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择电机数据文件", "", "Excel文件 (*.xlsx *.xls);;文本文件 (*.txt);;所有文件 (*)"
        )

        if not file_path:
            return

        self.statusBar.showMessage(f"正在加载电机数据: {file_path}")
        self.motor_file_label.setText(os.path.basename(file_path))
        self.motor_file_label.setStyleSheet("color: black; font-style: normal;")
        self.motor_clear_button.setEnabled(True)  # 启用清除按钮

        # 加载电机数据
        try:
            from pyma import load_motor_data
            self.motor_time_data, self.motor_acc_data = load_motor_data(file_path)

            if self.motor_time_data is not None and self.motor_acc_data is not None:
                self.statusBar.showMessage(f"电机数据加载成功: {os.path.basename(file_path)}")

                # 检查是否需要启用导出按钮
                self.update_export_button_state()

                # 自动分析数据
                self.analyze_data()
            else:
                self.statusBar.showMessage("电机数据加载失败")
                QMessageBox.warning(self, "错误", "无法加载电机数据文件，请检查文件格式。\n\n支持的格式：\n- Excel文件(.xlsx, .xls)\n- 文本文件(.txt)\n\n文件应包含两列：时间(s) 和 Z方向加速度(m/s²)")
        except Exception as e:
            self.statusBar.showMessage(f"加载出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"加载电机数据时发生错误:\n{str(e)}")

    def clear_fluid_data(self):
        """清除流体激振力数据"""
        reply = QMessageBox.question(
            self, "确认清除",
            "确定要清除流体激振力数据吗？这将清空所有分析结果。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置文件标签
            self.fluid_file_label.setText("未选择文件")
            self.fluid_file_label.setStyleSheet("color: gray; font-style: italic;")
            self.fluid_clear_button.setEnabled(False)

            # 清除数据
            self.time_data = None
            self.acc_data_dict = None
            self.results = {'X': None, 'Y': None, 'Z': None}

            # 更新导出按钮状态
            self.update_export_button_state()

            # 重新分析数据（如果还有电机数据）
            if self.motor_time_data is not None and self.motor_acc_data is not None:
                self.analyze_data()
            else:
                # 如果没有其他数据，重置显示
                self.reset_analysis_display()

            # 更新状态栏
            self.statusBar.showMessage("流体激振力数据已清除")

    def clear_motor_data(self):
        """清除电机数据"""
        reply = QMessageBox.question(
            self, "确认清除",
            "确定要清除电机数据吗？这将影响数据组合分析结果。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 重置文件标签
            self.motor_file_label.setText("未选择文件")
            self.motor_file_label.setStyleSheet("color: gray; font-style: italic;")
            self.motor_clear_button.setEnabled(False)

            # 清除电机数据
            self.motor_time_data = None
            self.motor_acc_data = None

            # 更新导出按钮状态
            self.update_export_button_state()

            # 重新分析数据（如果还有流体数据）
            if self.time_data is not None and self.acc_data_dict is not None:
                self.analyze_data()
            else:
                # 如果没有其他数据，重置显示
                self.reset_analysis_display()

            # 更新状态栏
            self.statusBar.showMessage("电机数据已清除")

    def update_export_button_state(self):
        """更新导出按钮状态"""
        # 如果有任何数据（流体数据或电机数据），启用导出按钮
        has_fluid_data = self.time_data is not None and self.acc_data_dict is not None
        has_motor_data = self.motor_time_data is not None and self.motor_acc_data is not None

        if has_fluid_data or has_motor_data:
            self.export_button.setEnabled(True)
        else:
            self.export_button.setEnabled(False)

    def reset_analysis_display(self):
        """重置分析显示界面"""
        # 清空所有图表
        self.time_canvas.axes.clear()
        self.time_canvas.axes.set_title("时域图")
        self.time_canvas.axes.set_xlabel("时间 (s)")
        self.time_canvas.axes.set_ylabel("加速度 (m/s²)")
        self.time_canvas.draw()

        self.spectrum_canvas.axes.clear()
        self.spectrum_canvas.axes.set_title("频谱图")
        self.spectrum_canvas.axes.set_xlabel("频率 (Hz)")
        self.spectrum_canvas.axes.set_ylabel("加速度 (m/s²)")
        self.spectrum_canvas.draw()

        self.octave_canvas.axes.clear()
        self.octave_canvas.axes.set_title("1/3倍频程图")
        self.octave_canvas.axes.set_xlabel("中心频率 (Hz)")
        self.octave_canvas.axes.set_ylabel("振动加速度级 (dB)")
        self.octave_canvas.draw()

        # 清空结果表格
        self.results_table.setRowCount(0)

        # 重置总振动加速度级显示
        axis_colors = {'Z': '#3498db', 'X': '#e74c3c', 'Y': '#2ecc71'}
        for axis in ['Z', 'X', 'Y']:
            self.total_level_labels[axis].setText("--")
            self.total_level_labels[axis].setStyleSheet(
                f"color: {axis_colors[axis]}; "
                "background-color: #f8f9fa; border-radius: 4px; padding: 10px;"
            )

        # 重置总振级显示
        self.total_vibration_label.setText("--")
        self.total_vibration_label.setStyleSheet("color: #8e44ad; background-color: #f8f9fa; border-radius: 4px; padding: 10px; border: 2px solid #8e44ad;")
        self.total_vibration_level = 0.0

    def on_frequency_range_changed(self):
        """频段范围选择变化时的处理"""
        # 获取当前选择的频段范围
        current_text = self.frequency_range_combo.currentText()
        if "总频段" in current_text:
            self.current_frequency_range = 'extended'
        else:
            self.current_frequency_range = 'standard'

        # 检查是否有任何数据需要重新分析
        has_fluid_data = self.time_data is not None and self.acc_data_dict is not None
        has_motor_data = self.motor_time_data is not None and self.motor_acc_data is not None

        if has_fluid_data or has_motor_data:
            self.statusBar.showMessage(f"正在切换到{current_text}...")
            self.analyze_data()
            self.statusBar.showMessage(f"已切换到{current_text}")
    
    def analyze_data(self):
        """分析振动数据"""
        # 检查数据状态
        has_fluid_data = self.time_data is not None and self.acc_data_dict is not None
        has_motor_data = self.motor_time_data is not None and self.motor_acc_data is not None

        if not has_fluid_data and not has_motor_data:
            QMessageBox.warning(self, "警告", "请先加载数据文件。")
            return

        self.statusBar.showMessage("正在分析数据...")

        # 确定数据组合模式
        self.data_combination_mode = has_fluid_data and has_motor_data

        if self.data_combination_mode:
            # 数据组合模式：同时有流体数据和电机数据
            self.analyze_combined_data()
        elif has_fluid_data:
            # 仅有流体数据
            self.analyze_fluid_only_data()
        elif has_motor_data:
            # 仅有电机数据
            self.analyze_motor_only_data()

        self.statusBar.showMessage("数据分析完成")

        # 更新显示
        self.update_display()

    def analyze_fluid_only_data(self):
        """分析仅有流体数据的情况"""
        from pyma import perform_fft, analyze_third_octave, calculate_total_vibration_level

        # 分析所有方向
        for axis in ['X', 'Y', 'Z']:
            # 执行FFT
            freq, amp = perform_fft(self.time_data, self.acc_data_dict[axis])

            # 进行1/3倍频程分析，使用当前选择的频段范围
            results = analyze_third_octave(freq, amp, axis, self.current_frequency_range)
            self.results[axis] = results

        # 计算总振级
        x_total = self.results['X']['total_L_A'] if self.results['X'] else 0.0
        y_total = self.results['Y']['total_L_A'] if self.results['Y'] else 0.0
        z_total = self.results['Z']['total_L_A'] if self.results['Z'] else 0.0

        # 存储总振级计算结果
        self.total_vibration_level = calculate_total_vibration_level(x_total, y_total, z_total)

    def analyze_motor_only_data(self):
        """分析仅有电机数据的情况（只有Z方向）"""
        from pyma import perform_fft, analyze_third_octave, calculate_total_vibration_level

        # 只分析Z方向
        freq, amp = perform_fft(self.motor_time_data, self.motor_acc_data)
        results = analyze_third_octave(freq, amp, 'Z', self.current_frequency_range)

        # 设置结果
        self.results['Z'] = results
        self.results['X'] = None  # 电机数据没有X方向
        self.results['Y'] = None  # 电机数据没有Y方向

        # 计算总振级（只有Z方向数据，X和Y为0）
        z_total = self.results['Z']['total_L_A'] if self.results['Z'] else 0.0

        # 存储总振级计算结果
        self.total_vibration_level = calculate_total_vibration_level(0.0, 0.0, z_total)

    def analyze_combined_data(self):
        """分析组合数据的情况"""
        from pyma import perform_fft, analyze_third_octave, combine_vibration_levels, calculate_total_vibration_level

        # 先分析流体数据
        fluid_results = {}
        for axis in ['X', 'Y', 'Z']:
            freq, amp = perform_fft(self.time_data, self.acc_data_dict[axis])
            results = analyze_third_octave(freq, amp, axis, self.current_frequency_range)
            fluid_results[axis] = results

        # 分析电机数据（只有Z方向）
        motor_freq, motor_amp = perform_fft(self.motor_time_data, self.motor_acc_data)
        motor_results = analyze_third_octave(motor_freq, motor_amp, 'Z', self.current_frequency_range)

        # 组合Z方向数据
        fluid_z_total = fluid_results['Z']['total_L_A']
        motor_z_total = motor_results['total_L_A']
        combined_z_total = combine_vibration_levels(fluid_z_total, motor_z_total)

        # 设置组合结果
        self.results['Z'] = fluid_results['Z'].copy()
        self.results['Z']['total_L_A'] = combined_z_total
        self.results['Z']['is_combined'] = True
        self.results['Z']['fluid_total'] = fluid_z_total
        self.results['Z']['motor_total'] = motor_z_total

        # X和Y方向：流体数据 + 电机数据（电机X、Y方向默认为0）
        # 由于电机X、Y方向数据为0，所以叠加后结果就是流体数据本身
        for axis in ['X', 'Y']:
            self.results[axis] = fluid_results[axis].copy()
            self.results[axis]['is_combined'] = True
            self.results[axis]['fluid_total'] = fluid_results[axis]['total_L_A']
            self.results[axis]['motor_total'] = 0.0  # 电机X、Y方向数据为0
            # 叠加结果：流体数据 + 0 = 流体数据
            self.results[axis]['total_L_A'] = fluid_results[axis]['total_L_A']

        # 计算总振级
        x_total = self.results['X']['total_L_A'] if self.results['X'] else 0.0
        y_total = self.results['Y']['total_L_A'] if self.results['Y'] else 0.0
        z_total = self.results['Z']['total_L_A'] if self.results['Z'] else 0.0

        # 存储总振级计算结果
        self.total_vibration_level = calculate_total_vibration_level(x_total, y_total, z_total)

    def update_display(self):
        """更新显示内容"""
        # 检查是否有任何数据
        has_fluid_data = self.acc_data_dict is not None
        has_motor_data = self.motor_time_data is not None and self.motor_acc_data is not None

        if not has_fluid_data and not has_motor_data:
            return

        # 获取当前选择的轴
        axis_map = {"Z 方向": "Z", "X 方向": "X", "Y 方向": "Y"}  # 更新映射顺序
        current_axis = axis_map[self.axis_combo.currentText()]

        # 检索当前轴的结果
        result = self.results[current_axis]
        if not result:
            # 如果当前轴没有结果，显示提示信息
            self.show_no_data_message(current_axis)
            return
            
        # 更新表格
        self.results_table.update_data(
            result['octave_centers'],
            result['L_A'],
            result['total_L_A']
        )

        # 更新时域图
        self.time_canvas.axes.clear()

        # 根据数据类型选择时域数据源
        time_data_source = None
        signal_data_source = None

        if self.data_combination_mode:
            # 组合模式：优先显示流体数据，如果当前轴是Z且有电机数据，显示组合信息
            if current_axis == 'Z' and self.motor_time_data is not None:
                # 显示流体数据，但在标题中标注组合模式
                if self.time_data is not None and current_axis in self.acc_data_dict:
                    time_data_source = self.time_data
                    signal_data_source = self.acc_data_dict[current_axis]
            elif self.time_data is not None and current_axis in self.acc_data_dict:
                time_data_source = self.time_data
                signal_data_source = self.acc_data_dict[current_axis]
        elif self.time_data is not None and self.acc_data_dict is not None and current_axis in self.acc_data_dict:
            # 仅流体数据
            time_data_source = self.time_data
            signal_data_source = self.acc_data_dict[current_axis]
        elif current_axis == 'Z' and self.motor_time_data is not None and self.motor_acc_data is not None:
            # 仅电机数据（只有Z方向）
            time_data_source = self.motor_time_data
            signal_data_source = self.motor_acc_data

        if time_data_source is not None and signal_data_source is not None:
            # 限制显示时间范围以便更清晰地查看信号
            max_time_idx = min(len(time_data_source)*0.5, int(len(time_data_source)*1.0))  # 显示前10%的数据
            if max_time_idx > 0:
                time_display = time_data_source[:max_time_idx]
                signal_display = signal_data_source[:max_time_idx]

                self.time_canvas.axes.plot(time_display, signal_display, color='#2ecc71', linewidth=1.0)
                self.time_canvas.axes.set_xlim(time_display[0], time_display[-1])

        self.time_canvas.axes.set_xlabel('时间 (s)')
        self.time_canvas.axes.set_ylabel('加速度 (m/s²)')

        # 根据数据模式设置标题
        if self.data_combination_mode and current_axis == 'Z':
            title = f'{current_axis} 方向时域振动信号 (组合模式)'
        elif self.motor_time_data is not None and self.acc_data_dict is None and current_axis == 'Z':
            title = f'{current_axis} 方向时域振动信号 (电机数据)'
        else:
            title = f'{current_axis} 方向时域振动信号'

        self.time_canvas.axes.set_title(title)
        self.time_canvas.axes.grid(True, linestyle='--', alpha=0.7)

        # 添加图例
        self.time_canvas.axes.legend(['时域信号'], loc='upper right')

        self.time_canvas.draw()
        
        # 更新频谱图
        self.spectrum_canvas.axes.clear()
        if 'freq_spectrum' in result and 'amp_spectrum' in result:
            freq = result['freq_spectrum']
            amp = result['amp_spectrum']

            # 根据频率范围选择确定显示范围
            if self.current_frequency_range == 'extended':
                max_freq = 10000  # 扩展频段
            else:
                max_freq = 315    # 标准频段

            # 限制显示范围到选定的频率范围
            max_freq_idx = min(len(freq), np.searchsorted(freq, max_freq, side='right'))
            if max_freq_idx > 0:
                freq_display = freq[:max_freq_idx]
                amp_display = amp[:max_freq_idx]

                self.spectrum_canvas.axes.plot(freq_display, amp_display, color='#3498db', linewidth=1.5)

                # 设置x轴为对数坐标以匹配1/3倍频程图
                self.spectrum_canvas.axes.set_xscale('log')
                self.spectrum_canvas.axes.set_xlim(10, max_freq)  # 从10Hz开始，与1/3倍频程一致

                # 设置x轴刻度与1/3倍频程图一致
                octave_centers = result['octave_centers']

                # 使用与1/3倍频程图相同的刻度设置逻辑
                if len(octave_centers) > 20:
                    # 对于大量频率点，每隔几个显示一个标签
                    step = max(1, len(octave_centers) // 15)  # 大约显示15个标签
                    tick_indices = list(range(0, len(octave_centers), step))
                    if tick_indices[-1] != len(octave_centers) - 1:
                        tick_indices.append(len(octave_centers) - 1)  # 确保显示最后一个

                    tick_positions = [octave_centers[i] for i in tick_indices]
                    tick_labels = [f'{octave_centers[i]:.0f}' if octave_centers[i] >= 100
                                  else f'{octave_centers[i]:.1f}' for i in tick_indices]
                else:
                    # 对于较少的频率点，显示所有标签
                    tick_positions = octave_centers
                    tick_labels = [f'{freq:.0f}' if freq >= 100 else f'{freq:.1f}'
                                  for freq in octave_centers]

                self.spectrum_canvas.axes.set_xticks(tick_positions)
                self.spectrum_canvas.axes.set_xticklabels(tick_labels, rotation=45, ha='right')
        
        self.spectrum_canvas.axes.set_xlabel('频率 (Hz)')
        self.spectrum_canvas.axes.set_ylabel('加速度 (m/s²)')
        self.spectrum_canvas.axes.set_title(f'{current_axis} 方向振动频谱')
        self.spectrum_canvas.axes.grid(True, linestyle='--', alpha=0.7)
        
        # 添加图例
        self.spectrum_canvas.axes.legend(['频谱'], loc='upper right')
        
        self.spectrum_canvas.draw()
        
        # 更新1/3倍频程图
        self.octave_canvas.axes.clear()

        # 使用条形图显示1/3倍频程结果
        bar_width = 0.8
        x = np.arange(len(result['octave_centers']))
        bars = self.octave_canvas.axes.bar(
            x, result['L_A'], width=bar_width,
            color='#3498db', alpha=0.7, label='振动加速度级'
        )

        # 数据标签已移除以简化图表显示
        # 注释：原来的数据标签代码已被移除，以避免图表过于拥挤
        # 用户可以通过悬停或查看结果表格获取具体数值

        # 设置坐标轴
        self.octave_canvas.axes.set_xlabel('中心频率 (Hz)')
        self.octave_canvas.axes.set_ylabel('振动加速度级 (dB)')

        # 根据结果中的频段信息和数据模式设置标题
        range_info = result.get('range_name', '总频段 (10-10k Hz)')
        base_title = f'{current_axis} 方向1/3倍频程分析 - {range_info}'

        # 添加数据模式信息
        if self.data_combination_mode and current_axis == 'Z':
            title = f"{base_title} (组合模式)"
        elif self.motor_time_data is not None and self.acc_data_dict is None and current_axis == 'Z':
            title = f"{base_title} (电机数据)"
        else:
            title = base_title

        self.octave_canvas.axes.set_title(title)

        # 优化x轴标签显示 - 对于扩展的频率范围，使用更智能的标签显示
        octave_centers = result['octave_centers']

        # 选择性显示标签，避免过于拥挤
        if len(octave_centers) > 20:
            # 对于大量频率点，每隔几个显示一个标签
            step = max(1, len(octave_centers) // 15)  # 大约显示15个标签
            tick_indices = list(range(0, len(octave_centers), step))
            if tick_indices[-1] != len(octave_centers) - 1:
                tick_indices.append(len(octave_centers) - 1)  # 确保显示最后一个

            self.octave_canvas.axes.set_xticks([x[i] for i in tick_indices])
            self.octave_canvas.axes.set_xticklabels([f'{octave_centers[i]:.0f}' if octave_centers[i] >= 100
                                                   else f'{octave_centers[i]:.1f}' for i in tick_indices],
                                                   rotation=45, ha='right')
        else:
            # 对于较少的频率点，显示所有标签
            self.octave_canvas.axes.set_xticks(x)
            self.octave_canvas.axes.set_xticklabels([f'{freq:.0f}' if freq >= 100 else f'{freq:.1f}'
                                                   for freq in octave_centers], rotation=45, ha='right')

        self.octave_canvas.axes.grid(True, linestyle='--', alpha=0.5, axis='y')
        
        # 添加总值文本
        self.octave_canvas.axes.text(
            0.95, 0.95, f'总值: {result["total_L_A"]:.1f} dB',
            transform=self.octave_canvas.axes.transAxes,
            fontsize=12, fontweight='bold',
            ha='right', va='top',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8, edgecolor='#dcdfe6')
        )
        
        self.octave_canvas.draw()
        
        # 更新所有方向的总振动加速度级显示
        for axis, result_data in self.results.items():
            if result_data:
                total_level = result_data['total_L_A']
                label = self.total_level_labels[axis]
                label.setText(f"{total_level:.1f} dB")

                # 根据振动级别设置颜色
                if total_level < 80:
                    # 低振动 - 绿色
                    bgcolor = "#eafaf1"
                elif total_level < 100:
                    # 中等振动 - 黄色
                    bgcolor = "#fef9e7"
                else:
                    # 高振动 - 红色
                    bgcolor = "#fdedec"

                # 对Z、X、Y方向保持各自的颜色，只改变背景色来反映振动严重程度
                axis_colors = {'Z': '#3498db', 'X': '#e74c3c', 'Y': '#2ecc71'}  # 保持颜色映射一致
                label.setStyleSheet(f"color: {axis_colors[axis]}; background-color: {bgcolor}; border-radius: 4px; padding: 10px;")
            else:
                self.total_level_labels[axis].setText("--")

        # 更新总振级显示
        if hasattr(self, 'total_vibration_level') and self.total_vibration_level > 0:
            self.total_vibration_label.setText(f"{self.total_vibration_level:.1f} dB")

            # 根据总振级设置背景色
            if self.total_vibration_level < 80:
                bgcolor = "#eafaf1"  # 低振动 - 浅绿色
            elif self.total_vibration_level < 100:
                bgcolor = "#fef9e7"  # 中等振动 - 浅黄色
            else:
                bgcolor = "#fdedec"  # 高振动 - 浅红色

            self.total_vibration_label.setStyleSheet(f"color: #8e44ad; background-color: {bgcolor}; border-radius: 4px; padding: 10px; border: 2px solid #8e44ad;")
        else:
            self.total_vibration_label.setText("--")
            self.total_vibration_label.setStyleSheet("color: #8e44ad; background-color: #f8f9fa; border-radius: 4px; padding: 10px; border: 2px solid #8e44ad;")

    def show_no_data_message(self, axis):
        """显示无数据消息"""
        # 清空所有图表并显示提示信息
        self.time_canvas.axes.clear()
        self.time_canvas.axes.text(0.5, 0.5, f'{axis} 方向无数据',
                                  transform=self.time_canvas.axes.transAxes,
                                  ha='center', va='center', fontsize=14, color='gray')
        self.time_canvas.axes.set_title(f'{axis} 方向时域振动信号')
        self.time_canvas.draw()

        self.spectrum_canvas.axes.clear()
        self.spectrum_canvas.axes.text(0.5, 0.5, f'{axis} 方向无数据',
                                      transform=self.spectrum_canvas.axes.transAxes,
                                      ha='center', va='center', fontsize=14, color='gray')
        self.spectrum_canvas.axes.set_title(f'{axis} 方向振动频谱')
        self.spectrum_canvas.draw()

        self.octave_canvas.axes.clear()
        self.octave_canvas.axes.text(0.5, 0.5, f'{axis} 方向无数据',
                                    transform=self.octave_canvas.axes.transAxes,
                                    ha='center', va='center', fontsize=14, color='gray')
        self.octave_canvas.axes.set_title(f'{axis} 方向1/3倍频程分析')
        self.octave_canvas.draw()

        # 清空结果表格
        self.results_table.setRowCount(0)

    def export_results(self):
        """导出分析结果到Excel文件"""
        # 检查是否有任何结果可以导出
        has_results = any(self.results.values())
        has_motor_only = (self.motor_time_data is not None and
                         self.acc_data_dict is None and
                         self.results['Z'] is not None)

        if not has_results and not has_motor_only:
            QMessageBox.warning(self, "警告", "没有可导出的分析结果。")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出结果", "振动分析结果.xlsx", "Excel文件 (*.xlsx)"
        )
        
        if not file_path:
            return
            
        try:
            self.statusBar.showMessage("正在导出结果...")
            
            # 使用ExcelWriter导出到Excel
            with pd.ExcelWriter(file_path) as writer:
                # 获取频段信息用于工作表命名
                frequency_range_text = self.frequency_range_combo.currentText()
                range_suffix = "总" if "总" in frequency_range_text else "低"

                # 标记是否是第一个工作表，用于添加总振级信息
                is_first_sheet = True

                for axis, results in self.results.items():
                    if results is None:
                        continue

                    # 准备导出数据
                    results_df = pd.DataFrame({
                        '中心频率 (Hz)': results['octave_centers'],
                        '振动加速度级 L_A (dB)': results['L_A']
                    })

                    # 添加频段信息行
                    info_row = pd.DataFrame([{
                        '中心频率 (Hz)': f'频段范围: {results.get("range_name", "未知")}',
                        '振动加速度级 L_A (dB)': f'频段数量: {results.get("num_bands", len(results["octave_centers"]))}'
                    }])

                    # 添加数据模式信息行
                    if results.get('is_combined', False):
                        mode_info_row = pd.DataFrame([{
                            '中心频率 (Hz)': '数据模式: 组合模式 (流体+电机)',
                            '振动加速度级 L_A (dB)': f'流体: {results["fluid_total"]:.2f} dB, 电机: {results["motor_total"]:.2f} dB'
                        }])
                        info_row = pd.concat([info_row, mode_info_row], ignore_index=True)
                    elif self.motor_time_data is not None and self.acc_data_dict is None:
                        mode_info_row = pd.DataFrame([{
                            '中心频率 (Hz)': '数据模式: 仅电机数据',
                            '振动加速度级 L_A (dB)': '仅Z方向有效'
                        }])
                        info_row = pd.concat([info_row, mode_info_row], ignore_index=True)

                    # 添加总计行
                    total_row = pd.DataFrame([{
                        '中心频率 (Hz)': '总计',
                        '振动加速度级 L_A (dB)': results['total_L_A']
                    }])

                    # 如果是第一个工作表且有总振级数据，添加总振级信息
                    if is_first_sheet and hasattr(self, 'total_vibration_level') and self.total_vibration_level > 0:
                        total_vibration_row = pd.DataFrame([{
                            '中心频率 (Hz)': '总振级 (三方向均方根)',
                            '振动加速度级 L_A (dB)': f'{self.total_vibration_level:.2f} dB'
                        }])
                        # 合并数据，包括总振级
                        results_df = pd.concat([info_row, results_df, total_row, total_vibration_row], ignore_index=True)
                        is_first_sheet = False
                    else:
                        # 合并数据
                        results_df = pd.concat([info_row, results_df, total_row], ignore_index=True)

                    # 写入Excel，工作表名称包含频段信息和数据模式
                    if results.get('is_combined', False):
                        sheet_name = f'{axis}方向_{range_suffix}频段_组合'
                    elif self.motor_time_data is not None and self.acc_data_dict is None:
                        sheet_name = f'{axis}方向_{range_suffix}频段_电机'
                    else:
                        sheet_name = f'{axis}方向_{range_suffix}频段'

                    results_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            self.statusBar.showMessage(f"结果成功导出到: {file_path}")
            QMessageBox.information(self, "导出成功", f"分析结果已成功导出到:\n{file_path}")
            
        except Exception as e:
            self.statusBar.showMessage(f"导出失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"导出结果时发生错误:\n{str(e)}")
    
    def return_to_result(self):
        """返回到结果界面"""
        if self.window_manager:
            self.hide()  # 隐藏当前窗口
            self.window_manager.switch_to(WindowType.RESULT)
    
    def closeEvent(self, event):
        """窗口关闭事件处理
        
        当用户关闭窗口时，隐藏窗口并显示主窗口
        """
        try:
            # 隐藏当前窗口
            self.hide()
            
            # 显示主窗口
            if self.window_manager:
                try:
                    main_window = self.window_manager.get_window(WindowType.MAIN)
                    if main_window:
                        main_window.show()
                except Exception as e:
                    print(f"显示主窗口时出错: {e}")
            
            # 忽略关闭事件，防止窗口被销毁
            event.ignore()
        except Exception as e:
            print(f"关闭振动分析窗口时出错: {e}")
            # 出现错误时接受关闭事件
            event.accept()


def show_vibration_analysis(window_manager: WindowManager) -> None:
    """显示振动分析界面
    
    Args:
        window_manager: 窗口管理器实例
    """
    # 如果未注册振动分析窗口，则创建并注册
    if not hasattr(window_manager, 'vibration_window'):
        vibration_window = VibrationAnalysisWindow(parent=None, window_manager=window_manager)
        window_manager.vibration_window = vibration_window
    
    # 获取当前窗口（结果窗口）
    result_window = window_manager.get_window(WindowType.RESULT)
    
    # 尝试隐藏当前窗口
    current_window = window_manager.get_current_window()
    if current_window:
        try:
            current_window.hide()
        except Exception as e:
            print(f"隐藏当前窗口时出错: {e}")
    
    # 显示振动分析窗口
    try:
        window_manager.vibration_window.show()
    except Exception as e:
        print(f"显示振动分析窗口时出错: {e}")
        # 如果显示失败，尝试重新创建窗口
        vibration_window = VibrationAnalysisWindow(parent=None, window_manager=window_manager)
        window_manager.vibration_window = vibration_window
        vibration_window.show() 