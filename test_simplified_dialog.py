"""
测试简化的网格参数对话框

此脚本验证简化后的网格参数对话框是否正常工作：
1. 界面加载正常
2. 基本参数输入、验证、保存功能正常
3. 界面外观与网格无关性界面保持一致
4. 生成的网格参数能够被 modal.py 脚本正确读取

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simplified_dialog():
    """测试简化的网格参数对话框"""
    print("🧪 测试简化的网格参数对话框...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_parameter_dialog_simplified import MeshParameterDialogSimplified
        from core.mesh_manager import MeshParameter, ElementType
        
        app = QApplication.instance() or QApplication([])
        
        print("✅ 导入模块成功")
        
        # 测试1: 创建新参数对话框
        print("\n📋 测试1: 创建新参数对话框")
        dialog = MeshParameterDialogSimplified()
        print("✅ 新参数对话框创建成功")
        
        # 测试2: 检查界面组件
        print("\n📋 测试2: 检查界面组件")
        required_components = [
            'lineEdit_mesh_name',
            'doubleSpinBox_mesh_size', 
            'comboBox_element_type',
            'btn_ok',
            'btn_cancel',
            'btn_reset'
        ]
        
        missing_components = []
        for component in required_components:
            if not hasattr(dialog.ui, component):
                missing_components.append(component)
        
        if not missing_components:
            print("✅ 所有必需组件都存在")
        else:
            print(f"❌ 缺少组件: {missing_components}")
            return False
        
        # 测试3: 检查默认值
        print("\n📋 测试3: 检查默认值")
        default_name = dialog.ui.lineEdit_mesh_name.text()
        default_size = dialog.ui.doubleSpinBox_mesh_size.value()
        default_type = dialog.ui.comboBox_element_type.currentText()
        
        print(f"  - 默认名称: {default_name}")
        print(f"  - 默认尺寸: {default_size} mm")
        print(f"  - 默认类型: {default_type}")
        
        if default_name and default_size > 0:
            print("✅ 默认值设置正确")
        else:
            print("❌ 默认值设置有问题")
            return False
        
        # 测试4: 测试参数创建
        print("\n📋 测试4: 测试参数创建")
        dialog.ui.lineEdit_mesh_name.setText("测试网格")
        dialog.ui.doubleSpinBox_mesh_size.setValue(5.0)
        dialog.ui.comboBox_element_type.setCurrentIndex(0)  # 四面体
        
        mesh_param = dialog._create_parameter_from_ui()
        
        if mesh_param and mesh_param.name == "测试网格" and mesh_param.size == 5.0:
            print("✅ 参数创建功能正常")
            print(f"  - 网格名称: {mesh_param.name}")
            print(f"  - 网格尺寸: {mesh_param.size} mm")
            print(f"  - 单元类型: {mesh_param.element_type}")
        else:
            print("❌ 参数创建功能异常")
            return False
        
        # 测试5: 测试参数验证
        print("\n📋 测试5: 测试参数验证")
        errors = mesh_param.validate()
        
        if not errors:
            print("✅ 参数验证通过")
        else:
            print(f"⚠️ 参数验证警告: {errors}")
        
        # 测试6: 测试编辑模式
        print("\n📋 测试6: 测试编辑模式")
        edit_dialog = MeshParameterDialogSimplified(mesh_parameter=mesh_param)
        
        loaded_name = edit_dialog.ui.lineEdit_mesh_name.text()
        loaded_size = edit_dialog.ui.doubleSpinBox_mesh_size.value()
        
        if loaded_name == "测试网格" and loaded_size == 5.0:
            print("✅ 编辑模式加载正确")
        else:
            print(f"❌ 编辑模式加载失败: {loaded_name}, {loaded_size}")
            return False
        
        # 测试7: 检查界面风格一致性
        print("\n📋 测试7: 检查界面风格一致性")
        
        # 检查字体
        font = dialog.font()
        if font.family() == "Microsoft YaHei UI" and font.pointSize() == 10:
            print("✅ 字体设置与主界面一致")
        else:
            print(f"⚠️ 字体设置可能不一致: {font.family()}, {font.pointSize()}pt")
        
        # 检查窗口大小
        size = dialog.size()
        if size.width() == 450 and size.height() == 300:
            print("✅ 窗口大小设置正确")
        else:
            print(f"⚠️ 窗口大小: {size.width()}x{size.height()}")
        
        # 检查样式表
        group_box = dialog.ui.groupBox_basic_params
        if group_box.styleSheet():
            print("✅ 样式表已应用")
        else:
            print("⚠️ 样式表可能未正确应用")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_modal_compatibility():
    """测试与 modal.py 的兼容性"""
    print("\n🔗 测试与 modal.py 的兼容性...")
    
    try:
        from views.mesh_parameter_dialog_simplified import MeshParameterDialogSimplified
        from PySide6.QtWidgets import QApplication
        
        app = QApplication.instance() or QApplication([])
        
        # 创建测试参数
        dialog = MeshParameterDialogSimplified()
        dialog.ui.lineEdit_mesh_name.setText("Modal测试网格")
        dialog.ui.doubleSpinBox_mesh_size.setValue(2.5)
        dialog.ui.comboBox_element_type.setCurrentIndex(0)
        
        mesh_param = dialog._create_parameter_from_ui()
        
        # 检查 modal.py 所需的关键参数
        required_attrs = ['name', 'size', 'element_type']
        missing_attrs = []
        
        for attr in required_attrs:
            if not hasattr(mesh_param, attr):
                missing_attrs.append(attr)
        
        if not missing_attrs:
            print("✅ 包含 modal.py 所需的所有关键参数")
            print(f"  - name: {mesh_param.name}")
            print(f"  - size: {mesh_param.size} mm (转换为米: {mesh_param.size/1000.0} m)")
            print(f"  - element_type: {mesh_param.element_type}")
        else:
            print(f"❌ 缺少关键参数: {missing_attrs}")
            return False
        
        # 检查参数格式
        if isinstance(mesh_param.size, (int, float)) and mesh_param.size > 0:
            print("✅ 网格尺寸格式正确")
        else:
            print(f"❌ 网格尺寸格式错误: {type(mesh_param.size)}, {mesh_param.size}")
            return False
        
        if isinstance(mesh_param.name, str) and mesh_param.name.strip():
            print("✅ 网格名称格式正确")
        else:
            print(f"❌ 网格名称格式错误: {type(mesh_param.name)}, '{mesh_param.name}'")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 兼容性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 70)
    print("🧪 简化网格参数对话框测试")
    print("=" * 70)
    print("\n测试目标：")
    print("✨ 验证简化对话框功能正常")
    print("✨ 验证界面风格一致性")
    print("✨ 验证与 modal.py 的兼容性")
    print("✨ 验证基本参数的输入和验证")
    print("=" * 70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.WARNING,  # 只显示警告和错误
        format='%(levelname)s: %(message)s'
    )
    
    success_count = 0
    total_tests = 2
    
    # 测试简化对话框
    if test_simplified_dialog():
        success_count += 1
    
    # 测试 modal.py 兼容性
    if test_modal_compatibility():
        success_count += 1
    
    print("\n" + "=" * 70)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！")
        print("✅ 简化对话框功能正常")
        print("✅ 界面风格保持一致")
        print("✅ 与 modal.py 完全兼容")
        print("\n🎯 简化效果：")
        print("• 移除了复杂的质量设置参数")
        print("• 移除了高级配置选项")
        print("• 保留了模态分析所需的核心参数")
        print("• 界面更简洁，操作更直观")
        print("• 完全兼容 modal.py 脚本需求")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
        print("请检查简化对话框的实现")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
