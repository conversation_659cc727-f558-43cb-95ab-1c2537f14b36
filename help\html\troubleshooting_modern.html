<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障排除指南 - 专业问题诊断解决系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }
        .code-inline {
            background: #f1f5f9;
            color: #475569;
            padding: 0.125rem 0.375rem;
            border-radius: 0.25rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-amber-600 to-orange-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-amber-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🔧 故障排除指南
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-amber-100">
                    专业问题诊断解决系统 | 快速定位与高效修复
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-amber-500 bg-opacity-20 text-amber-100 text-sm font-semibold px-4 py-2 rounded-full border border-amber-400">
                        🚀 启动问题 | ⚙️ 配置错误 | 🌐 网络故障 | 📊 性能优化
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎯 故障排除概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                本指南提供了振动传递计算软件常见问题的专业解决方案，涵盖安装、配置、运行和功能使用等各个方面。通过系统化的诊断流程，帮助用户快速定位并解决问题。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-red-800">启动问题</h3>
                    </div>
                    <p class="text-sm text-red-600">依赖库、环境配置</p>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">配置问题</h3>
                    </div>
                    <p class="text-sm text-blue-600">ANSYS路径、API设置</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">网络故障</h3>
                    </div>
                    <p class="text-sm text-green-600">API连接、端口占用</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">性能优化</h3>
                    </div>
                    <p class="text-sm text-purple-600">响应缓慢、资源占用</p>
                </div>
            </div>
        </section>

        <!-- Startup Issues -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🚀 启动和安装问题</h2>
            
            <div class="space-y-6">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📦</span>程序无法启动，提示缺少依赖库
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">这是最常见的启动问题，通常由于Python环境或依赖库配置不当引起。</p>
                        <div class="bg-red-50 p-4 rounded-lg mb-4">
                            <h4 class="font-semibold text-red-800 mb-2">解决步骤：</h4>
                            <ol class="text-sm text-red-600 space-y-2">
                                <li><strong>1. 检查Python版本</strong> - 确保已安装Python 3.12或更高版本</li>
                                <li><strong>2. 安装依赖库</strong></li>
                                <li><strong>3. 重新创建虚拟环境</strong>（如果问题持续）</li>
                            </ol>
                        </div>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-gray-800 mb-2">安装依赖库</h4>
                                <div class="code-block">
pip install -r requirements.txt
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-gray-800 mb-2">重建虚拟环境</h4>
                                <div class="code-block">
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
                                </div>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⌨️</span>启动时出现QKeySequence导入错误
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">这是PySide6版本兼容性问题，需要正确的导入路径。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">解决方案：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 确保从PySide6.QtGui而不是PySide6.QtCore导入QKeySequence</li>
                                <li>• 检查PySide6版本是否为6.5.0或更高版本</li>
                                <li>• 重新安装PySide6：<span class="code-inline">pip install --upgrade PySide6</span></li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🖥️</span>程序启动后界面显示异常
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">界面显示问题通常与系统显示设置或权限相关。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">排查步骤：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 检查显示器分辨率和DPI设置</li>
                                <li>• 尝试以管理员权限运行程序</li>
                                <li>• 清除配置文件：删除config/settings.json</li>
                                <li>• 重启计算机后再次尝试</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📝 日志和调试</h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📁 日志文件位置</h3>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded-lg shadow-sm">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-blue-800">应用程序日志</span>
                                <span class="code-inline">logs/app.log</span>
                            </div>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-green-800">API服务器日志</span>
                                <span class="code-inline">logs/api.log</span>
                            </div>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-red-800">错误日志</span>
                                <span class="code-inline">logs/error.log</span>
                            </div>
                        </div>
                        <div class="bg-white p-3 rounded-lg shadow-sm">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-purple-800">调试日志</span>
                                <span class="code-inline">logs/debug.log</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-purple-50 to-pink-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🔧 启用调试模式</h3>
                    <p class="text-gray-600 mb-4">在配置文件中设置调试级别：</p>
                    <div class="code-block">
{
    "logging": {
        "level": "DEBUG",
        "console_output": true,
        "file_output": true
    }
}
                    </div>
                </div>
            </div>
        </section>

        <!-- Technical Support -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📞 获取技术支持</h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-gradient-to-r from-red-50 to-orange-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-red-800 mb-4">⚠️ 联系技术支持前请准备</h3>
                    <ul class="space-y-2 text-sm text-red-700">
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                            <span>详细的问题描述和重现步骤</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                            <span>错误消息的完整截图</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                            <span>相关的日志文件</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                            <span>系统配置信息（操作系统、Python版本等）</span>
                        </li>
                        <li class="flex items-start">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-3 mt-2"></span>
                            <span>软件版本信息</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-blue-800 mb-4">📧 联系方式</h3>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <div>
                                <span class="font-semibold text-blue-800">邮箱</span>
                                <p class="text-sm text-blue-600"><EMAIL></p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z" />
                                </svg>
                            </div>
                            <div>
                                <span class="font-semibold text-green-800">技术论坛</span>
                                <p class="text-sm text-green-600">forum.example.com</p>
                            </div>
                        </div>

                        <div class="flex items-center p-3 bg-white rounded-lg shadow-sm">
                            <div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                            </div>
                            <div>
                                <span class="font-semibold text-purple-800">在线文档</span>
                                <p class="text-sm text-purple-600">docs.example.com</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">专业问题诊断解决系统 - 快速定位与高效修复</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
