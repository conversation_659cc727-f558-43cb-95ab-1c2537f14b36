# 🎨 多网格管理系统UI设计规范

## 📋 设计概述

本文档详细描述了多网格管理系统的UI界面设计，包括主界面布局、组件规范、样式定义和交互逻辑。

## 🏗️ 主界面设计 (`ui/mesh_new.ui`)

### 整体布局结构

```
┌─────────────────────────────────────────────────────────────┐
│                    网格无关性验证系统                        │
├─────────────────────────────────────────────────────────────┤
│  [网格管理] [网格生成] [模态分析] [结果对比]                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    标签页内容区域                           │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ [生成网格] [查看结果]     [上一步] [下一步] [主菜单]        │
└─────────────────────────────────────────────────────────────┘
```

### 标签页详细设计

#### 📑 标签页1：网格管理

**左侧面板 (600-650px宽)**：
- **网格参数管理组**：
  - 工具栏：[添加网格] [导入配置] [导出配置]
  - 网格参数表格：6列设计
    - 网格名称 | 尺寸(mm) | 状态 | 节点数 | 单元数 | 操作

**右侧面板**：
- **网格预览与详情组**：
  - 网格选择下拉框
  - 网格预览区域 (400x300px)
  - 网格统计信息网格布局

#### 📑 标签页2：网格生成

**左侧面板 (400-450px宽)**：
- **批量操作控制组**：
  - 全选网格复选框
  - 选中网格列表 (150px高)
  - 操作按钮：[批量生成] [停止生成]
  - 生成进度组：进度条 + 状态文本

**右侧面板**：
- **生成日志与质量对比组**：
  - 生成日志文本框 (300px高，控制台样式)
  - 网格质量对比表格 (200px高)

#### 📑 标签页3：模态分析

**上半部分 (150px高)**：
- **模态计算控制组**：
  - 参数设置：模态阶数、频率范围
  - 操作按钮：[单个模态计算] [批量模态计算]

**下半部分**：
- **频率收敛性分析组**：
  - matplotlib图表区域 (400px高)

#### 📑 标签页4：结果对比

**左侧面板 (300-350px宽)**：
- **对比控制组**：
  - 网格选择列表 (200px高，多选)
  - 显示选项：频率值、收敛线、网格线
  - [导出结果] 按钮

**右侧面板**：
- **网格无关性分析图表组**：
  - matplotlib图表区域 (600px高)

## 🎨 样式设计规范

### 颜色方案

```css
/* 主题颜色 */
主色调: #3498db (蓝色)
强调色: #2ecc71 (绿色) 
警告色: #e74c3c (红色)
辅助色: #f39c12 (橙色)
中性色: #34495e (深灰蓝)
背景色: #f8fafc (浅灰蓝)

/* 状态颜色 */
成功: #2ecc71
进行中: #f39c12  
错误: #e74c3c
禁用: #7f8c8d
```

### 字体规范

- **主字体**: Microsoft YaHei UI
- **代码字体**: Consolas
- **标题字体**: 24px, 粗体
- **正文字体**: 10-12px
- **按钮字体**: 12-14px, 粗体

### 组件样式

#### 按钮样式
```css
/* 主要按钮 */
background-color: #3498db;
color: white;
border-radius: 4px;
padding: 8px 16px;
font-weight: 500;

/* 成功按钮 */
background-color: #2ecc71;

/* 危险按钮 */
background-color: #e74c3c;

/* 次要按钮 */
background-color: #7f8c8d;
```

#### 表格样式
```css
/* 表格 */
border: 1px solid #dcdfe6;
background-color: white;
gridline-color: #e9eaec;
selection-background-color: #3498db;
alternate-background-color: #f5f7fa;

/* 表头 */
background-color: #f5f7fa;
border: 1px solid #dcdfe6;
font-weight: bold;
color: #34495e;
```

#### 分组框样式
```css
QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}
```

## 🔧 网格参数编辑对话框 (`ui/mesh_parameter_dialog.ui`)

### 对话框布局

```
┌─────────────────────────────────────────┐
│              网格参数配置                │
├─────────────────────────────────────────┤
│  基本参数                               │
│  ├─ 网格名称: [输入框]                  │
│  ├─ 网格尺寸: [数值框]                  │
│  └─ 单元类型: [下拉框]                  │
├─────────────────────────────────────────┤
│  质量设置                               │
│  ├─ 偏斜度: [数值框]                    │
│  ├─ 长宽比: [数值框]                    │
│  └─ 平滑迭代: [整数框]                  │
├─────────────────────────────────────────┤
│  高级设置                               │
│  ├─ □ 自动尺寸调整                      │
│  ├─ □ 捕获曲率                          │
│  └─ □ 捕获邻近性                        │
├─────────────────────────────────────────┤
│           [重置] [取消] [确定]           │
└─────────────────────────────────────────┘
```

### 对话框特性

- **尺寸**: 500x600px，模态对话框
- **布局**: 表单布局，垂直分组
- **验证**: 实时输入验证
- **默认值**: 智能默认参数设置

## 📱 响应式设计

### 最小尺寸要求

- **主窗口**: 1400x900px
- **对话框**: 500x600px
- **表格最小高度**: 200-300px
- **图表最小高度**: 300-400px

### 布局适配

- **左右分栏**: 固定左侧宽度，右侧自适应
- **表格列宽**: 自动调整，保持比例
- **按钮组**: 水平居中，固定间距

## 🎯 交互设计规范

### 状态反馈

- **加载状态**: 进度条 + 文字提示
- **成功状态**: 绿色图标 + 提示信息
- **错误状态**: 红色图标 + 错误信息
- **禁用状态**: 灰色显示 + 鼠标禁用

### 操作反馈

- **按钮悬停**: 颜色加深效果
- **表格选择**: 高亮显示选中行
- **输入验证**: 实时边框颜色变化
- **操作确认**: 重要操作显示确认对话框

### 键盘支持

- **Tab导航**: 支持Tab键在控件间切换
- **快捷键**: Ctrl+A全选，Delete删除等
- **回车确认**: 对话框支持回车确认
- **ESC取消**: 对话框支持ESC取消

## 🔍 可访问性设计

### 视觉辅助

- **高对比度**: 确保文字与背景对比度 > 4.5:1
- **字体大小**: 最小字体不小于10px
- **颜色编码**: 不仅依赖颜色传达信息
- **图标说明**: 重要图标配备文字说明

### 操作辅助

- **工具提示**: 重要控件提供详细说明
- **状态指示**: 清晰的状态文字描述
- **错误提示**: 具体的错误信息和解决建议
- **操作引导**: 新手友好的操作提示

## 📋 组件命名规范

### 命名约定

- **按钮**: `btn_` + 功能描述
- **标签**: `label_` + 内容描述  
- **输入框**: `lineEdit_` / `spinBox_` + 用途
- **表格**: `tableWidget_` + 数据类型
- **列表**: `listWidget_` + 内容类型
- **分组**: `groupBox_` + 功能分组

### 示例命名

```
btn_add_mesh          # 添加网格按钮
label_mesh_name       # 网格名称标签
lineEdit_mesh_name    # 网格名称输入框
tableWidget_mesh_params # 网格参数表格
groupBox_batch_control  # 批量控制分组
```

## 🎨 图标和资源

### 图标规范

- **尺寸**: 16x16px, 24x24px, 32x32px
- **格式**: PNG格式，支持透明背景
- **风格**: 线性图标，2px线宽
- **颜色**: 单色图标，支持主题色彩

### 资源路径

```
assets/
├── icons/
│   ├── add.png          # 添加图标
│   ├── delete.png       # 删除图标
│   ├── edit.png         # 编辑图标
│   ├── import.png       # 导入图标
│   └── export.png       # 导出图标
└── styles/
    └── mesh_style.qss   # 网格模块样式
```

---

这个UI设计规范确保了多网格管理系统界面的一致性、可用性和美观性，为后续的开发实现提供了详细的指导。
