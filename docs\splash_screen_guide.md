# 启动画面功能使用指南

## 📋 概述

启动画面功能为振动传递计算软件提供了现代化的启动体验，包含以下特性：

- ✅ **现代化设计** - 渐变背景、圆角边框、优雅动画
- ✅ **进度指示** - 实时显示初始化进度和状态
- ✅ **动画效果** - 淡入淡出、旋转动画
- ✅ **高DPI支持** - 适配不同分辨率和缩放比例
- ✅ **完全可配置** - 支持自定义颜色、字体、布局
- ✅ **性能优化** - 与现有性能监控系统集成

## 🎯 功能特性

### 视觉设计
- **渐变背景**: 蓝绿色渐变，现代化视觉效果
- **应用Logo**: 自动加载应用图标或使用默认设计
- **进度条**: 带渐变效果的进度指示器
- **旋转动画**: 右下角的加载指示器
- **淡入淡出**: 平滑的显示和隐藏动画

### 信息显示
- **应用名称**: 振动传递计算软件
- **版本信息**: v1.2.0
- **公司信息**: 振动传递计算软件开发团队
- **进度百分比**: 实时更新的百分比显示
- **状态文本**: 详细的初始化步骤说明

### 技术特性
- **配置驱动**: 通过JSON配置文件完全可定制
- **高DPI适配**: 自动适配不同屏幕分辨率
- **性能集成**: 与现有性能监控系统无缝集成
- **错误处理**: 启动画面失败时自动降级

## ⚙️ 配置选项

### 基本配置

启动画面的配置位于 `config/settings.json` 文件的 `splash_screen` 节点：

```json
{
  "splash_screen": {
    "enabled": true,
    "show_fade_in": true,
    "fade_in_duration": 500,
    "fade_out_duration": 300,
    "minimum_display_time": 2000,
    "show_progress": true,
    "show_rotation_animation": true,
    "show_company_info": true,
    "custom_background": null
  }
}
```

### 颜色配置

```json
{
  "colors": {
    "primary": "#3498db",
    "secondary": "#2ecc71", 
    "text": "#34495e",
    "progress_background": "#ecf0f1"
  }
}
```

### 字体配置

```json
{
  "fonts": {
    "title_size": 16,
    "version_size": 10,
    "status_size": 9,
    "font_family": "Arial"
  }
}
```

### 布局配置

```json
{
  "layout": {
    "width": 480,
    "height": 320,
    "logo_size": 64,
    "logo_y": 40,
    "title_y": 120,
    "progress_y": 220,
    "status_y": 260
  }
}
```

## 🔧 配置选项详解

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | boolean | true | 是否启用启动画面 |
| `show_fade_in` | boolean | true | 是否显示淡入动画 |
| `fade_in_duration` | number | 500 | 淡入动画持续时间（毫秒） |
| `fade_out_duration` | number | 300 | 淡出动画持续时间（毫秒） |
| `minimum_display_time` | number | 2000 | 最小显示时间（毫秒） |
| `show_progress` | boolean | true | 是否显示进度条 |
| `show_rotation_animation` | boolean | true | 是否显示旋转动画 |
| `show_company_info` | boolean | true | 是否显示公司信息 |
| `custom_background` | string | null | 自定义背景图片路径 |

### 颜色选项

| 选项 | 默认值 | 说明 |
|------|--------|------|
| `primary` | #3498db | 主色调（蓝色） |
| `secondary` | #2ecc71 | 辅助色（绿色） |
| `text` | #34495e | 文字颜色（深灰） |
| `progress_background` | #ecf0f1 | 进度条背景色 |

### 字体选项

| 选项 | 默认值 | 说明 |
|------|--------|------|
| `title_size` | 16 | 标题字体大小 |
| `version_size` | 10 | 版本信息字体大小 |
| `status_size` | 9 | 状态文本字体大小 |
| `font_family` | Arial | 字体族 |

## 🚀 使用方法

### 基本使用

启动画面会在应用程序启动时自动显示，无需额外代码：

```python
# 在 qt_new.py 中已经集成
app = initialize_application()  # 自动显示启动画面
```

### 手动控制

如果需要手动控制启动画面：

```python
from core.splash_screen import get_splash_manager

# 获取启动画面管理器
splash_manager = get_splash_manager()

# 显示启动画面
splash = splash_manager.show_splash()

# 更新进度
splash_manager.update_progress_by_percentage(50, "正在加载...")

# 隐藏启动画面
splash_manager.hide_splash()
```

### 自定义配置

```python
from core.splash_screen import SplashScreenManager

# 创建自定义配置
custom_config = {
    "colors": {
        "primary": "#e74c3c",  # 红色主题
        "secondary": "#f39c12"
    },
    "layout": {
        "width": 600,
        "height": 400
    }
}

# 使用自定义配置
splash_manager = SplashScreenManager(custom_config)
splash = splash_manager.show_splash()
```

## 🎨 自定义主题

### 创建自定义主题

1. **复制默认配置**:
   ```bash
   cp config/splash_config.json config/my_theme.json
   ```

2. **修改颜色方案**:
   ```json
   {
     "colors": {
       "primary": "#9b59b6",    // 紫色主题
       "secondary": "#3498db",
       "text": "#2c3e50"
     }
   }
   ```

3. **应用自定义主题**:
   ```python
   import json
   from core.splash_screen import SplashScreenManager
   
   with open('config/my_theme.json', 'r') as f:
       theme_config = json.load(f)
   
   splash_manager = SplashScreenManager(theme_config)
   ```

### 预设主题

#### 深色主题
```json
{
  "colors": {
    "primary": "#34495e",
    "secondary": "#95a5a6",
    "text": "#ecf0f1",
    "progress_background": "#2c3e50"
  }
}
```

#### 绿色主题
```json
{
  "colors": {
    "primary": "#27ae60",
    "secondary": "#2ecc71",
    "text": "#2c3e50",
    "progress_background": "#d5f4e6"
  }
}
```

#### 橙色主题
```json
{
  "colors": {
    "primary": "#e67e22",
    "secondary": "#f39c12",
    "text": "#2c3e50",
    "progress_background": "#fdeaa7"
  }
}
```

## 🔍 故障排除

### 常见问题

#### 1. 启动画面不显示

**可能原因**:
- 配置中 `enabled` 设置为 `false`
- 启动画面初始化失败

**解决方案**:
```python
# 检查配置
from core.config_manager import ConfigManager
config = ConfigManager()
splash_config = config.get("splash_screen", {})
print(f"启动画面启用状态: {splash_config.get('enabled', True)}")

# 手动启用
config.set("splash_screen.enabled", True)
```

#### 2. 图标不显示

**可能原因**:
- 图标文件路径错误
- 图标文件损坏

**解决方案**:
```python
import os
icon_path = "assets/icons/vibration_transfer_icon_alt.ico"
if not os.path.exists(icon_path):
    print(f"图标文件不存在: {icon_path}")
```

#### 3. 动画效果异常

**可能原因**:
- 系统性能不足
- Qt版本兼容性问题

**解决方案**:
```json
{
  "splash_screen": {
    "show_fade_in": false,
    "show_rotation_animation": false
  }
}
```

#### 4. 高DPI显示问题

**解决方案**:
```python
# 在应用程序启动前设置
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
```

### 调试模式

启用调试模式以获取更多信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启动画面将输出详细的调试信息
```

### 性能优化

如果启动画面影响启动速度：

```json
{
  "splash_screen": {
    "fade_in_duration": 200,
    "fade_out_duration": 100,
    "minimum_display_time": 1000,
    "show_rotation_animation": false
  }
}
```

## 📚 API 参考

### CustomSplashScreen 类

主要的启动画面类，继承自 QSplashScreen。

#### 构造函数
```python
CustomSplashScreen(pixmap=None, config=None, parent=None)
```

#### 主要方法
- `update_progress(progress, status)` - 更新进度
- `show_with_fade_in(duration)` - 带淡入效果显示
- `hide_with_fade_out(duration)` - 带淡出效果隐藏
- `start_rotation_animation()` - 启动旋转动画
- `stop_rotation_animation()` - 停止旋转动画

### SplashScreenManager 类

启动画面管理器，负责生命周期管理。

#### 构造函数
```python
SplashScreenManager(config=None)
```

#### 主要方法
- `show_splash()` - 显示启动画面
- `hide_splash()` - 隐藏启动画面
- `update_progress_by_percentage(percentage, status)` - 更新进度
- `update_progress(step_name)` - 根据步骤名更新进度

## 🔄 版本历史

### v1.0.0 (2025-01-28)
- ✅ 初始版本发布
- ✅ 基本启动画面功能
- ✅ 进度指示和动画效果
- ✅ 配置系统集成
- ✅ 高DPI支持
- ✅ 完整的文档和测试

---

如有问题或建议，请联系开发团队。
