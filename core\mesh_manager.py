"""
网格管理器模块

此模块提供了多网格管理的核心数据模型和业务逻辑，包括：
1. MeshParameter - 单个网格参数数据模型
2. MeshManager - 多网格管理器
3. 网格状态管理和验证
4. 网格参数的序列化和反序列化

作者: [作者名]
日期: [日期]
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass, asdict, field
from PySide6.QtCore import QObject, Signal

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class MeshStatus(Enum):
    """网格状态枚举"""
    NOT_GENERATED = "未生成"
    GENERATING = "生成中"
    GENERATED = "已生成"
    CALCULATING = "计算中"
    COMPLETED = "已完成"
    ERROR = "错误"


class ElementType(Enum):
    """单元类型枚举"""
    TETRAHEDRON = "四面体"
    HEXAHEDRON = "六面体"
    MIXED = "混合"


@dataclass
class MeshQualitySettings:
    """网格质量设置数据类"""
    skewness: float = 0.9  # 偏斜度
    aspect_ratio: float = 20.0  # 长宽比
    smoothing_iterations: int = 3  # 平滑迭代次数
    auto_sizing: bool = True  # 自动尺寸调整
    capture_curvature: bool = True  # 捕获曲率
    capture_proximity: bool = False  # 捕获邻近性

    def validate(self) -> List[str]:
        """验证质量设置参数
        
        Returns:
            List[str]: 错误信息列表，如果没有错误则为空列表
        """
        errors = []
        
        if not (0.0 <= self.skewness <= 1.0):
            errors.append("偏斜度必须在0.0到1.0之间")
        
        if not (1.0 <= self.aspect_ratio <= 100.0):
            errors.append("长宽比必须在1.0到100.0之间")
        
        if not (0 <= self.smoothing_iterations <= 10):
            errors.append("平滑迭代次数必须在0到10之间")
        
        return errors


@dataclass
class MeshStatistics:
    """网格统计信息数据类"""
    node_count: int = 0  # 节点数
    element_count: int = 0  # 单元数
    min_quality: float = 0.0  # 最小质量
    avg_quality: float = 0.0  # 平均质量
    max_quality: float = 0.0  # 最大质量
    generation_time: float = 0.0  # 生成时间(秒)


@dataclass
class ModalResults:
    """模态计算结果数据类"""
    frequencies: List[float] = field(default_factory=list)  # 频率列表
    mode_shapes: List[Dict[str, Any]] = field(default_factory=list)  # 振型数据
    calculation_time: float = 0.0  # 计算时间(秒)
    convergence_info: Dict[str, Any] = field(default_factory=dict)  # 收敛信息


class MeshParameter:
    """网格参数数据模型类"""
    
    def __init__(self, name: str = "", size: float = 10.0, element_type: ElementType = ElementType.TETRAHEDRON):
        """初始化网格参数
        
        Args:
            name: 网格名称
            size: 网格尺寸(mm)
            element_type: 单元类型
        """
        self.id = str(uuid.uuid4())  # 唯一标识符
        self.name = name
        self.size = size
        self.element_type = element_type
        self.status = MeshStatus.NOT_GENERATED
        self.quality_settings = MeshQualitySettings()
        self.statistics = MeshStatistics()
        self.modal_results = ModalResults()
        self.created_time = datetime.now()
        self.updated_time = datetime.now()
        self.error_message = ""  # 错误信息
        
    def validate(self) -> List[str]:
        """验证网格参数
        
        Returns:
            List[str]: 错误信息列表，如果没有错误则为空列表
        """
        errors = []
        
        # 验证名称
        if not self.name or not self.name.strip():
            errors.append("网格名称不能为空")
        elif len(self.name.strip()) > 50:
            errors.append("网格名称长度不能超过50个字符")
        
        # 验证尺寸
        if not (0.1 <= self.size <= 1000.0):
            errors.append("网格尺寸必须在0.1到1000.0mm之间")
        
        # 验证质量设置
        quality_errors = self.quality_settings.validate()
        errors.extend(quality_errors)
        
        return errors
    
    def update_status(self, status: MeshStatus, error_message: str = ""):
        """更新网格状态

        Args:
            status: 新状态
            error_message: 错误信息（可选）
        """
        import time
        time.sleep(0.001)  # 确保时间戳有差异
        self.status = status
        self.error_message = error_message
        self.updated_time = datetime.now()
        logger.debug(f"网格 {self.name} 状态更新为: {status.value}")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式
        
        Returns:
            Dict[str, Any]: 字典格式的网格参数
        """
        return {
            "id": self.id,
            "name": self.name,
            "size": self.size,
            "element_type": self.element_type.value,
            "status": self.status.value,
            "quality_settings": asdict(self.quality_settings),
            "statistics": asdict(self.statistics),
            "modal_results": asdict(self.modal_results),
            "created_time": self.created_time.isoformat(),
            "updated_time": self.updated_time.isoformat(),
            "error_message": self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MeshParameter':
        """从字典创建网格参数对象
        
        Args:
            data: 字典格式的网格参数数据
            
        Returns:
            MeshParameter: 网格参数对象
        """
        mesh_param = cls()
        mesh_param.id = data.get("id", str(uuid.uuid4()))
        mesh_param.name = data.get("name", "")
        mesh_param.size = data.get("size", 10.0)
        
        # 处理枚举类型
        element_type_str = data.get("element_type", ElementType.TETRAHEDRON.value)
        mesh_param.element_type = ElementType(element_type_str)
        
        status_str = data.get("status", MeshStatus.NOT_GENERATED.value)
        mesh_param.status = MeshStatus(status_str)
        
        # 处理嵌套对象
        quality_data = data.get("quality_settings", {})
        mesh_param.quality_settings = MeshQualitySettings(**quality_data)
        
        stats_data = data.get("statistics", {})
        mesh_param.statistics = MeshStatistics(**stats_data)
        
        modal_data = data.get("modal_results", {})
        mesh_param.modal_results = ModalResults(**modal_data)
        
        # 处理时间
        created_time_str = data.get("created_time")
        if created_time_str:
            mesh_param.created_time = datetime.fromisoformat(created_time_str)
        
        updated_time_str = data.get("updated_time")
        if updated_time_str:
            mesh_param.updated_time = datetime.fromisoformat(updated_time_str)
        
        mesh_param.error_message = data.get("error_message", "")
        
        return mesh_param
    
    def clone(self) -> 'MeshParameter':
        """克隆网格参数对象
        
        Returns:
            MeshParameter: 克隆的网格参数对象
        """
        cloned = MeshParameter.from_dict(self.to_dict())
        cloned.id = str(uuid.uuid4())  # 生成新的ID
        cloned.name = f"{self.name}_副本"
        cloned.status = MeshStatus.NOT_GENERATED  # 重置状态
        cloned.created_time = datetime.now()
        cloned.updated_time = datetime.now()
        return cloned


class MeshManagerSignals(QObject):
    """网格管理器信号类"""
    mesh_added = Signal(str)  # 网格添加信号，参数为网格ID
    mesh_removed = Signal(str)  # 网格移除信号，参数为网格ID
    mesh_updated = Signal(str)  # 网格更新信号，参数为网格ID
    status_changed = Signal(str, str)  # 状态变更信号，参数为网格ID和新状态
    current_mesh_changed = Signal(str)  # 当前网格变更信号，参数为网格ID
    batch_operation_progress = Signal(int, int)  # 批量操作进度信号，参数为当前进度和总数
    batch_import_completed = Signal(list)  # 批量导入完成信号，参数为导入的网格ID列表
    error_occurred = Signal(str, str)  # 错误发生信号，参数为网格ID和错误信息


class MeshManager:
    """多网格管理器类"""
    
    def __init__(self):
        """初始化网格管理器"""
        self._mesh_parameters: Dict[str, MeshParameter] = {}
        self._current_mesh_id: Optional[str] = None
        self.signals = MeshManagerSignals()

        logger.info("网格管理器初始化完成")

    def reset_all_mesh_states(self):
        """重置所有网格状态为未生成

        用于应用启动时确保所有网格都需要重新生成
        """
        try:
            reset_count = 0
            for mesh in self._mesh_parameters.values():
                if mesh.status in [MeshStatus.GENERATED, MeshStatus.COMPLETED, MeshStatus.CALCULATING, MeshStatus.GENERATING]:
                    mesh.update_status(MeshStatus.NOT_GENERATED)
                    # 清除生成和计算相关的数据
                    mesh.file_path = ""
                    mesh.statistics = MeshStatistics()
                    mesh.modal_results = ModalResults()
                    reset_count += 1

            if reset_count > 0:
                logger.info(f"重置了 {reset_count} 个网格的状态为未生成")

            return reset_count

        except Exception as e:
            logger.error(f"重置网格状态失败: {str(e)}", exc_info=True)
            return 0
    
    @property
    def mesh_count(self) -> int:
        """获取网格数量"""
        return len(self._mesh_parameters)
    
    @property
    def current_mesh(self) -> Optional[MeshParameter]:
        """获取当前选中的网格"""
        if self._current_mesh_id and self._current_mesh_id in self._mesh_parameters:
            return self._mesh_parameters[self._current_mesh_id]
        return None
    
    def get_all_meshes(self) -> List[MeshParameter]:
        """获取所有网格参数列表
        
        Returns:
            List[MeshParameter]: 网格参数列表
        """
        return list(self._mesh_parameters.values())
    
    def get_mesh_by_id(self, mesh_id: str) -> Optional[MeshParameter]:
        """根据ID获取网格参数
        
        Args:
            mesh_id: 网格ID
            
        Returns:
            Optional[MeshParameter]: 网格参数对象，如果不存在则返回None
        """
        return self._mesh_parameters.get(mesh_id)
    
    def get_mesh_by_name(self, name: str) -> Optional[MeshParameter]:
        """根据名称获取网格参数
        
        Args:
            name: 网格名称
            
        Returns:
            Optional[MeshParameter]: 网格参数对象，如果不存在则返回None
        """
        for mesh in self._mesh_parameters.values():
            if mesh.name == name:
                return mesh
        return None

    def add_mesh(self, mesh_param: MeshParameter) -> bool:
        """添加网格参数

        Args:
            mesh_param: 网格参数对象

        Returns:
            bool: 添加成功返回True，否则返回False
        """
        # 验证网格参数
        errors = mesh_param.validate()
        if errors:
            error_msg = "; ".join(errors)
            logger.error(f"添加网格失败，验证错误: {error_msg}")
            self.signals.error_occurred.emit(mesh_param.id, error_msg)
            return False

        # 检查名称是否重复
        if self.get_mesh_by_name(mesh_param.name):
            error_msg = f"网格名称 '{mesh_param.name}' 已存在"
            logger.error(error_msg)
            self.signals.error_occurred.emit(mesh_param.id, error_msg)
            return False

        # 添加网格
        self._mesh_parameters[mesh_param.id] = mesh_param

        # 如果是第一个网格，设置为当前网格
        if len(self._mesh_parameters) == 1:
            self._current_mesh_id = mesh_param.id

        logger.info(f"成功添加网格: {mesh_param.name} (ID: {mesh_param.id})")
        self.signals.mesh_added.emit(mesh_param.id)
        return True

    def remove_mesh(self, mesh_id: str) -> bool:
        """移除网格参数

        Args:
            mesh_id: 网格ID

        Returns:
            bool: 移除成功返回True，否则返回False
        """
        if mesh_id not in self._mesh_parameters:
            logger.warning(f"尝试移除不存在的网格: {mesh_id}")
            return False

        mesh_name = self._mesh_parameters[mesh_id].name
        del self._mesh_parameters[mesh_id]

        # 如果移除的是当前网格，重新选择当前网格
        if self._current_mesh_id == mesh_id:
            if self._mesh_parameters:
                self._current_mesh_id = next(iter(self._mesh_parameters.keys()))
            else:
                self._current_mesh_id = None

        logger.info(f"成功移除网格: {mesh_name} (ID: {mesh_id})")
        self.signals.mesh_removed.emit(mesh_id)
        return True

    def update_mesh(self, mesh_param: MeshParameter) -> bool:
        """更新网格参数

        Args:
            mesh_param: 更新后的网格参数对象

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        if mesh_param.id not in self._mesh_parameters:
            logger.warning(f"尝试更新不存在的网格: {mesh_param.id}")
            return False

        # 验证网格参数
        errors = mesh_param.validate()
        if errors:
            error_msg = "; ".join(errors)
            logger.error(f"更新网格失败，验证错误: {error_msg}")
            self.signals.error_occurred.emit(mesh_param.id, error_msg)
            return False

        # 检查名称是否与其他网格重复
        existing_mesh = self.get_mesh_by_name(mesh_param.name)
        if existing_mesh and existing_mesh.id != mesh_param.id:
            error_msg = f"网格名称 '{mesh_param.name}' 已被其他网格使用"
            logger.error(error_msg)
            self.signals.error_occurred.emit(mesh_param.id, error_msg)
            return False

        # 更新时间戳
        mesh_param.updated_time = datetime.now()

        # 更新网格
        self._mesh_parameters[mesh_param.id] = mesh_param

        logger.info(f"成功更新网格: {mesh_param.name} (ID: {mesh_param.id})")
        self.signals.mesh_updated.emit(mesh_param.id)
        return True

    def set_current_mesh(self, mesh_id: str) -> bool:
        """设置当前选中的网格

        Args:
            mesh_id: 网格ID

        Returns:
            bool: 设置成功返回True，否则返回False
        """
        if mesh_id not in self._mesh_parameters:
            logger.warning(f"尝试设置不存在的网格为当前网格: {mesh_id}")
            return False

        self._current_mesh_id = mesh_id
        logger.debug(f"当前网格设置为: {self._mesh_parameters[mesh_id].name}")
        return True

    def get_meshes_by_status(self, status: MeshStatus) -> List[MeshParameter]:
        """根据状态获取网格列表

        Args:
            status: 网格状态

        Returns:
            List[MeshParameter]: 指定状态的网格列表
        """
        return [mesh for mesh in self._mesh_parameters.values() if mesh.status == status]

    def clear_all_meshes(self) -> None:
        """清空所有网格"""
        mesh_count = len(self._mesh_parameters)
        self._mesh_parameters.clear()
        self._current_mesh_id = None
        logger.info(f"已清空所有网格，共移除 {mesh_count} 个网格")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式

        Returns:
            Dict[str, Any]: 字典格式的网格管理器数据
        """
        return {
            "mesh_parameters": {mesh_id: mesh.to_dict() for mesh_id, mesh in self._mesh_parameters.items()},
            "current_mesh_id": self._current_mesh_id,
            "version": "1.0.0"
        }

    def from_dict(self, data: Dict[str, Any], replace_existing: bool = True) -> bool:
        """从字典加载网格管理器数据

        Args:
            data: 字典格式的网格管理器数据
            replace_existing: 是否替换现有数据，False表示追加

        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            # 记录导入前的网格数量
            old_mesh_count = len(self._mesh_parameters)

            if replace_existing:
                # 清空现有数据
                self._mesh_parameters.clear()
                self._current_mesh_id = None

            # 加载网格参数
            mesh_data = data.get("mesh_parameters", {})
            imported_mesh_ids = []

            for mesh_id, mesh_dict in mesh_data.items():
                # 如果是追加模式且网格已存在，生成新ID
                if not replace_existing and mesh_id in self._mesh_parameters:
                    original_mesh = MeshParameter.from_dict(mesh_dict)
                    # 创建新的网格参数，使用新ID
                    new_mesh = MeshParameter(
                        name=f"{original_mesh.name}_导入",
                        size=original_mesh.size,
                        element_type=original_mesh.element_type
                    )
                    # 复制其他属性
                    new_mesh.status = original_mesh.status
                    new_mesh.statistics = original_mesh.statistics
                    new_mesh.modal_results = original_mesh.modal_results
                    new_mesh.quality_settings = original_mesh.quality_settings

                    self._mesh_parameters[new_mesh.id] = new_mesh
                    imported_mesh_ids.append(new_mesh.id)
                else:
                    mesh_param = MeshParameter.from_dict(mesh_dict)
                    # 重置网格状态为未生成，确保重新启动时需要重新生成
                    if mesh_param.status in [MeshStatus.GENERATED, MeshStatus.COMPLETED, MeshStatus.CALCULATING]:
                        mesh_param.update_status(MeshStatus.NOT_GENERATED)
                        # 清除生成和计算相关的数据
                        mesh_param.file_path = ""
                        mesh_param.statistics = MeshStatistics()
                        mesh_param.modal_results = ModalResults()
                        logger.debug(f"重置网格状态: {mesh_param.name} -> NOT_GENERATED")

                    self._mesh_parameters[mesh_id] = mesh_param
                    imported_mesh_ids.append(mesh_id)

            # 设置当前网格
            current_mesh_id = data.get("current_mesh_id")
            if current_mesh_id and current_mesh_id in self._mesh_parameters:
                self._current_mesh_id = current_mesh_id
            elif self._mesh_parameters and not self._current_mesh_id:
                self._current_mesh_id = next(iter(self._mesh_parameters.keys()))

            # 触发批量更新信号
            new_mesh_count = len(self._mesh_parameters)
            imported_count = len(imported_mesh_ids)
            logger.info(f"成功加载 {imported_count} 个网格参数，总数: {new_mesh_count}")

            # 发射批量导入完成信号
            if hasattr(self.signals, 'batch_import_completed'):
                self.signals.batch_import_completed.emit(imported_mesh_ids)

            # 为每个导入的网格发射添加信号
            for mesh_id in imported_mesh_ids:
                self.signals.mesh_added.emit(mesh_id)

            # 如果当前网格发生变化，发射信号
            if self._current_mesh_id:
                self.signals.current_mesh_changed.emit(self._current_mesh_id)

            return True

        except Exception as e:
            logger.error(f"加载网格管理器数据失败: {str(e)}", exc_info=True)
            return False

    def export_to_json(self, file_path: str) -> bool:
        """导出网格参数到JSON文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 导出成功返回True，否则返回False
        """
        try:
            data = self.to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"成功导出网格参数到: {file_path}")
            return True

        except Exception as e:
            logger.error(f"导出网格参数失败: {str(e)}", exc_info=True)
            return False

    def update_mesh_status(self, mesh_id: str, status: MeshStatus, error_message: str = "") -> bool:
        """更新网格状态并触发信号

        Args:
            mesh_id: 网格ID
            status: 新状态
            error_message: 错误信息（可选）

        Returns:
            bool: 更新成功返回True，否则返回False
        """
        try:
            if mesh_id not in self._mesh_parameters:
                logger.warning(f"网格不存在: {mesh_id}")
                return False

            mesh = self._mesh_parameters[mesh_id]
            old_status = mesh.status

            # 更新状态
            mesh.update_status(status, error_message)

            # 触发信号
            self.signals.mesh_updated.emit(mesh_id)
            self.signals.status_changed.emit(mesh_id, status.value)

            logger.info(f"网格状态更新: {mesh.name} {old_status.value} -> {status.value}")
            return True

        except Exception as e:
            logger.error(f"更新网格状态失败: {str(e)}", exc_info=True)
            return False

    def import_from_json(self, file_path: str, replace_existing: bool = False) -> bool:
        """从JSON文件导入网格参数

        Args:
            file_path: 文件路径
            replace_existing: 是否替换现有数据，默认False（追加模式）

        Returns:
            bool: 导入成功返回True，否则返回False
        """
        try:
            # 尝试不同的编码方式
            encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312']
            data = None

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        data = json.load(f)
                    break
                except UnicodeDecodeError:
                    continue
                except json.JSONDecodeError:
                    break  # JSON格式错误，不是编码问题

            if data is None:
                raise ValueError("无法读取文件，可能是编码问题")

            success = self.from_dict(data, replace_existing)
            if success:
                logger.info(f"成功从文件导入网格参数: {file_path}")
            return success

        except Exception as e:
            logger.error(f"导入网格参数失败: {str(e)}", exc_info=True)
            return False
