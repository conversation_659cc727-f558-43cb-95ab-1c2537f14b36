"""
模态分析集成示例

展示如何在现有网格界面中集成模态配置生成器，
实现从界面参数到 modal.py 脚本配置的无缝转换。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path

# 导入现有模块（示例）
# from views.mesh_window_merged import MeshWindow
# from core.mesh_manager import MeshManager
# from core.config_manager import ConfigManager
# from modal_config_generator import ModalConfigGenerator

logger = logging.getLogger(__name__)

class ModalAnalysisIntegration:
    """
    模态分析集成类
    
    负责在现有网格界面中集成模态分析功能，
    包括配置生成、参数验证和脚本调用。
    """
    
    def __init__(self, mesh_window):
        """
        初始化模态分析集成
        
        Args:
            mesh_window: 网格窗口实例
        """
        self.mesh_window = mesh_window
        self.mesh_manager = mesh_window.mesh_manager
        self.config_manager = getattr(mesh_window, 'config_manager', None)
        
        # 初始化配置生成器
        from modal_config_generator import ModalConfigGenerator
        self.config_generator = ModalConfigGenerator(
            self.mesh_manager, 
            self.config_manager
        )
        
        # 模态分析相关路径
        self.modal_script_path = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/originscript/modal.py"
        self.modal_config_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
        
    def prepare_modal_analysis(self) -> Dict[str, Any]:
        """
        准备模态分析
        
        Returns:
            Dict[str, Any]: 准备结果，包含状态和信息
        """
        result = {
            'success': False,
            'message': '',
            'config_path': '',
            'mesh_count': 0,
            'size_range': ''
        }
        
        try:
            logger.info("开始准备模态分析...")
            
            # 1. 检查网格参数
            all_meshes = self.mesh_manager.get_all_meshes()
            if not all_meshes:
                result['message'] = "没有可用的网格参数，请先添加网格配置"
                return result
            
            # 2. 验证网格参数
            valid_meshes = [mesh for mesh in all_meshes if hasattr(mesh, 'size') and mesh.size > 0]
            if not valid_meshes:
                result['message'] = "没有有效的网格尺寸，请检查网格参数设置"
                return result
            
            # 3. 生成模态配置文件
            success = self.config_generator.generate_modal_config(self.modal_config_path)
            if not success:
                result['message'] = "生成模态配置文件失败"
                return result
            
            # 4. 验证生成的配置文件
            validation = self.config_generator.validate_modal_config(self.modal_config_path)
            if not validation['valid']:
                result['message'] = f"配置文件验证失败: {', '.join(validation['errors'])}"
                return result
            
            # 5. 准备成功
            sizes = [mesh.size / 1000.0 for mesh in valid_meshes]  # 转换为米
            result.update({
                'success': True,
                'message': '模态分析准备完成',
                'config_path': self.modal_config_path,
                'mesh_count': len(valid_meshes),
                'size_range': f"{min(sizes):.4f}m - {max(sizes):.4f}m"
            })
            
            logger.info(f"模态分析准备完成: {result['mesh_count']} 个网格，尺寸范围 {result['size_range']}")
            
        except Exception as e:
            logger.error(f"准备模态分析失败: {str(e)}", exc_info=True)
            result['message'] = f"准备过程出错: {str(e)}"
        
        return result
    
    def preview_modal_config(self) -> Dict[str, Any]:
        """
        预览模态配置
        
        Returns:
            Dict[str, Any]: 预览的配置内容
        """
        try:
            return self.config_generator.preview_modal_config()
        except Exception as e:
            logger.error(f"预览模态配置失败: {str(e)}", exc_info=True)
            return {}
    
    def validate_modal_requirements(self) -> Dict[str, Any]:
        """
        验证模态分析的前置条件
        
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': False,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        try:
            # 1. 检查网格管理器
            if not self.mesh_manager:
                result['errors'].append("网格管理器未初始化")
                return result
            
            # 2. 检查网格参数
            all_meshes = self.mesh_manager.get_all_meshes()
            if not all_meshes:
                result['errors'].append("没有可用的网格参数")
                return result
            
            # 3. 检查网格尺寸
            valid_meshes = []
            invalid_meshes = []
            
            for mesh in all_meshes:
                if hasattr(mesh, 'size') and mesh.size > 0:
                    valid_meshes.append(mesh)
                else:
                    invalid_meshes.append(mesh)
            
            if not valid_meshes:
                result['errors'].append("没有有效的网格尺寸")
                return result
            
            if invalid_meshes:
                result['warnings'].append(f"发现 {len(invalid_meshes)} 个无效网格参数")
            
            # 4. 检查输出目录权限
            try:
                output_dir = self.config_generator._get_output_directory()
                parent_dir = os.path.dirname(output_dir)
                if not os.path.exists(parent_dir):
                    result['warnings'].append(f"输出目录的父目录不存在: {parent_dir}")
                elif not os.access(parent_dir, os.W_OK):
                    result['warnings'].append(f"输出目录没有写权限: {parent_dir}")
            except Exception as e:
                result['warnings'].append(f"无法检查输出目录权限: {str(e)}")
            
            # 5. 检查模态脚本文件
            if not os.path.exists(self.modal_script_path):
                result['warnings'].append(f"模态脚本文件不存在: {self.modal_script_path}")
            
            # 6. 设置验证结果
            result['valid'] = len(result['errors']) == 0
            result['info'] = {
                'total_meshes': len(all_meshes),
                'valid_meshes': len(valid_meshes),
                'invalid_meshes': len(invalid_meshes),
                'size_range': f"{min(m.size for m in valid_meshes):.1f} - {max(m.size for m in valid_meshes):.1f} mm" if valid_meshes else "无"
            }
            
        except Exception as e:
            logger.error(f"验证模态分析前置条件失败: {str(e)}", exc_info=True)
            result['errors'].append(f"验证过程出错: {str(e)}")
        
        return result
    
    def get_modal_analysis_summary(self) -> str:
        """
        获取模态分析摘要信息
        
        Returns:
            str: 摘要信息
        """
        try:
            validation = self.validate_modal_requirements()
            preview = self.preview_modal_config()
            
            if not validation['valid']:
                return f"❌ 模态分析前置条件不满足:\n" + "\n".join(validation['errors'])
            
            info = validation['info']
            element_sizes = preview.get('element_size', [])
            
            summary = f"""✅ 模态分析准备就绪

📊 网格信息:
  • 总网格数: {info['total_meshes']}
  • 有效网格: {info['valid_meshes']}
  • 尺寸范围: {info['size_range']}

🔧 分析配置:
  • 网格尺寸数量: {len(element_sizes)}
  • 尺寸范围: {min(element_sizes):.4f}m - {max(element_sizes):.4f}m (如果有数据)
  • 输出目录: {preview.get('output_directory', '未知')}

⚠️ 注意事项:"""
            
            if validation['warnings']:
                summary += "\n" + "\n".join(f"  • {warning}" for warning in validation['warnings'])
            else:
                summary += "\n  • 无警告"
            
            return summary
            
        except Exception as e:
            logger.error(f"获取模态分析摘要失败: {str(e)}", exc_info=True)
            return f"❌ 获取摘要信息失败: {str(e)}"


# 在现有网格窗口中的集成示例
class MeshWindowModalExtension:
    """
    网格窗口模态分析扩展
    
    展示如何在现有的 MeshWindow 类中添加模态分析功能
    """
    
    def __init__(self, mesh_window):
        self.mesh_window = mesh_window
        self.modal_integration = ModalAnalysisIntegration(mesh_window)
        
        # 添加模态分析相关的UI组件（示例）
        self._setup_modal_ui()
    
    def _setup_modal_ui(self):
        """设置模态分析相关的UI组件"""
        # 这里可以添加模态分析按钮、状态显示等UI组件
        # 例如：
        # self.btn_modal_analysis = QPushButton("开始模态分析")
        # self.btn_modal_analysis.clicked.connect(self._on_modal_analysis_clicked)
        pass
    
    def _on_modal_analysis_clicked(self):
        """模态分析按钮点击处理"""
        try:
            # 1. 验证前置条件
            validation = self.modal_integration.validate_modal_requirements()
            if not validation['valid']:
                error_msg = "模态分析前置条件不满足:\n" + "\n".join(validation['errors'])
                # 显示错误消息（使用现有的消息框）
                # QMessageBox.warning(self.mesh_window, "警告", error_msg)
                logger.warning(error_msg)
                return
            
            # 2. 显示分析摘要
            summary = self.modal_integration.get_modal_analysis_summary()
            logger.info(f"模态分析摘要:\n{summary}")
            
            # 3. 确认是否继续
            # reply = QMessageBox.question(
            #     self.mesh_window, "确认模态分析", 
            #     summary + "\n\n是否开始模态分析？"
            # )
            # if reply != QMessageBox.Yes:
            #     return
            
            # 4. 准备模态分析
            prepare_result = self.modal_integration.prepare_modal_analysis()
            if not prepare_result['success']:
                # QMessageBox.critical(self.mesh_window, "错误", prepare_result['message'])
                logger.error(prepare_result['message'])
                return
            
            # 5. 启动模态分析（这里可以调用 ANSYS Workbench 或其他分析工具）
            logger.info("模态分析配置已准备完成，可以启动 ANSYS Workbench 进行分析")
            logger.info(f"配置文件路径: {prepare_result['config_path']}")
            
            # 6. 显示成功消息
            success_msg = f"""模态分析准备完成！

网格数量: {prepare_result['mesh_count']}
尺寸范围: {prepare_result['size_range']}
配置文件: {prepare_result['config_path']}

请在 ANSYS Workbench 中运行模态分析脚本。"""
            
            # QMessageBox.information(self.mesh_window, "准备完成", success_msg)
            logger.info(success_msg)
            
        except Exception as e:
            logger.error(f"模态分析处理失败: {str(e)}", exc_info=True)
            # QMessageBox.critical(self.mesh_window, "错误", f"模态分析处理失败: {str(e)}")


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 模拟网格窗口（在实际使用中这将是真实的 MeshWindow 实例）
    class MockMeshWindow:
        def __init__(self):
            self.mesh_manager = None  # 在实际使用中这将是真实的 MeshManager
            self.config_manager = None  # 在实际使用中这将是真实的 ConfigManager
    
    # 创建模拟窗口和集成
    mock_window = MockMeshWindow()
    modal_integration = ModalAnalysisIntegration(mock_window)
    
    # 测试功能
    print("=== 模态分析集成测试 ===")
    
    # 预览配置
    preview = modal_integration.preview_modal_config()
    print("\n预览模态配置:")
    print(json.dumps(preview, indent=2, ensure_ascii=False))
    
    # 验证前置条件
    validation = modal_integration.validate_modal_requirements()
    print(f"\n前置条件验证: {'通过' if validation['valid'] else '失败'}")
    if validation['errors']:
        print(f"错误: {validation['errors']}")
    if validation['warnings']:
        print(f"警告: {validation['warnings']}")
    
    # 获取摘要
    summary = modal_integration.get_modal_analysis_summary()
    print(f"\n分析摘要:\n{summary}")
    
    print("\n=== 测试完成 ===")
