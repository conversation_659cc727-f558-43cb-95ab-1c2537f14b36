<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1400</width>
    <height>900</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1400</width>
    <height>900</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Microsoft YaHei UI</family>
    <pointsize>10</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>多网格管理系统</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <property name="spacing">
     <number>10</number>
    </property>
    <property name="leftMargin">
     <number>15</number>
    </property>
    <property name="topMargin">
     <number>15</number>
    </property>
    <property name="rightMargin">
     <number>15</number>
    </property>
    <property name="bottomMargin">
     <number>15</number>
    </property>
    <item>
     <widget class="QLabel" name="label_title">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>60</height>
       </size>
      </property>
      <property name="font">
       <font>
        <family>Microsoft YaHei UI</family>
        <pointsize>24</pointsize>
        <weight>75</weight>
        <bold>true</bold>
       </font>
      </property>
      <property name="text">
       <string>网格无关性验证系统</string>
      </property>
      <property name="alignment">
       <set>Qt::AlignmentFlag::AlignCenter</set>
      </property>
      <property name="styleSheet">
       <string>QLabel {
    color: #34495e;
    background-color: transparent;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget_main">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <property name="styleSheet">
       <string>QTabWidget::pane {
    border: 1px solid #e9eaec;
    border-radius: 0 6px 6px 6px;
    background: white;
    top: -1px;
}

QTabBar::tab {
    background: #f5f7fa;
    border: 1px solid #dcdfe6;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    min-width: 120px;
    padding: 12px 20px;
    font-weight: 500;
    font-size: 11px;
}

QTabBar::tab:selected {
    background: white;
    border-bottom-color: white;
    color: #3498db;
}

QTabBar::tab:hover:!selected {
    background: #ecf0f1;
}</string>
      </property>
      <widget class="QWidget" name="tab_mesh_management">
       <attribute name="title">
        <string>网格管理</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_mesh_mgmt">
        <property name="spacing">
         <number>15</number>
        </property>
        <item>
         <widget class="QWidget" name="widget_left_panel" native="true">
          <property name="minimumSize">
           <size>
            <width>600</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>650</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_left">
           <item>
            <widget class="QGroupBox" name="groupBox_mesh_params">
             <property name="title">
              <string>网格参数管理</string>
             </property>
             <property name="styleSheet">
              <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_params">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_toolbar">
                <item>
                 <widget class="QPushButton" name="btn_add_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>添加网格</string>
                  </property>
                  <property name="icon">
                   <iconset>
                    <normaloff>../assets/icons/add.png</normaloff>../assets/icons/add.png</iconset>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_import_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>导入配置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="btn_export_mesh">
                  <property name="minimumSize">
                   <size>
                    <width>100</width>
                    <height>35</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>导出配置</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_toolbar">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QTableWidget" name="tableWidget_mesh_params">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>300</height>
                 </size>
                </property>
                <property name="alternatingRowColors">
                 <bool>true</bool>
                </property>
                <property name="selectionBehavior">
                 <enum>QAbstractItemView::SelectionBehavior::SelectRows</enum>
                </property>
                <property name="gridStyle">
                 <enum>Qt::PenStyle::SolidLine</enum>
                </property>
                <property name="sortingEnabled">
                 <bool>true</bool>
                </property>
                <property name="styleSheet">
                 <string>QTableWidget {
    border: 1px solid #dcdfe6;
    background-color: white;
    gridline-color: #e9eaec;
    selection-background-color: #3498db;
    selection-color: white;
    alternate-background-color: #f5f7fa;
}

QHeaderView::section {
    background-color: #f5f7fa;
    border: 1px solid #dcdfe6;
    padding: 8px;
    font-weight: bold;
    color: #34495e;
}</string>
                </property>
                <column>
                 <property name="text">
                  <string>网格名称</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>尺寸(mm)</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>状态</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>节点数</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>单元数</string>
                 </property>
                </column>
                <column>
                 <property name="text">
                  <string>操作</string>
                 </property>
                </column>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_right_panel" native="true">
          <layout class="QVBoxLayout" name="verticalLayout_right">
           <item>
            <widget class="QGroupBox" name="groupBox_mesh_preview">
             <property name="title">
              <string>网格预览与详情</string>
             </property>
             <property name="styleSheet">
              <string>QGroupBox {
    background-color: white;
    border: 1px solid #e9eaec;
    border-radius: 6px;
    margin-top: 12px;
    padding: 15px;
    font-weight: bold;
    color: #34495e;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 5px;
    background-color: white;
}</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_preview">
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_preview_ctrl">
                <item>
                 <widget class="QLabel" name="label_select_mesh">
                  <property name="text">
                   <string>选择网格:</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="comboBox_mesh_select">
                  <property name="minimumSize">
                   <size>
                    <width>200</width>
                    <height>30</height>
                   </size>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_preview">
                  <property name="orientation">
                   <enum>Qt::Orientation::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </item>
              <item>
               <widget class="QLabel" name="label_mesh_preview">
                <property name="minimumSize">
                 <size>
                  <width>400</width>
                  <height>300</height>
                 </size>
                </property>
                <property name="font">
                 <font>
                  <family>Microsoft YaHei UI</family>
                  <pointsize>16</pointsize>
                 </font>
                </property>
                <property name="text">
                 <string>网格预览区域
(将集成matplotlib画布)</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
                <property name="styleSheet">
                 <string>QLabel {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background-color: #f9f9f9;
    color: #7f8c8d;
}</string>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QGroupBox" name="groupBox_mesh_stats">
                <property name="title">
                 <string>网格统计信息</string>
                </property>
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>120</height>
                 </size>
                </property>
                <layout class="QGridLayout" name="gridLayout_stats">
                 <item row="0" column="0">
                  <widget class="QLabel" name="label_nodes">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>节点数:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="0" column="1">
                  <widget class="QLabel" name="label_nodes_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #3498db;</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="0">
                  <widget class="QLabel" name="label_elements">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>单元数:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="1" column="1">
                  <widget class="QLabel" name="label_elements_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #3498db;</string>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="0">
                  <widget class="QLabel" name="label_quality">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                     <weight>75</weight>
                     <bold>true</bold>
                    </font>
                   </property>
                   <property name="text">
                    <string>网格质量:</string>
                   </property>
                  </widget>
                 </item>
                 <item row="2" column="1">
                  <widget class="QLabel" name="label_quality_value">
                   <property name="font">
                    <font>
                     <family>Microsoft YaHei UI</family>
                     <pointsize>12</pointsize>
                    </font>
                   </property>
                   <property name="text">
                    <string>--</string>
                   </property>
                   <property name="styleSheet">
                    <string>color: #2ecc71;</string>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_mesh_generation">
       <attribute name="title">
        <string>网格生成</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_generation">
        <item>
         <widget class="QGroupBox" name="groupBox_batch_control">
          <property name="title">
           <string>批量操作控制</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>400</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>450</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_batch">
           <item>
            <widget class="QCheckBox" name="checkBox_select_all">
             <property name="text">
              <string>全选网格</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QListWidget" name="listWidget_selected_meshes">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>150</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_batch_buttons">
             <item>
              <widget class="QPushButton" name="btn_batch_generate">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>批量生成</string>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: #2ecc71;
}
QPushButton:hover {
    background-color: #27ae60;
}</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_stop_generation">
               <property name="minimumSize">
                <size>
                 <width>120</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>停止生成</string>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: #e74c3c;
}
QPushButton:hover {
    background-color: #c0392b;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_progress">
             <property name="title">
              <string>生成进度</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_progress">
              <item>
               <widget class="QProgressBar" name="progressBar_generation">
                <property name="value">
                 <number>0</number>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QLabel" name="label_progress_text">
                <property name="text">
                 <string>等待开始...</string>
                </property>
                <property name="alignment">
                 <set>Qt::AlignmentFlag::AlignCenter</set>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_generation_log">
          <property name="title">
           <string>生成日志与质量对比</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_log">
           <item>
            <widget class="QTextEdit" name="textEdit_generation_log">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>300</height>
              </size>
             </property>
             <property name="font">
              <font>
               <family>Consolas</family>
               <pointsize>9</pointsize>
              </font>
             </property>
             <property name="styleSheet">
              <string>QTextEdit {
    border: 1px solid #dcdfe6;
    background-color: #2c3e50;
    color: #ecf0f1;
    selection-background-color: #3498db;
}</string>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTableWidget" name="tableWidget_mesh_comparison">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>200</height>
              </size>
             </property>
             <property name="alternatingRowColors">
              <bool>true</bool>
             </property>
             <column>
              <property name="text">
               <string>网格名称</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>节点数</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>单元数</string>
              </property>
             </column>
             <column>
              <property name="text">
               <string>质量</string>
              </property>
             </column>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_modal_analysis">
       <attribute name="title">
        <string>模态分析</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_modal">
        <item>
         <widget class="QGroupBox" name="groupBox_modal_control">
          <property name="title">
           <string>模态计算控制</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>150</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_modal_control">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_modal_params">
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_modal_count">
               <item>
                <widget class="QLabel" name="label_modal_count">
                 <property name="text">
                  <string>模态阶数:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QSpinBox" name="spinBox_modal_count">
                 <property name="minimum">
                  <number>0</number>
                 </property>
                 <property name="maximum">
                  <number>200</number>
                 </property>
                 <property name="value">
                  <number>5</number>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_freq_option">
               <item>
                <widget class="QCheckBox" name="checkBox_limit_freq">
                 <property name="text">
                  <string>限制频率范围</string>
                 </property>
                 <property name="checked">
                  <bool>true</bool>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_freq_option">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
              </layout>
             </item>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_freq_range">
               <item>
                <widget class="QLabel" name="label_freq_range">
                 <property name="text">
                  <string>频率范围:</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_freq_min">
                 <property name="maximum">
                  <double>100000.000000000000000</double>
                 </property>
                 <property name="value">
                  <double>0.000000000000000</double>
                 </property>
                 <property name="suffix">
                  <string> Hz</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QLabel" name="label_to">
                 <property name="text">
                  <string>-</string>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QDoubleSpinBox" name="doubleSpinBox_freq_max">
                 <property name="maximum">
                  <double>100000.000000000000000</double>
                 </property>
                 <property name="value">
                  <double>1000.000000000000000</double>
                 </property>
                 <property name="suffix">
                  <string> Hz</string>
                 </property>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_modal_buttons">
             <item>
              <widget class="QPushButton" name="btn_single_modal">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>单个模态计算</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="btn_batch_modal">
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>40</height>
                </size>
               </property>
               <property name="text">
                <string>批量模态计算</string>
               </property>
               <property name="styleSheet">
                <string>QPushButton {
    background-color: #2ecc71;
}
QPushButton:hover {
    background-color: #27ae60;
}</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="horizontalSpacer_modal">
             <property name="orientation">
              <enum>Qt::Orientation::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_convergence_chart">
          <property name="title">
           <string>频率收敛性分析</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_chart">
           <item>
            <widget class="QLabel" name="label_convergence_chart">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>400</height>
              </size>
             </property>
             <property name="text">
              <string>频率收敛性图表区域
(将集成matplotlib画布)</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>QLabel {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background-color: #f9f9f9;
    color: #7f8c8d;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_result_comparison">
       <attribute name="title">
        <string>结果对比</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_comparison">
        <item>
         <widget class="QGroupBox" name="groupBox_comparison_control">
          <property name="title">
           <string>对比控制</string>
          </property>
          <property name="minimumSize">
           <size>
            <width>300</width>
            <height>0</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>350</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_comparison_ctrl">
           <item>
            <widget class="QLabel" name="label_select_meshes">
             <property name="text">
              <string>选择对比网格:</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QListWidget" name="listWidget_comparison_meshes">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>200</height>
              </size>
             </property>
             <property name="selectionMode">
              <enum>QAbstractItemView::SelectionMode::MultiSelection</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_display_options">
             <property name="title">
              <string>显示选项</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_display">
              <item>
               <widget class="QCheckBox" name="checkBox_show_frequency">
                <property name="text">
                 <string>显示频率值</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="checkBox_show_convergence">
                <property name="text">
                 <string>显示收敛线</string>
                </property>
                <property name="checked">
                 <bool>true</bool>
                </property>
               </widget>
              </item>
              <item>
               <widget class="QCheckBox" name="checkBox_show_grid">
                <property name="text">
                 <string>显示网格线</string>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QPushButton" name="btn_export_results">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>40</height>
              </size>
             </property>
             <property name="text">
              <string>导出结果</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QGroupBox" name="groupBox_comparison_chart">
          <property name="title">
           <string>网格无关性分析图表</string>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_comparison_chart">
           <item>
            <widget class="QLabel" name="label_comparison_chart">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>600</height>
              </size>
             </property>
             <property name="text">
              <string>多网格结果对比图表区域
(将集成matplotlib画布)</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignmentFlag::AlignCenter</set>
             </property>
             <property name="styleSheet">
              <string>QLabel {
    border: 2px dashed #dcdfe6;
    border-radius: 6px;
    background-color: #f9f9f9;
    color: #7f8c8d;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_navigation">
      <property name="spacing">
       <number>15</number>
      </property>
      <item>
       <widget class="QPushButton" name="btn_generate_mesh">
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string>生成网格</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #2ecc71;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #27ae60;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_view_results">
        <property name="minimumSize">
         <size>
          <width>150</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string>查看结果</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    font-size: 14px;
    font-weight: bold;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_nav">
        <property name="orientation">
         <enum>Qt::Orientation::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="btn_previous">
        <property name="minimumSize">
         <size>
          <width>180</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string>上一步(前处理)</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #7f8c8d;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #6c7b7d;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_next">
        <property name="minimumSize">
         <size>
          <width>180</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string>下一步(连接设置)</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    font-size: 14px;
    font-weight: bold;
}</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="btn_main_menu">
        <property name="minimumSize">
         <size>
          <width>120</width>
          <height>50</height>
         </size>
        </property>
        <property name="text">
         <string>主菜单</string>
        </property>
        <property name="styleSheet">
         <string>QPushButton {
    background-color: #7f8c8d;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #6c7b7d;
}</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
 </widget>
</ui>
