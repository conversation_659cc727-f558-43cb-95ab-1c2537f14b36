# 振动传递计算软件 API 接口文档

本文档描述了振动传递计算软件与上游Electron应用程序的API接口。

## 1. 概述

振动传递计算软件提供了两种与上游Electron应用程序通信的方式：

1. **启动时配置文件加载**：上游应用在启动Python应用前，将参数写入临时配置文件。
2. **运行时API接口**：Python应用运行后，通过FastAPI提供的HTTP接口接收实时参数更新。

## 2. 启动时配置文件加载

### 2.1 配置文件路径

默认配置文件路径为项目根目录下的`temp/temp_startup_config.json`。

### 2.2 配置文件格式

```json
{
  "timeStep": 0.01,
  "endTime": 5.0,
  "forceOutputFolder": "D:/simulation_results/forces",
  "stiffnessCoefficient": 0.05,
  "massCoefficient": 0.01,
  "rotationSpeed": 1500,
  "rotationAxis": "z"
}
```

### 2.3 参数说明

| 参数名 | 类型 | 描述 | 必填 | 验证规则 |
|-------|-----|------|-----|---------|
| timeStep | float | 时间步长 | 是 | 必须大于0.0001且小于等于0.1 |
| endTime | float | 结束时间 | 是 | 必须大于0且大于timeStep |
| forceOutputFolder | string | 力输出文件夹路径 | 是 | 如果不存在会尝试创建 |
| stiffnessCoefficient | float | 刚度系数 | 否 | 必须大于等于0 |
| massCoefficient | float | 质量系数 | 否 | 必须大于等于0 |
| rotationSpeed | float | 旋转速度 | 否 | 必须大于等于0 |
| rotationAxis | string | 旋转轴(x/y/z) | 否 | 默认为"z" |

### 2.4 使用方法

1. 上游Electron应用在启动Python应用前，将参数写入配置文件
2. 启动Python应用
3. Python应用在启动时会自动检查并加载配置文件
4. 加载完成后，配置文件会被自动删除

## 3. 运行时API接口

### 3.1 API端点

- 基础URL: `http://127.0.0.1:8000`
- 参数更新端点: `/api/v1/simulation-params`
- 健康检查端点: `/health`

### 3.2 参数更新接口

#### 请求方法

`POST /api/v1/simulation-params`

#### 请求体

```json
{
  "timeStep": 0.01,
  "endTime": 5.0,
  "forceOutputFolder": "D:/simulation_results/forces",
  "stiffnessCoefficient": 0.05,
  "massCoefficient": 0.01,
  "rotationSpeed": 1500,
  "rotationAxis": "z"
}
```

参数说明同配置文件。

#### 响应

##### 成功响应

```json
{
  "status": "success",
  "message": "参数已更新",
  "data_received": {
    "timeStep": 0.01,
    "endTime": 5.0,
    "forceOutputFolder": "D:/simulation_results/forces",
    "stiffnessCoefficient": 0.05,
    "massCoefficient": 0.01,
    "rotationSpeed": 1500,
    "rotationAxis": "z"
  }
}
```

##### 警告响应

```json
{
  "status": "warning",
  "message": "服务器已接收参数，但未设置处理回调",
  "data_received": {
    "timeStep": 0.01,
    "endTime": 5.0,
    "forceOutputFolder": "D:/simulation_results/forces",
    "stiffnessCoefficient": 0.05,
    "massCoefficient": 0.01,
    "rotationSpeed": 1500,
    "rotationAxis": "z"
  }
}
```

##### 错误响应

```json
{
  "status": "error",
  "message": "参数验证失败: 结束时间必须大于时间步长",
  "data_received": {}
}
```

或

```json
{
  "status": "error",
  "message": "结束时间与时间步长比值过大，可能导致计算量过大",
  "data_received": {
    "timeStep": 0.0001,
    "endTime": 5.0,
    "forceOutputFolder": "D:/simulation_results/forces",
    "stiffnessCoefficient": 0.05,
    "massCoefficient": 0.01,
    "rotationSpeed": 1500,
    "rotationAxis": "z"
  }
}
```

### 3.3 健康检查接口

#### 请求方法

`GET /health`

#### 响应

```json
{
  "status": "ok",
  "service": "振动传递计算软件API"
}
```

## 4. 参数验证规则

API服务器对接收的参数进行以下验证：

1. **基本类型验证**
   - 时间步长(timeStep)必须是浮点数
   - 结束时间(endTime)必须是浮点数
   - 力输出文件夹路径(forceOutputFolder)必须是字符串

2. **值范围验证**
   - 时间步长(timeStep)必须大于0且小于等于结束时间
   - 结束时间(endTime)必须大于0且大于时间步长
   - 刚度系数(stiffnessCoefficient)必须大于等于0
   - 质量系数(massCoefficient)必须大于等于0
   - 旋转速度(rotationSpeed)必须大于等于0

3. **业务逻辑验证**
   - 结束时间与时间步长的比值不能过大(不超过10800)，以避免计算量过大
   - 力输出文件夹路径必须存在，如果不存在会尝试创建

## 5. 自动力文件验证

当API接收到力输出文件夹路径参数时，系统会自动执行以下操作：

1. 验证力文件夹中是否包含所需的所有文件：
   - yl-wall组：yl-fx.out, yl-fy.out, yl-fz.out
   - wk-wall组：wk-fx.out, wk-fy.out, wk-fz.out

2. 验证文件格式是否正确（.out文件格式）

3. 验证文件内容是否符合要求

4. 如果验证通过，自动设置力文件路径和相关参数

## 6. 注意事项

1. 确保Python应用启动后，API服务器在127.0.0.1:8000端口运行
2. 如果端口被占用，API服务器将无法启动
3. 配置文件路径可以在`ctrl/startup_config.py`中修改
4. API服务器的端口可以在`qt.py`的`initialize_application`函数中修改
5. 当参数验证失败时，API会返回详细的错误信息
6. 力文件夹路径必须包含所有必需的力文件，否则验证将失败 