# -*- coding: utf-8 -*-

# ====================================================================
#
#  Ansys Mechanical "Four-in-One" Automated Script - IronPython-Safe Version
#
# ====================================================================

import collections
import re
import json
import os
import io
import logging

# ====================================================================
# Robust Logging Setup
# ====================================================================
LOG_FILE_PATH = r"D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/output/ansys_automation_log.log"

logger = logging.getLogger('ansys_automation_script')
logger.setLevel(logging.INFO)

if not logger.handlers:
    try:
        log_dir = os.path.dirname(LOG_FILE_PATH)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        file_handler = logging.FileHandler(LOG_FILE_PATH, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        logger.addHandler(file_handler)
        
    except Exception as e:
        print("CRITICAL: Failed to initialize logger. Error: {}".format(e))

# ====================================================================
# Helper Classes and Functions
# ====================================================================

def sanitize_for_log(obj):
    """Converts an object to a string and removes problematic characters for logging."""
    try:
        s = str(obj)
        return s.replace('\x00', '')
    except Exception:
        return "[Unloggable Object]"

def parse_array_string(array_string):
    """Converts 'Array[int]((...))' format string to a Python list."""
    match = re.match(r"Array\[int\]\(\((.*?)\)\)", array_string)
    if match:
        content = match.group(1)
        if not content:
            return []
        return [int(x) for x in content.split(",")]
    return array_string

class SelectionManagerWrapper:
    """Wraps Named Selection retrieval, focusing only on face-based selections."""
    def __init__(self, ExtAPI):
        self.ExtAPI = ExtAPI
        self.Model = ExtAPI.DataModel.Project.Model
        self.GeoData = ExtAPI.DataModel.GeoData

    def get_face_selections_dict(self):
        """Gets a dictionary of all face-only Named Selections."""
        all_ns_dict = {ns.Name: ns.Ids for ns in self.Model.NamedSelections.Children}
        face_selections_dict = {}

        for name, ids in all_ns_dict.items():
            if not ids:
                continue

            is_exclusively_faces = True
            for entity_id in ids:
                try:
                    if self.GeoData.GeoEntityById(entity_id).Type != GeoCellTypeEnum.GeoFace:
                        is_exclusively_faces = False
                        break
                except Exception:
                    is_exclusively_faces = False
                    logger.warning("Could not validate entity ID %d in Named Selection '%s'.", entity_id, sanitize_for_log(name))
                    break

            if is_exclusively_faces:
                face_selections_dict[name] = str(ids)

        return face_selections_dict

# ====================================================================
# Core Function Area
# ====================================================================

def clean_numeric_named_selections():
    """Task 1: Clean up all numerically named Named Selections."""
    logger.info("Task 1: Starting cleanup of numeric Named Selections.")
    selections_to_delete = []
    model = ExtAPI.DataModel.Project.Model

    try:
        all_named_selections = model.NamedSelections.Children
    except AttributeError:
        logger.error("Could not access Named Selections. Please ensure the model is loaded correctly.")
        return

    logger.info("Scanning list of Named Selections...")
    for ns in all_named_selections:
        if ns.Name.replace(":", "").isdigit():
            logger.info("  > Marked for deletion: %s", sanitize_for_log(ns.Name))
            selections_to_delete.append(ns)

    if not selections_to_delete:
        logger.info("No numeric Named Selections found to clean up.")
    else:
        logger.info("Starting deletion process for %d item(s)...", len(selections_to_delete))
        for ns_to_delete in selections_to_delete:
            try:
                logger.info("  > Deleting: %s", sanitize_for_log(ns_to_delete.Name))
                ns_to_delete.Delete()
            except Exception as e:
                logger.error("Error while deleting '%s': %s", sanitize_for_log(ns_to_delete.Name), sanitize_for_log(e))
        logger.info("Cleanup complete!")

def create_or_update_ns_by_body_name(name_to_find, ns_name_override=None):
    """Task 2: Create a Named Selection based on a body name."""
    logger.info("Task 2: Creating/updating Named Selection for '%s'", sanitize_for_log(name_to_find))
    body_ids = []
    for assembly in DataModel.GeoData.Assemblies:
        for part in assembly.Parts:
            for body in part.Bodies:
                if body.Name.split('\\')[-1] == name_to_find:
                    body_ids.append(body.Id)
                    logger.info("  > Found matching body: %s (ID: %d)", sanitize_for_log(body.Name), body.Id)

    if not body_ids:
        logger.warning("-> Result: No bodies named '%s' were found.", sanitize_for_log(name_to_find))
        return

    final_ns_name = ns_name_override if ns_name_override else name_to_find
    logger.info("-> Result: Found %d matching bodies. Operating on Named Selection '%s'", len(body_ids), sanitize_for_log(final_ns_name))

    model = ExtAPI.DataModel.Project.Model
    logger.info("  > Searching for older version of '%s' (case-insensitive)...", sanitize_for_log(final_ns_name))

    for ns in list(model.NamedSelections.Children):
        if ns.Name.lower() == final_ns_name.lower():
            logger.info("  > Found and deleted old Named Selection: '%s'", sanitize_for_log(ns.Name))
            ns.Delete()

    logger.info("  > Creating new Named Selection...")
    selection = ExtAPI.SelectionManager.CreateSelectionInfo(SelectionTypeEnum.GeometryEntities)
    selection.Ids = body_ids
    new_ns = model.NamedSelections.AddNamedSelection()
    new_ns.Name = final_ns_name
    new_ns.Location = selection

    logger.info("-> Task complete!")

def unify_named_selections_to_lowercase():
    """Task 3: Unify all Named Selection names to lowercase."""
    logger.info("Task 3: Starting to unify Named Selection names to lowercase.")
    model = ExtAPI.DataModel.Project.Model
    target_name_groups = collections.defaultdict(list)
    
    all_ns_objects = list(model.NamedSelections.Children)
    if not all_ns_objects:
        logger.info("No Named Selections found in the model.")
        return

    for ns in all_ns_objects:
        target_name_groups[ns.Name.lower()].append(ns)

    items_to_rename = []
    conflicts_found = False
    logger.info("Analyzing for naming conflicts...")
    for target_name, original_items in target_name_groups.items():
        if len(original_items) > 1:
            conflicts_found = True
            original_names = [item.Name for item in original_items]
            logger.warning("  -> Conflict detected! Target name '%s' corresponds to multiple sources: %s", sanitize_for_log(target_name), sanitize_for_log(original_names))
        else:
            ns_to_check = original_items[0]
            if ns_to_check.Name != target_name:
                items_to_rename.append(ns_to_check)

    if not items_to_rename and not conflicts_found:
        logger.info("All Named Selection names already conform to the format or have no conflicts. No changes needed.")
        return

    if conflicts_found:
        logger.warning("Skipped all conflicting Named Selections. Please resolve them manually to remove ambiguity.")

    if items_to_rename:
        logger.info("Starting rename operation...")
        for ns in items_to_rename:
            old_name, new_name = ns.Name, ns.Name.lower()
            try:
                logger.info("  > Renaming '%s' to '%s'", sanitize_for_log(old_name), sanitize_for_log(new_name))
                ns.Name = new_name
            except Exception as e:
                logger.error("An unexpected error occurred while renaming '%s': %s", sanitize_for_log(old_name), sanitize_for_log(e))
        logger.info("Rename operation complete!")
    else:
        logger.info("No items could be safely renamed.")

def export_specified_face_selections(target_names, output_path):
    """Task 4: Export specified face-based Named Selections to a JSON file."""
    logger.info("Task 4: Starting export of specified face selections.")

    selection_manager = SelectionManagerWrapper(ExtAPI)
    available_face_selections = selection_manager.get_face_selections_dict()
    logger.info("Found %d face-only Named Selections in the current model.", len(available_face_selections))

    final_output_dict = {}
    logger.info("Finding and processing specified names...")
    for name in target_names:
        if name in available_face_selections:
            id_string = available_face_selections[name]
            final_output_dict[name] = parse_array_string(id_string)
            logger.info("  > Found '%s', contains %d face entities.", sanitize_for_log(name), len(final_output_dict[name]))
        else:
            final_output_dict[name] = None
            logger.warning("  > Did not find '%s', setting to None.", sanitize_for_log(name))

    try:
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            logger.info("Created output directory: %s", sanitize_for_log(output_dir))

        with io.open(output_path, "w", encoding='utf-8') as json_file:
            json.dump(final_output_dict, json_file, ensure_ascii=False, indent=4)
        logger.info("-> Task complete! JSON file has been saved to: %s", sanitize_for_log(output_path))
    except Exception as e:
        logger.error("-> Error while saving JSON file: %s", sanitize_for_log(e))

# ====================================================================
# Main Execution Block
# ====================================================================

# ------------------- Configuration Area -------------------
TARGET_SELECTION_NAMES = [
    "bushing_1", "bushing_2", "bushing_3", "bushing_4",
    "bearing_1", "bearing_2",
    "flange1", "flange2",
    "dianji_1", "dianji_2",
    "yl-wall",
    "wk-wall"
]
OUTPUT_FILE_PATH = r"D:/data/output/face_selections.json"
# ----------------------------------------------------------

try:
    logger.info("======== Starting Four-in-One Automation Script ========")

    if 'ExtAPI' not in globals() or 'DataModel' not in globals():
        raise NameError("Critical Ansys API objects (ExtAPI, DataModel) are not defined in the current environment. Please ensure this script is run within Ansys Mechanical.")

    # Step 1: Cleanup
    clean_numeric_named_selections()

    # Step 2: Create
    create_or_update_ns_by_body_name("ROTOR")

    # Step 3: Standardize
    unify_named_selections_to_lowercase()

    # Step 4: Export
    export_specified_face_selections(TARGET_SELECTION_NAMES, OUTPUT_FILE_PATH)

    # Finally, refresh the UI
    ExtAPI.DataModel.Tree.Refresh()
    logger.info("UI Tree has been refreshed.")

    logger.info("======== All automation tasks have been executed successfully ========")

except Exception as e:
    logger.error("A critical error occurred during script execution. Process terminated.", exc_info=True)