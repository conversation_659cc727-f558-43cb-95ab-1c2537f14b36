"""
全局异常处理器模块

此模块实现了全局异常捕获和处理机制，主要功能包括：
1. 捕获所有未处理的异常
2. 统一格式记录异常日志
3. 通过信号机制通知UI显示友好的错误提示
4. 支持线程安全的异常处理

作者: [作者名]
日期: [日期]
"""

import sys
import traceback
import logging
import os
import datetime
from typing import Dict, Any, Optional, Callable, Type, List

from PySide6.QtCore import QObject, Signal

# 获取模块日志记录器
logger = logging.getLogger(__name__)

# 异常严重性级别
class ExceptionSeverity:
    INFO = "信息"
    WARNING = "警告"
    ERROR = "错误"
    CRITICAL = "严重错误"

# 异常类型映射
EXCEPTION_MAPPINGS = {
    # 文件操作异常
    FileNotFoundError: {"severity": ExceptionSeverity.ERROR, "title": "文件未找到", "message": "无法找到指定的文件: {filename}"},
    PermissionError: {"severity": ExceptionSeverity.ERROR, "title": "权限错误", "message": "没有足够的权限执行操作: {operation}"},
    
    # 网络异常
    ConnectionError: {"severity": ExceptionSeverity.ERROR, "title": "连接错误", "message": "无法连接到服务器或API"},
    TimeoutError: {"severity": ExceptionSeverity.WARNING, "title": "连接超时", "message": "连接超时，请检查网络状态"},
    
    # 数据处理异常
    ValueError: {"severity": ExceptionSeverity.ERROR, "title": "数据错误", "message": "输入数据无效: {detail}"},
    TypeError: {"severity": ExceptionSeverity.ERROR, "title": "类型错误", "message": "数据类型不匹配: {detail}"},
    KeyError: {"severity": ExceptionSeverity.ERROR, "title": "键错误", "message": "访问了不存在的键: {detail}"},
    IndexError: {"severity": ExceptionSeverity.ERROR, "title": "索引错误", "message": "访问了无效的索引位置"},
    
    # 默认异常
    Exception: {"severity": ExceptionSeverity.ERROR, "title": "程序错误", "message": "发生了未知错误: {detail}"}
}

# 自定义异常基类
class ApplicationError(Exception):
    """应用程序自定义异常基类"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message)
        self.details = details or {}
        self.severity = ExceptionSeverity.ERROR
        self.title = "应用程序错误"

# 常见自定义异常
class ConfigurationError(ApplicationError):
    """配置错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, details)
        self.severity = ExceptionSeverity.ERROR
        self.title = "配置错误"

class FileOperationError(ApplicationError):
    """文件操作错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, details)
        self.severity = ExceptionSeverity.ERROR
        self.title = "文件操作错误"

class ValidationError(ApplicationError):
    """数据验证错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, details)
        self.severity = ExceptionSeverity.WARNING
        self.title = "数据验证错误"

class APIError(ApplicationError):
    """API调用错误"""
    def __init__(self, message: str, details: Dict[str, Any] = None):
        super().__init__(message, details)
        self.severity = ExceptionSeverity.ERROR
        self.title = "API调用错误"

# 异常信号发射器
class ExceptionSignaler(QObject):
    """异常信号发射器，用于将异常信息传递给UI线程"""
    
    # 定义信号
    exception_caught = Signal(str, str, str, str)  # 严重性, 标题, 消息, 详细信息
    
    # 单例实例
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ExceptionSignaler, cls).__new__(cls)
        return cls._instance

# 创建信号发射器实例
exception_signaler = ExceptionSignaler()

def format_exception_details(exc_type: Type[Exception], exc_value: Exception, exc_traceback) -> str:
    """格式化异常详细信息
    
    Args:
        exc_type: 异常类型
        exc_value: 异常值
        exc_traceback: 异常堆栈
        
    Returns:
        str: 格式化的异常详细信息
    """
    # 获取异常堆栈信息
    tb_list = traceback.format_exception(exc_type, exc_value, exc_traceback)
    
    # 如果是自定义异常，添加详细信息
    if isinstance(exc_value, ApplicationError) and exc_value.details:
        details_str = "\n详细信息:\n"
        for key, value in exc_value.details.items():
            details_str += f"  {key}: {value}\n"
        tb_list.append(details_str)
    
    return "".join(tb_list)

def get_exception_info(exc_type: Type[Exception], exc_value: Exception) -> Dict[str, str]:
    """获取异常信息
    
    Args:
        exc_type: 异常类型
        exc_value: 异常值
        
    Returns:
        Dict[str, str]: 包含严重性、标题和消息的字典
    """
    # 如果是自定义异常，使用其属性
    if isinstance(exc_value, ApplicationError):
        message = str(exc_value)
        return {
            "severity": exc_value.severity,
            "title": exc_value.title,
            "message": message
        }
    
    # 查找异常映射
    for exc_class, info in EXCEPTION_MAPPINGS.items():
        if issubclass(exc_type, exc_class):
            # 格式化消息，替换占位符
            message = info["message"]
            
            # 尝试从异常值中提取详细信息
            try:
                if "{filename}" in message and hasattr(exc_value, "filename"):
                    message = message.replace("{filename}", str(exc_value.filename))
                
                if "{operation}" in message and hasattr(exc_value, "strerror"):
                    message = message.replace("{operation}", str(exc_value.strerror))
                
                if "{detail}" in message:
                    message = message.replace("{detail}", str(exc_value))
            except:
                # 如果提取失败，使用原始异常消息
                message = str(exc_value)
            
            return {
                "severity": info["severity"],
                "title": info["title"],
                "message": message
            }
    
    # 默认使用通用异常信息
    return {
        "severity": ExceptionSeverity.ERROR,
        "title": "程序错误",
        "message": f"发生了未知错误: {str(exc_value)}"
    }

def global_exception_handler(exc_type: Type[Exception], exc_value: Exception, exc_traceback):
    """全局异常处理函数
    
    Args:
        exc_type: 异常类型
        exc_value: 异常值
        exc_traceback: 异常堆栈
    """
    # 忽略KeyboardInterrupt异常
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 格式化异常详细信息
    exception_details = format_exception_details(exc_type, exc_value, exc_traceback)
    
    # 获取异常信息
    exc_info = get_exception_info(exc_type, exc_value)
    
    # 根据异常严重性级别选择日志级别
    if exc_info["severity"] == ExceptionSeverity.CRITICAL:
        logger.critical(f"{exc_info['title']}: {exc_info['message']}\n{exception_details}")
    elif exc_info["severity"] == ExceptionSeverity.ERROR:
        logger.error(f"{exc_info['title']}: {exc_info['message']}\n{exception_details}")
    elif exc_info["severity"] == ExceptionSeverity.WARNING:
        logger.warning(f"{exc_info['title']}: {exc_info['message']}\n{exception_details}")
    else:
        logger.info(f"{exc_info['title']}: {exc_info['message']}\n{exception_details}")
    
    # 发送异常信号
    try:
        exception_signaler.exception_caught.emit(
            exc_info["severity"],
            exc_info["title"],
            exc_info["message"],
            exception_details
        )
    except Exception as e:
        # 如果信号发送失败，记录错误并回退到标准错误处理
        logger.error(f"发送异常信号失败: {str(e)}")
        sys.__excepthook__(exc_type, exc_value, exc_traceback)

def thread_exception_handler(args):
    """线程异常处理函数
    
    Args:
        args: 包含线程和异常信息的元组
    """
    # 解包参数
    thread, exc_tuple = args
    
    # 记录线程信息
    logger.error(f"线程 '{thread.name}' 中发生异常")
    
    # 调用全局异常处理函数
    global_exception_handler(exc_tuple[0], exc_tuple[1], exc_tuple[2])

def setup_exception_handling():
    """设置全局异常处理
    
    这个函数应该在应用程序启动时调用
    
    Returns:
        ExceptionSignaler: 异常信号发射器实例
    """
    # 设置主线程异常处理
    sys.excepthook = global_exception_handler
    
    # 设置子线程异常处理
    sys.threading = __import__('threading')
    sys.threading._threading_excepthook = thread_exception_handler
    sys.threading.excepthook = sys.threading._threading_excepthook
    
    logger.info("全局异常处理已设置")
    
    return exception_signaler 