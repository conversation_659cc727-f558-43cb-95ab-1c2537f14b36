"""
模态分析图表组件

此模块定义了专门用于模态分析结果显示的matplotlib图表组件：
1. 集成matplotlib与PySide6
2. 支持三种图表类型：频率对比、模态分布、网格收敛性分析
3. 提供中文字体支持和交互功能
4. 支持图表保存和导出

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import logging
import numpy as np
from typing import List, Dict, Any, Optional
from PySide6.QtWidgets import QWidget, QVBoxLayout, QSizePolicy
from PySide6.QtCore import Signal

# 导入数据管理器
try:
    from .modal_data_manager import ModalDataManager, ModalResult
    DATA_MANAGER_AVAILABLE = True
except ImportError:
    DATA_MANAGER_AVAILABLE = False
    ModalDataManager = None
    ModalResult = None

try:
    import matplotlib
    # 尝试不同的后端配置
    try:
        matplotlib.use('Qt5Agg')  # 首选Qt5Agg
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    except ImportError:
        try:
            matplotlib.use('TkAgg')  # 备选TkAgg
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg as FigureCanvas
        except ImportError:
            matplotlib.use('Agg')  # 最后选择Agg（无GUI）
            from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas

    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure
    import matplotlib.font_manager as fm
    import platform

    # 配置中文字体支持
    def setup_chinese_font_advanced():
        """改进的matplotlib中文字体配置"""
        try:
            system = platform.system()
            logging.info(f"检测到操作系统: {system}")

            if system == "Windows":
                # Windows系统字体（按优先级排序）
                chinese_fonts = [
                    'Microsoft YaHei',  # 微软雅黑（推荐）
                    'SimHei',           # 黑体
                    'SimSun',           # 宋体
                    'KaiTi',            # 楷体
                    'FangSong'          # 仿宋
                ]
            elif system == "Darwin":  # macOS
                # macOS系统字体
                chinese_fonts = [
                    'PingFang SC',
                    'Hiragino Sans GB',
                    'STHeiti',
                    'Arial Unicode MS',
                    'STSong'
                ]
            else:  # Linux
                # Linux系统字体
                chinese_fonts = [
                    'WenQuanYi Micro Hei',
                    'WenQuanYi Zen Hei',
                    'Noto Sans CJK SC',
                    'Source Han Sans SC',
                    'AR PL UMing CN'
                ]

            # 测试中文字符
            test_chinese_chars = "导入当前模态分析"

            # 尝试设置中文字体
            for font_name in chinese_fonts:
                try:
                    logging.debug(f"测试字体: {font_name}")

                    # 检查字体是否存在
                    font_prop = fm.FontProperties(family=font_name)
                    font_path = fm.findfont(font_prop)

                    # 验证字体路径有效且不是DejaVu（不支持中文）
                    if font_path and 'DejaVu' not in font_path and 'default' not in font_path.lower():
                        # 尝试使用该字体渲染中文字符
                        try:
                            # 导入matplotlib.pyplot
                            import matplotlib.pyplot as plt

                            # 临时设置字体
                            original_font = plt.rcParams['font.sans-serif'].copy()
                            plt.rcParams['font.sans-serif'] = [font_name]
                            plt.rcParams['axes.unicode_minus'] = False

                            # 创建测试图形验证中文渲染
                            fig, ax = plt.subplots(figsize=(1, 1))
                            ax.text(0.5, 0.5, test_chinese_chars, fontsize=12)
                            plt.close(fig)

                            # 如果没有异常，说明字体可用
                            logging.info(f"✅ 成功配置中文字体: {font_name}")
                            logging.info(f"字体路径: {font_path}")
                            return True

                        except Exception as render_error:
                            # 恢复原始字体设置
                            plt.rcParams['font.sans-serif'] = original_font
                            logging.debug(f"字体 {font_name} 渲染测试失败: {render_error}")
                            continue
                    else:
                        logging.debug(f"字体 {font_name} 路径无效或为DejaVu: {font_path}")

                except Exception as font_error:
                    logging.debug(f"字体 {font_name} 检测失败: {font_error}")
                    continue

            # 如果没有找到合适的中文字体，使用降级方案
            logging.warning("⚠️ 未找到可用的中文字体，将使用英文标签")
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            return False

        except Exception as e:
            logging.error(f"❌ 中文字体配置失败: {str(e)}")
            # 最基本的降级配置
            try:
                plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass
            return False

    # 初始化中文字体
    CHINESE_FONT_AVAILABLE = setup_chinese_font_advanced()

    def process_chart_labels(labels, chinese_available=True):
        """处理图表标签，根据字体可用性调整显示"""
        if chinese_available:
            return labels

        # 中文字体不可用时，转换为英文标签
        processed_labels = []
        for label in labels:
            # 替换中文标记
            english_label = label.replace('[导入]', '[Imported]')
            english_label = english_label.replace('[当前]', '[Current]')
            english_label = english_label.replace('导入', 'Imported')
            english_label = english_label.replace('当前', 'Current')
            english_label = english_label.replace('模态', 'Modal')
            english_label = english_label.replace('分析', 'Analysis')
            english_label = english_label.replace('参考', 'Reference')
            english_label = english_label.replace('模型', 'Model')
            english_label = english_label.replace('网格', 'Mesh')
            english_label = english_label.replace('精细', 'Fine')
            english_label = english_label.replace('粗糙', 'Coarse')
            english_label = english_label.replace('中等', 'Medium')
            processed_labels.append(english_label)

        return processed_labels

    # 抑制字体和matplotlib相关警告
    import warnings

    def setup_warning_filters():
        """设置matplotlib相关警告过滤器"""
        # 抑制字体管理器警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.font_manager')
        # 抑制字形缺失警告（更精确的模式匹配）
        warnings.filterwarnings('ignore', message=r'.*Glyph \d+ .*missing from font.*')
        warnings.filterwarnings('ignore', message='.*missing from font.*DejaVu.*')
        # 抑制颜色属性覆盖警告
        warnings.filterwarnings('ignore', message='.*Setting the.*color.*property will override.*')
        # 抑制所有matplotlib相关警告
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.pyplot')
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.backends')
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.figure')
        warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib.text')
        # 抑制findfont相关警告
        warnings.filterwarnings('ignore', message='.*findfont.*')
        warnings.filterwarnings('ignore', message='.*font family.*not found.*')
        # 抑制所有包含CJK字符的警告
        warnings.filterwarnings('ignore', message='.*CJK UNIFIED IDEOGRAPH.*')

    # 应用警告过滤器
    setup_warning_filters()

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    FigureCanvas = QWidget  # 降级处理

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class ModalChartWidget(FigureCanvas if MATPLOTLIB_AVAILABLE else QWidget):
    """模态分析图表组件类"""
    
    # 自定义信号
    chart_updated = Signal(str)  # 图表更新信号，传递图表类型
    
    def __init__(self, parent=None, data_manager=None):
        """初始化图表组件"""
        # 初始化基本属性
        self.current_chart_type = "frequency_comparison"
        self.current_data = []
        self.chart_options = {
            'show_frequencies': True,
            'show_mode_shapes': True,
            'show_mesh_info': True,
            'show_imported': True,
            'show_current': True
        }

        # 数据管理器
        self.data_manager = data_manager

        if not MATPLOTLIB_AVAILABLE:
            super().__init__(parent)
            self._setup_fallback_ui()
            logger.warning("matplotlib不可用，使用降级UI")
            return

        try:
            # 创建matplotlib图形
            self.figure = Figure(figsize=(10, 6), dpi=100)
            super().__init__(self.figure)
            self.setParent(parent)

            # 设置组件属性
            FigureCanvas.setSizePolicy(self, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            FigureCanvas.updateGeometry(self)

            # 确认字体配置
            self._confirm_font_setup()

            # 初始化图表
            self._setup_initial_chart()

            logger.debug("模态分析图表组件初始化完成")

        except Exception as e:
            logger.error(f"图表组件初始化失败: {str(e)}", exc_info=True)
            # 降级到基本QWidget
            super().__init__(parent)
            self._setup_fallback_ui()
    
    def _setup_fallback_ui(self, error_msg="matplotlib不可用"):
        """设置降级UI（当matplotlib不可用时）"""
        try:
            layout = QVBoxLayout(self)
            from PySide6.QtWidgets import QLabel
            from PySide6.QtCore import Qt

            label = QLabel(f"{error_msg}\n请安装matplotlib以显示图表")
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("color: #999; font-size: 14px; padding: 20px;")
            layout.addWidget(label)

            logger.warning(f"使用降级UI: {error_msg}")
        except Exception as e:
            logger.error(f"降级UI设置失败: {str(e)}", exc_info=True)
    
    def _confirm_font_setup(self):
        """确认字体配置状态"""
        try:
            current_font = plt.rcParams['font.sans-serif'][0] if plt.rcParams['font.sans-serif'] else 'Unknown'

            if CHINESE_FONT_AVAILABLE:
                logger.info(f"✅ 中文字体可用，当前字体: {current_font}")
                self.chinese_font_available = True
            else:
                logger.warning(f"⚠️ 中文字体不可用，使用英文字体: {current_font}")
                self.chinese_font_available = False
                # 确保使用英文字体
                plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
                plt.rcParams['axes.unicode_minus'] = False

            # 设置图表样式
            plt.style.use('default')

            logger.debug("字体配置确认完成")

        except Exception as e:
            logger.error(f"字体配置确认失败: {str(e)}")
            self.chinese_font_available = False
            # 最基本的降级配置
            try:
                plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
            except:
                pass

    
    def _setup_initial_chart(self):
        """设置初始图表"""
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            
            # 显示欢迎信息
            ax.text(0.5, 0.5, 'Modal Analysis Results Chart\n\nPlease select mesh results and click "Update Chart"',
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=14, color='#666')
            
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')
            
            self.figure.tight_layout()
            self.draw()
            
        except Exception as e:
            logger.error(f"初始图表设置失败: {str(e)}", exc_info=True)
    
    def update_chart(self, chart_type: str, mesh_data: List[Dict[str, Any]], options: Dict[str, bool] = None):
        """更新图表显示

        Args:
            chart_type: 图表类型 ('frequency_comparison', 'mode_distribution', 'mesh_convergence')
            mesh_data: 网格数据列表
            options: 显示选项
        """
        if not MATPLOTLIB_AVAILABLE:
            return

        try:
            # 在图表更新期间抑制matplotlib警告
            import warnings
            with warnings.catch_warnings():
                warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
                warnings.filterwarnings('ignore', message=r'.*Glyph \d+ .*missing from font.*')
                warnings.filterwarnings('ignore', message='.*CJK UNIFIED IDEOGRAPH.*')
                warnings.filterwarnings('ignore', message='.*Setting the.*color.*property will override.*')

                self.current_chart_type = chart_type
                self.current_data = mesh_data
                if options:
                    self.chart_options.update(options)

                # 更新数据管理器中的当前结果
                if self.data_manager:
                    self.data_manager.update_current_results(mesh_data)

                # 清空图表
                self.figure.clear()
            
                if not mesh_data:
                    self._show_no_data_message()
                    return

                # 根据图表类型绘制
                if chart_type == "frequency_comparison":
                    self._draw_frequency_comparison(mesh_data)
                elif chart_type == "mode_distribution":
                    self._draw_mode_distribution(mesh_data)
                elif chart_type == "mesh_convergence":
                    self._draw_mesh_convergence(mesh_data)
                else:
                    self._show_unsupported_chart_message(chart_type)

                self.figure.tight_layout()
                self.draw()

                # 发出更新信号
                self.chart_updated.emit(chart_type)

                logger.info(f"图表更新完成: {chart_type}, 数据量: {len(mesh_data)}")
            
        except Exception as e:
            logger.error(f"图表更新失败: {str(e)}", exc_info=True)
            self._show_error_message(str(e))

    def _get_all_chart_data(self, current_data: List[Dict[str, Any]]):
        """获取所有图表数据（直接使用传入的混合数据）"""
        # 现在current_data已经包含了当前结果和导入结果的混合数据
        # 每个数据项都有'source'字段标识数据来源
        all_data = []

        for data in current_data:
            chart_data = data.copy()

            # 根据数据来源设置显示样式
            if chart_data.get('source') == 'imported':
                chart_data['display_style'] = 'dashed'
            else:
                chart_data['display_style'] = 'solid'

            all_data.append(chart_data)

        return all_data

    def _draw_frequency_comparison(self, mesh_data: List[Dict[str, Any]]):
        """绘制频率对比图（支持当前+导入结果对比）"""
        ax = self.figure.add_subplot(111)

        # 获取所有数据（当前+导入）
        all_data = self._get_all_chart_data(mesh_data)

        if not all_data:
            self._show_no_data_message()
            return

        # 准备数据
        mesh_names = [data['name'] for data in all_data]
        mesh_sizes = [data['size'] for data in all_data]

        # 获取所有频率数据
        all_frequencies = []
        max_modes = 0
        for data in all_data:
            frequencies = data.get('frequencies', [])
            all_frequencies.append(frequencies)
            max_modes = max(max_modes, len(frequencies))

        if max_modes == 0:
            self._show_no_frequency_data_message()
            return
        
        # 创建柱状图
        x = np.arange(len(mesh_names))
        width = 0.8 / min(max_modes, 5)  # 最多显示前5阶
        
        colors = plt.cm.Set3(np.linspace(0, 1, min(max_modes, 5)))
        
        for mode_idx in range(min(max_modes, 5)):
            frequencies_for_mode = []
            alphas = []
            edge_colors = []

            for i, (freq_list, data) in enumerate(zip(all_frequencies, all_data)):
                if mode_idx < len(freq_list):
                    frequencies_for_mode.append(freq_list[mode_idx])
                else:
                    frequencies_for_mode.append(0)

                # 根据数据源设置样式
                if data.get('source') == 'imported':
                    alphas.append(0.6)  # 导入数据透明度较低
                    edge_colors.append('black')  # 导入数据有黑色边框
                else:
                    alphas.append(0.8)  # 当前数据透明度较高
                    edge_colors.append('none')  # 当前数据无边框

            offset = (mode_idx - min(max_modes, 5)/2 + 0.5) * width

            # 绘制柱状图
            bars = ax.bar(x + offset, frequencies_for_mode, width,
                         label=f'Mode {mode_idx+1}', color=colors[mode_idx])

            # 根据数据源设置柱状图样式
            for i, (bar, data) in enumerate(zip(bars, all_data)):
                if data.get('source') == 'imported':
                    bar.set_alpha(0.6)  # 导入数据透明度较低
                    bar.set_edgecolor('black')  # 导入数据有黑色边框
                    bar.set_linewidth(1.5)
                else:
                    bar.set_alpha(0.8)  # 当前数据透明度较高
                    bar.set_edgecolor('none')  # 当前数据无边框
            
            # 添加数值标签
            if self.chart_options.get('show_frequencies', True):
                for i, freq in enumerate(frequencies_for_mode):
                    if freq > 0:
                        ax.text(x[i] + offset, freq + max([max(f) if f else 0 for f in all_frequencies])*0.01,
                               f'{freq:.1f}', ha='center', va='bottom', fontsize=8)
        
        # 设置图表属性
        ax.set_xlabel('Mesh Scheme')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_title('Modal Frequency Comparison Analysis')
        ax.set_xticks(x)
        
        # 设置x轴标签（智能处理中文）
        if self.chart_options.get('show_mesh_info', True):
            labels = [f'{name}\n({size:.1f}mm)' for name, size in zip(mesh_names, mesh_sizes)]
        else:
            labels = mesh_names

        # 根据字体可用性处理标签
        processed_labels = process_chart_labels(labels, getattr(self, 'chinese_font_available', CHINESE_FONT_AVAILABLE))
        ax.set_xticklabels(processed_labels, rotation=45, ha='right')
        
        # 创建组合图例
        handles, labels = ax.get_legend_handles_labels()

        # 添加数据源图例
        has_current = any(data.get('source') != 'imported' for data in all_data)
        has_imported = any(data.get('source') == 'imported' for data in all_data)

        if has_current or has_imported:
            from matplotlib.patches import Patch
            source_handles = []
            if has_current:
                source_handles.append(Patch(facecolor='gray', alpha=0.8, label='Current Results'))
            if has_imported:
                source_handles.append(Patch(facecolor='gray', alpha=0.6,
                                          edgecolor='black', linewidth=1.5, label='Imported Results'))
            handles.extend(source_handles)

        ax.legend(handles=handles, bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
    
    def _draw_mode_distribution(self, mesh_data: List[Dict[str, Any]]):
        """绘制模态分布图"""
        ax = self.figure.add_subplot(111)
        
        # 准备数据
        frequency_ranges = [(0, 100), (100, 300), (300, 500), (500, 1000), (1000, float('inf'))]
        range_labels = ['0-100Hz', '100-300Hz', '300-500Hz', '500-1000Hz', '>1000Hz']
        
        mesh_names = [data['name'] for data in mesh_data]
        distribution_data = []
        
        for data in mesh_data:
            frequencies = data.get('frequencies', [])
            distribution = []
            
            for freq_min, freq_max in frequency_ranges:
                count = sum(1 for f in frequencies if freq_min <= f < freq_max)
                distribution.append(count)
            
            distribution_data.append(distribution)
        
        # 创建堆叠柱状图
        x = np.arange(len(mesh_names))
        width = 0.6
        colors = plt.cm.Set2(np.linspace(0, 1, len(range_labels)))
        
        bottom = np.zeros(len(mesh_names))
        for i, (label, color) in enumerate(zip(range_labels, colors)):
            values = [dist[i] for dist in distribution_data]
            bars = ax.bar(x, values, width, bottom=bottom, label=label, color=color, alpha=0.8)
            
            # 添加数值标签
            if self.chart_options.get('show_mode_shapes', True):
                for j, bar in enumerate(bars):
                    height = bar.get_height()
                    if height > 0:
                        ax.text(bar.get_x() + bar.get_width()/2., 
                               bottom[j] + height/2, f'{int(height)}',
                               ha='center', va='center', fontsize=9, fontweight='bold')
            
            bottom += values
        
        # 设置图表属性
        ax.set_xlabel('Mesh Scheme')
        ax.set_ylabel('Modal Count')
        ax.set_title('Modal Frequency Distribution Analysis')
        ax.set_xticks(x)
        # 处理x轴标签
        processed_mesh_names = process_chart_labels(mesh_names, getattr(self, 'chinese_font_available', CHINESE_FONT_AVAILABLE))
        ax.set_xticklabels(processed_mesh_names, rotation=45, ha='right')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3, axis='y')
    
    def _draw_mesh_convergence(self, mesh_data: List[Dict[str, Any]]):
        """绘制网格收敛性分析图"""
        ax = self.figure.add_subplot(111)
        
        # 按网格尺寸排序
        sorted_data = sorted(mesh_data, key=lambda x: x['size'])
        
        mesh_sizes = [data['size'] for data in sorted_data]
        mesh_names = [data['name'] for data in sorted_data]
        
        # 绘制前几阶频率的收敛性
        colors = plt.cm.tab10(np.linspace(0, 1, 5))
        
        for mode_idx in range(5):  # 显示前5阶
            frequencies = []
            valid_sizes = []
            valid_names = []
            
            for i, data in enumerate(sorted_data):
                freq_list = data.get('frequencies', [])
                if mode_idx < len(freq_list):
                    frequencies.append(freq_list[mode_idx])
                    valid_sizes.append(mesh_sizes[i])
                    valid_names.append(mesh_names[i])
            
            if frequencies:
                ax.plot(valid_sizes, frequencies, 'o-',
                       color=colors[mode_idx], linewidth=2, markersize=8,
                       label=f'Mode {mode_idx+1} Frequency')

                # 添加数值标签
                if self.chart_options.get('show_frequencies', True):
                    for size, freq in zip(valid_sizes, frequencies):
                        ax.annotate(f'{freq:.1f}Hz',
                                   (size, freq),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8, alpha=0.8)
        
        # 设置图表属性
        ax.set_xlabel('Mesh Size (mm)')
        ax.set_ylabel('Frequency (Hz)')
        ax.set_title('Mesh Convergence Analysis')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 设置对数坐标（如果数据范围较大）
        if max(mesh_sizes) / min(mesh_sizes) > 10:
            ax.set_xscale('log')
    
    def _show_no_data_message(self):
        """显示无数据消息"""
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, 'No Data Available\n\nPlease select meshes with modal results for comparison',
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=14, color='#999')
        ax.axis('off')
    
    def _show_no_frequency_data_message(self):
        """显示无频率数据消息"""
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, 'Selected meshes have no frequency data\n\nPlease complete modal calculation first',
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=14, color='#999')
        ax.axis('off')
    
    def _show_unsupported_chart_message(self, chart_type: str):
        """显示不支持的图表类型消息"""
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, f'Unsupported chart type: {chart_type}',
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=14, color='#f44336')
        ax.axis('off')
    
    def _show_error_message(self, error_msg: str):
        """显示错误消息"""
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, f'Chart Generation Error:\n{error_msg}',
               horizontalalignment='center', verticalalignment='center',
               transform=ax.transAxes, fontsize=12, color='#f44336')
        ax.axis('off')
        self.draw()
    
    def save_chart(self, filepath: str, dpi: int = 300):
        """保存图表到文件
        
        Args:
            filepath: 保存路径
            dpi: 图片分辨率
        """
        if not MATPLOTLIB_AVAILABLE:
            raise RuntimeError("matplotlib不可用，无法保存图表")
            
        try:
            self.figure.savefig(filepath, dpi=dpi, bbox_inches='tight', 
                               facecolor='white', edgecolor='none')
            logger.info(f"图表已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存图表失败: {str(e)}", exc_info=True)
            raise
    
    def get_chart_data(self) -> Dict[str, Any]:
        """获取当前图表数据"""
        return {
            'chart_type': self.current_chart_type,
            'data': self.current_data,
            'options': self.chart_options.copy()
        }
