<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>MainWindow</name>
    <message>
        <source>振动传递计算软件</source>
        <translation>Vibration Transfer Analysis Software</translation>
    </message>
    <message>
        <source>工具(&amp;T)</source>
        <translation>&amp;Tools</translation>
    </message>
    <message>
        <source>日志查看器(&amp;L)</source>
        <translation>&amp;Log Viewer</translation>
    </message>
    <message>
        <source>振动分析工具(&amp;V)</source>
        <translation>&amp;Vibration Analysis Tool</translation>
    </message>
    <message>
        <source>帮助(&amp;H)</source>
        <translation>&amp;Help</translation>
    </message>
    <message>
        <source>查看帮助(&amp;H)</source>
        <translation>View &amp;Help</translation>
    </message>
    <message>
        <source>调试路径(&amp;D)</source>
        <translation>&amp;Debug Paths</translation>
    </message>
    <message>
        <source>关于(&amp;A)</source>
        <translation>&amp;About</translation>
    </message>
    <message>
        <source>语言设置(&amp;L)</source>
        <translation>&amp;Language Settings</translation>
    </message>
    <message>
        <source>关于振动传递计算软件</source>
        <translation>About Vibration Transfer Analysis Software</translation>
    </message>
    <message>
        <source>振动传递计算软件 v1.0.0

本软件用于机械振动传递路径分析计算与可视化。
基于ANSYS Workbench和PySide6开发。

© 2023 版权所有</source>
        <translation>Vibration Transfer Analysis Software v1.0.0

This software is used for mechanical vibration transfer path analysis and visualization.
Developed based on ANSYS Workbench and PySide6.

© 2023 All Rights Reserved</translation>
    </message>
    <message>
        <source>错误</source>
        <translation>Error</translation>
    </message>
    <message>
        <source>无法显示语言设置对话框: </source>
        <translation>Unable to display language settings dialog: </translation>
    </message>
    <message>
        <source>提示</source>
        <translation>Information</translation>
    </message>
    <message>
        <source>是否要退出程序？</source>
        <translation>Do you want to exit the program?</translation>
    </message>
    <message>
        <source>成功</source>
        <translation>Success</translation>
    </message>
    <message>
        <source>配置错误</source>
        <translation>Configuration Error</translation>
    </message>
</context>
<context>
    <name>LanguageSelector</name>
    <message>
        <source>语言设置</source>
        <translation>Language Settings</translation>
    </message>
    <message>
        <source>界面语言:</source>
        <translation>Interface Language:</translation>
    </message>
    <message>
        <source>应用</source>
        <translation>Apply</translation>
    </message>
    <message>
        <source>确定</source>
        <translation>OK</translation>
    </message>
    <message>
        <source>取消</source>
        <translation>Cancel</translation>
    </message>
    <message>
        <source>语言信息:</source>
        <translation>Language Information:</translation>
    </message>
    <message>
        <source>名称</source>
        <translation>Name</translation>
    </message>
    <message>
        <source>代码</source>
        <translation>Code</translation>
    </message>
    <message>
        <source>方向</source>
        <translation>Direction</translation>
    </message>
    <message>
        <source>从左到右</source>
        <translation>Left to Right</translation>
    </message>
    <message>
        <source>从右到左</source>
        <translation>Right to Left</translation>
    </message>
    <message>
        <source>翻译状态</source>
        <translation>Translation Status</translation>
    </message>
    <message>
        <source>可用</source>
        <translation>Available</translation>
    </message>
    <message>
        <source>不可用</source>
        <translation>Not Available</translation>
    </message>
    <message>
        <source>设置已应用</source>
        <translation>Settings Applied</translation>
    </message>
    <message>
        <source>语言设置已应用。
某些更改可能需要重启应用程序才能完全生效。</source>
        <translation>Language settings have been applied.
Some changes may require restarting the application to take full effect.</translation>
    </message>
    <message>
        <source>应用设置时发生错误: </source>
        <translation>Error occurred while applying settings: </translation>
    </message>
    <message>
        <source>语言:</source>
        <translation>Language:</translation>
    </message>
</context>
<context>
    <name>ThreadSafeErrorHandler</name>
    <message>
        <source>关键错误</source>
        <translation>Critical Error</translation>
    </message>
</context>
</TS>
