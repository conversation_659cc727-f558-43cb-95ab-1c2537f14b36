@echo off
chcp 65001 >nul 2>&1
echo ========================================
echo 振动传递计算软件 - 简化打包脚本
echo ========================================
echo.

echo [1/4] 清理旧的构建文件...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
echo ✅ 清理完成

echo.
echo [2/4] 检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
)

echo.
echo [3/4] 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
python -m PyInstaller --clean --noconfirm qt_new.spec

echo.
echo [4/4] 检查打包结果...
if exist "dist\vibration_transfer\振动传递计算软件.exe" (
    echo ✅ 打包成功!
    echo 📁 输出目录: dist\vibration_transfer\
    echo 🚀 可执行文件: 振动传递计算软件.exe
    echo.
    echo 是否要打开输出目录? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        explorer "dist\vibration_transfer"
    )
) else (
    echo ❌ 打包失败!
    echo 请检查错误信息或运行 python build_package.py 获取详细日志
)

echo.
echo 按任意键退出...
pause >nul
