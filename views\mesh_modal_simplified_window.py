"""
简化的网格与模态分析窗口

此窗口整合了网格生成和模态分析功能，提供一体化的操作流程：
1. 用户选择网格和参数
2. 点击"开始模态分析"自动完成网格生成和模态计算
3. 显示分析进度和结果

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import logging
from typing import List, Dict, Any, Optional
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtWidgets import QMainWindow, QMessageBox, QListWidgetItem, QApplication
from PySide6.QtGui import QIcon

from ui.ui_mesh_modal_simplified import Ui_MeshModalSimplified


class ModalAnalysisWorker(QThread):
    """模态分析工作线程"""
    
    # 信号定义
    progress_updated = Signal(int, str, str)  # 进度, 当前步骤, 详细状态
    analysis_completed = Signal(dict)  # 分析完成，传递结果
    analysis_failed = Signal(str)  # 分析失败，传递错误信息
    
    def __init__(self, selected_meshes: List[str], mesh_params: Dict[str, Any], modal_params: Dict[str, Any]):
        super().__init__()
        self.selected_meshes = selected_meshes
        self.mesh_params = mesh_params
        self.modal_params = modal_params
        self.is_paused = False
        self.is_stopped = False
    
    def run(self):
        """执行模态分析"""
        try:
            total_meshes = len(self.selected_meshes)
            
            for i, mesh_name in enumerate(self.selected_meshes):
                if self.is_stopped:
                    break
                
                # 等待暂停状态
                while self.is_paused and not self.is_stopped:
                    self.msleep(100)
                
                if self.is_stopped:
                    break
                
                # 步骤1: 生成网格
                step_progress = int((i / total_meshes) * 100)
                self.progress_updated.emit(
                    step_progress, 
                    f"网格生成 ({i+1}/{total_meshes})", 
                    f"正在为 {mesh_name} 生成网格..."
                )
                
                # 模拟网格生成过程
                self._simulate_mesh_generation(mesh_name)
                
                if self.is_stopped:
                    break
                
                # 步骤2: 模态计算
                self.progress_updated.emit(
                    step_progress + 30, 
                    f"模态计算 ({i+1}/{total_meshes})", 
                    f"正在计算 {mesh_name} 的模态..."
                )
                
                # 模拟模态计算过程
                results = self._simulate_modal_calculation(mesh_name)
                
                if self.is_stopped:
                    break
            
            if not self.is_stopped:
                # 分析完成
                self.progress_updated.emit(100, "分析完成", "所有网格的模态分析已完成")
                
                # 汇总结果
                summary_results = {
                    "total_meshes": total_meshes,
                    "completed_meshes": total_meshes,
                    "analysis_time": "模拟时间",
                    "results": [f"{mesh}_result.dat" for mesh in self.selected_meshes]
                }
                
                self.analysis_completed.emit(summary_results)
                
        except Exception as e:
            self.analysis_failed.emit(f"分析过程中发生错误: {str(e)}")
    
    def _simulate_mesh_generation(self, mesh_name: str):
        """模拟网格生成过程"""
        # 模拟网格生成耗时
        for j in range(10):
            if self.is_stopped:
                break
            self.msleep(200)  # 模拟耗时操作
    
    def _simulate_modal_calculation(self, mesh_name: str) -> Dict[str, Any]:
        """模拟模态计算过程"""
        # 模拟模态计算耗时
        for j in range(15):
            if self.is_stopped:
                break
            self.msleep(150)  # 模拟耗时操作
        
        # 返回模拟结果
        return {
            "mesh_name": mesh_name,
            "modal_frequencies": [100.5, 250.3, 380.7, 520.1, 680.9],
            "modal_count": self.modal_params.get("modal_count", 10),
            "max_frequency": self.modal_params.get("max_frequency", 1000.0)
        }
    
    def pause(self):
        """暂停分析"""
        self.is_paused = True
    
    def resume(self):
        """恢复分析"""
        self.is_paused = False
    
    def stop(self):
        """停止分析"""
        self.is_stopped = True
        self.is_paused = False


class MeshModalSimplifiedWindow(QMainWindow):
    """简化的网格与模态分析窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ui = Ui_MeshModalSimplified()
        self.ui.setupUi(self)
        
        # 工作线程
        self.analysis_worker = None
        
        # 初始化界面
        self.init_ui()
        self.connect_signals()
        self.load_available_meshes()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("网格与模态分析 - 简化版")
        
        # 设置窗口图标
        icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # 初始化状态
        self.ui.label_current_step.setText("准备就绪")
        self.ui.label_detailed_status.setText("请选择网格并设置参数，然后点击'开始模态分析'")
        self.ui.progressBar_analysis.setValue(0)
        
        # 设置默认参数
        self.ui.doubleSpinBox_mesh_size.setValue(1.0)
        self.ui.comboBox_mesh_quality.setCurrentIndex(1)  # 中等质量
        self.ui.spinBox_modal_count.setValue(10)
        self.ui.doubleSpinBox_max_freq.setValue(1000.0)
    
    def connect_signals(self):
        """连接信号和槽"""
        # 网格选择相关
        self.ui.btn_select_all.clicked.connect(self.select_all_meshes)
        self.ui.btn_clear_selection.clicked.connect(self.clear_mesh_selection)
        self.ui.listWidget_meshes.itemSelectionChanged.connect(self.update_selection_count)
        
        # 分析控制相关
        self.ui.btn_start_modal_analysis.clicked.connect(self.start_modal_analysis)
        self.ui.btn_pause_analysis.clicked.connect(self.pause_analysis)
        self.ui.btn_stop_analysis.clicked.connect(self.stop_analysis)
        
        # 结果相关
        self.ui.btn_view_results.clicked.connect(self.view_results)
        self.ui.btn_export_results.clicked.connect(self.export_results)
    
    def load_available_meshes(self):
        """加载可用的网格列表"""
        # 模拟加载网格列表
        sample_meshes = [
            "结构体_001.step",
            "结构体_002.step", 
            "结构体_003.step",
            "结构体_004.step",
            "结构体_005.step",
            "复杂结构_A.step",
            "复杂结构_B.step",
            "测试模型_001.step"
        ]
        
        self.ui.listWidget_meshes.clear()
        for mesh in sample_meshes:
            item = QListWidgetItem(mesh)
            item.setData(Qt.UserRole, mesh)
            self.ui.listWidget_meshes.addItem(item)
        
        self.update_selection_count()
    
    def select_all_meshes(self):
        """全选网格"""
        self.ui.listWidget_meshes.selectAll()
        self.update_selection_count()
    
    def clear_mesh_selection(self):
        """清空网格选择"""
        self.ui.listWidget_meshes.clearSelection()
        self.update_selection_count()
    
    def update_selection_count(self):
        """更新选择计数"""
        selected_count = len(self.ui.listWidget_meshes.selectedItems())
        total_count = self.ui.listWidget_meshes.count()
        self.ui.label_selection_count.setText(f"已选择: {selected_count} / {total_count} 个网格")
    
    def get_selected_meshes(self) -> List[str]:
        """获取选中的网格列表"""
        selected_items = self.ui.listWidget_meshes.selectedItems()
        return [item.data(Qt.UserRole) for item in selected_items]
    
    def get_mesh_parameters(self) -> Dict[str, Any]:
        """获取网格参数"""
        return {
            "mesh_size": self.ui.doubleSpinBox_mesh_size.value(),
            "mesh_quality": self.ui.comboBox_mesh_quality.currentText(),
            "element_type": self.ui.comboBox_element_type.currentText()
        }
    
    def get_modal_parameters(self) -> Dict[str, Any]:
        """获取模态分析参数"""
        return {
            "modal_count": int(self.ui.spinBox_modal_count.value()),
            "max_frequency": self.ui.doubleSpinBox_max_freq.value()
        }
    
    def start_modal_analysis(self):
        """开始模态分析"""
        # 检查是否选择了网格
        selected_meshes = self.get_selected_meshes()
        if not selected_meshes:
            QMessageBox.warning(self, "警告", "请至少选择一个网格进行分析！")
            return
        
        # 获取参数
        mesh_params = self.get_mesh_parameters()
        modal_params = self.get_modal_parameters()
        
        # 确认开始分析
        reply = QMessageBox.question(
            self, "确认分析", 
            f"将对 {len(selected_meshes)} 个网格进行模态分析。\n"
            f"网格尺寸: {mesh_params['mesh_size']} mm\n"
            f"网格质量: {mesh_params['mesh_quality']}\n"
            f"模态数量: {modal_params['modal_count']}\n"
            f"最大频率: {modal_params['max_frequency']} Hz\n\n"
            f"确定要开始分析吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply != QMessageBox.Yes:
            return
        
        # 启动分析线程
        self.analysis_worker = ModalAnalysisWorker(selected_meshes, mesh_params, modal_params)
        self.analysis_worker.progress_updated.connect(self.update_analysis_progress)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.analysis_failed.connect(self.on_analysis_failed)
        
        # 更新界面状态
        self.set_analysis_running(True)
        
        # 启动线程
        self.analysis_worker.start()
        
        logging.info(f"开始模态分析: {len(selected_meshes)} 个网格")
    
    def pause_analysis(self):
        """暂停分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            if self.analysis_worker.is_paused:
                self.analysis_worker.resume()
                self.ui.btn_pause_analysis.setText("暂停")
                self.ui.label_current_step.setText("分析继续")
            else:
                self.analysis_worker.pause()
                self.ui.btn_pause_analysis.setText("继续")
                self.ui.label_current_step.setText("分析已暂停")
    
    def stop_analysis(self):
        """停止分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            reply = QMessageBox.question(
                self, "确认停止", 
                "确定要停止当前的模态分析吗？\n已完成的分析结果将会丢失。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.analysis_worker.stop()
                self.analysis_worker.wait()  # 等待线程结束
                self.set_analysis_running(False)
                self.ui.label_current_step.setText("分析已停止")
                self.ui.label_detailed_status.setText("分析被用户停止")
                self.ui.progressBar_analysis.setValue(0)
    
    def set_analysis_running(self, running: bool):
        """设置分析运行状态"""
        # 控制按钮状态
        self.ui.btn_start_modal_analysis.setEnabled(not running)
        self.ui.btn_pause_analysis.setEnabled(running)
        self.ui.btn_stop_analysis.setEnabled(running)
        
        # 控制参数设置
        self.ui.groupBox_mesh_params.setEnabled(not running)
        self.ui.groupBox_modal_params.setEnabled(not running)
        self.ui.listWidget_meshes.setEnabled(not running)
        self.ui.btn_select_all.setEnabled(not running)
        self.ui.btn_clear_selection.setEnabled(not running)
        
        if not running:
            self.ui.btn_pause_analysis.setText("暂停")
    
    def update_analysis_progress(self, progress: int, current_step: str, detailed_status: str):
        """更新分析进度"""
        self.ui.progressBar_analysis.setValue(progress)
        self.ui.label_current_step.setText(current_step)
        self.ui.label_detailed_status.setText(detailed_status)
        
        # 处理界面事件
        QApplication.processEvents()
    
    def on_analysis_completed(self, results: Dict[str, Any]):
        """分析完成处理"""
        self.set_analysis_running(False)
        
        # 显示结果
        self.ui.listWidget_results.clear()
        for result_file in results.get("results", []):
            item = QListWidgetItem(f"✅ {result_file}")
            self.ui.listWidget_results.addItem(item)
        
        # 启用结果操作按钮
        self.ui.btn_view_results.setEnabled(True)
        self.ui.btn_export_results.setEnabled(True)
        
        # 显示完成消息
        QMessageBox.information(
            self, "分析完成", 
            f"模态分析已成功完成！\n"
            f"共分析了 {results['total_meshes']} 个网格\n"
            f"结果文件已生成"
        )
        
        logging.info(f"模态分析完成: {results['total_meshes']} 个网格")
    
    def on_analysis_failed(self, error_message: str):
        """分析失败处理"""
        self.set_analysis_running(False)
        self.ui.label_current_step.setText("分析失败")
        self.ui.label_detailed_status.setText(error_message)
        
        QMessageBox.critical(self, "分析失败", f"模态分析失败:\n{error_message}")
        logging.error(f"模态分析失败: {error_message}")
    
    def view_results(self):
        """查看结果"""
        selected_items = self.ui.listWidget_results.selectedItems()
        if not selected_items:
            QMessageBox.information(self, "提示", "请选择要查看的结果文件")
            return
        
        # 这里可以实现结果查看功能
        QMessageBox.information(self, "查看结果", "结果查看功能将在后续版本中实现")
    
    def export_results(self):
        """导出结果"""
        if self.ui.listWidget_results.count() == 0:
            QMessageBox.information(self, "提示", "没有可导出的结果")
            return
        
        # 这里可以实现结果导出功能
        QMessageBox.information(self, "导出结果", "结果导出功能将在后续版本中实现")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            reply = QMessageBox.question(
                self, "确认关闭", 
                "分析正在进行中，确定要关闭窗口吗？\n这将停止当前的分析。",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.analysis_worker.stop()
                self.analysis_worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
