<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结果查看界面 - 高级结果可视化系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-cyan-600 to-blue-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-cyan-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    📈 结果查看界面
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-cyan-100">
                    高级结果可视化系统 | 多维度数据分析与专业报告生成
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-cyan-500 bg-opacity-20 text-cyan-100 text-sm font-semibold px-4 py-2 rounded-full border border-cyan-400">
                        📊 数据可视化 | 📈 图表分析 | 📋 报告生成 | 🔍 结果验证
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Interface Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🖥️ 界面概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                结果查看界面是分析结果的可视化和处理中心，提供了丰富的数据展示、分析和导出功能。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">结果树</h3>
                    </div>
                    <p class="text-sm text-blue-600">位于左侧，显示所有可用的分析结果</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">3D视图</h3>
                    </div>
                    <p class="text-sm text-green-600">中央区域，显示三维结果可视化</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">控制面板</h3>
                    </div>
                    <p class="text-sm text-purple-600">右侧面板，提供显示和分析控制选项</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">数据面板</h3>
                    </div>
                    <p class="text-sm text-orange-600">底部区域，显示数值数据和图表</p>
                </div>
            </div>
        </section>

        <!-- Result Types -->
        <section class="mb-12 scroll-reveal">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-2">📊 结果类型</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">支持多种分析结果的可视化和处理</p>
            </div>
            
            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📐</span>位移结果 (Displacement)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">显示内容</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 总位移大小</li>
                                    <li>• X、Y、Z方向分量</li>
                                    <li>• 旋转角度</li>
                                    <li>• 变形动画</li>
                                </ul>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">可视化选项</h4>
                                <p class="text-sm text-blue-600">云图显示、等值线、矢量图、变形缩放控制</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚡</span>应力结果 (Stress)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">应力类型</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• Von Mises应力</li>
                                    <li>• 主应力（σ1, σ2, σ3）</li>
                                    <li>• 正应力分量</li>
                                    <li>• 剪应力分量</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">分析功能</h4>
                                <p class="text-sm text-green-600">应力集中识别、安全系数计算、失效分析</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌊</span>模态结果 (Modal)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">模态信息</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 固有频率</li>
                                    <li>• 振型向量</li>
                                    <li>• 模态参与因子</li>
                                    <li>• 有效质量</li>
                                </ul>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800 mb-2">动画显示</h4>
                                <p class="text-sm text-purple-600">振型动画、频率扫描、模态叠加</p>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📈</span>频响结果 (Frequency Response)
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">响应数据</h4>
                                <ul class="space-y-1 text-sm">
                                    <li>• 幅频特性</li>
                                    <li>• 相频特性</li>
                                    <li>• 传递函数</li>
                                    <li>• 相干函数</li>
                                </ul>
                            </div>
                            <div class="bg-orange-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-orange-800 mb-2">图表分析</h4>
                                <p class="text-sm text-orange-600">Bode图、Nyquist图、瀑布图、功率谱</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Visualization Controls -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🎨 可视化控制</h2>

            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">🌈 颜色映射</h3>
                    <div class="grid md:grid-cols-4 gap-4">
                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-blue-800 mb-1">彩虹色谱</h4>
                            <p class="text-xs text-blue-600">经典的彩虹颜色映射</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-green-800 mb-1">热力图</h4>
                            <p class="text-xs text-green-600">红-黄-蓝热力色谱</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-purple-800 mb-1">灰度图</h4>
                            <p class="text-xs text-purple-600">黑白灰度映射</p>
                        </div>

                        <div class="bg-white p-4 rounded-lg shadow-sm text-center">
                            <div class="w-12 h-12 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mx-auto mb-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707" />
                                </svg>
                            </div>
                            <h4 class="font-semibold text-orange-800 mb-1">自定义</h4>
                            <p class="text-xs text-orange-600">用户自定义色谱</p>
                        </div>
                    </div>
                </div>

                <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">📊 显示选项</h3>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-green-400">
                                <h4 class="font-semibold text-green-800 mb-2">云图显示</h4>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>• 平滑云图</li>
                                    <li>• 分级云图</li>
                                    <li>• 透明度控制</li>
                                    <li>• 等值线叠加</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-blue-400">
                                <h4 class="font-semibold text-blue-800 mb-2">矢量显示</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 矢量箭头</li>
                                    <li>• 缩放因子</li>
                                    <li>• 颜色编码</li>
                                    <li>• 密度控制</li>
                                </ul>
                            </div>
                        </div>
                        <div class="space-y-3">
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-purple-400">
                                <h4 class="font-semibold text-purple-800 mb-2">变形显示</h4>
                                <ul class="text-sm text-purple-600 space-y-1">
                                    <li>• 变形缩放</li>
                                    <li>• 原始形状叠加</li>
                                    <li>• 动画播放</li>
                                    <li>• 时间步控制</li>
                                </ul>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm border-l-4 border-orange-400">
                                <h4 class="font-semibold text-orange-800 mb-2">截面分析</h4>
                                <ul class="text-sm text-orange-600 space-y-1">
                                    <li>• 平面截面</li>
                                    <li>• 球面截面</li>
                                    <li>• 自由截面</li>
                                    <li>• 多截面对比</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Data Analysis Tools -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔍 数据分析工具</h2>

            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4">📏 测量工具</h3>
                    <ul class="space-y-2 text-sm text-blue-600">
                        <li>• 点值查询</li>
                        <li>• 距离测量</li>
                        <li>• 角度测量</li>
                        <li>• 面积/体积计算</li>
                        <li>• 路径积分</li>
                    </ul>
                </div>

                <div class="bg-green-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-green-800 mb-4">📊 统计分析</h3>
                    <ul class="space-y-2 text-sm text-green-600">
                        <li>• 最大/最小值</li>
                        <li>• 平均值/标准差</li>
                        <li>• 直方图分析</li>
                        <li>• 分布统计</li>
                        <li>• 趋势分析</li>
                    </ul>
                </div>

                <div class="bg-purple-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 mb-4">📈 图表生成</h3>
                    <ul class="space-y-2 text-sm text-purple-600">
                        <li>• XY曲线图</li>
                        <li>• 频谱图</li>
                        <li>• 瀑布图</li>
                        <li>• 极坐标图</li>
                        <li>• 3D表面图</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Report Generation -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📋 报告生成</h2>

            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">📄 报告类型</h3>
                <p class="text-gray-600 mb-4">系统提供多种专业报告模板：</p>

                <div class="grid md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-indigo-800 mb-2">📊 技术报告</h4>
                            <ul class="text-sm text-indigo-600 space-y-1">
                                <li>• 分析概述和目标</li>
                                <li>• 模型描述和参数</li>
                                <li>• 结果分析和讨论</li>
                                <li>• 结论和建议</li>
                            </ul>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-purple-800 mb-2">📈 数据报告</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 数值结果表格</li>
                                <li>• 统计分析数据</li>
                                <li>• 图表和曲线</li>
                                <li>• 原始数据导出</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-blue-800 mb-2">🖼️ 图像报告</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 高分辨率截图</li>
                                <li>• 多视角展示</li>
                                <li>• 动画序列</li>
                                <li>• 标注和说明</li>
                            </ul>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <h4 class="font-semibold text-green-800 mb-2">📋 自定义报告</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 模板自定义</li>
                                <li>• 内容选择</li>
                                <li>• 格式设置</li>
                                <li>• 批量生成</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📊</span>如何查看不同类型的结果？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">通过结果树可以访问所有可用的分析结果。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">操作步骤：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 在左侧结果树中展开相应的结果类别</li>
                                <li>• 双击或右键选择要查看的结果项</li>
                                <li>• 在控制面板中调整显示选项</li>
                                <li>• 使用颜色映射和缩放控制优化显示效果</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🎨</span>如何自定义颜色映射？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">系统提供了丰富的颜色映射选项和自定义功能。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">自定义方法：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 在控制面板中选择"颜色映射"选项</li>
                                <li>• 选择预定义色谱或创建自定义色谱</li>
                                <li>• 调整颜色范围和分级数量</li>
                                <li>• 保存自定义设置供后续使用</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📈</span>如何导出结果数据？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">系统支持多种格式的数据导出。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">导出选项：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• CSV格式 - 用于表格数据</li>
                                <li>• 图像格式 - PNG、JPG、SVG等</li>
                                <li>• 报告格式 - PDF、Word文档</li>
                                <li>• 原始数据 - 二进制或文本格式</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔍</span>如何进行结果验证？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">结果验证是确保分析准确性的重要步骤。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">验证方法：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 检查结果的物理合理性</li>
                                <li>• 对比理论计算或实验数据</li>
                                <li>• 进行网格收敛性分析</li>
                                <li>• 检查边界条件的影响</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes and Warnings -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 结果显示可能需要较长时间，请耐心等待</li>
                    <li>• 大型模型的3D显示可能影响系统性能</li>
                    <li>• 导出高分辨率图像需要足够的内存</li>
                    <li>• 动画播放时避免进行其他操作</li>
                    <li>• 定期保存重要的分析结果</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 从整体结果开始，逐步深入细节分析</li>
                    <li>• 使用多种可视化方式验证结果</li>
                    <li>• 充分利用测量和统计工具</li>
                    <li>• 创建标准化的报告模板</li>
                    <li>• 定期备份重要的结果文件</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">高级结果可视化系统 - 多维度数据分析与专业报告生成</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
