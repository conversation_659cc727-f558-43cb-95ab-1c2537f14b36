"""
模态分析数据管理器

此模块负责管理模态分析结果数据，包括：
1. 导入外部模态分析结果
2. 数据格式验证和转换
3. 数据持久化存储
4. 导入结果的管理（增删改查）

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import json
import csv
import os
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
from datetime import datetime
import pickle

logger = logging.getLogger(__name__)


@dataclass
class ModalResult:
    """模态分析结果数据类"""
    name: str                    # 网格名称
    size: float                  # 网格尺寸 (mm)
    frequencies: List[float]     # 模态频率列表 (Hz)
    node_count: int              # 节点数
    element_count: int           # 单元数
    source: str = "imported"     # 数据来源 ("current", "imported")
    import_time: str = ""        # 导入时间
    file_path: str = ""          # 原始文件路径
    description: str = ""        # 描述信息
    
    def __post_init__(self):
        if not self.import_time:
            self.import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModalResult':
        """从字典创建实例"""
        return cls(**data)
    
    def validate(self) -> bool:
        """验证数据有效性"""
        try:
            # 检查必需字段
            if not self.name or not isinstance(self.name, str):
                return False
            if not isinstance(self.size, (int, float)) or self.size <= 0:
                return False
            if not isinstance(self.frequencies, list) or len(self.frequencies) == 0:
                return False
            if not all(isinstance(f, (int, float)) and f > 0 for f in self.frequencies):
                return False
            if not isinstance(self.node_count, int) or self.node_count <= 0:
                return False
            if not isinstance(self.element_count, int) or self.element_count <= 0:
                return False
            return True
        except Exception:
            return False


class ModalDataManager:
    """模态分析数据管理器"""
    
    def __init__(self, storage_path: str = "modal_data_storage.pkl"):
        self.storage_path = storage_path
        self.imported_results: List[ModalResult] = []
        self.current_results: List[ModalResult] = []
        self.load_data()
    
    def import_from_file(self, file_path: str) -> bool:
        """从文件导入模态分析结果
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 导入是否成功
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.json':
                return self._import_from_json(file_path)
            elif file_ext == '.csv':
                return self._import_from_csv(file_path)
            elif file_ext == '.txt':
                return self._import_from_txt(file_path)
            else:
                logger.error(f"Unsupported file format: {file_ext}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to import from {file_path}: {str(e)}")
            return False
    
    def _import_from_json(self, file_path: str) -> bool:
        """从JSON文件导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 支持单个结果或结果列表
            if isinstance(data, dict):
                data = [data]
            
            imported_count = 0
            for item in data:
                result = self._create_modal_result_from_dict(item, file_path)
                if result and result.validate():
                    self.imported_results.append(result)
                    imported_count += 1
                else:
                    logger.warning(f"Invalid data in {file_path}: {item}")
            
            if imported_count > 0:
                self.save_data()
                logger.info(f"Successfully imported {imported_count} results from {file_path}")
                return True
            else:
                logger.error(f"No valid data found in {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to import JSON from {file_path}: {str(e)}")
            return False
    
    def _import_from_csv(self, file_path: str) -> bool:
        """从CSV文件导入"""
        try:
            imported_count = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    result = self._create_modal_result_from_dict(row, file_path)
                    if result and result.validate():
                        self.imported_results.append(result)
                        imported_count += 1
                    else:
                        logger.warning(f"Invalid row in {file_path}: {row}")
            
            if imported_count > 0:
                self.save_data()
                logger.info(f"Successfully imported {imported_count} results from {file_path}")
                return True
            else:
                logger.error(f"No valid data found in {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to import CSV from {file_path}: {str(e)}")
            return False
    
    def _import_from_txt(self, file_path: str) -> bool:
        """从TXT文件导入（假设是制表符分隔的格式）"""
        try:
            imported_count = 0
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                logger.error(f"Invalid TXT file format: {file_path}")
                return False
            
            # 第一行作为标题
            headers = lines[0].strip().split('\t')
            
            for line in lines[1:]:
                if line.strip():
                    values = line.strip().split('\t')
                    if len(values) == len(headers):
                        row_dict = dict(zip(headers, values))
                        result = self._create_modal_result_from_dict(row_dict, file_path)
                        if result and result.validate():
                            self.imported_results.append(result)
                            imported_count += 1
                        else:
                            logger.warning(f"Invalid line in {file_path}: {line}")
            
            if imported_count > 0:
                self.save_data()
                logger.info(f"Successfully imported {imported_count} results from {file_path}")
                return True
            else:
                logger.error(f"No valid data found in {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to import TXT from {file_path}: {str(e)}")
            return False
    
    def _create_modal_result_from_dict(self, data: Dict[str, Any], file_path: str) -> Optional[ModalResult]:
        """从字典创建ModalResult实例"""
        try:
            # 处理频率数据（可能是字符串格式）
            frequencies = data.get('frequencies', [])
            if isinstance(frequencies, str):
                # 尝试解析字符串格式的频率列表
                frequencies = frequencies.strip('[]').split(',')
                frequencies = [float(f.strip()) for f in frequencies if f.strip()]
            elif not isinstance(frequencies, list):
                frequencies = []
            
            result = ModalResult(
                name=str(data.get('name', 'Unknown')),
                size=float(data.get('size', 0)),
                frequencies=frequencies,
                node_count=int(data.get('node_count', 0)),
                element_count=int(data.get('element_count', 0)),
                source="imported",
                file_path=file_path,
                description=str(data.get('description', ''))
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to create ModalResult from data: {data}, error: {str(e)}")
            return None
    
    def get_all_results(self) -> List[ModalResult]:
        """获取所有结果（当前+导入）"""
        return self.current_results + self.imported_results
    
    def get_imported_results(self) -> List[ModalResult]:
        """获取导入的结果"""
        return self.imported_results.copy()
    
    def get_current_results(self) -> List[ModalResult]:
        """获取当前计算结果"""
        return self.current_results.copy()
    
    def update_current_results(self, results: List[Dict[str, Any]]):
        """更新当前计算结果"""
        self.current_results = []
        for result_dict in results:
            result = ModalResult(
                name=result_dict.get('name', 'Current'),
                size=result_dict.get('size', 0),
                frequencies=result_dict.get('frequencies', []),
                node_count=result_dict.get('node_count', 0),
                element_count=result_dict.get('element_count', 0),
                source="current"
            )
            if result.validate():
                self.current_results.append(result)
    
    def remove_imported_result(self, index: int) -> bool:
        """删除导入的结果"""
        try:
            if 0 <= index < len(self.imported_results):
                removed = self.imported_results.pop(index)
                self.save_data()
                logger.info(f"Removed imported result: {removed.name}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to remove result at index {index}: {str(e)}")
            return False

    def clear_all_imported_results(self) -> bool:
        """清空所有导入的结果"""
        try:
            if not self.imported_results:
                logger.info("No imported results to clear")
                return True

            cleared_count = len(self.imported_results)
            cleared_names = [result.name for result in self.imported_results[:5]]  # 记录前5个名称用于日志

            self.imported_results.clear()
            self.save_data()

            if cleared_count <= 5:
                logger.info(f"Cleared {cleared_count} imported results: {', '.join(cleared_names)}")
            else:
                logger.info(f"Cleared {cleared_count} imported results: {', '.join(cleared_names)} and {cleared_count - 5} more")

            return True
        except Exception as e:
            logger.error(f"Failed to clear all imported results: {str(e)}")
            return False
    
    def clear_imported_results(self):
        """清空所有导入的结果"""
        self.imported_results.clear()
        self.save_data()
        logger.info("Cleared all imported results")
    
    def save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'imported_results': [result.to_dict() for result in self.imported_results],
                'save_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            with open(self.storage_path, 'wb') as f:
                pickle.dump(data, f)
            logger.debug(f"Data saved to {self.storage_path}")
        except Exception as e:
            logger.error(f"Failed to save data: {str(e)}")
    
    def load_data(self):
        """从文件加载数据"""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'rb') as f:
                    data = pickle.load(f)
                
                self.imported_results = [
                    ModalResult.from_dict(result_dict) 
                    for result_dict in data.get('imported_results', [])
                ]
                logger.info(f"Loaded {len(self.imported_results)} imported results")
            else:
                logger.info("No existing data file found, starting fresh")
        except Exception as e:
            logger.error(f"Failed to load data: {str(e)}")
            self.imported_results = []
    
    def export_to_file(self, file_path: str, include_current: bool = True, include_imported: bool = True) -> bool:
        """导出结果到文件"""
        try:
            results = []
            if include_current:
                results.extend(self.current_results)
            if include_imported:
                results.extend(self.imported_results)
            
            if not results:
                logger.warning("No results to export")
                return False
            
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.json':
                return self._export_to_json(file_path, results)
            elif file_ext == '.csv':
                return self._export_to_csv(file_path, results)
            else:
                logger.error(f"Unsupported export format: {file_ext}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to export to {file_path}: {str(e)}")
            return False
    
    def _export_to_json(self, file_path: str, results: List[ModalResult]) -> bool:
        """导出到JSON文件"""
        try:
            data = [result.to_dict() for result in results]
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"Exported {len(results)} results to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export JSON: {str(e)}")
            return False
    
    def _export_to_csv(self, file_path: str, results: List[ModalResult]) -> bool:
        """导出到CSV文件"""
        try:
            if not results:
                return False
            
            fieldnames = ['name', 'size', 'frequencies', 'node_count', 'element_count', 
                         'source', 'import_time', 'description']
            
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for result in results:
                    row = result.to_dict()
                    # 将频率列表转换为字符串
                    row['frequencies'] = str(row['frequencies'])
                    writer.writerow(row)
            
            logger.info(f"Exported {len(results)} results to {file_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to export CSV: {str(e)}")
            return False
