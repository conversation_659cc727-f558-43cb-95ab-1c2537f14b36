"""
基础窗口模块

此模块定义了应用程序的基础窗口类，所有其他窗口都继承自此类。
提供了通用的窗口行为，如窗口关闭事件处理、帮助显示等。

作者: [作者名]
日期: [日期]
"""

import os
from PySide6.QtCore import Qt, Property, QPropertyAnimation, QEasingCurve, QTimer
from PySide6.QtGui import QIcon, QCloseEvent, QShortcut, QKeySequence, QColor
from PySide6.QtWidgets import QMainWindow, QMessageBox, QPushButton, QGraphicsDropShadowEffect

from window_manager import WindowManager, WindowType, WindowObserver

# 导入优化相关模块
try:
    from core.optimization_config import is_optimization_enabled, get_config_value
    HAS_OPTIMIZATION = True
except ImportError:
    HAS_OPTIMIZATION = False
    def is_optimization_enabled(name): return False
    def get_config_value(key, default=None): return default


class AnimatedButton(QPushButton):
    """带有3D浮动效果的按钮，鼠标悬停时按钮会抬升起来（性能优化版，支持延迟初始化）"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 设置初始属性
        self._elevation = 0
        self._animations_initialized = False
        self._lazy_animations_enabled = is_optimization_enabled('lazy_animations') if HAS_OPTIMIZATION else False

        # 动画对象（延迟初始化）
        self.shadow = None
        self.hover_anim = None
        self.reset_anim = None

        # 总是先设置基本样式，确保按钮立即有正确的外观
        self._setup_basic_style()

        # 如果禁用延迟动画，立即初始化完整动画
        if not self._lazy_animations_enabled:
            self._initialize_animations()
        # 如果启用延迟动画，基本样式已经设置，动画会在需要时初始化
        
    def _setup_basic_style(self):
        """设置基本样式（不包含动画效果）"""
        self.setStyleSheet("""
            QPushButton {
                border-radius: 6px;
                background-color: #4f46e5;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }

            QPushButton:hover {
                background-color: #4338ca;
            }

            QPushButton:pressed {
                background-color: #312e81;
            }
        """)

    def _get_merged_style(self, original_style: str) -> str:
        """合并原始样式和AnimatedButton样式

        Args:
            original_style: 原始按钮的样式表

        Returns:
            str: 合并后的样式表
        """
        # AnimatedButton的基本样式（优先级高）
        base_style = """
            QPushButton {
                border-radius: 6px;
                background-color: #4f46e5;
                color: white;
                padding: 8px 16px;
                border: none;
                font-weight: bold;
                margin: 3px 0px;
            }

            QPushButton:hover {
                background-color: #4338ca;
            }

            QPushButton:pressed {
                background-color: #312e81;
            }
        """

        # 如果原始样式包含特殊的颜色或其他属性，可以在这里进行智能合并
        # 目前简单地使用基本样式，确保一致性
        return base_style

    def _initialize_animations(self):
        """初始化动画效果"""
        if self._animations_initialized:
            return

        # 创建阴影效果
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(8)
        self.shadow.setColor(QColor(0, 0, 0, 100))
        self.shadow.setOffset(0, 3)
        self.setGraphicsEffect(self.shadow)

        # 创建动画对象
        self.hover_anim = QPropertyAnimation(self, b"elevation")
        self.hover_anim.setDuration(100)
        self.hover_anim.setStartValue(0)
        self.hover_anim.setEndValue(-8)
        self.hover_anim.setEasingCurve(QEasingCurve.OutQuad)

        self.reset_anim = QPropertyAnimation(self, b"elevation")
        self.reset_anim.setDuration(70)
        self.reset_anim.setStartValue(-8)
        self.reset_anim.setEndValue(0)
        self.reset_anim.setEasingCurve(QEasingCurve.InQuad)

        # 基本样式已经在构造函数中设置，这里不需要重复设置

        self._animations_initialized = True

    def _ensure_animations_initialized(self):
        """确保动画已初始化（延迟初始化的入口点）"""
        if not self._animations_initialized:
            if self._lazy_animations_enabled:
                # 延迟初始化
                delay_ms = get_config_value('animation_creation_delay_ms', 50) if HAS_OPTIMIZATION else 50
                QTimer.singleShot(delay_ms, self._initialize_animations)
            else:
                # 立即初始化
                self._initialize_animations()
    
    # 简化属性设置，减少运行时计算
    def get_elevation(self):
        return self._elevation
        
    def set_elevation(self, elevation):
        if self._elevation == elevation:
            return  # 避免不必要的更新

        self._elevation = elevation

        # 只有在动画已初始化时才应用视觉效果
        if self._animations_initialized and self.shadow:
            if elevation < 0:  # 向上抬升
                self.shadow.setOffset(0, 3 - elevation / 2)
                self.setContentsMargins(0, elevation, 0, -elevation)
            else:  # 平面状态
                self.shadow.setOffset(0, 3)
                self.setContentsMargins(0, 0, 0, 0)
        
    elevation = Property(float, get_elevation, set_elevation)
    
    def enterEvent(self, event):
        """鼠标悬停时抬升按钮"""
        self._ensure_animations_initialized()
        if self._animations_initialized and self.reset_anim and self.hover_anim:
            self.reset_anim.stop()
            self.hover_anim.start()
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开时恢复按钮"""
        if self._animations_initialized and self.hover_anim and self.reset_anim:
            self.hover_anim.stop()
            self.reset_anim.start()
        super().leaveEvent(event)

    def mousePressEvent(self, event):
        """按下时按钮直接设置状态，不使用动画"""
        if self._animations_initialized:
            if self.hover_anim:
                self.hover_anim.stop()
            if self.reset_anim:
                self.reset_anim.stop()
            self.set_elevation(0)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """释放时立即恢复状态"""
        if self._animations_initialized:
            if self.rect().contains(event.position().toPoint()):
                self.set_elevation(-8)
            else:
                self.set_elevation(0)
        super().mouseReleaseEvent(event)


class BaseWindow(QMainWindow, WindowObserver):
    """基础窗口类"""
    def __init__(self, window_manager):
        super().__init__()
        self.setAttribute(Qt.WA_DeleteOnClose)
        self.window_manager = window_manager
        self.window_manager.register_observer(self)
        
        # 设置窗口通用样式
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(os.path.dirname(__file__)),"assets","icons","vibration_transfer_icon_alt.ico")))
        self.setStyleSheet("QMainWindow { border: 1px solid #dcdfe6; }")
        
        # 为F1键添加帮助快捷键
        help_shortcut = QShortcut(QKeySequence("F1"), self)
        help_shortcut.activated.connect(self.show_f1_help)

    def closeEvent(self, event: QCloseEvent) -> None:
        """关闭窗口时触发"""
        try:
            # 如果这是主窗口，则直接接受关闭事件
            if hasattr(self, 'is_main_window') and self.is_main_window:
                # 主窗口的closeEvent会单独处理，这里不做任何操作
                return
                
            # 对于其他窗口，隐藏而不是关闭
            self.hide()

            # 如果有窗口管理器，使用窗口管理器切换到主窗口
            if hasattr(self, 'window_manager') and self.window_manager:
                try:
                    # 使用窗口管理器的switch_to方法，确保正确的窗口状态管理
                    self.window_manager.switch_to(WindowType.MAIN)
                    print(f"通过窗口管理器切换到主窗口")
                except Exception as e:
                    print(f"切换到主窗口时出错: {str(e)}")
                    # 如果窗口管理器切换失败，回退到直接显示
                    try:
                        main_window = self.window_manager.get_window(WindowType.MAIN)
                        if main_window:
                            main_window.show()
                    except Exception as e2:
                        print(f"回退显示主窗口也失败: {str(e2)}")

            # 忽略关闭事件，防止窗口被销毁
            event.ignore()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            # 出现错误时接受关闭事件
            event.accept()
        
    def on_window_changed(self, from_window: WindowType, to_window: WindowType) -> None:
        """处理窗口切换事件"""
        pass

    def window_closed(self, window_id):
        '''窗口关闭的回调函数，由WindowManager调用'''
        pass
        
    def show_f1_help(self):
        """F1快捷键显示帮助"""
        # 查找父类中是否有MainWindow
        parent = self
        while parent and not hasattr(parent, 'show_help'):
            if hasattr(parent, 'parent'):
                parent = parent.parent()
            else:
                parent = None
                
        if parent and hasattr(parent, 'show_help'):
            parent.show_help()
        else:
            # 如果没有找到MainWindow，则直接创建HelpDialog
            from .help_dialog import HelpDialog
            
            # 修改帮助文件路径的获取方式，以支持PyInstaller打包环境
            import sys
            if getattr(sys, 'frozen', False):
                # 如果是打包后的可执行文件
                base_path = sys._MEIPASS
            else:
                # 开发环境
                base_path = os.path.dirname(os.path.dirname(__file__))
                
            help_file = os.path.join(base_path, "help", "html", "index.html")
            
            if os.path.exists(help_file):
                try:
                    help_dialog = HelpDialog(help_file, self)
                    help_dialog.exec()
                except Exception as e:
                    QMessageBox.warning(
                        self,
                        "错误",
                        f"无法显示帮助文档：{str(e)}"
                    )
            else:
                QMessageBox.warning(
                    self,
                    "错误",
                    f"找不到帮助文档文件：{help_file}\n请确保help/html目录下存在index.html文件。"
                )

    def apply_animated_buttons(self, buttons):
        """将普通按钮转换为动画按钮

        Args:
            buttons: 要转换的按钮列表
        """
        for btn in buttons:
            if not isinstance(btn, AnimatedButton):
                # 获取原始按钮的属性
                parent = btn.parent()
                text = btn.text()
                obj_name = btn.objectName()
                font = btn.font()
                size_policy = btn.sizePolicy()
                min_size = btn.minimumSize()
                geometry = btn.geometry()
                enabled = btn.isEnabled()
                style_sheet = btn.styleSheet()

                # 创建新的动画按钮
                new_btn = AnimatedButton(text, parent)
                new_btn.setObjectName(obj_name)
                new_btn.setFont(font)
                new_btn.setSizePolicy(size_policy)
                new_btn.setMinimumSize(min_size)
                new_btn.setGeometry(geometry)
                new_btn.setEnabled(enabled)

                # 确保动画和样式正确初始化
                new_btn._ensure_animations_initialized()

                # 处理样式表：优先保持AnimatedButton的样式
                if style_sheet and style_sheet.strip():
                    # 合并样式：AnimatedButton的基本样式 + 原始样式的特定属性
                    merged_style = new_btn._get_merged_style(style_sheet)
                    new_btn.setStyleSheet(merged_style)
                else:
                    # 确保基本样式已应用
                    new_btn._setup_basic_style()

                # 确保按钮可见
                new_btn.setVisible(True)
                new_btn.show()

                # 强制刷新样式
                new_btn.style().unpolish(new_btn)
                new_btn.style().polish(new_btn)
                new_btn.update()

                # 更新UI引用（在替换之前）
                attr_name = obj_name
                if hasattr(self.ui, attr_name):
                    setattr(self.ui, attr_name, new_btn)

                # 替换原始按钮
                layout = parent.layout()
                if layout:
                    # 如果按钮在布局中，使用布局替换
                    layout.replaceWidget(btn, new_btn)
                    new_btn.show()
                else:
                    # 否则直接设置几何位置
                    new_btn.setGeometry(btn.geometry())
                    new_btn.show()

                # 删除原始按钮
                btn.hide()
                btn.setParent(None)
                btn.deleteLater()