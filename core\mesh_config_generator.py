#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格配置文件生成器

此模块负责基于用户选择的计算结果自动生成mesh_config_last.json配置文件，
用于与finalscript的网格生成功能集成。

主要功能：
1. 基于用户选择的单个结果生成配置文件
2. 生成符合finalscript期望格式的JSON配置
3. 提供动态路径管理，替代硬编码路径
4. 确保与现有网格生成流程的兼容性

作者: AI Assistant
日期: 2025-08-01
"""

import os
import json
import logging
from typing import Dict, Any, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class MeshConfigGenerator:
    """
    网格配置文件生成器
    
    负责基于用户选择的计算结果生成mesh_config_last.json配置文件，
    并提供动态路径管理功能。
    """
    
    def __init__(self, work_dir: str = None):
        """
        初始化网格配置生成器
        
        Args:
            work_dir: 工作目录路径，如果为None则使用当前目录
        """
        self.work_dir = work_dir or os.getcwd()
        self.config_filename = "mesh_config_last.json"
        
    def generate_config_from_selection(self, 
                                     selected_result: Dict[str, Any],
                                     output_dir: str = None) -> str:
        """
        基于用户选择的单个结果生成网格配置文件
        
        Args:
            selected_result: 用户选择的计算结果，包含网格信息
            output_dir: 输出目录，如果为None则使用默认目录
            
        Returns:
            str: 生成的配置文件路径
            
        Raises:
            ValueError: 当选择的结果格式不正确时
            FileNotFoundError: 当输出目录无法创建时
        """
        try:
            logger.info("开始基于用户选择生成网格配置文件")
            
            # 验证输入参数
            if not selected_result:
                raise ValueError("选择的结果不能为空")
            
            # 提取网格尺寸信息
            element_size = self._extract_element_size(selected_result)
            if element_size is None:
                raise ValueError("无法从选择的结果中提取网格尺寸信息")
            
            # 确定输出目录
            if output_dir is None:
                output_dir = os.path.join(self.work_dir, "temp")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 生成配置内容
            config_content = self._create_config_content(element_size, selected_result)
            
            # 生成配置文件路径
            config_path = os.path.join(output_dir, self.config_filename)
            
            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_content, f, indent=4, ensure_ascii=False)
            
            logger.info(f"网格配置文件已生成: {config_path}")
            logger.info(f"配置内容: {config_content}")
            
            return config_path
            
        except Exception as e:
            logger.error(f"生成网格配置文件失败: {str(e)}")
            raise
    
    def _extract_element_size(self, selected_result: Dict[str, Any]) -> Optional[float]:
        """
        从选择的结果中提取网格尺寸
        
        Args:
            selected_result: 选择的结果数据
            
        Returns:
            float: 网格尺寸（米），如果无法提取则返回None
        """
        try:
            # 尝试多种可能的字段名
            size_fields = [
                'element_size',
                'mesh_size', 
                'size',
                'element_size_m',
                'mesh_element_size'
            ]
            
            for field in size_fields:
                if field in selected_result:
                    size_value = selected_result[field]
                    if isinstance(size_value, (int, float)):
                        # 如果尺寸大于1，假设单位是毫米，需要转换为米
                        if size_value > 1:
                            return size_value / 1000.0
                        else:
                            return float(size_value)
            
            # 尝试从网格对象中提取
            if 'mesh' in selected_result:
                mesh_obj = selected_result['mesh']
                if hasattr(mesh_obj, 'size'):
                    size_value = mesh_obj.size
                    # 假设mesh.size是毫米单位，转换为米
                    return size_value / 1000.0 if size_value > 1 else size_value
            
            # 尝试从文件名中提取（如果有的话）
            if 'filename' in selected_result:
                filename = selected_result['filename']
                import re
                # 匹配类似 "modal_freq_0.015.json" 的模式
                match = re.search(r'(\d+\.?\d*)', filename)
                if match:
                    return float(match.group(1))
            
            logger.warning("无法从选择的结果中提取网格尺寸")
            return None
            
        except Exception as e:
            logger.error(f"提取网格尺寸时发生错误: {str(e)}")
            return None
    
    def _create_config_content(self, element_size: float, selected_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建配置文件内容
        
        Args:
            element_size: 网格尺寸（米）
            selected_result: 选择的结果数据
            
        Returns:
            Dict: 配置文件内容
        """
        try:
            # 基础配置内容，参考finalscript的run_mesh_setting函数期望的格式
            config = {
                "element_size": element_size,
                "generated_time": datetime.now().isoformat(),
                "source": "user_selection",
                "description": "基于用户选择的计算结果自动生成的网格配置"
            }
            
            # 添加可选的元数据
            if 'mesh_name' in selected_result:
                config['mesh_name'] = selected_result['mesh_name']
            
            if 'calculation_time' in selected_result:
                config['calculation_time'] = selected_result['calculation_time']
            
            if 'frequencies' in selected_result:
                config['modal_frequencies_count'] = len(selected_result['frequencies'])
            
            # 添加输出目录信息（如果有的话）
            if 'output_directory' in selected_result:
                config['reference_output_directory'] = selected_result['output_directory']
            
            return config
            
        except Exception as e:
            logger.error(f"创建配置内容时发生错误: {str(e)}")
            # 返回最基本的配置
            return {
                "element_size": element_size,
                "generated_time": datetime.now().isoformat(),
                "source": "user_selection"
            }
    
    def get_config_path(self, output_dir: str = None) -> str:
        """
        获取配置文件的完整路径
        
        Args:
            output_dir: 输出目录，如果为None则使用默认目录
            
        Returns:
            str: 配置文件的完整路径
        """
        if output_dir is None:
            output_dir = os.path.join(self.work_dir, "temp")
        
        return os.path.join(output_dir, self.config_filename).replace("\\", "/")
    
    def validate_config_file(self, config_path: str) -> bool:
        """
        验证配置文件是否有效
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            bool: 配置文件是否有效
        """
        try:
            if not os.path.exists(config_path):
                logger.warning(f"配置文件不存在: {config_path}")
                return False
            
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 检查必需的字段
            if 'element_size' not in config:
                logger.warning("配置文件缺少element_size字段")
                return False
            
            element_size = config['element_size']
            if not isinstance(element_size, (int, float)) or element_size <= 0:
                logger.warning(f"element_size值无效: {element_size}")
                return False
            
            logger.info(f"配置文件验证通过: {config_path}")
            return True
            
        except Exception as e:
            logger.error(f"验证配置文件时发生错误: {str(e)}")
            return False
    
    @staticmethod
    def create_from_mesh_parameter(mesh_param, output_dir: str = None, work_dir: str = None) -> str:
        """
        从MeshParameter对象创建配置文件
        
        Args:
            mesh_param: MeshParameter对象
            output_dir: 输出目录
            work_dir: 工作目录
            
        Returns:
            str: 生成的配置文件路径
        """
        generator = MeshConfigGenerator(work_dir)
        
        # 构建选择结果数据
        selected_result = {
            'mesh': mesh_param,
            'mesh_name': mesh_param.name,
            'element_size': mesh_param.size,  # 假设是毫米单位
            'element_type': mesh_param.element_type.value if hasattr(mesh_param.element_type, 'value') else str(mesh_param.element_type)
        }
        
        return generator.generate_config_from_selection(selected_result, output_dir)
    
    @staticmethod
    def create_from_modal_result(modal_result: Dict[str, Any], output_dir: str = None, work_dir: str = None) -> str:
        """
        从模态计算结果创建配置文件

        Args:
            modal_result: 模态计算结果数据
            output_dir: 输出目录
            work_dir: 工作目录

        Returns:
            str: 生成的配置文件路径
        """
        generator = MeshConfigGenerator(work_dir)
        return generator.generate_config_from_selection(modal_result, output_dir)

    @staticmethod
    def create_single_modal_config(mesh_param, work_dir: str = None) -> str:
        """
        为单模态计算创建mesh_config_latest.json配置文件

        Args:
            mesh_param: MeshParameter对象
            work_dir: 工作目录

        Returns:
            str: 生成的配置文件路径
        """
        try:
            if work_dir is None:
                work_dir = os.getcwd()

            # 确保json目录存在
            json_dir = os.path.join(work_dir, "json")
            os.makedirs(json_dir, exist_ok=True)

            # 配置文件路径
            config_path = os.path.join(json_dir, "mesh_config_latest.json")

            # 创建配置内容
            config_content = {
                "element_size": mesh_param.size / 1000.0,  # 转换为米
                "generated_time": datetime.now().isoformat(),
                "source": "single_modal_calculation",
                "description": "单模态计算配置文件",
                "mesh_name": mesh_param.name,
                "mesh_id": mesh_param.id,
                "element_type": mesh_param.element_type.value if hasattr(mesh_param.element_type, 'value') else str(mesh_param.element_type)
            }

            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_content, f, indent=4, ensure_ascii=False)

            logger.info(f"单模态计算配置文件已生成: {config_path}")
            logger.info(f"配置内容: element_size={config_content['element_size']}m, mesh_name={config_content['mesh_name']}")

            return config_path

        except Exception as e:
            logger.error(f"生成单模态计算配置文件失败: {str(e)}")
            raise


def get_dynamic_config_path(work_dir: str = None) -> str:
    """
    获取动态配置文件路径，用于替代硬编码路径
    
    Args:
        work_dir: 工作目录，如果为None则使用当前目录
        
    Returns:
        str: 动态配置文件路径
    """
    generator = MeshConfigGenerator(work_dir)
    return generator.get_config_path()


def replace_hardcoded_paths(script_content: str, work_dir: str = None, config_type: str = "single") -> str:
    """
    替换脚本中的硬编码路径

    Args:
        script_content: 脚本内容
        work_dir: 工作目录
        config_type: 配置类型，支持以下类型：
            - "single": 单模态计算，使用json/mesh_config_latest.json
            - "batch": 批处理模态分析，使用temp/batch_mesh_config.json
            - "result": 结果分析计算，使用temp/mesh_config_last.json

    Returns:
        str: 替换后的脚本内容
    """
    try:
        # 根据配置类型选择不同的配置文件和目录
        if work_dir is None:
            work_dir = os.getcwd()

        if config_type == "batch":
            # 批处理模态分析使用temp/batch_mesh_config.json
            config_dir = os.path.join(work_dir, "temp")
            config_filename = "batch_mesh_config.json"
        elif config_type == "result":
            # 结果分析计算使用temp/mesh_config_last.json
            config_dir = os.path.join(work_dir, "temp")
            config_filename = "mesh_config_last.json"
        elif config_type == "single":
            # 单模态计算使用json/mesh_config_latest.json
            config_dir = os.path.join(work_dir, "json")
            config_filename = "mesh_config_latest.json"
        else:
            # 默认使用单模态计算配置
            logger.warning(f"未知的配置类型: {config_type}，使用默认的单模态计算配置")
            config_dir = os.path.join(work_dir, "json")
            config_filename = "mesh_config_latest.json"

        # 构建动态配置路径
        dynamic_config_path = os.path.join(config_dir, config_filename).replace("\\", "/")

        # 定义需要替换的硬编码路径模式
        hardcoded_patterns = [
            r'cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"',
            r'cfg_path = r"D:\data\all-XM\autoworkbench\csdaima\mesh_config.json"',
            r'cfg_path = "D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"',
            r'cfg_path = "D:\data\all-XM\autoworkbench\csdaima\mesh_config.json"'
        ]

        # 新的动态路径
        new_path = f'cfg_path = r"{dynamic_config_path}"'

        # 执行替换
        for pattern in hardcoded_patterns:
            script_content = script_content.replace(pattern, new_path)

        logger.info(f"已将硬编码路径替换为动态路径: {dynamic_config_path} (类型: {config_type})")
        return script_content

    except Exception as e:
        logger.error(f"替换硬编码路径时发生错误: {str(e)}")
        return script_content
