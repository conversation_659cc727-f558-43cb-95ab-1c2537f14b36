#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格更新功能修复验证脚本

此脚本用于验证修复后的网格更新功能，确保：
1. MeshManager.update_mesh()方法调用参数正确
2. 网格参数能够正确更新
3. 网格ID正确设置
4. 更新后的网格状态正确

作者: AI Assistant
日期: 2025-08-01
"""

import sys
import os
import logging
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mesh_manager_update_method():
    """测试MeshManager.update_mesh()方法的正确调用"""
    try:
        logger.info("开始测试MeshManager.update_mesh()方法")
        
        # 导入必要的类
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        
        # 创建网格管理器
        mesh_manager = MeshManager()
        
        # 创建测试网格
        original_mesh = MeshParameter(
            name="test_mesh",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        # 添加网格
        success = mesh_manager.add_mesh(original_mesh)
        assert success, "添加网格应该成功"
        
        logger.info(f"原始网格: {original_mesh.name}, ID: {original_mesh.id}, 尺寸: {original_mesh.size}")
        
        # 创建更新后的网格参数
        updated_mesh = MeshParameter(
            name="updated_test_mesh",
            size=15.0,
            element_type=ElementType.HEXAHEDRON
        )
        
        # 设置正确的ID（这是关键）
        updated_mesh.id = original_mesh.id
        
        logger.info(f"更新网格: {updated_mesh.name}, ID: {updated_mesh.id}, 尺寸: {updated_mesh.size}")
        
        # 测试update_mesh方法 - 只传递一个参数
        success = mesh_manager.update_mesh(updated_mesh)
        assert success, "更新网格应该成功"
        
        # 验证更新结果
        retrieved_mesh = mesh_manager.get_mesh(original_mesh.id)
        assert retrieved_mesh is not None, "应该能够获取更新后的网格"
        assert retrieved_mesh.name == "updated_test_mesh", f"网格名称应该更新为'updated_test_mesh'，实际为'{retrieved_mesh.name}'"
        assert retrieved_mesh.size == 15.0, f"网格尺寸应该更新为15.0，实际为{retrieved_mesh.size}"
        assert retrieved_mesh.element_type == ElementType.HEXAHEDRON, f"单元类型应该更新为HEXAHEDRON"
        
        logger.info(f"✅ MeshManager.update_mesh()方法测试通过")
        logger.info(f"  - 更新后网格名称: {retrieved_mesh.name}")
        logger.info(f"  - 更新后网格尺寸: {retrieved_mesh.size}")
        logger.info(f"  - 更新后单元类型: {retrieved_mesh.element_type.value}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MeshManager.update_mesh()方法测试失败: {str(e)}", exc_info=True)
        return False

def test_mesh_window_update_method():
    """测试修复后的_update_mesh方法逻辑"""
    try:
        logger.info("开始测试修复后的_update_mesh方法逻辑")
        
        # 模拟_update_mesh方法的逻辑
        def mock_update_mesh(mesh_id: str, updated_param):
            """模拟修复后的_update_mesh方法"""
            from core.mesh_manager import MeshManager
            
            # 创建模拟的mesh_manager
            mesh_manager = MeshManager()
            
            # 添加原始网格
            from core.mesh_manager import MeshParameter, ElementType
            original_mesh = MeshParameter(
                name="original_mesh",
                size=10.0,
                element_type=ElementType.TETRAHEDRON
            )
            original_mesh.id = mesh_id  # 设置指定的ID
            mesh_manager.add_mesh(original_mesh)
            
            # 确保updated_param有正确的ID
            updated_param.id = mesh_id
            
            # MeshManager.update_mesh()只需要一个MeshParameter参数
            success = mesh_manager.update_mesh(updated_param)
            
            return success, mesh_manager.get_mesh(mesh_id)
        
        # 测试数据
        test_mesh_id = "test-mesh-id-12345"
        
        # 创建更新参数
        from core.mesh_manager import MeshParameter, ElementType
        updated_param = MeshParameter(
            name="updated_mesh_name",
            size=20.0,
            element_type=ElementType.HEXAHEDRON
        )
        
        # 执行更新
        success, updated_mesh = mock_update_mesh(test_mesh_id, updated_param)
        
        # 验证结果
        assert success, "更新应该成功"
        assert updated_mesh is not None, "应该能够获取更新后的网格"
        assert updated_mesh.id == test_mesh_id, f"网格ID应该保持为{test_mesh_id}"
        assert updated_mesh.name == "updated_mesh_name", f"网格名称应该更新"
        assert updated_mesh.size == 20.0, f"网格尺寸应该更新"
        
        logger.info(f"✅ _update_mesh方法逻辑测试通过")
        logger.info(f"  - 网格ID: {updated_mesh.id}")
        logger.info(f"  - 更新后名称: {updated_mesh.name}")
        logger.info(f"  - 更新后尺寸: {updated_mesh.size}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ _update_mesh方法逻辑测试失败: {str(e)}", exc_info=True)
        return False

def test_parameter_validation():
    """测试参数验证逻辑"""
    try:
        logger.info("开始测试参数验证逻辑")
        
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        
        mesh_manager = MeshManager()
        
        # 测试有效参数
        valid_mesh = MeshParameter(
            name="valid_mesh",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        success = mesh_manager.add_mesh(valid_mesh)
        assert success, "有效参数应该添加成功"
        
        # 测试更新为有效参数
        updated_valid_mesh = MeshParameter(
            name="updated_valid_mesh",
            size=15.0,
            element_type=ElementType.HEXAHEDRON
        )
        updated_valid_mesh.id = valid_mesh.id
        
        success = mesh_manager.update_mesh(updated_valid_mesh)
        assert success, "有效参数更新应该成功"
        
        # 测试无效参数（空名称）
        invalid_mesh = MeshParameter(
            name="",  # 空名称
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        invalid_mesh.id = valid_mesh.id
        
        success = mesh_manager.update_mesh(invalid_mesh)
        assert not success, "无效参数更新应该失败"
        
        # 测试无效参数（尺寸超出范围）
        invalid_size_mesh = MeshParameter(
            name="invalid_size_mesh",
            size=2000.0,  # 超出范围
            element_type=ElementType.TETRAHEDRON
        )
        invalid_size_mesh.id = valid_mesh.id
        
        success = mesh_manager.update_mesh(invalid_size_mesh)
        assert not success, "无效尺寸更新应该失败"
        
        logger.info(f"✅ 参数验证逻辑测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 参数验证逻辑测试失败: {str(e)}", exc_info=True)
        return False

def test_id_consistency():
    """测试ID一致性"""
    try:
        logger.info("开始测试ID一致性")
        
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        
        mesh_manager = MeshManager()
        
        # 创建原始网格
        original_mesh = MeshParameter(
            name="original",
            size=10.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        original_id = original_mesh.id
        mesh_manager.add_mesh(original_mesh)
        
        # 创建更新参数，但不设置ID
        updated_mesh = MeshParameter(
            name="updated",
            size=20.0,
            element_type=ElementType.HEXAHEDRON
        )
        
        # 模拟修复后的逻辑：确保ID正确设置
        updated_mesh.id = original_id
        
        # 验证ID设置正确
        assert updated_mesh.id == original_id, f"ID应该设置为{original_id}"
        
        # 执行更新
        success = mesh_manager.update_mesh(updated_mesh)
        assert success, "更新应该成功"
        
        # 验证更新后的网格
        retrieved_mesh = mesh_manager.get_mesh(original_id)
        assert retrieved_mesh is not None, "应该能够获取网格"
        assert retrieved_mesh.id == original_id, "ID应该保持不变"
        assert retrieved_mesh.name == "updated", "名称应该更新"
        
        logger.info(f"✅ ID一致性测试通过")
        logger.info(f"  - 原始ID: {original_id}")
        logger.info(f"  - 更新后ID: {retrieved_mesh.id}")
        logger.info(f"  - ID一致性: {original_id == retrieved_mesh.id}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ID一致性测试失败: {str(e)}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始网格更新功能修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 运行测试
    tests = [
        ("MeshManager.update_mesh()方法测试", test_mesh_manager_update_method),
        ("_update_mesh方法逻辑测试", test_mesh_window_update_method),
        ("参数验证逻辑测试", test_parameter_validation),
        ("ID一致性测试", test_id_consistency)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}", exc_info=True)
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！网格更新功能修复验证成功")
        logger.info("\n📋 修复要点总结:")
        logger.info("• ✅ 修复了MeshManager.update_mesh()方法调用参数错误")
        logger.info("• ✅ 确保网格ID正确设置")
        logger.info("• ✅ 保持参数验证逻辑正常工作")
        logger.info("• ✅ 维护ID一致性")
        logger.info("\n🔧 修复内容:")
        logger.info("• 将 mesh_manager.update_mesh(mesh_id, updated_param)")
        logger.info("• 改为 mesh_manager.update_mesh(updated_param)")
        logger.info("• 并确保 updated_param.id = mesh_id")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
