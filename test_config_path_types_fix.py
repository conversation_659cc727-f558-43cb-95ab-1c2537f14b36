#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件路径类型修复验证测试

此脚本用于验证单模态计算、批处理模态分析、结果分析计算各自使用正确的配置文件，确保：
1. 单模态计算使用json/mesh_config_latest.json
2. 批处理模态分析使用temp/batch_mesh_config.json
3. 结果分析计算使用temp/mesh_config_last.json
4. 路径替换逻辑智能区分不同计算类型
5. 所有配置文件路径错误完全修复

作者: AI Assistant
日期: 2025-08-02
"""

import sys
import os
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_type_path_mapping():
    """测试配置类型与路径映射"""
    try:
        logger.info("开始测试配置类型与路径映射")
        
        from core.mesh_config_generator import replace_hardcoded_paths
        
        # 模拟脚本内容
        test_script_content = '''
# 测试脚本
cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"

def run():
    with open(cfg_path, 'r') as f:
        config = json.load(f)
    return config
'''
        
        work_dir = os.getcwd()
        
        # 测试单模态计算配置类型
        single_result = replace_hardcoded_paths(test_script_content, work_dir, config_type="single")
        
        # 测试批处理配置类型
        batch_result = replace_hardcoded_paths(test_script_content, work_dir, config_type="batch")
        
        # 测试结果分析配置类型
        result_result = replace_hardcoded_paths(test_script_content, work_dir, config_type="result")
        
        # 验证路径映射
        expected_paths = {
            "single": "json/mesh_config_latest.json",
            "batch": "temp/batch_mesh_config.json",
            "result": "temp/mesh_config_last.json"
        }
        
        test_results = {
            "single": single_result,
            "batch": batch_result,
            "result": result_result
        }
        
        for config_type, expected_path in expected_paths.items():
            result_content = test_results[config_type]
            
            if expected_path not in result_content:
                logger.error(f"{config_type}配置类型未正确替换为{expected_path}")
                return False
            
            # 检查是否包含其他类型的路径（不应该包含）
            other_paths = [path for key, path in expected_paths.items() if key != config_type]
            for other_path in other_paths:
                if other_path in result_content:
                    logger.error(f"{config_type}配置类型错误地包含了{other_path}")
                    return False
        
        logger.info("✅ 配置类型与路径映射测试通过")
        logger.info(f"  - 单模态计算: {expected_paths['single']}")
        logger.info(f"  - 批处理模态分析: {expected_paths['batch']}")
        logger.info(f"  - 结果分析计算: {expected_paths['result']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置类型与路径映射测试失败: {str(e)}")
        return False

def test_single_modal_config_generation():
    """测试单模态计算配置文件生成"""
    try:
        logger.info("开始测试单模态计算配置文件生成")
        
        from core.mesh_manager import MeshParameter, ElementType
        from core.mesh_config_generator import MeshConfigGenerator
        
        # 创建测试网格参数
        mesh_param = MeshParameter(
            name="单模态测试网格",
            size=18.0,  # 毫米
            element_type=ElementType.TETRAHEDRON
        )
        
        # 生成单模态计算配置文件
        work_dir = os.getcwd()
        config_path = MeshConfigGenerator.create_single_modal_config(mesh_param, work_dir)
        
        # 验证文件路径
        expected_path = os.path.join(work_dir, "json", "mesh_config_latest.json")
        if config_path != expected_path:
            logger.error(f"配置文件路径不正确，期望: {expected_path}, 实际: {config_path}")
            return False
        
        # 验证文件存在
        if not os.path.exists(config_path):
            logger.error(f"配置文件未生成: {config_path}")
            return False
        
        # 验证文件内容
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = json.load(f)
        
        # 验证必需字段
        required_fields = ['element_size', 'source', 'mesh_name', 'mesh_id']
        for field in required_fields:
            if field not in config_content:
                logger.error(f"配置文件缺少必需字段: {field}")
                return False
        
        # 验证element_size转换（18mm -> 0.018m）
        expected_element_size = 0.018
        actual_element_size = config_content['element_size']
        if abs(actual_element_size - expected_element_size) > 0.001:
            logger.error(f"element_size转换不正确，期望: {expected_element_size}, 实际: {actual_element_size}")
            return False
        
        # 验证source字段
        if config_content['source'] != 'single_modal_calculation':
            logger.error(f"source字段不正确，期望: 'single_modal_calculation', 实际: '{config_content['source']}'")
            return False
        
        logger.info("✅ 单模态计算配置文件生成测试通过")
        logger.info(f"  - 配置文件路径: {config_path}")
        logger.info(f"  - element_size: {actual_element_size}m")
        logger.info(f"  - 网格名称: {config_content['mesh_name']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 单模态计算配置文件生成测试失败: {str(e)}")
        return False

def test_config_file_format_compatibility():
    """测试配置文件格式兼容性"""
    try:
        logger.info("开始测试配置文件格式兼容性")
        
        work_dir = os.getcwd()
        
        # 测试各种配置文件的格式
        config_files = {
            "single": os.path.join(work_dir, "json", "mesh_config_latest.json"),
            "batch": os.path.join(work_dir, "temp", "batch_mesh_config.json"),
            "result": os.path.join(work_dir, "temp", "mesh_config_last.json")
        }
        
        # 验证单模态配置文件格式
        if os.path.exists(config_files["single"]):
            with open(config_files["single"], 'r', encoding='utf-8') as f:
                single_config = json.load(f)
            
            # 验证单模态配置格式
            if not isinstance(single_config.get('element_size'), (int, float)):
                logger.error("单模态配置的element_size应该是数值")
                return False
            
            if single_config.get('source') != 'single_modal_calculation':
                logger.error("单模态配置的source字段不正确")
                return False
            
            logger.info("✅ 单模态配置文件格式正确")
        
        # 验证批处理配置文件格式
        if os.path.exists(config_files["batch"]):
            with open(config_files["batch"], 'r', encoding='utf-8') as f:
                batch_config = json.load(f)
            
            # 验证批处理配置格式
            if not isinstance(batch_config.get('element_size'), list):
                logger.error("批处理配置的element_size应该是数组")
                return False
            
            if not batch_config.get('batch_mode', False):
                logger.error("批处理配置的batch_mode应该为True")
                return False
            
            logger.info("✅ 批处理配置文件格式正确")
        
        # 验证结果分析配置文件格式
        if os.path.exists(config_files["result"]):
            try:
                with open(config_files["result"], 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        logger.info("⚠️  结果分析配置文件为空，跳过格式验证")
                    else:
                        result_config = json.loads(content)

                        # 验证结果分析配置格式
                        if not isinstance(result_config.get('element_size'), (int, float)):
                            logger.error("结果分析配置的element_size应该是数值")
                            return False

                        if result_config.get('source') != 'user_selection':
                            logger.error("结果分析配置的source字段不正确")
                            return False

                        logger.info("✅ 结果分析配置文件格式正确")
            except json.JSONDecodeError:
                logger.info("⚠️  结果分析配置文件格式无效，跳过验证")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件格式兼容性测试失败: {str(e)}")
        return False

def test_path_replacement_in_different_contexts():
    """测试在不同上下文中的路径替换"""
    try:
        logger.info("开始测试在不同上下文中的路径替换")
        
        from core.mesh_config_generator import replace_hardcoded_paths
        
        # 模拟不同计算类型的脚本内容
        script_templates = {
            "single_modal": '''
def run_single_modal():
    cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
    with open(cfg_path, "r") as f:
        config = json.load(f)
    element_size = config.get("element_size")
    return element_size
''',
            "batch_modal": '''
def run_batch_modal():
    cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
    with open(cfg_path, "r") as f:
        config = json.load(f)
    sizes_to_test = config.get("element_size", [])
    return sizes_to_test
''',
            "result_analysis": '''
def run_result_analysis():
    cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
    with open(cfg_path, "r") as f:
        config = json.load(f)
    selected_size = config.get("element_size")
    return selected_size
'''
        }
        
        work_dir = os.getcwd()
        
        # 测试单模态计算路径替换
        single_script = replace_hardcoded_paths(
            script_templates["single_modal"], 
            work_dir, 
            config_type="single"
        )
        
        if "json/mesh_config_latest.json" not in single_script:
            logger.error("单模态计算脚本未正确替换路径")
            return False
        
        # 测试批处理模态分析路径替换
        batch_script = replace_hardcoded_paths(
            script_templates["batch_modal"], 
            work_dir, 
            config_type="batch"
        )
        
        if "temp/batch_mesh_config.json" not in batch_script:
            logger.error("批处理模态分析脚本未正确替换路径")
            return False
        
        # 测试结果分析计算路径替换
        result_script = replace_hardcoded_paths(
            script_templates["result_analysis"], 
            work_dir, 
            config_type="result"
        )
        
        if "temp/mesh_config_last.json" not in result_script:
            logger.error("结果分析计算脚本未正确替换路径")
            return False
        
        logger.info("✅ 不同上下文中的路径替换测试通过")
        logger.info("  - 单模态计算 → json/mesh_config_latest.json")
        logger.info("  - 批处理模态分析 → temp/batch_mesh_config.json")
        logger.info("  - 结果分析计算 → temp/mesh_config_last.json")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 不同上下文中的路径替换测试失败: {str(e)}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    try:
        logger.info("开始测试向后兼容性")
        
        from core.mesh_config_generator import replace_hardcoded_paths
        
        # 测试默认配置类型
        test_script = '''
cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
'''
        
        work_dir = os.getcwd()
        
        # 不指定config_type，应该默认为"single"
        result_default = replace_hardcoded_paths(test_script, work_dir)
        
        # 显式指定config_type="single"
        result_explicit = replace_hardcoded_paths(test_script, work_dir, config_type="single")
        
        # 两者应该相同
        if result_default != result_explicit:
            logger.error("默认配置类型与显式指定single类型的结果不一致")
            return False
        
        # 应该都包含json/mesh_config_latest.json
        if "json/mesh_config_latest.json" not in result_default:
            logger.error("默认配置类型未正确使用单模态计算路径")
            return False
        
        # 测试未知配置类型
        result_unknown = replace_hardcoded_paths(test_script, work_dir, config_type="unknown")
        
        # 未知类型应该回退到单模态计算配置
        if "json/mesh_config_latest.json" not in result_unknown:
            logger.error("未知配置类型未正确回退到单模态计算路径")
            return False
        
        logger.info("✅ 向后兼容性测试通过")
        logger.info("  - 默认配置类型正确")
        logger.info("  - 未知配置类型正确回退")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 向后兼容性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始配置文件路径类型修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    # 运行测试
    tests = [
        ("配置类型与路径映射测试", test_config_type_path_mapping),
        ("单模态计算配置文件生成测试", test_single_modal_config_generation),
        ("配置文件格式兼容性测试", test_config_file_format_compatibility),
        ("不同上下文中的路径替换测试", test_path_replacement_in_different_contexts),
        ("向后兼容性测试", test_backward_compatibility)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}")
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！配置文件路径类型修复成功")
        logger.info("\n📋 修复验证结果:")
        logger.info("• ✅ 单模态计算使用json/mesh_config_latest.json")
        logger.info("• ✅ 批处理模态分析使用temp/batch_mesh_config.json")
        logger.info("• ✅ 结果分析计算使用temp/mesh_config_last.json")
        logger.info("• ✅ 路径替换逻辑智能区分不同计算类型")
        logger.info("• ✅ 所有配置文件路径错误完全修复")
        logger.info("\n🔧 修复内容:")
        logger.info("• 扩展replace_hardcoded_paths函数支持三种配置类型")
        logger.info("• 添加create_single_modal_config函数生成单模态配置")
        logger.info("• 修改各计算类型的路径替换调用")
        logger.info("• 保持向后兼容性和智能回退机制")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
        logger.error("请检查配置文件路径和类型设置")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
