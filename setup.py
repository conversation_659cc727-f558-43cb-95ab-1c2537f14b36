from setuptools import setup, find_packages
import os

# 读取 requirements.txt
with open('requirements.txt', 'r', encoding='utf-8') as f:
    requirements = [line.strip() for line in f if not line.startswith('#') and line.strip()]

setup(
    name="vibration_transfer",
    version="0.2.0",
    description="ANSYS Workbench 振动传递计算软件",
    author="项目团队",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/vibration_transfer",
    packages=find_packages(),
    py_modules=["qt_new", "window_manager", "resource_manager"],
    entry_points={
        "console_scripts": [
            "vibration_transfer=qt_new:initialize_application",
        ],
    },
    install_requires=requirements,
    include_package_data=True,
    package_data={
        "": ["assets/icons/*.ico", "assets/styles/*.qss", "help/html/*"],
    },
    python_requires=">=3.12",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3.12",
        "Topic :: Scientific/Engineering",
    ],
) 