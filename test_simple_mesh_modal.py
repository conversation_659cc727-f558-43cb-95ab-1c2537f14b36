"""
简单的网格与模态分析界面测试

此脚本用于快速测试简化界面的基本功能。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主测试函数"""
    print("🧪 简化的网格与模态分析界面测试")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from views.mesh_modal_simplified_window import MeshModalSimplifiedWindow
        
        print("✅ 模块导入成功")
        
        # 创建应用程序
        app = QApplication([])
        
        # 创建简化的网格与模态分析窗口
        window = MeshModalSimplifiedWindow()
        
        print("✅ 简化界面窗口创建成功")
        
        # 显示窗口
        window.show()
        
        print("✅ 窗口显示成功")
        
        # 验证界面组件
        mesh_count = window.ui.listWidget_meshes.count()
        print(f"\n📊 界面状态：")
        print(f"  - 可用网格数量: {mesh_count}")
        print(f"  - 默认网格尺寸: {window.ui.doubleSpinBox_mesh_size.value()} mm")
        print(f"  - 默认网格质量: {window.ui.comboBox_mesh_quality.currentText()}")
        print(f"  - 默认模态数量: {int(window.ui.spinBox_modal_count.value())}")
        print(f"  - 默认最大频率: {window.ui.doubleSpinBox_max_freq.value()} Hz")
        
        print("\n🎯 简化界面特性：")
        print("  ✅ 网格选择和参数设置在同一界面")
        print("  ✅ 一键开始模态分析")
        print("  ✅ 自动完成网格生成和模态计算")
        print("  ✅ 实时显示分析进度")
        print("  ✅ 集中显示分析结果")
        
        print("\n💡 测试说明：")
        print("  1. 选择一个或多个网格文件")
        print("  2. 调整网格参数（尺寸、质量等）")
        print("  3. 设置模态分析参数（数量、频率等）")
        print("  4. 点击'开始模态分析'按钮")
        print("  5. 观察进度显示和结果")
        print("  6. 关闭窗口结束测试")
        
        print("\n🚀 界面已打开，请手动测试功能...")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
