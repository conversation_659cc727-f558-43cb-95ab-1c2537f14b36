"""
语言选择器组件

此模块提供语言选择功能，包括：
1. 语言选择下拉框
2. 动态语言切换
3. 语言变更通知
4. 界面实时更新

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Optional, Callable
from PySide6.QtCore import QObject, Signal, QCoreApplication
from PySide6.QtWidgets import QComboBox, QWidget, QHBoxLayout, QLabel, QDialog, QVBoxLayout, QPushButton, QMessageBox
from PySide6.QtGui import QIcon

logger = logging.getLogger(__name__)


def tr(text: str) -> str:
    """翻译函数的便捷包装"""
    return QCoreApplication.translate("LanguageSelector", text)


class LanguageSelector(QComboBox):
    """语言选择器组件
    
    提供语言选择下拉框，支持动态语言切换。
    """
    
    # 信号定义
    language_changed = Signal(str)  # 语言变更信号
    
    def __init__(self, i18n_manager=None, parent=None):
        super().__init__(parent)
        self._i18n_manager = i18n_manager
        self._updating = False  # 防止递归更新
        
        # 设置样式
        self.setStyleSheet("""
            QComboBox {
                min-width: 120px;
                padding: 5px;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox:hover {
                border: 1px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(assets/icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """)
        
        # 初始化语言选项
        self._setup_languages()
        
        # 连接信号
        self.currentTextChanged.connect(self._on_language_selected)
        
        # 如果有i18n管理器，连接其信号
        if self._i18n_manager:
            self._i18n_manager.language_changed.connect(self._on_i18n_language_changed)
            # 设置当前语言
            self._update_current_language()
        
        logger.debug("LanguageSelector 初始化完成")
    
    def _setup_languages(self) -> None:
        """设置语言选项"""
        try:
            if self._i18n_manager:
                languages = self._i18n_manager.get_supported_languages()
                
                for lang_code, lang_info in languages.items():
                    # 显示格式：🇨🇳 简体中文
                    display_text = f"{lang_info['flag']} {lang_info['native_name']}"
                    self.addItem(display_text, lang_code)
            else:
                # 默认语言选项
                self.addItem("🇨🇳 简体中文", "zh_CN")
                self.addItem("🇺🇸 English", "en_US")
                self.addItem("🇯🇵 日本語", "ja_JP")
                
        except Exception as e:
            logger.error(f"设置语言选项失败: {e}")
    
    def _update_current_language(self) -> None:
        """更新当前语言选择"""
        try:
            if not self._i18n_manager:
                return
            
            current_lang = self._i18n_manager.get_current_language()
            
            # 查找对应的索引
            for i in range(self.count()):
                if self.itemData(i) == current_lang:
                    self._updating = True
                    self.setCurrentIndex(i)
                    self._updating = False
                    break
                    
        except Exception as e:
            logger.error(f"更新当前语言选择失败: {e}")
    
    def _on_language_selected(self, text: str) -> None:
        """处理语言选择事件"""
        try:
            if self._updating:
                return
            
            # 获取选中的语言代码
            current_index = self.currentIndex()
            if current_index >= 0:
                lang_code = self.itemData(current_index)
                
                if lang_code and self._i18n_manager:
                    # 切换语言
                    if self._i18n_manager.change_language(lang_code):
                        logger.info(f"语言已切换到: {lang_code}")
                        self.language_changed.emit(lang_code)
                    else:
                        logger.error(f"语言切换失败: {lang_code}")
                        # 恢复到之前的选择
                        self._update_current_language()
                        
        except Exception as e:
            logger.error(f"处理语言选择事件失败: {e}")
    
    def _on_i18n_language_changed(self, lang_code: str) -> None:
        """处理i18n管理器的语言变更事件"""
        self._update_current_language()
    
    def set_i18n_manager(self, i18n_manager) -> None:
        """设置i18n管理器
        
        Args:
            i18n_manager: 国际化管理器实例
        """
        self._i18n_manager = i18n_manager
        if i18n_manager:
            i18n_manager.language_changed.connect(self._on_i18n_language_changed)
            self._update_current_language()


class LanguageSettingsDialog(QDialog):
    """语言设置对话框
    
    提供更详细的语言设置选项。
    """
    
    def __init__(self, i18n_manager=None, parent=None):
        super().__init__(parent)
        self._i18n_manager = i18n_manager
        self._setup_ui()
        self._load_current_settings()
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        self.setWindowTitle(tr("语言设置"))
        self.setModal(True)
        self.resize(400, 300)
        
        layout = QVBoxLayout(self)
        
        # 语言选择区域
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel(tr("界面语言:")))
        
        self.language_selector = LanguageSelector(self._i18n_manager, self)
        lang_layout.addWidget(self.language_selector)
        lang_layout.addStretch()
        
        layout.addLayout(lang_layout)
        
        # 语言信息显示
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        self.info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 10px;
                margin: 10px 0;
            }
        """)
        layout.addWidget(self.info_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.apply_button = QPushButton(tr("应用"))
        self.apply_button.clicked.connect(self._apply_settings)
        button_layout.addWidget(self.apply_button)
        
        self.ok_button = QPushButton(tr("确定"))
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)
        
        self.cancel_button = QPushButton(tr("取消"))
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.language_selector.language_changed.connect(self._update_language_info)
        
        # 更新语言信息
        self._update_language_info()
    
    def _load_current_settings(self) -> None:
        """加载当前设置"""
        # 语言选择器会自动加载当前语言
        pass
    
    def _update_language_info(self, lang_code: str = None) -> None:
        """更新语言信息显示
        
        Args:
            lang_code: 语言代码，如果为None则使用当前选择
        """
        try:
            if not self._i18n_manager:
                return
            
            if not lang_code:
                # 获取当前选择的语言
                current_index = self.language_selector.currentIndex()
                if current_index >= 0:
                    lang_code = self.language_selector.itemData(current_index)
            
            if lang_code:
                lang_info = self._i18n_manager.get_language_info(lang_code)
                if lang_info:
                    info_text = tr("语言信息:") + "\n\n"
                    info_text += f"{tr('名称')}: {lang_info['native_name']}\n"
                    info_text += f"{tr('代码')}: {lang_info['locale']}\n"
                    info_text += f"{tr('方向')}: {tr('从左到右') if lang_info['direction'] == 'ltr' else tr('从右到左')}\n"
                    
                    # 检查翻译文件状态
                    ts_file = self._i18n_manager.get_translation_file_path(lang_code, 'app')
                    import os
                    if os.path.exists(ts_file.replace('.qm', '.ts')):
                        info_text += f"\n{tr('翻译状态')}: {tr('可用')}"
                    else:
                        info_text += f"\n{tr('翻译状态')}: {tr('不可用')}"
                    
                    self.info_label.setText(info_text)
                    
        except Exception as e:
            logger.error(f"更新语言信息失败: {e}")
    
    def _apply_settings(self) -> None:
        """应用设置"""
        try:
            QMessageBox.information(
                self,
                tr("设置已应用"),
                tr("语言设置已应用。\n某些更改可能需要重启应用程序才能完全生效。")
            )
        except Exception as e:
            logger.error(f"应用设置失败: {e}")
            QMessageBox.warning(
                self,
                tr("错误"),
                tr("应用设置时发生错误: ") + str(e)
            )


def create_language_toolbar_widget(i18n_manager=None) -> QWidget:
    """创建语言工具栏组件
    
    Args:
        i18n_manager: 国际化管理器实例
        
    Returns:
        QWidget: 语言工具栏组件
    """
    widget = QWidget()
    layout = QHBoxLayout(widget)
    layout.setContentsMargins(5, 0, 5, 0)
    
    # 语言标签
    label = QLabel(tr("语言:"))
    layout.addWidget(label)
    
    # 语言选择器
    selector = LanguageSelector(i18n_manager)
    layout.addWidget(selector)
    
    return widget


def show_language_settings_dialog(i18n_manager=None, parent=None) -> bool:
    """显示语言设置对话框
    
    Args:
        i18n_manager: 国际化管理器实例
        parent: 父窗口
        
    Returns:
        bool: 是否确认了设置
    """
    dialog = LanguageSettingsDialog(i18n_manager, parent)
    return dialog.exec() == QDialog.Accepted
