# 振动传递计算软件打包指南

## 📦 概述

本指南详细说明如何使用PyInstaller将振动传递计算软件打包为独立的可执行文件。

## 🛠️ 环境要求

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **Python版本**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **磁盘空间**: 至少 2GB 可用空间

### 依赖包
确保已安装所有必需的依赖包：
```bash
pip install -r requirements.txt
```

主要依赖包括：
- PySide6 (Qt界面框架)
- PyInstaller (打包工具)
- numpy, matplotlib, pandas (科学计算)
- fastapi, uvicorn (API服务)
- openpyxl (Excel文件处理)

## 🚀 打包方法

### 方法一：使用自动化脚本（推荐）

#### 1. 完整打包脚本
```bash
python build_package.py
```

**特点**：
- ✅ 完整的环境检查
- ✅ 自动清理旧文件
- ✅ 详细的日志记录
- ✅ 打包结果验证
- ✅ 错误诊断

#### 2. 简化打包脚本
```bash
build_simple.bat
```

**特点**：
- ✅ 快速打包
- ✅ 简单易用
- ✅ 自动打开输出目录

### 方法二：手动PyInstaller命令

```bash
# 清理旧文件
rmdir /s /q dist build

# 执行打包
python -m PyInstaller --clean --noconfirm qt_new.spec
```

## 📋 打包配置说明

### qt_new.spec 文件配置

#### 数据文件包含
```python
datas=[
    ('assets', 'assets'),           # 资源文件
    ('help/html', 'help/html'),     # 帮助文档
    ('script', 'script'),           # 脚本文件
    ('ui', 'ui'),                   # UI文件
    ('config', 'config'),           # 配置文件
    ('translations', 'translations'), # 翻译文件
    # ... 更多数据文件
]
```

#### 隐藏导入模块
```python
hiddenimports=[
    # PySide6 模块
    'PySide6.QtCore', 'PySide6.QtGui', 'PySide6.QtWidgets',
    
    # 科学计算库
    'matplotlib', 'numpy', 'pandas', 'openpyxl',
    
    # 项目模块
    'core.*', 'views.*', 'ctrl.*',
    
    # ... 更多模块
]
```

#### 排除模块
```python
excludes=[
    'tkinter', 'test', 'unittest', 'pytest',
    'setuptools', 'pip', 'wheel'
]
```

## 📁 输出结构

打包完成后，输出目录结构如下：
```
dist/vibration_transfer/
├── 振动传递计算软件.exe    # 主执行文件
├── assets/                  # 资源文件
│   ├── icons/              # 图标文件
│   └── styles/             # 样式文件
├── help/                   # 帮助文档
│   ├── html/               # HTML帮助页面
│   ├── topics/             # 帮助主题
│   └── images/             # 帮助图片
├── config/                 # 配置文件
├── translations/           # 翻译文件
├── script/                 # 脚本文件
├── originscript/           # 原始脚本
├── ui/                     # UI文件
└── _internal/              # PyInstaller内部文件
```

## 🔧 常见问题与解决方案

### 1. 模块导入错误
**问题**: 运行时提示"No module named 'xxx'"

**解决方案**:
- 在`qt_new.spec`的`hiddenimports`中添加缺失的模块
- 检查模块是否正确安装

### 2. 资源文件缺失
**问题**: 程序运行时找不到图标、样式等文件

**解决方案**:
- 检查`qt_new.spec`中的`datas`配置
- 确保资源文件路径正确

### 3. 打包文件过大
**问题**: 生成的exe文件过大

**解决方案**:
- 在`excludes`中添加不需要的模块
- 使用UPX压缩（已在spec中配置）
- 移除不必要的数据文件

### 4. 启动速度慢
**问题**: 程序启动时间较长

**解决方案**:
- 启用懒加载模式（已在代码中实现）
- 优化导入顺序
- 减少启动时的初始化操作

### 5. 中文字符显示问题
**问题**: 中文字符显示乱码

**解决方案**:
- 确保所有文本文件使用UTF-8编码
- 检查字体文件是否正确包含
- 验证翻译文件是否正确打包

## 📊 性能优化

### 打包大小优化
1. **排除不必要的模块**
   ```python
   excludes=['tkinter', 'test', 'unittest', 'pytest']
   ```

2. **使用UPX压缩**
   ```python
   upx=True
   ```

3. **选择性包含数据文件**
   - 只包含必需的资源文件
   - 压缩图片和音频文件

### 启动速度优化
1. **懒加载模式**
   - 主窗口优先加载
   - 其他窗口按需创建

2. **样式缓存**
   - 关键样式优先加载
   - 完整样式延迟加载

## 🧪 测试打包结果

### 基本功能测试
1. **启动测试**
   - 双击exe文件能否正常启动
   - 主界面是否正确显示

2. **功能测试**
   - 各个模块是否正常工作
   - 文件读写是否正常
   - API服务是否正常启动

3. **资源测试**
   - 图标是否正确显示
   - 样式是否正确应用
   - 帮助文档是否可以打开

### 兼容性测试
1. **不同Windows版本**
   - Windows 10
   - Windows 11

2. **不同硬件配置**
   - 不同CPU架构
   - 不同内存大小

## 📝 打包日志

打包过程中的详细日志保存在：
- `build_package.log` - 完整打包日志
- PyInstaller输出 - 控制台输出

## 🎯 最佳实践

1. **打包前准备**
   - 确保代码无语法错误
   - 运行完整测试套件
   - 更新版本信息

2. **定期清理**
   - 删除旧的构建文件
   - 清理临时文件

3. **版本管理**
   - 为每个版本创建标签
   - 保存打包配置的变更记录

4. **分发准备**
   - 创建安装包
   - 准备用户手册
   - 测试在干净环境中的运行

## 🔗 相关文件

- `qt_new.spec` - PyInstaller配置文件
- `build_package.py` - 自动化打包脚本
- `build_simple.bat` - 简化打包脚本
- `requirements.txt` - 依赖包列表
- `version_info.txt` - 版本信息文件

## 📞 技术支持

如果在打包过程中遇到问题，请：
1. 查看打包日志文件
2. 检查依赖包是否完整安装
3. 验证spec文件配置是否正确
4. 参考PyInstaller官方文档
