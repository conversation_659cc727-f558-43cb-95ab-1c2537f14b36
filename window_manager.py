"""
窗口管理器模块

此模块实现了窗口管理器，负责处理：
1. 窗口实例的管理（支持懒加载）
2. 窗口状态的维护
3. 窗口切换的控制
4. 窗口间的通信
5. 处理流程状态跟踪

设计模式：
- 观察者模式：处理窗口间的状态同步
- 状态模式：管理窗口切换状态
- 工厂模式：窗口懒加载创建

优化特性：
- 窗口懒加载：按需创建窗口实例
- 性能监控：集成启动性能测量
- 配置驱动：支持优化开关控制

作者: [作者名]
日期: [日期]
"""

from typing import Dict, Optional, List, Any, Callable
from enum import Enum, auto
from PySide6.QtWidgets import QMainWindow
from PySide6.QtCore import QMutex, QReadWriteLock, QMutexLocker, QReadLocker, QWriteLocker, QTimer
import threading
import logging

# 导入优化相关模块
try:
    from core.optimization_config import is_optimization_enabled, get_config_value
    from core.performance_monitor import measure_performance, log_checkpoint
    HAS_OPTIMIZATION = True
except ImportError:
    HAS_OPTIMIZATION = False
    def is_optimization_enabled(name): return False
    def get_config_value(key, default=None): return default
    def measure_performance(name): return lambda f: f
    def log_checkpoint(name): pass


class WindowType(Enum):
    """窗口类型枚举"""
    MAIN = auto()
    MESH = auto()
    PRE = auto()
    CONNECTION = auto()
    ANALYSIS = auto()
    CONSTRAIN = auto()
    RESULT = auto()
    VIBRATION = auto()
    LOG = auto()  # 新增日志查看器窗口类型


class ProcessStatus:
    """流程状态管理类
    
    跟踪各个界面的完成状态，确保流程按顺序执行。
    实现了单例模式，确保全局状态一致。
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ProcessStatus, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._status = {
            WindowType.MESH: False,
            WindowType.CONNECTION: False,
            WindowType.ANALYSIS: False,
            WindowType.CONSTRAIN: False,
            WindowType.RESULT: False
        }
        self._initialized = True
    
    def set_completed(self, window_type: WindowType) -> None:
        """设置指定窗口的完成状态
        
        Args:
            window_type: 窗口类型
        """
        if window_type in self._status:
            self._status[window_type] = True
    
    def reset_status(self, window_type: WindowType) -> None:
        """重置指定窗口的完成状态
        
        Args:
            window_type: 窗口类型
        """
        if window_type in self._status:
            self._status[window_type] = False
    
    def is_completed(self, window_type: WindowType) -> bool:
        """检查指定窗口是否完成
        
        Args:
            window_type: 窗口类型
            
        Returns:
            是否完成
        """
        return self._status.get(window_type, False)
    
    def check_previous_completed(self, window_type: WindowType) -> bool:
        """检查指定窗口之前的所有步骤是否完成
        
        Args:
            window_type: 窗口类型
            
        Returns:
            之前的步骤是否都已完成
        """
        order = [
            WindowType.MESH,
            WindowType.CONNECTION,
            WindowType.ANALYSIS,
            WindowType.CONSTRAIN,
            WindowType.RESULT
        ]
        
        if window_type not in order:
            return False
            
        current_index = order.index(window_type)
        for window in order[:current_index]:
            if not self._status[window]:
                return False
        return True
    
    def reset_all(self) -> None:
        """重置所有状态"""
        for window_type in self._status:
            self._status[window_type] = False


class WindowObserver:
    """窗口观察者接口"""
    def on_window_changed(self, from_window: WindowType, to_window: WindowType) -> None:
        """当窗口切换时被调用
        
        Args:
            from_window: 切换前的窗口类型
            to_window: 切换后的窗口类型
        """
        pass 


class WindowManager:
    """窗口管理器类

    负责管理所有窗口实例和状态，支持懒加载优化。
    """

    def __init__(self):
        self._windows: Dict[WindowType, QMainWindow] = {}
        self._window_factories: Dict[WindowType, Callable[[], QMainWindow]] = {}
        self._current_window: Optional[WindowType] = None
        self._observers: List[WindowObserver] = []
        self.process_status = ProcessStatus()
        self.process_status.reset_all()
        self.initial_data = {}  # 存储从启动配置文件或API接收的数据

        # 性能优化相关
        self._window_states_cache: Dict[WindowType, Dict] = {}
        self._switch_performance: Dict[str, float] = {}
        self._transition_timer: Optional[QTimer] = None

        # 线程安全相关
        self._windows_lock = QReadWriteLock()  # 读写锁，用于窗口字典访问
        self._data_lock = QMutex()  # 互斥锁，用于数据访问
        self._observers_lock = QMutex()  # 互斥锁，用于观察者列表访问
        self._cache_lock = QMutex()  # 互斥锁，用于缓存访问
        self._factories_lock = QMutex()  # 互斥锁，用于工厂函数访问

        # 懒加载相关
        self._lazy_loading_enabled = is_optimization_enabled('lazy_loading') if HAS_OPTIMIZATION else False
        self._preload_timer: Optional[QTimer] = None

        # 日志记录
        self.logger = logging.getLogger(__name__)

        if self._lazy_loading_enabled:
            self.logger.info("WindowManager 懒加载模式已启用")
        else:
            self.logger.info("WindowManager 传统模式")

        print("WindowManager instance created.")
    
    def register_window(self, window_type: WindowType, window: QMainWindow) -> None:
        """注册窗口实例（线程安全）

        Args:
            window_type: 窗口类型
            window: 窗口实例
        """
        with QWriteLocker(self._windows_lock):
            self._windows[window_type] = window
            self.logger.info(f"注册窗口: {window_type.name}")

    def register_window_factory(self, window_type: WindowType, factory: Callable[[], QMainWindow]) -> None:
        """注册窗口工厂函数（用于懒加载）

        Args:
            window_type: 窗口类型
            factory: 窗口创建工厂函数
        """
        with QMutexLocker(self._factories_lock):
            self._window_factories[window_type] = factory
            self.logger.info(f"注册窗口工厂: {window_type.name}")

    @measure_performance("create_window_lazy")
    def _create_window_lazy(self, window_type: WindowType) -> Optional[QMainWindow]:
        """懒加载创建窗口

        Args:
            window_type: 窗口类型

        Returns:
            创建的窗口实例，如果失败则返回None
        """
        with QMutexLocker(self._factories_lock):
            factory = self._window_factories.get(window_type)

        if factory is None:
            self.logger.error(f"未找到窗口工厂: {window_type.name}")
            return None

        try:
            self.logger.info(f"懒加载创建窗口: {window_type.name}")
            window = factory()

            # 注册创建的窗口
            with QWriteLocker(self._windows_lock):
                self._windows[window_type] = window

            return window

        except Exception as e:
            self.logger.error(f"懒加载创建窗口失败 {window_type.name}: {e}")
            return None

    def start_preload_timer(self) -> None:
        """启动预加载定时器"""
        if not self._lazy_loading_enabled or not get_config_value('preload_critical_windows', True):
            return

        delay_ms = get_config_value('lazy_loading_delay_ms', 100)

        if self._preload_timer is None:
            self._preload_timer = QTimer()
            self._preload_timer.setSingleShot(True)
            self._preload_timer.timeout.connect(self._preload_critical_windows)

        self._preload_timer.start(delay_ms)
        self.logger.info(f"预加载定时器已启动，延迟 {delay_ms}ms")

    def _preload_critical_windows(self) -> None:
        """预加载关键窗口"""
        critical_windows = [WindowType.MESH, WindowType.PRE, WindowType.CONNECTION]

        for window_type in critical_windows:
            if window_type not in self._windows and window_type in self._window_factories:
                self._create_window_lazy(window_type)

        self.logger.info("关键窗口预加载完成")

    def register_observer(self, observer: WindowObserver) -> None:
        """注册观察者（线程安全）

        Args:
            observer: 观察者实例
        """
        with QMutexLocker(self._observers_lock):
            if observer not in self._observers:
                self._observers.append(observer)
    
    def unregister_observer(self, observer: WindowObserver) -> None:
        """注销观察者
        
        Args:
            observer: 观察者实例
        """
        if observer in self._observers:
            self._observers.remove(observer)
    
    def switch_to(self, window_type: WindowType, hide_current: bool = True) -> None:
        """切换到指定窗口（性能优化版本）

        在切换窗口时，会检查是否满足前置条件。
        优化特性：
        1. 窗口状态缓存
        2. 批量样式应用
        3. 异步过渡效果
        4. 性能监控

        Args:
            window_type: 目标窗口类型
            hide_current: 是否隐藏当前窗口，默认为True

        Raises:
            ValueError: 如果窗口类型未注册
        """
        import time
        start_time = time.time()

        # 获取或创建目标窗口
        new_window = self.get_window(window_type)
        if new_window is None:
            raise ValueError(f"Window type {window_type} not registered and cannot be created")

        # 如果切换到相同窗口，直接返回
        if self._current_window == window_type:
            return

        # 获取当前窗口实例
        current_window = self._windows.get(self._current_window) if self._current_window else None

        # 批量处理窗口状态
        self._batch_window_switch(current_window, new_window, hide_current)

        # 通知观察者（异步）
        self._notify_observers_async(self._current_window, window_type)

        # 更新当前窗口
        previous_window = self._current_window
        self._current_window = window_type

        # 记录性能指标
        duration = time.time() - start_time
        self._record_switch_performance(previous_window, window_type, duration)

        print(f"Window switch completed: {previous_window} -> {window_type} ({duration:.3f}s)")
    
    def apply_transition_effect(self, window: QMainWindow) -> None:
        """应用窗口过渡效果
        
        为窗口切换添加平滑的视觉过渡效果。
        
        Args:
            window: 要应用效果的窗口实例
        """
        # 使用简单的透明度设置，避免使用可能导致问题的动画效果
        # 直接设置窗口不透明度为1.0
        window.setWindowOpacity(1.0)
    
    def get_window(self, window_type: WindowType) -> Optional[QMainWindow]:
        """获取指定类型的窗口实例（线程安全，支持懒加载）

        Args:
            window_type: 窗口类型

        Returns:
            窗口实例，如果不存在则返回None
        """
        # 首先检查窗口是否已存在
        with QReadLocker(self._windows_lock):
            window = self._windows.get(window_type)

        # 如果窗口不存在且启用了懒加载，尝试创建
        if window is None and self._lazy_loading_enabled:
            window = self._create_window_lazy(window_type)

        return window
    
    def get_current_window(self) -> Optional[QMainWindow]:
        """获取当前窗口实例
        
        Returns:
            当前窗口实例，如果不存在则返回None
        """
        if self._current_window:
            return self._windows.get(self._current_window)
        return None
    
    def get_current_window_type(self) -> Optional[WindowType]:
        """获取当前窗口类型

        Returns:
            当前窗口类型，如果不存在则返回None
        """
        return self._current_window

    def _batch_window_switch(self, current_window: Optional[QMainWindow],
                           new_window: QMainWindow, hide_current: bool) -> None:
        """批量处理窗口切换操作

        Args:
            current_window: 当前窗口实例
            new_window: 新窗口实例
            hide_current: 是否隐藏当前窗口
        """
        try:
            # 缓存当前窗口状态并隐藏
            if current_window and hide_current:
                print(f"隐藏当前窗口: {current_window.__class__.__name__}")
                self._cache_window_state(self._current_window, current_window)
                current_window.hide()
                # 确保窗口真正隐藏
                current_window.setVisible(False)

            # 恢复新窗口状态
            target_window_type = self._get_target_window_type(new_window)
            self._restore_window_state(target_window_type, new_window)

            # 应用过渡效果并显示
            self.apply_transition_effect(new_window)
            print(f"显示新窗口: {new_window.__class__.__name__}")
            new_window.show()
            # 确保窗口真正显示
            new_window.setVisible(True)
            new_window.raise_()  # 将窗口提到前台
            new_window.activateWindow()  # 激活窗口

        except Exception as e:
            print(f"Error in batch window switch: {e}")
            import traceback
            traceback.print_exc()

    def _cache_window_state(self, window_type: Optional[WindowType], window: QMainWindow) -> None:
        """缓存窗口状态

        Args:
            window_type: 窗口类型
            window: 窗口实例
        """
        if not window_type:
            return

        try:
            state = {
                'geometry': window.geometry(),
                'window_state': window.windowState(),
                'opacity': window.windowOpacity(),
                'visible': window.isVisible()
            }
            self._window_states_cache[window_type] = state
        except Exception as e:
            print(f"Error caching window state for {window_type}: {e}")

    def _restore_window_state(self, window_type: Optional[WindowType], window: QMainWindow) -> None:
        """恢复窗口状态

        Args:
            window_type: 窗口类型
            window: 窗口实例
        """
        if not window_type or window_type not in self._window_states_cache:
            return

        try:
            state = self._window_states_cache[window_type]
            window.setGeometry(state['geometry'])
            window.setWindowState(state['window_state'])
            window.setWindowOpacity(state['opacity'])
        except Exception as e:
            print(f"Error restoring window state for {window_type}: {e}")

    def _get_target_window_type(self, window: QMainWindow) -> Optional[WindowType]:
        """根据窗口实例获取窗口类型

        Args:
            window: 窗口实例

        Returns:
            窗口类型
        """
        for window_type, registered_window in self._windows.items():
            if registered_window is window:
                return window_type
        return None

    def _notify_observers_async(self, from_window: Optional[WindowType],
                              to_window: WindowType) -> None:
        """异步通知观察者

        Args:
            from_window: 源窗口类型
            to_window: 目标窗口类型
        """
        try:
            # 使用QTimer实现异步通知，避免阻塞UI
            if not self._transition_timer:
                from PySide6.QtCore import QTimer
                self._transition_timer = QTimer()
                self._transition_timer.setSingleShot(True)

            # 断开之前的连接，避免重复连接
            try:
                self._transition_timer.timeout.disconnect()
            except (RuntimeError, TypeError):
                # 如果没有连接则忽略错误
                pass

            def notify():
                for observer in self._observers:
                    try:
                        observer.on_window_changed(from_window, to_window)
                    except Exception as e:
                        print(f"Observer notification error: {e}")

            self._transition_timer.timeout.connect(notify)
            self._transition_timer.start(10)  # 10ms延迟

        except Exception as e:
            print(f"Error in async observer notification: {e}")

    def _record_switch_performance(self, from_window: Optional[WindowType],
                                 to_window: WindowType, duration: float) -> None:
        """记录窗口切换性能

        Args:
            from_window: 源窗口类型
            to_window: 目标窗口类型
            duration: 切换耗时
        """
        switch_key = f"{from_window}->{to_window}"
        self._switch_performance[switch_key] = duration

        # 保持最近50次记录
        if len(self._switch_performance) > 50:
            oldest_key = next(iter(self._switch_performance))
            del self._switch_performance[oldest_key]

    def get_performance_stats(self) -> Dict[str, float]:
        """获取性能统计信息

        Returns:
            性能统计字典
        """
        if not self._switch_performance:
            return {}

        times = list(self._switch_performance.values())
        return {
            'average_switch_time': sum(times) / len(times),
            'min_switch_time': min(times),
            'max_switch_time': max(times),
            'total_switches': len(times)
        }

    def is_window_registered(self, window_type: WindowType) -> bool:
        """检查窗口是否已注册

        Args:
            window_type: 窗口类型

        Returns:
            bool: 是否已注册
        """
        return window_type in self._windows

    def get_registered_window_types(self) -> List[WindowType]:
        """获取所有已注册的窗口类型

        Returns:
            List[WindowType]: 已注册的窗口类型列表
        """
        return list(self._windows.keys())

    def clear_performance_stats(self) -> None:
        """清除性能统计数据"""
        self._switch_performance.clear()
        print("性能统计数据已清除")

    def safe_update_initial_data(self, data: Dict[str, Any]) -> None:
        """线程安全地更新初始数据

        Args:
            data: 要更新的数据
        """
        with QMutexLocker(self._data_lock):
            if hasattr(self, 'initial_data'):
                self.initial_data.update(data)
            else:
                self.initial_data = data.copy()

    def safe_get_initial_data(self) -> Dict[str, Any]:
        """线程安全地获取初始数据

        Returns:
            Dict[str, Any]: 初始数据的副本
        """
        with QMutexLocker(self._data_lock):
            return self.initial_data.copy() if hasattr(self, 'initial_data') else {}

    def safe_get_all_windows(self) -> Dict[WindowType, QMainWindow]:
        """线程安全地获取所有窗口

        Returns:
            Dict[WindowType, QMainWindow]: 窗口字典的副本
        """
        with QReadLocker(self._windows_lock):
            return self._windows.copy()

    def safe_get_observers(self) -> List[WindowObserver]:
        """线程安全地获取观察者列表

        Returns:
            List[WindowObserver]: 观察者列表的副本
        """
        with QMutexLocker(self._observers_lock):
            return self._observers.copy()

    def validate_window_states(self) -> Dict[str, bool]:
        """验证所有窗口的显示状态

        Returns:
            Dict[str, bool]: 窗口类型到显示状态的映射
        """
        states = {}
        for window_type, window in self._windows.items():
            try:
                is_visible = window.isVisible()
                states[window_type.name] = is_visible
                print(f"窗口 {window_type.name}: {'显示' if is_visible else '隐藏'}")
            except Exception as e:
                print(f"检查窗口 {window_type.name} 状态时出错: {e}")
                states[window_type.name] = False

        return states

    def force_hide_all_except(self, except_window_type: WindowType) -> None:
        """强制隐藏除指定窗口外的所有窗口

        Args:
            except_window_type: 不隐藏的窗口类型
        """
        for window_type, window in self._windows.items():
            if window_type != except_window_type:
                try:
                    window.hide()
                    window.setVisible(False)
                    print(f"强制隐藏窗口: {window_type.name}")
                except Exception as e:
                    print(f"强制隐藏窗口 {window_type.name} 时出错: {e}")

    def debug_switch_to(self, window_type: WindowType, hide_current: bool = True) -> None:
        """调试版本的窗口切换，包含详细日志

        Args:
            window_type: 目标窗口类型
            hide_current: 是否隐藏当前窗口
        """
        print(f"\n=== 开始窗口切换调试 ===")
        print(f"目标窗口: {window_type.name}")
        print(f"当前窗口: {self._current_window.name if self._current_window else 'None'}")
        print(f"隐藏当前窗口: {hide_current}")

        # 切换前的状态
        print("切换前窗口状态:")
        self.validate_window_states()

        # 执行切换
        try:
            self.switch_to(window_type, hide_current)
            print("窗口切换完成")
        except Exception as e:
            print(f"窗口切换失败: {e}")
            import traceback
            traceback.print_exc()

        # 切换后的状态
        print("切换后窗口状态:")
        self.validate_window_states()
        print(f"=== 窗口切换调试结束 ===\n")