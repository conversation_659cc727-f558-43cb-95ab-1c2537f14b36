<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>前处理</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout_main">
    <property name="spacing">
     <number>10</number>
    </property>
    <property name="leftMargin">
     <number>10</number>
    </property>
    <property name="topMargin">
     <number>10</number>
    </property>
    <property name="rightMargin">
     <number>10</number>
    </property>
    <property name="bottomMargin">
     <number>10</number>
    </property>
    <item>
     <widget class="QTabWidget" name="tabWidget_main">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="tab_geometry">
       <attribute name="title">
        <string>几何处理</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_geometry">
        <item>
         <widget class="QLabel" name="label_geometry_info">
          <property name="text">
           <string>几何处理功能区域</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="font">
           <font>
            <family>Microsoft YaHei UI</family>
            <pointsize>16</pointsize>
           </font>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer_geometry">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="tab_material">
       <attribute name="title">
        <string>材料管理</string>
       </attribute>
       <layout class="QHBoxLayout" name="horizontalLayout_material">
        <property name="spacing">
         <number>15</number>
        </property>
        <item>
         <widget class="QWidget" name="widget_material_left">
          <property name="maximumWidth">
           <number>400</number>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_material_left">
           <property name="spacing">
            <number>10</number>
           </property>
           <item>
            <widget class="QLineEdit" name="lineEdit_material_search">
             <property name="placeholderText">
              <string>搜索材料...</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QTreeWidget" name="treeWidget_material_library">
             <property name="headerHidden">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_material_buttons">
             <item>
              <widget class="QPushButton" name="pushButton_material_new">
               <property name="text">
                <string>新建</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_material_copy">
               <property name="text">
                <string>复制</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="pushButton_material_delete">
               <property name="text">
                <string>删除</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget_material_right">
          <layout class="QVBoxLayout" name="verticalLayout_material_right">
           <property name="spacing">
            <number>15</number>
           </property>
           <item>
            <widget class="QGroupBox" name="groupBox_material_properties">
             <property name="title">
              <string>材料属性</string>
             </property>
             <layout class="QFormLayout" name="formLayout_material_properties">
              <property name="fieldGrowthPolicy">
               <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
              </property>
              <item row="0" column="0">
               <widget class="QLabel" name="label_material_name">
                <property name="text">
                 <string>材料名称:</string>
                </property>
               </widget>
              </item>
              <item row="0" column="1">
               <widget class="QLineEdit" name="lineEdit_material_name"/>
              </item>
              <item row="1" column="0">
               <widget class="QLabel" name="label_young_modulus">
                <property name="text">
                 <string>杨氏模量 (GPa):</string>
                </property>
               </widget>
              </item>
              <item row="1" column="1">
               <widget class="QDoubleSpinBox" name="doubleSpinBox_young_modulus">
                <property name="suffix">
                 <string> GPa</string>
                </property>
                <property name="decimals">
                 <number>2</number>
                </property>
                <property name="maximum">
                 <double>999999.000000000000000</double>
                </property>
                <property name="value">
                 <double>200.000000000000000</double>
                </property>
               </widget>
              </item>
              <item row="2" column="0">
               <widget class="QLabel" name="label_density">
                <property name="text">
                 <string>密度 (kg/m³):</string>
                </property>
               </widget>
              </item>
              <item row="2" column="1">
               <widget class="QDoubleSpinBox" name="doubleSpinBox_density">
                <property name="suffix">
                 <string> kg/m³</string>
                </property>
                <property name="decimals">
                 <number>2</number>
                </property>
                <property name="maximum">
                 <double>99999.000000000000000</double>
                </property>
                <property name="value">
                 <double>7850.000000000000000</double>
                </property>
               </widget>
              </item>
              <item row="3" column="0">
               <widget class="QLabel" name="label_poisson_ratio">
                <property name="text">
                 <string>泊松比:</string>
                </property>
               </widget>
              </item>
              <item row="3" column="1">
               <widget class="QDoubleSpinBox" name="doubleSpinBox_poisson_ratio">
                <property name="decimals">
                 <number>3</number>
                </property>
                <property name="maximum">
                 <double>0.500000000000000</double>
                </property>
                <property name="singleStep">
                 <double>0.010000000000000</double>
                </property>
                <property name="value">
                 <double>0.300000000000000</double>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <widget class="QGroupBox" name="groupBox_material_assignment">
             <property name="title">
              <string>结构体分配</string>
             </property>
             <layout class="QVBoxLayout" name="verticalLayout_assignment">
              <item>
               <layout class="QFormLayout" name="formLayout_assignment">
                <item row="0" column="0">
                 <widget class="QLabel" name="label_structure_select">
                  <property name="text">
                   <string>选择结构体:</string>
                  </property>
                 </widget>
                </item>
                <item row="0" column="1">
                 <widget class="QComboBox" name="comboBox_structure_select"/>
                </item>
               </layout>
              </item>
              <item>
               <layout class="QHBoxLayout" name="horizontalLayout_assignment_buttons">
                <item>
                 <widget class="QPushButton" name="pushButton_assign_material">
                  <property name="text">
                   <string>分配材料</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="pushButton_apply_all">
                  <property name="text">
                   <string>应用到全部</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_material_right">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>40</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <layout class="QHBoxLayout" name="horizontalLayout_navigation">
      <property name="spacing">
       <number>20</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_left">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="push_finish">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>完成前处理</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_meshui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>250</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>下一步(连接设置)</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="push_mainui">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>200</width>
          <height>65</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft YaHei UI</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>返回主界面</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_right">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
