"""
模态分析图表控制面板

此模块提供图表控制界面，包括：
1. 图表类型选择
2. 显示选项控制
3. 导入结果管理
4. 图表保存和导出

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import logging
from typing import Dict, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QComboBox, 
    QCheckBox, QGroupBox, QLabel, QFileDialog, QMessageBox
)
from PySide6.QtCore import Signal

from .modal_data_manager import ModalDataManager
from .modal_chart_widget import ModalChartWidget
from .modal_import_dialog import ModalImportDialog

logger = logging.getLogger(__name__)


class ModalChartControlPanel(QWidget):
    """模态分析图表控制面板"""
    
    # 信号定义
    chart_type_changed = Signal(str)  # 图表类型改变
    options_changed = Signal(dict)    # 显示选项改变
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化数据管理器
        self.data_manager = ModalDataManager()
        
        # 初始化图表组件
        self.chart_widget = ModalChartWidget(parent=self, data_manager=self.data_manager)
        
        # 当前数据
        self.current_mesh_data = []
        
        self.setup_ui()
        self.connect_signals()
    
    def setup_ui(self):
        """设置用户界面"""
        main_layout = QVBoxLayout(self)
        
        # 控制面板
        control_group = self.create_control_group()
        main_layout.addWidget(control_group)
        
        # 图表组件
        main_layout.addWidget(self.chart_widget)
        
        # 设置布局比例
        main_layout.setStretch(0, 0)  # 控制面板固定高度
        main_layout.setStretch(1, 1)  # 图表组件可伸缩
    
    def create_control_group(self) -> QGroupBox:
        """创建控制组"""
        group = QGroupBox("Chart Controls")
        layout = QVBoxLayout(group)
        
        # 第一行：图表类型和基本控制
        row1_layout = QHBoxLayout()
        
        # 图表类型选择
        row1_layout.addWidget(QLabel("Chart Type:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "Frequency Comparison",
            "Modal Distribution",
            "Mesh Convergence Analysis"
        ])
        row1_layout.addWidget(self.chart_type_combo)
        
        # 更新按钮
        self.update_chart_button = QPushButton("Update Chart")
        row1_layout.addWidget(self.update_chart_button)
        
        # 保存按钮
        self.save_chart_button = QPushButton("Save Chart")
        row1_layout.addWidget(self.save_chart_button)
        
        row1_layout.addStretch()
        layout.addLayout(row1_layout)
        
        # 第二行：导入和显示选项
        row2_layout = QHBoxLayout()
        
        # 导入结果按钮
        self.import_button = QPushButton("Import Results")
        self.import_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        row2_layout.addWidget(self.import_button)
        
        # 管理导入结果按钮
        self.manage_button = QPushButton("Manage Imported")
        row2_layout.addWidget(self.manage_button)
        
        # 分隔符
        row2_layout.addWidget(QLabel("|"))
        
        # 显示选项
        self.show_current_cb = QCheckBox("Show Current Results")
        self.show_current_cb.setChecked(True)
        row2_layout.addWidget(self.show_current_cb)
        
        self.show_imported_cb = QCheckBox("Show Imported Results")
        self.show_imported_cb.setChecked(True)
        row2_layout.addWidget(self.show_imported_cb)
        
        self.show_frequencies_cb = QCheckBox("Show Frequency Labels")
        self.show_frequencies_cb.setChecked(True)
        row2_layout.addWidget(self.show_frequencies_cb)
        
        row2_layout.addStretch()
        layout.addLayout(row2_layout)
        
        # 第三行：统计信息
        row3_layout = QHBoxLayout()
        self.stats_label = QLabel("Ready")
        self.stats_label.setStyleSheet("color: #666; font-size: 11px;")
        row3_layout.addWidget(self.stats_label)
        row3_layout.addStretch()
        layout.addLayout(row3_layout)
        
        return group
    
    def connect_signals(self):
        """连接信号"""
        # 控制按钮
        self.chart_type_combo.currentTextChanged.connect(self.on_chart_type_changed)
        self.update_chart_button.clicked.connect(self.update_chart)
        self.save_chart_button.clicked.connect(self.save_chart)
        
        # 导入功能
        self.import_button.clicked.connect(self.import_results)
        self.manage_button.clicked.connect(self.manage_imported_results)
        
        # 显示选项
        self.show_current_cb.toggled.connect(self.on_options_changed)
        self.show_imported_cb.toggled.connect(self.on_options_changed)
        self.show_frequencies_cb.toggled.connect(self.on_options_changed)
        
        # 数据管理器信号（如果有的话）
        # self.data_manager.results_updated.connect(self.update_stats)
    
    def on_chart_type_changed(self, text: str):
        """图表类型改变处理"""
        chart_type_map = {
            "Frequency Comparison": "frequency_comparison",
            "Modal Distribution": "mode_distribution",
            "Mesh Convergence Analysis": "mesh_convergence"
        }
        chart_type = chart_type_map.get(text, "frequency_comparison")
        self.chart_type_changed.emit(chart_type)
        
        # 自动更新图表
        if self.current_mesh_data:
            self.update_chart()
    
    def on_options_changed(self):
        """显示选项改变处理"""
        options = self.get_current_options()
        self.options_changed.emit(options)
        
        # 自动更新图表
        if self.current_mesh_data:
            self.update_chart()
    
    def get_current_options(self) -> Dict[str, Any]:
        """获取当前显示选项"""
        return {
            'show_current': self.show_current_cb.isChecked(),
            'show_imported': self.show_imported_cb.isChecked(),
            'show_frequencies': self.show_frequencies_cb.isChecked(),
            'show_mode_shapes': True,
            'show_mesh_info': True
        }
    
    def get_current_chart_type(self) -> str:
        """获取当前图表类型"""
        chart_type_map = {
            "Frequency Comparison": "frequency_comparison",
            "Modal Distribution": "mode_distribution", 
            "Mesh Convergence Analysis": "mesh_convergence"
        }
        return chart_type_map.get(self.chart_type_combo.currentText(), "frequency_comparison")
    
    def update_mesh_data(self, mesh_data):
        """更新网格数据"""
        self.current_mesh_data = mesh_data
        self.update_chart()
        self.update_stats()
    
    def update_chart(self):
        """更新图表"""
        if not self.current_mesh_data:
            return
        
        chart_type = self.get_current_chart_type()
        options = self.get_current_options()
        
        try:
            self.chart_widget.update_chart(chart_type, self.current_mesh_data, options)
            logger.debug(f"Chart updated: {chart_type}")
        except Exception as e:
            logger.error(f"Failed to update chart: {str(e)}")
            QMessageBox.critical(self, "Chart Update Error", f"Failed to update chart: {str(e)}")
    
    def save_chart(self):
        """保存图表"""
        file_dialog = QFileDialog(self)
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setNameFilter("PNG Files (*.png);;PDF Files (*.pdf);;SVG Files (*.svg)")
        file_dialog.setDefaultSuffix("png")
        
        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            try:
                self.chart_widget.save_chart(file_path, dpi=300)
                QMessageBox.information(self, "Save Complete", f"Chart saved to {file_path}")
            except Exception as e:
                logger.error(f"Failed to save chart: {str(e)}")
                QMessageBox.critical(self, "Save Error", f"Failed to save chart: {str(e)}")
    
    def import_results(self):
        """导入结果"""
        file_dialog = QFileDialog(self)
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFiles)
        file_dialog.setNameFilter(
            "Modal Results Files (*.json *.csv *.txt);;JSON Files (*.json);;CSV Files (*.csv);;Text Files (*.txt)"
        )
        
        if file_dialog.exec():
            selected_files = file_dialog.selectedFiles()
            success_count = 0
            
            for file_path in selected_files:
                if self.data_manager.import_from_file(file_path):
                    success_count += 1
            
            if success_count > 0:
                self.update_chart()
                self.update_stats()
                QMessageBox.information(
                    self, "Import Complete", 
                    f"Successfully imported {success_count} out of {len(selected_files)} files."
                )
            else:
                QMessageBox.warning(self, "Import Failed", "No files were successfully imported.")
    
    def manage_imported_results(self):
        """管理导入的结果"""
        dialog = ModalImportDialog(self.data_manager, self)
        dialog.results_imported.connect(self.on_results_updated)
        dialog.results_updated.connect(self.on_results_updated)
        dialog.exec()
    
    def on_results_updated(self):
        """导入结果更新处理"""
        self.update_chart()
        self.update_stats()
    
    def update_stats(self):
        """更新统计信息"""
        current_count = len(self.current_mesh_data)
        imported_count = len(self.data_manager.get_imported_results())
        total_count = current_count + imported_count
        
        self.stats_label.setText(
            f"Data: {current_count} current, {imported_count} imported, {total_count} total results"
        )
    
    def get_chart_widget(self) -> ModalChartWidget:
        """获取图表组件"""
        return self.chart_widget
    
    def get_data_manager(self) -> ModalDataManager:
        """获取数据管理器"""
        return self.data_manager
