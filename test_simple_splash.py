"""
测试简化版启动画面

此脚本测试兼容性更好的简化版启动画面，避免了复杂的绘制操作
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_splash():
    """测试简化版启动画面"""
    print("🎨 测试简化版启动画面...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QIcon
        from core.splash_screen_simple import SimpleSplashScreenManager
        
        print("✅ 模块导入成功")
        
        # 创建应用程序
        app = QApplication([])
        
        # 设置应用程序图标
        icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
            print(f"✅ 应用图标加载成功")
        
        # 创建启动画面管理器
        splash_manager = SimpleSplashScreenManager()
        
        # 显示启动画面
        splash = splash_manager.show_splash()
        print("✅ 启动画面显示成功")
        
        # 模拟进度更新
        steps = [
            (15, "正在初始化系统..."),
            (30, "正在加载配置文件..."),
            (45, "正在创建用户界面..."),
            (60, "正在连接服务..."),
            (75, "正在完成设置..."),
            (90, "正在启动应用..."),
            (100, "启动完成！")
        ]
        
        for progress, status in steps:
            splash_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 进度更新: {progress}% - {status}")
            time.sleep(1.8)  # 增加延迟时间，让用户更好地看到每个步骤
            app.processEvents()
        
        # 等待一下再隐藏
        time.sleep(2)
        splash_manager.hide_splash()
        print("✅ 启动画面隐藏成功")
        
        print("\n🎉 简化版启动画面测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_config():
    """测试自定义配置"""
    print("\n🌈 测试自定义配置...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from core.splash_screen_simple import SimpleSplashScreenManager
        
        app = QApplication.instance() or QApplication([])
        
        # 自定义配置
        custom_config = {
            "colors": {
                "primary": "#e74c3c",    # 红色
                "secondary": "#f39c12",  # 橙色
                "text": "#ffffff",
                "progress_background": "#34495e"
            },
            "layout": {
                "width": 500,
                "height": 340
            },
            "show_rotation_animation": True,
            "show_fade_in": True,
            "fade_in_duration": 600
        }
        
        custom_manager = SimpleSplashScreenManager(custom_config)
        custom_splash = custom_manager.show_splash()
        
        print("✅ 自定义配置启动画面显示成功")
        
        custom_steps = [
            (25, "加载自定义主题..."),
            (50, "应用红橙配色方案..."),
            (75, "优化视觉效果..."),
            (100, "自定义主题加载完成！")
        ]
        
        for progress, status in custom_steps:
            custom_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 自定义进度: {progress}% - {status}")
            time.sleep(2.0)  # 增加延迟时间，展示自定义主题效果
            app.processEvents()
        
        time.sleep(2)
        custom_manager.hide_splash()
        print("✅ 自定义配置测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 自定义配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_handling():
    """测试错误处理"""
    print("\n🛡️ 测试错误处理...")
    
    try:
        from core.splash_screen_simple import SimpleSplashScreenManager
        
        # 测试无效配置
        invalid_config = {
            "colors": {
                "primary": "invalid_color"  # 无效颜色
            },
            "layout": {
                "width": -100,  # 无效尺寸
                "height": -100
            }
        }
        
        error_manager = SimpleSplashScreenManager(invalid_config)
        error_splash = error_manager.show_splash()
        
        if error_splash:
            print("✅ 错误处理测试通过 - 启动画面仍能正常显示")
            error_manager.update_progress_by_percentage(50, "测试错误处理...")
            time.sleep(1)
            error_manager.hide_splash()
        else:
            print("⚠️ 启动画面未显示，但程序未崩溃")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🎨 简化版启动画面功能测试")
    print("=" * 60)
    print("\n简化版特点：")
    print("✨ 兼容性更好，避免复杂绘制操作")
    print("✨ 深色到浅色的美化渐变背景")
    print("✨ 基本的装饰元素")
    print("✨ 现代化的进度条设计")
    print("✨ 可选的旋转动画")
    print("✨ 良好的错误处理")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试基本功能
    print("\n1. 测试基本启动画面功能")
    if test_simple_splash():
        success_count += 1
    
    # 测试自定义配置
    print("\n2. 测试自定义配置")
    if test_custom_config():
        success_count += 1
    
    # 测试错误处理
    print("\n3. 测试错误处理")
    if test_error_handling():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"🎉 测试完成！成功 {success_count}/{total_tests} 项测试")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！简化版启动画面功能正常。")
        print("✅ 兼容性问题已解决")
        print("✅ 视觉效果良好")
        print("✅ 错误处理完善")
    else:
        print(f"⚠️ 有 {total_tests - success_count} 项测试失败")
    
    print("=" * 60)
    print("\n🎨 简化版启动画面已准备就绪！")
    print("可以在主应用程序中使用 core.splash_screen_simple 模块")

if __name__ == "__main__":
    main()
