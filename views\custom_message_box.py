"""
自定义消息框模块

此模块定义了自定义的消息框类，用于替换系统默认的QMessageBox，
确保按钮样式清晰可见，提供更好的用户体验。

作者: AI Assistant
日期: 2025-07-26
"""

import logging
from typing import Optional
from PySide6.QtCore import Qt
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QFrame)
from PySide6.QtGui import QFont, QIcon, QPixmap

# 获取模块日志记录器
logger = logging.getLogger(__name__)


class CustomMessageBox(QDialog):
    """自定义消息框类"""
    
    # 消息框类型
    Information = 0
    Question = 1
    Warning = 2
    Critical = 3
    
    # 按钮类型
    Ok = 0
    Cancel = 1
    Yes = 2
    No = 3
    
    def __init__(self, parent=None, title="提示", message="", message_type=Information, 
                 buttons=None, default_button=None):
        super().__init__(parent)
        
        self.message_type = message_type
        self.buttons = buttons or [self.Ok]
        self.default_button = default_button
        self.result_button = None
        
        self._setup_ui(title, message)
        self._setup_connections()
        
        logger.debug(f"自定义消息框创建: {title}")
    
    def _setup_ui(self, title: str, message: str):
        """设置UI界面"""
        self.setWindowTitle(title)
        self.setModal(True)
        self.setMinimumWidth(400)
        self.setMaximumWidth(600)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 根据消息类型设置图标和颜色
        if self.message_type == self.Information:
            icon_label.setText("ℹ️")
            icon_label.setStyleSheet("""
                QLabel {
                    background-color: #e3f2fd;
                    border: 2px solid #2196f3;
                    border-radius: 24px;
                    font-size: 24px;
                }
            """)
        elif self.message_type == self.Question:
            icon_label.setText("❓")
            icon_label.setStyleSheet("""
                QLabel {
                    background-color: #fff3e0;
                    border: 2px solid #ff9800;
                    border-radius: 24px;
                    font-size: 24px;
                }
            """)
        elif self.message_type == self.Warning:
            icon_label.setText("⚠️")
            icon_label.setStyleSheet("""
                QLabel {
                    background-color: #fff8e1;
                    border: 2px solid #ffc107;
                    border-radius: 24px;
                    font-size: 24px;
                }
            """)
        elif self.message_type == self.Critical:
            icon_label.setText("❌")
            icon_label.setStyleSheet("""
                QLabel {
                    background-color: #ffebee;
                    border: 2px solid #f44336;
                    border-radius: 24px;
                    font-size: 24px;
                }
            """)
        
        content_layout.addWidget(icon_label)
        
        # 消息文本
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        message_font = QFont()
        message_font.setPointSize(11)
        message_label.setFont(message_font)
        message_label.setStyleSheet("color: #333333; line-height: 1.4;")
        
        content_layout.addWidget(message_label, 1)
        layout.addLayout(content_layout)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("color: #e0e0e0;")
        layout.addWidget(separator)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 创建按钮
        self.button_widgets = {}
        for button_type in self.buttons:
            button = self._create_button(button_type)
            self.button_widgets[button_type] = button
            button_layout.addWidget(button)
        
        layout.addLayout(button_layout)
        
        # 设置默认按钮
        if self.default_button and self.default_button in self.button_widgets:
            self.button_widgets[self.default_button].setDefault(True)
            self.button_widgets[self.default_button].setFocus()
    
    def _create_button(self, button_type: int) -> QPushButton:
        """创建按钮"""
        button_texts = {
            self.Ok: "确定",
            self.Cancel: "取消", 
            self.Yes: "是",
            self.No: "否"
        }
        
        button = QPushButton(button_texts.get(button_type, "确定"))
        button.setMinimumSize(80, 35)
        
        # 根据按钮类型设置样式
        if button_type in [self.Ok, self.Yes]:
            # 确认类按钮 - 蓝色
            button.setStyleSheet("""
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 12px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                    border: 1px solid #0d47a1;
                }
                QPushButton:pressed {
                    background-color: #0d47a1;
                }
                QPushButton:default {
                    border: 2px solid #1976d2;
                }
            """)
        else:
            # 取消类按钮 - 灰色
            button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 12px;
                    padding: 8px 16px;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                    border: 1px solid #34495e;
                }
                QPushButton:pressed {
                    background-color: #34495e;
                }
            """)
        
        return button
    
    def _setup_connections(self):
        """设置信号连接"""
        for button_type, button in self.button_widgets.items():
            button.clicked.connect(lambda checked=False, bt=button_type: self._on_button_clicked(bt))
    
    def _on_button_clicked(self, button_type: int):
        """按钮点击处理"""
        self.result_button = button_type
        
        if button_type in [self.Ok, self.Yes]:
            self.accept()
        else:
            self.reject()
    
    def get_result(self) -> int:
        """获取用户选择的按钮"""
        return self.result_button
    
    @staticmethod
    def information(parent, title: str, message: str) -> int:
        """显示信息对话框"""
        dialog = CustomMessageBox(parent, title, message, CustomMessageBox.Information, [CustomMessageBox.Ok])
        dialog.exec()
        return dialog.get_result()
    
    @staticmethod
    def question(parent, title: str, message: str) -> int:
        """显示问题对话框"""
        dialog = CustomMessageBox(parent, title, message, CustomMessageBox.Question, 
                                 [CustomMessageBox.Yes, CustomMessageBox.No], CustomMessageBox.Yes)
        dialog.exec()
        return dialog.get_result()
    
    @staticmethod
    def warning(parent, title: str, message: str) -> int:
        """显示警告对话框"""
        dialog = CustomMessageBox(parent, title, message, CustomMessageBox.Warning, [CustomMessageBox.Ok])
        dialog.exec()
        return dialog.get_result()
    
    @staticmethod
    def critical(parent, title: str, message: str) -> int:
        """显示错误对话框"""
        dialog = CustomMessageBox(parent, title, message, CustomMessageBox.Critical, [CustomMessageBox.Ok])
        dialog.exec()
        return dialog.get_result()
