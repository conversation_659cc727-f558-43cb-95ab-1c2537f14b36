<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化支持 - 振动传递计算软件</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>国际化支持 (i18n)</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>🌍 概述</h2>
            <p>v1.2.0版本引入了完整的国际化支持，让全球用户都能以母语使用本软件。系统支持动态语言切换，无需重启应用程序即可切换界面语言。</p>
            
            <div class="feature-highlight">
                <h3>支持的语言</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🇨🇳 简体中文</h4>
                        <p>完整的中文界面支持，包括所有菜单、对话框和帮助文档。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🇺🇸 English</h4>
                        <p>Professional English interface with complete documentation and user guides.</p>
                    </div>
                    <div class="feature-card">
                        <h4>🇯🇵 日本語</h4>
                        <p>完全な日本語インターフェースとドキュメンテーション。</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 核心功能</h2>
            
            <div class="feature-card">
                <h3>动态语言切换</h3>
                <ul>
                    <li>运行时无需重启即可切换语言</li>
                    <li>界面文本实时更新</li>
                    <li>保持当前工作状态</li>
                    <li>自动保存语言偏好设置</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>翻译管理</h3>
                <ul>
                    <li>完整的.ts/.qm文件生成和管理</li>
                    <li>自动扫描源代码中的可翻译字符串</li>
                    <li>支持翻译文件的编译和部署</li>
                    <li>翻译质量检查和验证</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>UI组件集成</h3>
                <ul>
                    <li>专用的语言选择器组件</li>
                    <li>语言设置对话框</li>
                    <li>菜单栏语言切换选项</li>
                    <li>状态栏语言指示器</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📱 使用指南</h2>
            
            <div class="step-guide">
                <h3>切换界面语言</h3>
                <ol>
                    <li><strong>访问语言设置</strong>
                        <p>点击菜单栏"帮助" → "语言设置"，或使用快捷键组合。</p>
                    </li>
                    <li><strong>选择目标语言</strong>
                        <p>在语言设置对话框中选择您希望使用的语言。</p>
                    </li>
                    <li><strong>应用更改</strong>
                        <p>点击"应用"或"确定"按钮，界面将立即切换到新语言。</p>
                    </li>
                    <li><strong>自动保存</strong>
                        <p>语言设置会自动保存，下次启动时自动恢复。</p>
                    </li>
                </ol>
            </div>

            <div class="ui-description">
                <h3>语言选择器</h3>
                <p>语言选择器提供直观的语言切换界面：</p>
                <ul>
                    <li>显示语言的本地化名称和国旗图标</li>
                    <li>当前选中语言高亮显示</li>
                    <li>支持键盘导航和快捷键</li>
                    <li>实时预览语言切换效果</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ 技术实现</h2>
            
            <div class="feature-card">
                <h3>国际化管理器</h3>
                <p><code>core/i18n_manager.py</code></p>
                <ul>
                    <li>QTranslator集成和管理</li>
                    <li>翻译文件加载和缓存</li>
                    <li>语言切换事件处理</li>
                    <li>配置持久化管理</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>语言选择组件</h3>
                <p><code>views/language_selector.py</code></p>
                <ul>
                    <li>现代化的语言选择界面</li>
                    <li>国旗图标和本地化名称显示</li>
                    <li>实时语言切换预览</li>
                    <li>无障碍访问支持</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>翻译文件管理</h3>
                <p><code>translations/</code> 目录</p>
                <ul>
                    <li>app_zh_CN.ts/qm - 简体中文翻译</li>
                    <li>app_en_US.ts/qm - 英语翻译</li>
                    <li>app_ja_JP.ts/qm - 日语翻译</li>
                    <li>README.md - 翻译文件说明</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔧 开发工具</h2>
            
            <div class="feature-card">
                <h3>翻译文件生成器</h3>
                <p><code>tools/generate_translations.py</code></p>
                <ul>
                    <li>自动扫描源代码中的tr()调用</li>
                    <li>生成和更新.ts翻译源文件</li>
                    <li>编译.qm运行时文件</li>
                    <li>翻译完整性检查</li>
                </ul>
                
                <h4>使用方法：</h4>
                <pre><code>
# 扫描源代码并更新翻译文件
python tools/generate_translations.py --scan

# 编译翻译文件
python tools/generate_translations.py --compile

# 检查翻译完整性
python tools/generate_translations.py --check
                </code></pre>
            </div>

            <div class="feature-card">
                <h3>简单QM编译器</h3>
                <p><code>tools/simple_qm_compiler.py</code></p>
                <ul>
                    <li>轻量级的QM文件编译器</li>
                    <li>支持基本的翻译文件格式</li>
                    <li>快速的编译和部署</li>
                    <li>错误检查和验证</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📝 开发者指南</h2>
            
            <div class="note">
                <h3>添加新的可翻译字符串</h3>
                <ol>
                    <li><strong>包装字符串</strong>
                        <pre><code>
# 在文件顶部导入翻译函数
from core.i18n_manager import tr

# 使用tr()函数包装用户可见字符串
button_text = tr("确定", "DialogName")
window_title = tr("设置窗口", "DialogName")
                        </code></pre>
                    </li>
                    <li><strong>更新翻译文件</strong>
                        <pre><code>
# 扫描并更新翻译文件
python tools/generate_translations.py --scan
                        </code></pre>
                    </li>
                    <li><strong>添加翻译内容</strong>
                        <p>编辑translations/app_*.ts文件，为新字符串添加翻译。</p>
                    </li>
                    <li><strong>编译部署</strong>
                        <pre><code>
# 编译翻译文件
python tools/generate_translations.py --compile
                        </code></pre>
                    </li>
                </ol>
            </div>

            <div class="warning">
                <h3>最佳实践</h3>
                <ul>
                    <li>为所有用户可见的字符串添加翻译支持</li>
                    <li>使用有意义的上下文标识符</li>
                    <li>避免在翻译字符串中硬编码格式</li>
                    <li>定期检查翻译文件的完整性</li>
                    <li>测试所有支持语言的界面显示</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🧪 测试验证</h2>
            
            <div class="interpretation">
                <h3>自动化测试</h3>
                <p><code>tests/test_i18n.py</code> 提供完整的国际化功能测试：</p>
                <ul>
                    <li>国际化管理器初始化测试</li>
                    <li>语言切换功能测试</li>
                    <li>翻译文件加载测试</li>
                    <li>语言选择器组件测试</li>
                    <li>线程安全翻译调用测试</li>
                </ul>
            </div>

            <div class="tip">
                <h3>手动测试指南</h3>
                <ol>
                    <li>启动应用程序，验证默认语言显示</li>
                    <li>切换到不同语言，检查界面更新</li>
                    <li>重启应用程序，验证语言设置保存</li>
                    <li>测试所有对话框和菜单的翻译</li>
                    <li>验证帮助文档的多语言支持</li>
                </ol>
            </div>
        </div>

        <div class="footer">
            <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
            <p><a href="index.html" class="back-link">← 返回主页</a></p>
        </div>
    </div>
</body>
</html>
