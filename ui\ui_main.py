# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'main.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, <PERSON>Brush, QColor, QC<PERSON>alGradient,
    QCursor, QF<PERSON>, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QLabel, QMainWindow,
    QMenu, QMenuBar, QPushButton, QSizePolicy,
    QSpacerItem, QStatusBar, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(700, 520)
        MainWindow.setMinimumSize(QSize(700, 520))
        font = QFont()
        font.setFamilies([u"\u5b8b\u4f53"])
        MainWindow.setFont(font)
        self.actionwb_root_file = QAction(MainWindow)
        self.actionwb_root_file.setObjectName(u"actionwb_root_file")
        self.actionworkfile = QAction(MainWindow)
        self.actionworkfile.setObjectName(u"actionworkfile")
        self.actionnew = QAction(MainWindow)
        self.actionnew.setObjectName(u"actionnew")
        self.actionSave = QAction(MainWindow)
        self.actionSave.setObjectName(u"actionSave")
        self.actionExit = QAction(MainWindow)
        self.actionExit.setObjectName(u"actionExit")
        self.actionExport = QAction(MainWindow)
        self.actionExport.setObjectName(u"actionExport")
        self.actionAbout = QAction(MainWindow)
        self.actionAbout.setObjectName(u"actionAbout")
        self.actionOpen = QAction(MainWindow)
        self.actionOpen.setObjectName(u"actionOpen")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.horizontalLayout = QHBoxLayout(self.centralwidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.label = QLabel(self.centralwidget)
        self.label.setObjectName(u"label")
        self.label.setMinimumSize(QSize(350, 450))
        font1 = QFont()
        font1.setFamilies([u"\u9ed1\u4f53"])
        font1.setPointSize(30)
        font1.setBold(True)
        font1.setHintingPreference(QFont.PreferDefaultHinting)
        self.label.setFont(font1)
        self.label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout.addWidget(self.label)

        self.horizontalSpacer = QSpacerItem(115, 20, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Minimum)

        self.horizontalLayout.addItem(self.horizontalSpacer)

        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.preprocessing = QPushButton(self.centralwidget)
        self.preprocessing.setObjectName(u"preprocessing")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.preprocessing.sizePolicy().hasHeightForWidth())
        self.preprocessing.setSizePolicy(sizePolicy)
        self.preprocessing.setMinimumSize(QSize(200, 30))
        font2 = QFont()
        font2.setFamilies([u"\u5b8b\u4f53"])
        font2.setPointSize(15)
        font2.setBold(False)
        self.preprocessing.setFont(font2)
        self.preprocessing.setAutoDefault(False)

        self.verticalLayout.addWidget(self.preprocessing)

        self.connection = QPushButton(self.centralwidget)
        self.connection.setObjectName(u"connection")
        sizePolicy.setHeightForWidth(self.connection.sizePolicy().hasHeightForWidth())
        self.connection.setSizePolicy(sizePolicy)
        self.connection.setMinimumSize(QSize(200, 30))
        font3 = QFont()
        font3.setFamilies([u"\u5b8b\u4f53"])
        font3.setPointSize(15)
        self.connection.setFont(font3)
        self.connection.setAutoDefault(False)

        self.verticalLayout.addWidget(self.connection)

        self.analysis = QPushButton(self.centralwidget)
        self.analysis.setObjectName(u"analysis")
        sizePolicy.setHeightForWidth(self.analysis.sizePolicy().hasHeightForWidth())
        self.analysis.setSizePolicy(sizePolicy)
        self.analysis.setMinimumSize(QSize(200, 30))
        self.analysis.setFont(font3)
        self.analysis.setAutoDefault(False)

        self.verticalLayout.addWidget(self.analysis)

        self.constrain = QPushButton(self.centralwidget)
        self.constrain.setObjectName(u"constrain")
        sizePolicy.setHeightForWidth(self.constrain.sizePolicy().hasHeightForWidth())
        self.constrain.setSizePolicy(sizePolicy)
        self.constrain.setMinimumSize(QSize(200, 30))
        self.constrain.setFont(font3)
        self.constrain.setAutoDefault(False)

        self.verticalLayout.addWidget(self.constrain)

        self.mesh = QPushButton(self.centralwidget)
        self.mesh.setObjectName(u"mesh")
        sizePolicy.setHeightForWidth(self.mesh.sizePolicy().hasHeightForWidth())
        self.mesh.setSizePolicy(sizePolicy)
        self.mesh.setMinimumSize(QSize(200, 30))
        self.mesh.setFont(font2)
        self.mesh.setAutoDefault(False)

        self.verticalLayout.addWidget(self.mesh)

        self.result = QPushButton(self.centralwidget)
        self.result.setObjectName(u"result")
        sizePolicy.setHeightForWidth(self.result.sizePolicy().hasHeightForWidth())
        self.result.setSizePolicy(sizePolicy)
        self.result.setMinimumSize(QSize(200, 30))
        self.result.setFont(font3)
        self.result.setAutoDefault(False)

        self.verticalLayout.addWidget(self.result)

        self.post = QPushButton(self.centralwidget)
        self.post.setObjectName(u"post")
        sizePolicy.setHeightForWidth(self.post.sizePolicy().hasHeightForWidth())
        self.post.setSizePolicy(sizePolicy)
        self.post.setMinimumSize(QSize(200, 30))
        self.post.setFont(font3)
        self.post.setAutoDefault(False)

        self.verticalLayout.addWidget(self.post)


        self.horizontalLayout.addLayout(self.verticalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.menubar = QMenuBar(MainWindow)
        self.menubar.setObjectName(u"menubar")
        self.menubar.setGeometry(QRect(0, 0, 700, 33))
        self.menu = QMenu(self.menubar)
        self.menu.setObjectName(u"menu")
        font4 = QFont()
        font4.setFamilies([u"Times New Roman"])
        font4.setPointSize(12)
        self.menu.setFont(font4)
        self.menuSeting = QMenu(self.menubar)
        self.menuSeting.setObjectName(u"menuSeting")
        self.menuReport = QMenu(self.menubar)
        self.menuReport.setObjectName(u"menuReport")
        MainWindow.setMenuBar(self.menubar)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.menubar.addAction(self.menu.menuAction())
        self.menubar.addAction(self.menuSeting.menuAction())
        self.menubar.addAction(self.menuReport.menuAction())
        self.menu.addAction(self.actionnew)
        self.menu.addAction(self.actionOpen)
        self.menu.addAction(self.actionSave)
        self.menu.addAction(self.actionExit)
        self.menuSeting.addAction(self.actionwb_root_file)
        self.menuSeting.addAction(self.actionworkfile)
        self.menuReport.addAction(self.actionExport)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.actionwb_root_file.setText(QCoreApplication.translate("MainWindow", u"wb root file", None))
        self.actionworkfile.setText(QCoreApplication.translate("MainWindow", u"workfile", None))
        self.actionnew.setText(QCoreApplication.translate("MainWindow", u"New", None))
        self.actionSave.setText(QCoreApplication.translate("MainWindow", u"Save", None))
        self.actionExit.setText(QCoreApplication.translate("MainWindow", u"Exit", None))
        self.actionExport.setText(QCoreApplication.translate("MainWindow", u"Export", None))
        self.actionAbout.setText(QCoreApplication.translate("MainWindow", u"About", None))
        self.actionOpen.setText(QCoreApplication.translate("MainWindow", u"Open", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"\u632f\u52a8\u4f20\u9012\u8ba1\u7b97\u8f6f\u4ef6", None))
        self.preprocessing.setText(QCoreApplication.translate("MainWindow", u"\u524d\u5904\u7406", None))
        self.connection.setText(QCoreApplication.translate("MainWindow", u"\u8fde\u63a5\u8bbe\u7f6e", None))
        self.analysis.setText(QCoreApplication.translate("MainWindow", u"\u5206\u6790\u8bbe\u7f6e", None))
        self.constrain.setText(QCoreApplication.translate("MainWindow", u"\u8bbe\u7f6e\u7ea6\u675f", None))
        self.mesh.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u65e0\u5173\u6027\u9a8c\u8bc1", None))
        self.result.setText(QCoreApplication.translate("MainWindow", u"\u8ba1\u7b97\u7ed3\u679c", None))
        self.post.setText(QCoreApplication.translate("MainWindow", u"\u540e\u5904\u7406", None))
        self.menu.setTitle(QCoreApplication.translate("MainWindow", u"File", None))
        self.menuSeting.setTitle(QCoreApplication.translate("MainWindow", u"Seting", None))
        self.menuReport.setTitle(QCoreApplication.translate("MainWindow", u"Report", None))
    # retranslateUi

