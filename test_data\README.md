# 模态分析结果导入测试数据

本目录包含了多种类型的测试数据文件，用于验证网格无关性界面的导入和对比功能。

## 📁 文件列表

### 1. 参考模型数据
- **`reference_models.json`** - 文献参考模型
  - 包含3个不同精度的参考模型
  - 用于验证当前分析结果的准确性

### 2. 实验数据
- **`experimental_data.json`** - 实验测试数据
  - 包含3组实验测试结果
  - 用于与仿真结果进行对比验证

### 3. 基准测试数据
- **`benchmark_models.csv`** - NAFEMS和行业标准基准
  - 包含NAFEMS标准测试案例
  - 包含行业标准验证模型

### 4. 文献对比数据
- **`literature_comparison.csv`** - 学术文献对比
  - 包含研究论文和会议发表的结果
  - 用于学术验证和对比

### 5. 软件对比数据
- **`software_comparison.txt`** - 不同软件结果对比
  - 包含ANSYS、ABAQUS、NASTRAN等软件结果
  - 用于跨软件验证

### 6. 收敛性研究数据
- **`convergence_study.json`** - 网格收敛性研究
  - 包含6个不同网格密度的结果
  - 用于网格无关性分析

### 7. 材料对比数据
- **`material_comparison.csv`** - 不同材料属性对比
  - 包含钢材、铝合金、钛合金、碳纤维等材料
  - 用于材料选择和对比分析

### 8. 边界条件对比
- **`boundary_conditions.txt`** - 不同边界条件对比
  - 包含固定、简支、悬臂等边界条件
  - 用于边界条件影响分析

## 🎯 使用方法

### 1. 基本导入测试
```
1. 在网格管理界面切换到"模态结果对比"标签页
2. 点击紫色的"导入结果"按钮
3. 选择任意一个测试数据文件
4. 查看导入结果并更新图表
```

### 2. 批量导入测试
```
1. 同时选择多个文件进行批量导入
2. 建议组合：
   - reference_models.json + experimental_data.json
   - benchmark_models.csv + literature_comparison.csv
   - convergence_study.json (单独导入，数据较多)
```

### 3. 不同格式测试
```
- JSON格式：reference_models.json, experimental_data.json, convergence_study.json
- CSV格式：benchmark_models.csv, literature_comparison.csv, material_comparison.csv  
- TXT格式：software_comparison.txt, boundary_conditions.txt
```

### 4. 对比分析场景

#### 网格收敛性分析
- 导入：`convergence_study.json`
- 图表类型：网格收敛性分析
- 用途：验证网格密度对结果的影响

#### 实验验证
- 导入：`experimental_data.json`
- 图表类型：频率对比图
- 用途：仿真结果与实验结果对比

#### 软件交叉验证
- 导入：`software_comparison.txt`
- 图表类型：频率对比图
- 用途：不同软件结果的一致性验证

#### 材料选择分析
- 导入：`material_comparison.csv`
- 图表类型：模态分布图
- 用途：不同材料对模态特性的影响

#### 基准测试验证
- 导入：`benchmark_models.csv`
- 图表类型：频率对比图
- 用途：与标准基准案例对比

## 📊 数据特点

### 频率范围
- **低频段**：0-100 Hz（刚体模态、一阶弯曲）
- **中频段**：100-500 Hz（主要工作频率范围）
- **高频段**：500+ Hz（高阶模态）

### 网格尺寸范围
- **超精细**：0.3-0.5 mm
- **精细**：0.8-1.2 mm
- **中等**：1.5-2.5 mm
- **粗糙**：3.0-4.0 mm

### 节点数范围
- **大型模型**：20,000-45,000 节点
- **中型模型**：10,000-20,000 节点
- **小型模型**：3,000-10,000 节点

## 🧪 测试建议

### 1. 功能验证测试
```
目标：验证导入功能基本可用性
文件：reference_models.json
步骤：单文件导入 → 查看结果 → 更新图表
```

### 2. 格式兼容性测试
```
目标：验证多种文件格式支持
文件：各种格式文件
步骤：分别导入JSON、CSV、TXT格式文件
```

### 3. 批量导入测试
```
目标：验证批量导入功能
文件：选择2-3个文件同时导入
步骤：多选文件 → 批量导入 → 查看所有结果
```

### 4. 对比分析测试
```
目标：验证对比分析功能
文件：convergence_study.json
步骤：导入 → 选择当前结果 → 更新图表 → 查看对比效果
```

### 5. 数据管理测试
```
目标：验证导入结果管理功能
步骤：导入数据 → 重命名 → 查看详情 → 删除 → 导出
```

## ⚠️ 注意事项

1. **文件路径**：确保文件路径正确，建议使用绝对路径
2. **编码格式**：所有文件均使用UTF-8编码
3. **数据格式**：频率数组在CSV和TXT文件中使用字符串格式
4. **内存使用**：convergence_study.json包含较多数据，注意内存使用
5. **对比效果**：建议先导入少量数据测试，再进行大批量导入

## 🎉 预期效果

使用这些测试数据，您应该能够看到：

- **清晰的对比图表**：当前结果与导入结果的视觉区分
- **完整的图例**：标识不同数据来源
- **收敛性趋势**：网格尺寸对频率的影响
- **实验验证**：仿真与实验结果的一致性
- **软件对比**：不同软件结果的差异分析

这些测试数据涵盖了模态分析中的各种典型场景，
能够全面验证导入对比功能的实用性和准确性。
