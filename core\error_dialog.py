"""
错误对话框模块

此模块提供了一个用于显示错误信息的对话框，主要功能包括：
1. 显示友好的错误提示
2. 提供查看详细信息的选项
3. 根据异常严重性调整对话框样式

作者: [作者名]
日期: [日期]
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QTextEdit, QWidget, QSizePolicy, QSpacerItem, QStyle
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont

class ErrorDialog(QDialog):
    """错误对话框，用于显示友好的错误信息"""
    
    def __init__(self, severity: str, title: str, message: str, details: str = None, parent=None):
        """初始化错误对话框
        
        Args:
            severity: 错误严重性（信息、警告、错误、严重错误）
            title: 错误标题
            message: 错误消息
            details: 错误详细信息（可选）
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 设置窗口属性
        self.setWindowTitle(f"{severity}: {title}")
        self.setMinimumWidth(500)
        self.setModal(True)
        
        # 根据严重性设置图标
        if severity == "严重错误":
            icon = self.style().standardIcon(QStyle.SP_MessageBoxCritical)
        elif severity == "错误":
            icon = self.style().standardIcon(QStyle.SP_MessageBoxCritical)
        elif severity == "警告":
            icon = self.style().standardIcon(QStyle.SP_MessageBoxWarning)
        else:
            icon = self.style().standardIcon(QStyle.SP_MessageBoxInformation)
        
        # 创建布局
        main_layout = QVBoxLayout(self)
        
        # 创建顶部区域（图标和消息）
        top_widget = QWidget()
        top_layout = QHBoxLayout(top_widget)
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加图标
        icon_label = QLabel()
        icon_label.setPixmap(icon.pixmap(QSize(48, 48)))
        icon_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        top_layout.addWidget(icon_label)
        
        # 添加消息
        message_label = QLabel(message)
        message_label.setWordWrap(True)
        message_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        font = message_label.font()
        font.setPointSize(10)
        message_label.setFont(font)
        top_layout.addWidget(message_label, 1)
        
        main_layout.addWidget(top_widget)
        
        # 如果有详细信息，添加详细信息区域
        self.details_text = None
        if details:
            # 添加分隔线
            main_layout.addSpacerItem(QSpacerItem(20, 10, QSizePolicy.Expanding, QSizePolicy.Minimum))
            
            # 创建"显示详细信息"按钮
            self.toggle_details_button = QPushButton("显示详细信息")
            self.toggle_details_button.clicked.connect(self.toggle_details)
            main_layout.addWidget(self.toggle_details_button, 0, Qt.AlignRight)
            
            # 创建详细信息文本框（初始隐藏）
            self.details_text = QTextEdit()
            self.details_text.setReadOnly(True)
            self.details_text.setText(details)
            self.details_text.setMinimumHeight(200)
            self.details_text.setVisible(False)
            
            # 设置等宽字体，便于阅读堆栈跟踪
            font = QFont("Courier New")
            font.setStyleHint(QFont.Monospace)
            self.details_text.setFont(font)
            
            main_layout.addWidget(self.details_text)
        
        # 添加按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        # 添加复制按钮
        if details:
            copy_button = QPushButton("复制详细信息")
            copy_button.clicked.connect(lambda: self.copy_to_clipboard(details))
            button_layout.addWidget(copy_button)
        
        # 添加确定按钮
        ok_button = QPushButton("确定")
        ok_button.setDefault(True)
        ok_button.clicked.connect(self.accept)
        button_layout.addWidget(ok_button)
        
        main_layout.addLayout(button_layout)
        
        # 根据严重性设置样式
        self.set_style_by_severity(severity)
    
    def toggle_details(self):
        """切换详细信息的显示状态"""
        if self.details_text:
            visible = not self.details_text.isVisible()
            self.details_text.setVisible(visible)
            self.toggle_details_button.setText("隐藏详细信息" if visible else "显示详细信息")
            
            # 调整对话框大小
            if visible:
                self.resize(self.width(), self.height() + 200)
            else:
                self.resize(self.width(), self.height() - 200)
    
    def copy_to_clipboard(self, text):
        """复制文本到剪贴板
        
        Args:
            text: 要复制的文本
        """
        from PySide6.QtGui import QGuiApplication
        clipboard = QGuiApplication.clipboard()
        clipboard.setText(text)
    
    def set_style_by_severity(self, severity):
        """根据严重性设置对话框样式
        
        Args:
            severity: 错误严重性
        """
        base_style = """
            QDialog {
                background-color: #f8f9fa;
                border: 1px solid #dcdfe6;
            }
            QLabel {
                color: #2c3e50;
            }
            QPushButton {
                background-color: #409eff;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #66b1ff;
            }
            QPushButton:pressed {
                background-color: #3a8ee6;
            }
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dcdfe6;
                border-radius: 4px;
                padding: 5px;
            }
        """
        
        # 根据严重性添加特定样式
        if severity == "严重错误":
            specific_style = """
                QDialog {
                    border-left: 6px solid #f56c6c;
                }
                QLabel {
                    color: #f56c6c;
                    font-weight: bold;
                }
            """
        elif severity == "错误":
            specific_style = """
                QDialog {
                    border-left: 6px solid #f56c6c;
                }
            """
        elif severity == "警告":
            specific_style = """
                QDialog {
                    border-left: 6px solid #e6a23c;
                }
                QPushButton {
                    background-color: #e6a23c;
                }
                QPushButton:hover {
                    background-color: #ebb563;
                }
                QPushButton:pressed {
                    background-color: #cf9236;
                }
            """
        else:  # 信息
            specific_style = """
                QDialog {
                    border-left: 6px solid #409eff;
                }
            """
        
        # 应用样式
        self.setStyleSheet(base_style + specific_style) 