"""
统一导航管理器

此模块提供统一的界面导航管理功能，包括：
1. 工作流程顺序定义和管理
2. 上一步/下一步导航逻辑
3. 前置条件检查和访问控制
4. 统一的导航函数接口

作者: [作者名]
日期: [日期]
"""

import logging
from typing import Optional, Dict, List, Tuple
from enum import Enum
from dataclasses import dataclass

from window_manager import WindowManager, WindowType
from error_handler import ErrorHandler, AppError, ErrorSeverity
from core.workflow_state import get_workflow_state, WorkflowStep

logger = logging.getLogger(__name__)

class NavigationDirection(Enum):
    """导航方向枚举"""
    PREVIOUS = "previous"
    NEXT = "next"
    MAIN = "main"

@dataclass
class NavigationResult:
    """导航结果类"""
    success: bool
    target_window: Optional[WindowType] = None
    error_message: Optional[str] = None
    missing_dependencies: Optional[List[str]] = None

class WorkflowNavigationManager:
    """工作流程导航管理器
    
    提供统一的界面导航管理功能，支持：
    - 基于工作流程顺序的自动导航
    - 前置条件检查
    - 用户友好的错误提示
    - 向后兼容的导航接口
    """
    
    # 工作流程顺序定义
    WORKFLOW_ORDER = [
        WindowType.PRE,           # 前处理
        WindowType.CONNECTION,    # 连接设置  
        WindowType.ANALYSIS,      # 分析设置
        WindowType.CONSTRAIN,     # 设置约束
        WindowType.MESH,          # 网格无关性验证
        WindowType.RESULT,        # 计算结果
        WindowType.VIBRATION      # 后处理
    ]
    
    # 窗口类型到工作流程步骤的映射
    WINDOW_TO_WORKFLOW_STEP = {
        WindowType.PRE: WorkflowStep.PREPROCESSING,
        WindowType.CONNECTION: WorkflowStep.CONNECTION,
        WindowType.ANALYSIS: WorkflowStep.ANALYSIS,
        WindowType.CONSTRAIN: WorkflowStep.CONSTRAIN,
        WindowType.MESH: WorkflowStep.MESH_VALIDATION,
        WindowType.RESULT: WorkflowStep.RESULT,
        WindowType.VIBRATION: WorkflowStep.POST_PROCESSING
    }
    
    # 窗口类型的中文名称映射
    WINDOW_DISPLAY_NAMES = {
        WindowType.PRE: "前处理",
        WindowType.CONNECTION: "连接设置",
        WindowType.ANALYSIS: "分析设置", 
        WindowType.CONSTRAIN: "设置约束",
        WindowType.MESH: "网格无关性验证",
        WindowType.RESULT: "计算结果",
        WindowType.VIBRATION: "后处理",
        WindowType.MAIN: "主界面"
    }
    
    def __init__(self):
        """初始化导航管理器"""
        self.workflow_state = get_workflow_state()
        self.error_handler = ErrorHandler()
        logger.info("工作流程导航管理器初始化完成")
    
    def get_previous_step(self, current_window: WindowType) -> Optional[WindowType]:
        """获取当前窗口的上一步窗口
        
        Args:
            current_window: 当前窗口类型
            
        Returns:
            上一步窗口类型，如果没有则返回None
        """
        try:
            current_index = self.WORKFLOW_ORDER.index(current_window)
            if current_index > 0:
                return self.WORKFLOW_ORDER[current_index - 1]
            return None
        except ValueError:
            logger.warning(f"窗口类型 {current_window} 不在工作流程顺序中")
            return None
    
    def get_next_step(self, current_window: WindowType) -> Optional[WindowType]:
        """获取当前窗口的下一步窗口
        
        Args:
            current_window: 当前窗口类型
            
        Returns:
            下一步窗口类型，如果没有则返回None
        """
        try:
            current_index = self.WORKFLOW_ORDER.index(current_window)
            if current_index < len(self.WORKFLOW_ORDER) - 1:
                return self.WORKFLOW_ORDER[current_index + 1]
            return None
        except ValueError:
            logger.warning(f"窗口类型 {current_window} 不在工作流程顺序中")
            return None
    
    def can_navigate_to_step(self, target_window: WindowType) -> Tuple[bool, List[str]]:
        """检查是否可以导航到目标步骤
        
        Args:
            target_window: 目标窗口类型
            
        Returns:
            (是否可以导航, 缺失的依赖步骤列表)
        """
        # 主界面和所有工作流程步骤都可以直接访问，不需要严格的前置条件
        # 这样用户可以灵活地在各个步骤之间导航
        if target_window in [WindowType.MAIN, WindowType.PRE, WindowType.CONNECTION,
                           WindowType.ANALYSIS, WindowType.CONSTRAIN, WindowType.MESH,
                           WindowType.RESULT, WindowType.VIBRATION]:
            return True, []
        
        # 检查工作流程步骤的前置条件
        if target_window in self.WINDOW_TO_WORKFLOW_STEP:
            workflow_step = self.WINDOW_TO_WORKFLOW_STEP[target_window]
            missing_deps = self.workflow_state.get_missing_dependencies(workflow_step)
            
            if missing_deps:
                # 转换为中文名称
                missing_names = [
                    self.WINDOW_DISPLAY_NAMES.get(
                        self._workflow_step_to_window(dep), str(dep)
                    ) for dep in missing_deps
                ]
                return False, missing_names
        
        return True, []
    
    def _workflow_step_to_window(self, workflow_step: WorkflowStep) -> Optional[WindowType]:
        """将工作流程步骤转换为窗口类型"""
        for window_type, step in self.WINDOW_TO_WORKFLOW_STEP.items():
            if step == workflow_step:
                return window_type
        return None
    
    def navigate_to_previous_step(self, window_manager: WindowManager, 
                                current_window: WindowType) -> NavigationResult:
        """导航到上一步
        
        Args:
            window_manager: 窗口管理器实例
            current_window: 当前窗口类型
            
        Returns:
            导航结果
        """
        previous_window = self.get_previous_step(current_window)
        
        if previous_window is None:
            return NavigationResult(
                success=False,
                error_message=f"当前步骤 '{self.WINDOW_DISPLAY_NAMES.get(current_window, str(current_window))}' 没有上一步"
            )
        
        return self._perform_navigation(window_manager, previous_window, NavigationDirection.PREVIOUS)
    
    def navigate_to_next_step(self, window_manager: WindowManager, 
                            current_window: WindowType) -> NavigationResult:
        """导航到下一步
        
        Args:
            window_manager: 窗口管理器实例
            current_window: 当前窗口类型
            
        Returns:
            导航结果
        """
        next_window = self.get_next_step(current_window)
        
        if next_window is None:
            return NavigationResult(
                success=False,
                error_message=f"当前步骤 '{self.WINDOW_DISPLAY_NAMES.get(current_window, str(current_window))}' 没有下一步"
            )
        
        # 检查前置条件
        can_navigate, missing_deps = self.can_navigate_to_step(next_window)
        if not can_navigate:
            return NavigationResult(
                success=False,
                target_window=next_window,
                error_message=f"无法进入 '{self.WINDOW_DISPLAY_NAMES.get(next_window, str(next_window))}'，请先完成前置步骤",
                missing_dependencies=missing_deps
            )
        
        return self._perform_navigation(window_manager, next_window, NavigationDirection.NEXT)
    
    def navigate_to_main_menu(self, window_manager: WindowManager) -> NavigationResult:
        """导航到主界面
        
        Args:
            window_manager: 窗口管理器实例
            
        Returns:
            导航结果
        """
        return self._perform_navigation(window_manager, WindowType.MAIN, NavigationDirection.MAIN)
    
    def _perform_navigation(self, window_manager: WindowManager, 
                          target_window: WindowType, 
                          direction: NavigationDirection) -> NavigationResult:
        """执行实际的导航操作
        
        Args:
            window_manager: 窗口管理器实例
            target_window: 目标窗口类型
            direction: 导航方向
            
        Returns:
            导航结果
        """
        try:
            # 强制隐藏其他窗口（除了主界面）
            if target_window != WindowType.MAIN:
                window_manager.force_hide_all_except(target_window)
            
            # 切换到目标窗口
            window_manager.switch_to(target_window)
            
            target_name = self.WINDOW_DISPLAY_NAMES.get(target_window, str(target_window))
            logger.info(f"成功导航到 {target_name}")
            
            return NavigationResult(
                success=True,
                target_window=target_window
            )
            
        except Exception as e:
            target_name = self.WINDOW_DISPLAY_NAMES.get(target_window, str(target_window))
            error_msg = f"导航到 {target_name} 失败: {str(e)}"
            logger.error(error_msg)
            
            return NavigationResult(
                success=False,
                target_window=target_window,
                error_message=error_msg
            )

# 全局导航管理器实例
_navigation_manager = None

def get_navigation_manager() -> WorkflowNavigationManager:
    """获取全局导航管理器实例（单例模式）"""
    global _navigation_manager
    if _navigation_manager is None:
        _navigation_manager = WorkflowNavigationManager()
    return _navigation_manager

# 统一的导航函数接口（向后兼容）
def navigate_to_previous_step(window_manager: WindowManager, current_window: WindowType) -> bool:
    """导航到上一步（简化接口）"""
    nav_manager = get_navigation_manager()
    result = nav_manager.navigate_to_previous_step(window_manager, current_window)
    
    if not result.success:
        nav_manager.error_handler.handle_error(
            AppError(result.error_message, ErrorSeverity.WARNING)
        )
    
    return result.success

def navigate_to_next_step(window_manager: WindowManager, current_window: WindowType) -> bool:
    """导航到下一步（简化接口）"""
    nav_manager = get_navigation_manager()
    result = nav_manager.navigate_to_next_step(window_manager, current_window)
    
    if not result.success:
        # 如果有缺失的依赖，显示详细信息
        if result.missing_dependencies:
            missing_text = "、".join(result.missing_dependencies)
            detailed_message = f"{result.error_message}：{missing_text}"
        else:
            detailed_message = result.error_message
            
        nav_manager.error_handler.handle_error(
            AppError(detailed_message, ErrorSeverity.WARNING)
        )
    
    return result.success

def navigate_to_main_menu(window_manager: WindowManager) -> bool:
    """导航到主界面（简化接口）"""
    nav_manager = get_navigation_manager()
    result = nav_manager.navigate_to_main_menu(window_manager)
    
    if not result.success:
        nav_manager.error_handler.handle_error(
            AppError(result.error_message, ErrorSeverity.ERROR)
        )
    
    return result.success
