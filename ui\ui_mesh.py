# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QLabel, QLineEdit,
    QMainWindow, QPushButton, QScrollArea, QSizePolicy,
    QStatusBar, QVBoxLayout, QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1185, 700)
        MainWindow.setMinimumSize(QSize(1185, 700))
        font = QFont()
        font.setFamilies([u"\u5b8b\u4f53"])
        font.setPointSize(12)
        MainWindow.setFont(font)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.label_2 = QLabel(self.centralwidget)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setMinimumSize(QSize(500, 100))
        font1 = QFont()
        font1.setFamilies([u"\u9ed1\u4f53"])
        font1.setPointSize(30)
        self.label_2.setFont(font1)
        self.label_2.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout.addWidget(self.label_2)

        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.scrollArea = QScrollArea(self.centralwidget)
        self.scrollArea.setObjectName(u"scrollArea")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.scrollArea.sizePolicy().hasHeightForWidth())
        self.scrollArea.setSizePolicy(sizePolicy)
        self.scrollArea.setMinimumSize(QSize(500, 300))
        self.scrollArea.setWidgetResizable(True)
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 486, 300))
        self.scrollAreaWidgetContents.setMinimumSize(QSize(300, 300))
        self.layoutWidget = QWidget(self.scrollAreaWidgetContents)
        self.layoutWidget.setObjectName(u"layoutWidget")
        self.layoutWidget.setGeometry(QRect(20, 10, 451, 52))
        self.horizontalLayout_3 = QHBoxLayout(self.layoutWidget)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_3.setContentsMargins(0, 0, 0, 0)
        self.label_16 = QLabel(self.layoutWidget)
        self.label_16.setObjectName(u"label_16")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.label_16.sizePolicy().hasHeightForWidth())
        self.label_16.setSizePolicy(sizePolicy1)
        self.label_16.setMinimumSize(QSize(140, 50))
        self.label_16.setMaximumSize(QSize(180, 16777215))

        self.horizontalLayout_3.addWidget(self.label_16)

        self.allbodymeshsize = QLineEdit(self.layoutWidget)
        self.allbodymeshsize.setObjectName(u"allbodymeshsize")
        self.allbodymeshsize.setMinimumSize(QSize(100, 40))
        self.allbodymeshsize.setMaximumSize(QSize(200, 16777215))

        self.horizontalLayout_3.addWidget(self.allbodymeshsize)

        self.addmeshsize = QPushButton(self.layoutWidget)
        self.addmeshsize.setObjectName(u"addmeshsize")
        self.addmeshsize.setMinimumSize(QSize(150, 40))

        self.horizontalLayout_3.addWidget(self.addmeshsize)

        self.scrollArea.setWidget(self.scrollAreaWidgetContents)

        self.horizontalLayout_2.addWidget(self.scrollArea)

        self.meshfig = QLabel(self.centralwidget)
        self.meshfig.setObjectName(u"meshfig")
        sizePolicy2 = QSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Preferred)
        sizePolicy2.setHorizontalStretch(0)
        sizePolicy2.setVerticalStretch(0)
        sizePolicy2.setHeightForWidth(self.meshfig.sizePolicy().hasHeightForWidth())
        self.meshfig.setSizePolicy(sizePolicy2)
        self.meshfig.setMinimumSize(QSize(400, 300))
        font2 = QFont()
        font2.setFamilies([u"\u5b8b\u4f53"])
        font2.setPointSize(25)
        self.meshfig.setFont(font2)
        self.meshfig.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.horizontalLayout_2.addWidget(self.meshfig)


        self.verticalLayout.addLayout(self.horizontalLayout_2)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.nodes_number = QLabel(self.centralwidget)
        self.nodes_number.setObjectName(u"nodes_number")
        sizePolicy1.setHeightForWidth(self.nodes_number.sizePolicy().hasHeightForWidth())
        self.nodes_number.setSizePolicy(sizePolicy1)
        self.nodes_number.setMinimumSize(QSize(250, 70))
        font3 = QFont()
        font3.setFamilies([u"Times New Roman"])
        font3.setPointSize(20)
        self.nodes_number.setFont(font3)

        self.horizontalLayout.addWidget(self.nodes_number)

        self.elenments_number = QLabel(self.centralwidget)
        self.elenments_number.setObjectName(u"elenments_number")
        sizePolicy1.setHeightForWidth(self.elenments_number.sizePolicy().hasHeightForWidth())
        self.elenments_number.setSizePolicy(sizePolicy1)
        self.elenments_number.setMinimumSize(QSize(250, 70))
        self.elenments_number.setFont(font3)

        self.horizontalLayout.addWidget(self.elenments_number)


        self.verticalLayout.addLayout(self.horizontalLayout)

        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.push_generatemesh = QPushButton(self.centralwidget)
        self.push_generatemesh.setObjectName(u"push_generatemesh")
        sizePolicy3 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy3.setHorizontalStretch(0)
        sizePolicy3.setVerticalStretch(0)
        sizePolicy3.setHeightForWidth(self.push_generatemesh.sizePolicy().hasHeightForWidth())
        self.push_generatemesh.setSizePolicy(sizePolicy3)
        self.push_generatemesh.setMinimumSize(QSize(200, 65))
        font4 = QFont()
        font4.setFamilies([u"\u5b8b\u4f53"])
        font4.setPointSize(20)
        self.push_generatemesh.setFont(font4)

        self.horizontalLayout_4.addWidget(self.push_generatemesh)

        self.push_result = QPushButton(self.centralwidget)
        self.push_result.setObjectName(u"push_result")
        sizePolicy3.setHeightForWidth(self.push_result.sizePolicy().hasHeightForWidth())
        self.push_result.setSizePolicy(sizePolicy3)
        self.push_result.setMinimumSize(QSize(250, 65))
        self.push_result.setFont(font4)

        self.horizontalLayout_4.addWidget(self.push_result)

        self.push_preui = QPushButton(self.centralwidget)
        self.push_preui.setObjectName(u"push_preui")
        sizePolicy3.setHeightForWidth(self.push_preui.sizePolicy().hasHeightForWidth())
        self.push_preui.setSizePolicy(sizePolicy3)
        self.push_preui.setMinimumSize(QSize(250, 65))
        self.push_preui.setFont(font4)

        self.horizontalLayout_4.addWidget(self.push_preui)

        self.push_connectionui = QPushButton(self.centralwidget)
        self.push_connectionui.setObjectName(u"push_connectionui")
        sizePolicy3.setHeightForWidth(self.push_connectionui.sizePolicy().hasHeightForWidth())
        self.push_connectionui.setSizePolicy(sizePolicy3)
        self.push_connectionui.setMinimumSize(QSize(250, 65))
        self.push_connectionui.setFont(font4)

        self.horizontalLayout_4.addWidget(self.push_connectionui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy3.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy3)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font4)

        self.horizontalLayout_4.addWidget(self.push_mainui)


        self.verticalLayout.addLayout(self.horizontalLayout_4)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u751f\u6210", None))
        self.label_16.setText(QCoreApplication.translate("MainWindow", u"\u5168\u5c40\u7f51\u683c\u5c3a\u5bf8(mm):", None))
        self.allbodymeshsize.setText(QCoreApplication.translate("MainWindow", u"20", None))
        self.addmeshsize.setText(QCoreApplication.translate("MainWindow", u"\u6dfb\u52a0\u7f51\u683c\u5c3a\u5bf8", None))
        self.meshfig.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9884\u89c8\u56fe", None))
        self.nodes_number.setText(QCoreApplication.translate("MainWindow", u"Nodes:", None))
        self.elenments_number.setText(QCoreApplication.translate("MainWindow", u"Elements:", None))
        self.push_generatemesh.setText(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u7f51\u683c", None))
        self.push_result.setText(QCoreApplication.translate("MainWindow", u"\u67e5\u770b\u7ed3\u679c", None))
        self.push_preui.setText(QCoreApplication.translate("MainWindow", u"\u4e0a\u4e00\u6b65(\u524d\u5904\u7406)", None))
        self.push_connectionui.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u8fde\u63a5\u8bbe\u7f6e)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

