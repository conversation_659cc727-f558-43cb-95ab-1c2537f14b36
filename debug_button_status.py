#!/usr/bin/env python3
"""
调试按钮状态脚本
检查 btn_single_modal 按钮为什么被禁用
"""

import sys
import os
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from window_manager import WindowManager, WindowType
from models.mesh_models import MeshStatus

def debug_button_status():
    """调试按钮状态"""
    print("=== 调试 btn_single_modal 按钮状态 ===")
    
    try:
        # 创建窗口管理器
        window_manager = WindowManager()
        
        # 获取网格窗口
        mesh_window = window_manager.get_window(WindowType.MESH)
        if not mesh_window:
            print("❌ 无法获取网格窗口")
            return
            
        print("✅ 成功获取网格窗口")
        
        # 检查按钮是否存在
        if not hasattr(mesh_window.ui, 'btn_single_modal'):
            print("❌ 按钮 btn_single_modal 不存在")
            return
            
        print("✅ 按钮 btn_single_modal 存在")
        
        # 检查按钮状态
        button = mesh_window.ui.btn_single_modal
        is_enabled = button.isEnabled()
        is_visible = button.isVisible()
        
        print(f"按钮启用状态: {'✅ 启用' if is_enabled else '❌ 禁用'}")
        print(f"按钮可见状态: {'✅ 可见' if is_visible else '❌ 隐藏'}")
        
        if not is_enabled:
            print("\n--- 检查禁用原因 ---")
            
            # 检查选中的网格
            selected_meshes = getattr(mesh_window, 'selected_meshes_for_generation', [])
            print(f"选中网格数量: {len(selected_meshes)}")
            
            if len(selected_meshes) == 0:
                print("❌ 问题: 没有选中任何网格")
                print("解决方案: 在网格状态列表中选择一个网格")
            elif len(selected_meshes) > 1:
                print(f"❌ 问题: 选中了多个网格 ({len(selected_meshes)} 个)")
                print("解决方案: 只选择一个网格进行单个模态计算")
            else:
                mesh = selected_meshes[0]
                print(f"✅ 选中一个网格: {mesh.name}")
                print(f"网格状态: {mesh.status}")
                
                # 检查网格状态
                if mesh.status != MeshStatus.GENERATED:
                    print(f"❌ 问题: 网格状态不是'已生成'，当前状态: {mesh.status}")
                    print("解决方案: 确保网格已生成")
                else:
                    print("✅ 网格状态正确")
            
            # 检查是否有已生成的网格
            all_meshes = mesh_window.mesh_manager.get_all_meshes()
            generated_meshes = [m for m in all_meshes if m.status == MeshStatus.GENERATED]
            print(f"总网格数量: {len(all_meshes)}")
            print(f"已生成网格数量: {len(generated_meshes)}")
            
            if len(generated_meshes) == 0:
                print("❌ 问题: 没有已生成的网格")
                print("解决方案: 先生成至少一个网格")
            
            # 检查是否在计算中
            is_calculating = getattr(mesh_window, 'is_calculating', False)
            print(f"是否在计算中: {'是' if is_calculating else '否'}")
            
            if is_calculating:
                print("❌ 问题: 当前正在进行计算")
                print("解决方案: 等待当前计算完成")
        
        print("\n--- 网格列表状态 ---")
        all_meshes = mesh_window.mesh_manager.get_all_meshes()
        if all_meshes:
            for i, mesh in enumerate(all_meshes):
                status_icon = "✅" if mesh.status == MeshStatus.GENERATED else "❌"
                print(f"{i+1}. {mesh.name} - {mesh.status} {status_icon}")
        else:
            print("没有找到任何网格")
            
        print("\n--- 选择操作建议 ---")
        if not all_meshes:
            print("1. 先添加网格参数")
            print("2. 生成网格")
            print("3. 选择已生成的网格")
            print("4. 点击单个模态计算")
        elif not generated_meshes:
            print("1. 生成现有网格")
            print("2. 选择已生成的网格")
            print("3. 点击单个模态计算")
        elif len(selected_meshes) != 1:
            print("1. 在网格状态列表中选择一个已生成的网格")
            print("2. 确保只选中一个网格")
            print("3. 点击单个模态计算")
        else:
            print("所有条件都满足，按钮应该是启用的")
            
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_button_status() 