<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>线程安全架构 - 高并发稳定性保障系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
        }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-violet-600 to-purple-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-violet-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🛡️ 线程安全架构
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-violet-100">
                    高并发稳定性保障系统 | 零崩溃风险的企业级架构
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-violet-500 bg-opacity-20 text-violet-100 text-sm font-semibold px-4 py-2 rounded-full border border-violet-400">
                        🔒 线程安全 | ⚡ 高并发 | 🛡️ 零崩溃 | 📊 性能监控
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- Architecture Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🏗️ 架构概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                v1.2.0版本引入了全新的线程安全架构，彻底解决了API线程与UI线程之间的数据竞争问题，显著提升了应用程序的稳定性和可靠性。
            </p>

            <div class="bg-gradient-to-r from-violet-50 to-purple-50 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">🎯 核心改进</h3>
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-violet-100 text-violet-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700">线程安全通信 - Qt信号槽机制</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700">跨线程错误处理 - 完整异常传播</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700">数据访问同步 - QMutex保护机制</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center mr-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                                </svg>
                            </div>
                            <span class="text-sm font-medium text-gray-700">线程验证机制 - 主线程UI更新</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">零崩溃风险</h3>
                    </div>
                    <p class="text-sm text-blue-600">100%消除数据竞争</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">高并发支持</h3>
                    </div>
                    <p class="text-sm text-green-600">显著提升并发性能</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">完整错误处理</h3>
                    </div>
                    <p class="text-sm text-purple-600">全面覆盖异常情况</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">详细调试</h3>
                    </div>
                    <p class="text-sm text-orange-600">大幅改善调试能力</p>
                </div>
            </div>
        </section>

        <!-- Technical Implementation -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🔧 技术实现</h2>

            <div class="space-y-6">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🖥️</span>线程安全窗口管理器
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">核心组件：<code class="bg-gray-100 px-2 py-1 rounded text-sm">core/thread_safe_window_manager.py</code></p>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800 mb-2">主要功能</h4>
                                <ul class="text-sm text-blue-600 space-y-1">
                                    <li>• 信号槽机制跨线程通信</li>
                                    <li>• 自动线程检测和选择</li>
                                    <li>• 线程安全窗口切换</li>
                                    <li>• 高并发API调用支持</li>
                                </ul>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800 mb-2">技术特点</h4>
                                <ul class="text-sm text-green-600 space-y-1">
                                    <li>• Qt信号槽自动排队</li>
                                    <li>• 线程上下文自动切换</li>
                                    <li>• 状态管理线程安全</li>
                                    <li>• 资源访问互斥保护</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚠️</span>跨线程错误处理
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">核心组件：<code class="bg-gray-100 px-2 py-1 rounded text-sm">core/thread_safe_error_handler.py</code></p>
                        <div class="bg-red-50 p-4 rounded-lg mb-4">
                            <h4 class="font-semibold text-red-800 mb-2">错误处理机制</h4>
                            <div class="code-block">
class ThreadSafeErrorHandler:
    def handle_error(self, error, context):
        # 收集错误信息
        error_info = {
            'thread_id': threading.current_thread().ident,
            'timestamp': datetime.now(),
            'error_type': type(error).__name__,
            'context': context
        }

        # 线程安全的错误报告
        self.error_signal.emit(error_info)
                            </div>
                        </div>
                        <div class="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">核心功能</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 全局错误收集机制</li>
                                    <li>• 线程上下文信息记录</li>
                                    <li>• 异常安全传播处理</li>
                                    <li>• 详细错误日志记录</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-2">调试支持</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• 错误堆栈跟踪</li>
                                    <li>• 线程状态快照</li>
                                    <li>• 性能影响分析</li>
                                    <li>• 自动恢复建议</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌐</span>优化的API服务器
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-4">核心组件：<code class="bg-gray-100 px-2 py-1 rounded text-sm">ctrl/api_server.py</code></p>
                        <div class="bg-purple-50 p-4 rounded-lg mb-4">
                            <h4 class="font-semibold text-purple-800 mb-2">API调用示例</h4>
                            <div class="code-block">
# Python示例 - 安全的API调用
import requests

# 系统自动处理线程安全问题
response = requests.post(
    'http://localhost:8000/api/v1/simulation-params',
    json={'param': 'value'}
)

if response.status_code == 200:
    print("参数更新成功")
                            </div>
                        </div>
                        <div class="grid md:grid-cols-4 gap-4">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <div class="text-2xl mb-2">🔒</div>
                                <h5 class="font-semibold text-blue-800">线程安全</h5>
                                <p class="text-xs text-blue-600">请求处理保护</p>
                            </div>
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <div class="text-2xl mb-2">⚡</div>
                                <h5 class="font-semibold text-green-800">异步操作</h5>
                                <p class="text-xs text-green-600">高性能处理</p>
                            </div>
                            <div class="text-center p-3 bg-purple-50 rounded-lg">
                                <div class="text-2xl mb-2">🛡️</div>
                                <h5 class="font-semibold text-purple-800">资源保护</h5>
                                <p class="text-xs text-purple-600">访问控制机制</p>
                            </div>
                            <div class="text-center p-3 bg-orange-50 rounded-lg">
                                <div class="text-2xl mb-2">📊</div>
                                <h5 class="font-semibold text-orange-800">性能监控</h5>
                                <p class="text-xs text-orange-600">实时统计分析</p>
                            </div>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Performance Comparison -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">📊 性能对比</h2>

            <div class="overflow-x-auto">
                <table class="w-full bg-white rounded-lg shadow-sm">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">性能指标</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">优化前</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">优化后</th>
                            <th class="px-6 py-4 text-left text-sm font-semibold text-gray-800">改进效果</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr class="hover:bg-green-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">崩溃风险</td>
                            <td class="px-6 py-4 text-sm text-red-600">存在数据竞争</td>
                            <td class="px-6 py-4 text-sm text-green-600">零崩溃风险</td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    ✅ 100%消除
                                </span>
                            </td>
                        </tr>
                        <tr class="hover:bg-blue-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">并发支持</td>
                            <td class="px-6 py-4 text-sm text-orange-600">有限支持</td>
                            <td class="px-6 py-4 text-sm text-blue-600">高并发处理</td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    ✅ 显著提升
                                </span>
                            </td>
                        </tr>
                        <tr class="hover:bg-purple-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">错误处理</td>
                            <td class="px-6 py-4 text-sm text-gray-600">基础机制</td>
                            <td class="px-6 py-4 text-sm text-purple-600">完整覆盖</td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    ✅ 全面覆盖
                                </span>
                            </td>
                        </tr>
                        <tr class="hover:bg-orange-50">
                            <td class="px-6 py-4 text-sm font-medium text-gray-900">调试能力</td>
                            <td class="px-6 py-4 text-sm text-red-600">调试困难</td>
                            <td class="px-6 py-4 text-sm text-orange-600">详细日志</td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                    ✅ 大幅改善
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🛡️</span>线程安全架构如何保证零崩溃？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">通过多层次的保护机制确保系统稳定性。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">保护机制：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• Qt信号槽机制自动处理线程间通信</li>
                                <li>• QMutex互斥锁保护共享资源访问</li>
                                <li>• 线程验证确保UI操作在主线程执行</li>
                                <li>• 全局错误处理机制捕获所有异常</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚡</span>性能是否会受到影响？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">线程安全机制实际上提升了整体性能。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">性能优势：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 高并发API调用支持，提升响应速度</li>
                                <li>• 异步操作避免界面阻塞</li>
                                <li>• 智能资源管理减少内存占用</li>
                                <li>• 优化的错误处理降低系统开销</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>开发者需要注意什么？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">系统自动处理线程安全，开发者无需特殊处理。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">开发建议：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 正常使用API接口，系统自动保证线程安全</li>
                                <li>• 关注错误日志中的线程信息</li>
                                <li>• 利用详细的调试信息排查问题</li>
                                <li>• 遵循现有的API调用模式</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">📊</span>如何监控线程安全状态？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">系统提供完整的监控和日志记录功能。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">监控功能：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 实时线程状态监控</li>
                                <li>• 详细的错误日志记录</li>
                                <li>• 性能指标统计分析</li>
                                <li>• 自动异常检测和报告</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 线程安全机制在v1.2.0版本开始生效</li>
                    <li>• 旧版本项目可能需要重新测试</li>
                    <li>• 大量并发调用时注意系统资源</li>
                    <li>• 关注错误日志中的线程信息</li>
                    <li>• 定期检查系统性能指标</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 充分利用高并发API调用能力</li>
                    <li>• 关注系统提供的详细调试信息</li>
                    <li>• 定期更新到最新版本</li>
                    <li>• 向开发团队反馈使用体验</li>
                    <li>• 参考性能监控数据优化使用方式</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">高并发稳定性保障系统 - 零崩溃风险的企业级架构</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
