"""
核心工具包

此包提供了应用程序的核心工具类和函数，包括：
1. 全局异常处理
2. 错误对话框
3. 自定义异常类型
4. 配置管理
5. 日志系统

作者: [作者名]
日期: [日期]
"""

from .exception_handler import (
    setup_exception_handling, 
    exception_signaler,
    ApplicationError,
    ConfigurationError,
    FileOperationError,
    ValidationError,
    APIError,
    ExceptionSeverity
)

from .error_dialog import ErrorDialog
from .config_manager import ConfigManager
from .logger_setup import setup_logging

# 优化相关模块
try:
    from .performance_monitor import get_profiler, measure_performance, log_checkpoint, print_performance_summary
    from .optimization_config import get_optimization_manager, is_optimization_enabled
    from .style_manager import get_style_manager, apply_critical_styles, apply_full_styles
    HAS_OPTIMIZATION_MODULES = True
except ImportError:
    HAS_OPTIMIZATION_MODULES = False

__all__ = [
    'setup_exception_handling',
    'exception_signaler',
    'ApplicationError',
    'ConfigurationError',
    'FileOperationError',
    'ValidationError',
    'APIError',
    'ExceptionSeverity',
    'ErrorDialog',
    'ConfigManager',
    'setup_logging'
]

# 添加优化模块到导出列表
if HAS_OPTIMIZATION_MODULES:
    __all__.extend([
        'get_profiler',
        'measure_performance',
        'log_checkpoint',
        'print_performance_summary',
        'get_optimization_manager',
        'is_optimization_enabled',
        'get_style_manager',
        'apply_critical_styles',
        'apply_full_styles'
    ])