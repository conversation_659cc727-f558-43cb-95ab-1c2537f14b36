"""
测试最终的英文版本（完全无中文字符）

验证所有中文字符都已替换为英文，不再有字体警告
"""

import sys
import os
import logging
import warnings

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_clean_english_version():
    """测试干净的英文版本"""
    print("🧪 测试最终英文版本（无中文字符）...")
    
    # 捕获所有警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        try:
            # 设置matplotlib使用Agg后端
            import matplotlib
            matplotlib.use('Agg')
            
            from PySide6.QtWidgets import QApplication
            from ui.components.modal_chart_widget import ModalChartWidget
            
            app = QApplication.instance() or QApplication([])
            
            # 创建图表组件
            chart_widget = ModalChartWidget()
            
            # 准备测试数据
            test_data = [
                {
                    'name': 'Fine Mesh',
                    'size': 1.0,
                    'frequencies': [42.5, 75.8, 108.2, 145.6, 189.3],
                    'node_count': 10000,
                    'element_count': 8000
                },
                {
                    'name': 'Medium Mesh',
                    'size': 2.5,
                    'frequencies': [41.8, 74.2, 106.5, 143.1, 186.2],
                    'node_count': 5000,
                    'element_count': 4000
                },
                {
                    'name': 'Coarse Mesh',
                    'size': 5.0,
                    'frequencies': [40.2, 71.9, 103.8, 139.7, 181.5],
                    'node_count': 2000,
                    'element_count': 1500
                }
            ]
            
            # 测试三种图表类型
            chart_types = [
                ("frequency_comparison", "Frequency Comparison"),
                ("mode_distribution", "Mode Distribution"),
                ("mesh_convergence", "Mesh Convergence")
            ]
            
            success_count = 0
            for chart_type, description in chart_types:
                try:
                    print(f"  📊 Testing {description}...")
                    chart_widget.update_chart(chart_type, test_data)
                    
                    # 保存图表
                    filename = f"final_english_{chart_type}.png"
                    chart_widget.save_chart(filename, dpi=150)
                    
                    if os.path.exists(filename):
                        file_size = os.path.getsize(filename)
                        print(f"  ✅ {description} generated successfully ({file_size} bytes)")
                        success_count += 1
                    else:
                        print(f"  ❌ {description} file not generated")
                        
                except Exception as e:
                    print(f"  ❌ {description} generation failed: {str(e)}")
            
            # 检查警告
            font_warnings = [warning for warning in w if "Glyph" in str(warning.message) and "missing from font" in str(warning.message)]
            
            print(f"\n📊 警告统计:")
            print(f"  总警告数: {len(w)}")
            print(f"  字体相关警告: {len(font_warnings)}")
            
            if font_warnings:
                print("  ⚠️ 仍有字体警告，可能存在未替换的中文字符")
                # 显示前几个警告作为示例
                for i, warning in enumerate(font_warnings[:3]):
                    print(f"    {i+1}. {warning.message}")
                if len(font_warnings) > 3:
                    print(f"    ... 还有 {len(font_warnings) - 3} 个类似警告")
            else:
                print("  ✅ 无字体相关警告！")
            
            return success_count, len(chart_types), len(font_warnings)
            
        except Exception as e:
            print(f"❌ Test failed: {str(e)}")
            return 0, 3, -1

def test_no_data_scenarios():
    """测试无数据场景"""
    print("\n🧪 测试无数据场景...")
    
    try:
        import matplotlib
        matplotlib.use('Agg')
        
        from PySide6.QtWidgets import QApplication
        from ui.components.modal_chart_widget import ModalChartWidget
        
        app = QApplication.instance() or QApplication([])
        chart_widget = ModalChartWidget()
        
        # 测试空数据
        chart_widget.update_chart("frequency_comparison", [])
        chart_widget.save_chart("test_no_data.png")
        
        # 测试无频率数据
        no_freq_data = [
            {
                'name': 'Test Mesh',
                'size': 1.0,
                'frequencies': [],
                'node_count': 1000,
                'element_count': 800
            }
        ]
        chart_widget.update_chart("frequency_comparison", no_freq_data)
        chart_widget.save_chart("test_no_frequency.png")
        
        # 测试不支持的图表类型
        chart_widget.update_chart("unsupported_type", [])
        chart_widget.save_chart("test_unsupported.png")
        
        print("  ✅ 无数据场景测试完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 无数据场景测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🎯 最终英文版本测试（完全无中文字符）")
    print("=" * 60)
    
    # 配置日志只显示错误
    logging.basicConfig(level=logging.ERROR)
    
    # 测试主要功能
    success, tests, font_warnings = test_clean_english_version()
    
    # 测试无数据场景
    no_data_ok = test_no_data_scenarios()
    
    print("\n" + "=" * 60)
    print("📋 最终测试结果:")
    print(f"图表生成测试: {success}/{tests} 通过")
    print(f"无数据场景测试: {'✅ 通过' if no_data_ok else '❌ 失败'}")
    print(f"字体警告数量: {font_warnings if font_warnings >= 0 else '未知'}")
    
    if success == tests and no_data_ok and font_warnings == 0:
        print("\n🎉 完美！英文版本测试全部通过！")
        print("✅ 所有图表都能正确生成")
        print("✅ 完全没有中文字体警告")
        print("✅ 错误处理正常工作")
        print("✅ 专业的英文界面")
        print("\n🎯 中文字体问题已彻底解决！")
    elif success == tests and font_warnings == 0:
        print("\n🎉 主要功能完美！")
        print("✅ 图表生成正常，无字体警告")
        if not no_data_ok:
            print("⚠️ 无数据场景处理需要检查")
    elif success == tests:
        print("\n✅ 图表功能正常")
        if font_warnings > 0:
            print(f"⚠️ 仍有 {font_warnings} 个字体警告，可能有遗漏的中文字符")
    else:
        print("\n❌ 部分功能仍有问题，需要进一步修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
