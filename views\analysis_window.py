"""
分析设置窗口模块

此模块定义了应用程序的分析设置窗口类，负责：
1. 显示分析设置界面
2. 处理分析参数输入验证

作者: [作者名]
日期: [日期]
"""

from PySide6.QtGui import QDoubleValidator
from ui import ui_analysis
from .base_window import BaseWindow


class AnalysisWindow(BaseWindow):
    """分析设置窗口类"""
    def __init__(self, window_manager):
        super().__init__(window_manager)
        self.ui = ui_analysis.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("分析设置")
        
        # 设置窗口样式
        self.setStyleSheet(self.styleSheet() + """
            QMainWindow::title {
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # 设置数值验证器
        double_validator = QDoubleValidator(0.0, 1e6, -1, self)
        for widget in [
            self.ui.lineEdit_stependline,
            self.ui.lineEdit_timestep,
            self.ui.lineEdit_stiffness,
            self.ui.lineEdit_mass
        ]:
            widget.setValidator(double_validator)
            widget.textChanged.connect(self.validate_input)
            
        # 应用按钮动画效果
        self.setup_animated_buttons()

    def validate_input(self, text):
        """验证输入值"""
        sender = self.sender()
        try:
            value = float(text)
            if value > 1e6:
                sender.setText("1000000")
        except ValueError:
            pass
            
    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.push_finish,
            self.ui.push_mainui,
            self.ui.push_connectionui,
            self.ui.push_constrainui
        ]
        
        # 保存按钮的点击处理函数
        from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step
        from window_manager import WindowType
        # 注意：不在这里连接finish按钮，避免重复连接
        # finish按钮的连接在ctrl/analysis_slot.py中统一处理
        # 使用统一的导航管理器
        mainui_handler = lambda: navigate_to_main_menu(self.window_manager)
        connectionui_handler = lambda: navigate_to_previous_step(self.window_manager, WindowType.ANALYSIS)  # 上一步(连接设置)
        constrainui_handler = lambda: navigate_to_next_step(self.window_manager, WindowType.ANALYSIS)  # 下一步(约束设置)

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        # 重新连接信号（不包括finish按钮）
        # self.ui.push_finish.clicked.connect(finish_handler)  # 移除重复连接
        self.ui.push_mainui.clicked.connect(mainui_handler)
        self.ui.push_connectionui.clicked.connect(connectionui_handler)
        self.ui.push_constrainui.clicked.connect(constrainui_handler)