"""
连接设置界面控制器

此模块负责处理连接设置界面的所有操作，主要包括：
1. 界面跳转控制
2. 轴承连接设置
3. Bushing连接设置
4. 连接参数配置

工作流程：
1. 初始化连接设置界面
2. 配置连接类型和参数
3. 保存连接设置
4. 界面跳转管理

作者: [作者名]
日期: [日期]
"""

from typing import Optional
from PySide6.QtWidgets import QWidget
from window_manager import WindowManager, WindowType
from error_handler import ErrorHandler, AppError, ErrorSeverity, ValidationError, FileOperationError
from resource_manager import ResourceManager
from core.workflow_state import get_workflow_state, WorkflowStep
import re
import json
import os
import shutil
from datetime import datetime

# 导航函数已移至统一的导航管理器
# 使用 core.navigation_manager 中的统一导航接口

def to_pre_slot(window_manager: WindowManager) -> None:
    """跳转到前处理界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    window_manager.switch_to(WindowType.PRE)

def validate_matrix_values(matrix: dict, name: str) -> None:
    """验证矩阵数据的有效性
    
    Args:
        matrix: 矩阵数据
        name: 矩阵名称(用于错误提示)
        
    Raises:
        ValidationError: 当数据无效时抛出
    """
    # 检查数值范围
    for axis, values in matrix.items():
        for pos, val in values.items():
            if not isinstance(val, (int, float)):
                raise ValidationError(f"{name}矩阵{axis}轴位置{pos}的值必须是数字")
            if val < 0:
                raise ValidationError(f"{name}矩阵{axis}轴位置{pos}的值不能为负")

def validate_bearing_values(bearing: dict, bearing_id: int) -> None:
    """验证轴承参数的有效性
    
    Args:
        bearing: 轴承数据
        bearing_id: 轴承ID
        
    Raises:
        ValidationError: 当数据无效时抛出
    """
    # 检查必填字段
    required_fields = ["ref_name", "mob_name", "RotationPlane"]
    for field in required_fields:
        if not bearing.get(field):
            raise ValidationError(f"轴承{bearing_id}的{field}不能为空")
            
    # 检查刚度和阻尼系数
    for prefix in ['k', 'c']:
        for suffix in ['11', '22', '12', '21']:
            key = f"{prefix}{suffix}"
            value = bearing.get(key)
            if value is None:
                raise ValidationError(f"轴承{bearing_id}的{key}不能为空")
            if value < 0:
                raise ValidationError(f"轴承{bearing_id}的{key}不能为负")

def get_bearing_data(ui, bearing_id: int) -> dict:
    """获取轴承数据
    
    Args:
        ui: 连接窗口UI实例
        bearing_id: 轴承ID (1或2)
        
    Returns:
        dict: 轴承配置数据
        
    Raises:
        ValidationError: 当输入数据无效时抛出
    """
    bearing = {}
    
    # 获取下拉框数据
    for field in ["reference", "mobile", "rotation"]:
        value = getattr(ui, f"{field}_zhou{bearing_id}").currentText()
        if not value:
            raise ValidationError(f"轴承{bearing_id}的{field}不能为空")
        if field == "reference":
            bearing["ref_name"] = value
        elif field == "mobile":
            bearing["mob_name"] = value
        else:
            bearing["RotationPlane"] = value
    
    # 获取刚度和阻尼矩阵
    try:
        stiffness = getattr(ui, f"stiffness_zhou{bearing_id}").text().strip()
        damping = getattr(ui, f"damping_zhou{bearing_id}").text().strip()
        
        if not stiffness or not damping:
            raise ValidationError(f"轴承{bearing_id}的刚度和阻尼矩阵不能为空")
        
        # 解析刚度矩阵 - 支持空格、逗号或分号分隔
        k_values = [float(x.strip()) for x in re.split(r'[,;\s]+', stiffness) if x.strip()]
        if len(k_values) != 4:
            raise ValidationError(f"轴承{bearing_id}的刚度矩阵必须包含4个数值，当前包含{len(k_values)}个")
            
        bearing.update({
            "k11": k_values[0],
            "k22": k_values[1], 
            "k12": k_values[2],
            "k21": k_values[3]
        })
        
        # 解析阻尼矩阵 - 支持空格、逗号或分号分隔
        c_values = [float(x.strip()) for x in re.split(r'[,;\s]+', damping) if x.strip()]
        if len(c_values) != 4:
            raise ValidationError(f"轴承{bearing_id}的阻尼矩阵必须包含4个数值，当前包含{len(c_values)}个")
            
        bearing.update({
            "c11": c_values[0],
            "c22": c_values[1],
            "c12": c_values[2],
            "c21": c_values[3]
        })
        
        # 验证数值有效性
        validate_bearing_values(bearing, bearing_id)
        
    except ValueError as e:
        raise ValidationError(f"轴承{bearing_id}的参数格式错误: {str(e)}")
        
    return bearing

def get_matrix_data(table_widget, matrix_name: str) -> dict:
    """从表格控件获取矩阵数据
    
    Args:
        table_widget: QTableWidget实例
        matrix_name: 矩阵名称(用于错误提示)
        
    Returns:
        dict: 矩阵数据
        
    Raises:
        ValidationError: 当输入数据无效时抛出
    """
    matrix = {
        "X": {"0": 0, "1": 0, "2": 0},
        "Y": {"1": 0, "2": 0},
        "Z": {"2": 0}
    }
    
    try:
        # X列数据(3行)
        for row in range(3):
            item = table_widget.item(row, 0)
            if item and item.text():
                matrix["X"][str(row)] = float(item.text())
        
        # Y列数据(2-3行)
        for row in range(1, 3):
            item = table_widget.item(row, 1)
            if item and item.text():
                matrix["Y"][str(row)] = float(item.text())
        
        # Z列数据(第3行)
        item = table_widget.item(2, 2)
        if item and item.text():
            matrix["Z"]["2"] = float(item.text())
            
        # 验证矩阵数据
        validate_matrix_values(matrix, matrix_name)
            
    except ValueError as e:
        raise ValidationError(f"{matrix_name}矩阵数据格式错误: {str(e)}")
        
    return matrix

def get_bushing_data(ui, bushing_id: int) -> dict:
    """获取Bushing数据
    
    Args:
        ui: 连接窗口UI实例
        bushing_id: Bushing ID (1-4)
        
    Returns:
        dict: Bushing配置数据
        
    Raises:
        ValidationError: 当输入数据无效时抛出
    """
    bushing = {
        "mob_name": getattr(ui, f"mobile_bushing{bushing_id}").currentText(),
        "connection_type": "BodyToGround"
    }
    
    if not bushing["mob_name"]:
        raise ValidationError(f"Bushing {bushing_id}的移动体名称不能为空")
    
    # 获取刚度矩阵
    stiffness_table = getattr(ui, f"tableWidget_stiffness_bushing{bushing_id}")
    bushing["stiffness_matrix"] = get_matrix_data(stiffness_table, f"Bushing {bushing_id}刚度")
    
    # 获取阻尼矩阵
    damping_table = getattr(ui, f"tableWidget_damping_bushing{bushing_id}")
    bushing["damping_matrix"] = get_matrix_data(damping_table, f"Bushing {bushing_id}阻尼")
    
    return bushing

def cleanup_old_configs(json_dir: str, prefix: str, max_files: int = 5) -> None:
    """清理旧的配置文件，只保留最新的几个文件
    
    Args:
        json_dir: JSON文件目录
        prefix: 文件名前缀
        max_files: 保留的最大文件数
    """
    try:
        # 获取所有匹配的配置文件
        files = [f for f in os.listdir(json_dir) if f.startswith(prefix) and not f.endswith('_latest.json')]
        if len(files) <= max_files:
            return
            
        # 按修改时间排序
        files.sort(key=lambda x: os.path.getmtime(os.path.join(json_dir, x)), reverse=True)
        
        # 删除旧文件
        for f in files[max_files:]:
            try:
                os.remove(os.path.join(json_dir, f))
                print(f"删除旧配置文件 {f}")
            except Exception as e:
                print(f"警告: 无法删除旧配置文件 {f}: {str(e)}")
                
    except Exception as e:
        print(f"警告: 清理旧配置文件时出错: {str(e)}")

def set_input_hints(connection_window: QWidget) -> None:
    """设置输入框的提示信息
    
    Args:
        connection_window: 连接设置窗口实例
    """
    # 轴承刚度和阻尼矩阵输入提示
    stiffness_hint = "请输入4个数值，用空格、逗号或分号分隔。例如: 1000 2000 0 0"
    damping_hint = "请输入4个数值，用空格、逗号或分号分隔。例如: 1.0 2.0 0 0"
    
    for i in range(1, 3):  # 轴承1和2
        getattr(connection_window.ui, f"stiffness_zhou{i}").setPlaceholderText(stiffness_hint)
        getattr(connection_window.ui, f"damping_zhou{i}").setPlaceholderText(damping_hint)

def connection_slot(window_manager: WindowManager) -> None:
    """初始化连接设置界面的所有槽函数连接
    
    此函数负责将连接设置界面上的各个控件与对应的槽函数连接起来，
    包括界面跳转按钮和连接设置功能按钮。
    
    Args:
        window_manager: 窗口管理器实例
    """
    connection_window = window_manager.get_window(WindowType.CONNECTION)
    if not connection_window:
        ErrorHandler().handle_error(
            AppError("无法获取连接设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 设置输入提示
    set_input_hints(connection_window)
        
    # 界面跳转按钮连接（使用统一的导航管理器）
    from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step

    connection_window.ui.push_mainui.clicked.connect(
        lambda: navigate_to_main_menu(window_manager))

    # 连接设置的下一步应该是分析设置
    connection_window.ui.push_analysisui.clicked.connect(
        lambda: navigate_to_next_step(window_manager, WindowType.CONNECTION))

    # push_meshui按钮文本是"上一步(前处理)"，应该跳转到前处理
    if hasattr(connection_window.ui, 'push_meshui'):
        connection_window.ui.push_meshui.clicked.connect(
            lambda: navigate_to_previous_step(window_manager, WindowType.CONNECTION))

    # 完成连接设置
    connection_window.ui.push_generateconnection.clicked.connect(
        lambda: get_connection_json(window_manager))

def get_connection_json(window_manager: WindowManager) -> None:
    """获取连接设置界面的配置信息并保存为JSON文件
    
    此函数完成以下任务：
    1. 获取界面上的连接参数
    2. 验证参数有效性
    3. 生成配置JSON
    4. 保存到文件
    
    Args:
        window_manager: 窗口管理器实例
    """
    connection_window = window_manager.get_window(WindowType.CONNECTION)
    error_handler = ErrorHandler()
    
    if not connection_window:
        error_handler.handle_error(
            AppError("无法获取连接设置窗口实例", ErrorSeverity.CRITICAL)
        )
        return
    
    try:
        # 获取轴承数据
        bearings = [
            get_bearing_data(connection_window.ui, 1),
            get_bearing_data(connection_window.ui, 2)
        ]
        
        # 获取Bushing数据
        bushings = [
            get_bushing_data(connection_window.ui, i) 
            for i in range(1, 5)
        ]
        
        # 生成配置数据
        connection_config = {
            "bearings": bearings,
            "bushings": bushings
        }

        # 保存配置文件
        main_window = window_manager.get_window(WindowType.MAIN)
        resource_manager = ResourceManager()
        resource_manager.initialize(main_window.ANSYS_Work_Dir)

        try:
            # 确保json目录存在
            os.makedirs(resource_manager.json_dir, exist_ok=True)
            
            # 清理旧的配置文件
            cleanup_old_configs(resource_manager.json_dir, "connection_config_")
            
            # 生成带时间戳的文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            config_filename = f"connection_config_{timestamp}.json"
            config_file = os.path.join(resource_manager.json_dir, config_filename)
            
            # 保存新的配置文件
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(connection_config, f, indent=4)
                
            # 创建或更新最新配置文件的链接
            latest_link = os.path.join(resource_manager.json_dir, "connection_config_latest.json")
            if os.path.exists(latest_link):
                try:
                    os.remove(latest_link)
                except Exception as e:
                    print(f"警告: 无法删除旧的链接文件: {str(e)}")
                    
            try:
                os.symlink(config_file, latest_link)
            except Exception as e:
                print(f"警告: 无法创建软链接，将复制文件: {str(e)}")
                shutil.copy2(config_file, latest_link)
            
        except Exception as e:
            raise FileOperationError(
                "保存配置文件失败",
                details={'file': config_file}
            ) from e
        
        # 设置完成状态
        window_manager.process_status.set_completed(WindowType.CONNECTION)

        # 标记工作流程步骤完成
        workflow_state = get_workflow_state()
        workflow_state.mark_step_completed(WorkflowStep.CONNECTION, {
            'config_file': config_file,
            'timestamp': datetime.now().isoformat()
        })

        # 显示成功消息
        error_handler.handle_error(
            AppError(
                "连接设置已保存",
                ErrorSeverity.INFO
            ),
            connection_window
        )
        
    except Exception as e:
        error_handler.handle_exception(e, connection_window)
    