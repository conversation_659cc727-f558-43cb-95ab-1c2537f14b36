# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'analysis.ui'
##
## Created by: Qt User Interface Compiler version 6.8.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QCheckBox, QGroupBox, QHBoxLayout,
    QLabel, QLineEdit, QMainWindow, QPushButton,
    QSizePolicy, QSpacerItem, QStatusBar, QVBoxLayout,
    QWidget)

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1000, 500)
        MainWindow.setMinimumSize(QSize(1000, 500))
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_4 = QVBoxLayout(self.centralwidget)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_3 = QVBoxLayout()
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.horizontalLayout_2 = QHBoxLayout()
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.label = QLabel(self.centralwidget)
        self.label.setObjectName(u"label")
        self.label.setMinimumSize(QSize(250, 40))
        font = QFont()
        font.setFamilies([u"Times New Roman"])
        font.setPointSize(20)
        self.label.setFont(font)

        self.horizontalLayout_2.addWidget(self.label)

        self.lineEdit_stependline = QLineEdit(self.centralwidget)
        self.lineEdit_stependline.setObjectName(u"lineEdit_stependline")
        self.lineEdit_stependline.setMinimumSize(QSize(250, 30))

        self.horizontalLayout_2.addWidget(self.lineEdit_stependline)

        self.horizontalSpacer = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_2.addItem(self.horizontalSpacer)


        self.verticalLayout_3.addLayout(self.horizontalLayout_2)

        self.horizontalLayout_3 = QHBoxLayout()
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.label_2 = QLabel(self.centralwidget)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setMinimumSize(QSize(250, 40))
        self.label_2.setFont(font)

        self.horizontalLayout_3.addWidget(self.label_2)

        self.lineEdit_timestep = QLineEdit(self.centralwidget)
        self.lineEdit_timestep.setObjectName(u"lineEdit_timestep")
        self.lineEdit_timestep.setMinimumSize(QSize(250, 30))

        self.horizontalLayout_3.addWidget(self.lineEdit_timestep)

        self.horizontalSpacer_2 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_3.addItem(self.horizontalSpacer_2)


        self.verticalLayout_3.addLayout(self.horizontalLayout_3)


        self.verticalLayout_4.addLayout(self.verticalLayout_3)

        self.horizontalLayout_8 = QHBoxLayout()
        self.horizontalLayout_8.setObjectName(u"horizontalLayout_8")
        self.groupBox_2 = QGroupBox(self.centralwidget)
        self.groupBox_2.setObjectName(u"groupBox_2")
        self.groupBox_2.setMinimumSize(QSize(300, 0))
        self.groupBox_2.setFont(font)
        self.horizontalLayout_7 = QHBoxLayout(self.groupBox_2)
        self.horizontalLayout_7.setObjectName(u"horizontalLayout_7")
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.checkBox_stress = QCheckBox(self.groupBox_2)
        self.checkBox_stress.setObjectName(u"checkBox_stress")
        self.checkBox_stress.setEnabled(True)
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.checkBox_stress.sizePolicy().hasHeightForWidth())
        self.checkBox_stress.setSizePolicy(sizePolicy)
        self.checkBox_stress.setMinimumSize(QSize(0, 20))
        font1 = QFont()
        font1.setFamilies([u"Times New Roman"])
        font1.setPointSize(12)
        self.checkBox_stress.setFont(font1)
        self.checkBox_stress.setIconSize(QSize(16, 16))
        self.checkBox_stress.setChecked(True)
        self.checkBox_stress.setAutoRepeat(False)
        self.checkBox_stress.setTristate(False)

        self.verticalLayout.addWidget(self.checkBox_stress)

        self.checkBox_strain = QCheckBox(self.groupBox_2)
        self.checkBox_strain.setObjectName(u"checkBox_strain")
        self.checkBox_strain.setMinimumSize(QSize(0, 20))
        self.checkBox_strain.setFont(font1)
        self.checkBox_strain.setChecked(True)

        self.verticalLayout.addWidget(self.checkBox_strain)

        self.checkBox_contactdata = QCheckBox(self.groupBox_2)
        self.checkBox_contactdata.setObjectName(u"checkBox_contactdata")
        self.checkBox_contactdata.setMinimumSize(QSize(0, 20))
        self.checkBox_contactdata.setFont(font1)
        self.checkBox_contactdata.setChecked(True)

        self.verticalLayout.addWidget(self.checkBox_contactdata)

        self.checkBox_volumeandenergy = QCheckBox(self.groupBox_2)
        self.checkBox_volumeandenergy.setObjectName(u"checkBox_volumeandenergy")
        self.checkBox_volumeandenergy.setMinimumSize(QSize(0, 20))
        self.checkBox_volumeandenergy.setFont(font1)
        self.checkBox_volumeandenergy.setChecked(True)

        self.verticalLayout.addWidget(self.checkBox_volumeandenergy)

        self.checkBox_eulerangles = QCheckBox(self.groupBox_2)
        self.checkBox_eulerangles.setObjectName(u"checkBox_eulerangles")
        self.checkBox_eulerangles.setMinimumSize(QSize(0, 20))
        self.checkBox_eulerangles.setFont(font1)
        self.checkBox_eulerangles.setIconSize(QSize(16, 16))
        self.checkBox_eulerangles.setChecked(True)
        self.checkBox_eulerangles.setAutoRepeatDelay(300)
        self.checkBox_eulerangles.setAutoRepeatInterval(100)

        self.verticalLayout.addWidget(self.checkBox_eulerangles)


        self.horizontalLayout_7.addLayout(self.verticalLayout)


        self.horizontalLayout_8.addWidget(self.groupBox_2)

        self.groupBox_dampingcontrols = QGroupBox(self.centralwidget)
        self.groupBox_dampingcontrols.setObjectName(u"groupBox_dampingcontrols")
        self.groupBox_dampingcontrols.setFont(font)
        self.verticalLayout_2 = QVBoxLayout(self.groupBox_dampingcontrols)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.label_3 = QLabel(self.groupBox_dampingcontrols)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setMinimumSize(QSize(300, 50))
        self.label_3.setFont(font)

        self.horizontalLayout_4.addWidget(self.label_3)

        self.lineEdit_stiffness = QLineEdit(self.groupBox_dampingcontrols)
        self.lineEdit_stiffness.setObjectName(u"lineEdit_stiffness")
        self.lineEdit_stiffness.setMinimumSize(QSize(250, 40))

        self.horizontalLayout_4.addWidget(self.lineEdit_stiffness)

        self.horizontalSpacer_3 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_4.addItem(self.horizontalSpacer_3)


        self.verticalLayout_2.addLayout(self.horizontalLayout_4)

        self.horizontalLayout_5 = QHBoxLayout()
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.label_4 = QLabel(self.groupBox_dampingcontrols)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setMinimumSize(QSize(300, 50))
        self.label_4.setFont(font)

        self.horizontalLayout_5.addWidget(self.label_4)

        self.lineEdit_mass = QLineEdit(self.groupBox_dampingcontrols)
        self.lineEdit_mass.setObjectName(u"lineEdit_mass")
        self.lineEdit_mass.setMinimumSize(QSize(250, 40))

        self.horizontalLayout_5.addWidget(self.lineEdit_mass)

        self.horizontalSpacer_4 = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_5.addItem(self.horizontalSpacer_4)


        self.verticalLayout_2.addLayout(self.horizontalLayout_5)


        self.horizontalLayout_8.addWidget(self.groupBox_dampingcontrols)


        self.verticalLayout_4.addLayout(self.horizontalLayout_8)

        self.horizontalLayout = QHBoxLayout()
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.push_finish = QPushButton(self.centralwidget)
        self.push_finish.setObjectName(u"push_finish")
        sizePolicy1 = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        sizePolicy1.setHorizontalStretch(0)
        sizePolicy1.setVerticalStretch(0)
        sizePolicy1.setHeightForWidth(self.push_finish.sizePolicy().hasHeightForWidth())
        self.push_finish.setSizePolicy(sizePolicy1)
        self.push_finish.setMinimumSize(QSize(200, 65))
        font2 = QFont()
        font2.setFamilies([u"\u5b8b\u4f53"])
        font2.setPointSize(20)
        self.push_finish.setFont(font2)

        self.horizontalLayout.addWidget(self.push_finish)

        self.push_connectionui = QPushButton(self.centralwidget)
        self.push_connectionui.setObjectName(u"push_connectionui")
        sizePolicy1.setHeightForWidth(self.push_connectionui.sizePolicy().hasHeightForWidth())
        self.push_connectionui.setSizePolicy(sizePolicy1)
        self.push_connectionui.setMinimumSize(QSize(250, 65))
        self.push_connectionui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_connectionui)

        self.push_constrainui = QPushButton(self.centralwidget)
        self.push_constrainui.setObjectName(u"push_constrainui")
        sizePolicy1.setHeightForWidth(self.push_constrainui.sizePolicy().hasHeightForWidth())
        self.push_constrainui.setSizePolicy(sizePolicy1)
        self.push_constrainui.setMinimumSize(QSize(250, 65))
        self.push_constrainui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_constrainui)

        self.push_mainui = QPushButton(self.centralwidget)
        self.push_mainui.setObjectName(u"push_mainui")
        sizePolicy1.setHeightForWidth(self.push_mainui.sizePolicy().hasHeightForWidth())
        self.push_mainui.setSizePolicy(sizePolicy1)
        self.push_mainui.setMinimumSize(QSize(200, 65))
        self.push_mainui.setFont(font2)

        self.horizontalLayout.addWidget(self.push_mainui)


        self.verticalLayout_4.addLayout(self.horizontalLayout)

        MainWindow.setCentralWidget(self.centralwidget)
        self.statusbar = QStatusBar(MainWindow)
        self.statusbar.setObjectName(u"statusbar")
        MainWindow.setStatusBar(self.statusbar)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"Step End Time", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"Time Step", None))
        self.groupBox_2.setTitle(QCoreApplication.translate("MainWindow", u"Output Controls", None))
        self.checkBox_stress.setText(QCoreApplication.translate("MainWindow", u"Stress", None))
        self.checkBox_strain.setText(QCoreApplication.translate("MainWindow", u"Strain", None))
        self.checkBox_contactdata.setText(QCoreApplication.translate("MainWindow", u"Contact Data", None))
        self.checkBox_volumeandenergy.setText(QCoreApplication.translate("MainWindow", u"Volume and Energy", None))
        self.checkBox_eulerangles.setText(QCoreApplication.translate("MainWindow", u"Euler Angles", None))
        self.groupBox_dampingcontrols.setTitle(QCoreApplication.translate("MainWindow", u"Damping Controls", None))
        self.label_3.setText(QCoreApplication.translate("MainWindow", u"Stiffness Coefficient", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"Mass Coefficient", None))
        self.push_finish.setText(QCoreApplication.translate("MainWindow", u"\u5b8c\u6210\u8bbe\u7f6e", None))
        self.push_connectionui.setText(QCoreApplication.translate("MainWindow", u"\u4e0a\u4e00\u6b65(\u8fde\u63a5\u8bbe\u7f6e)", None))
        self.push_constrainui.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u7ea6\u675f\u8bbe\u7f6e)", None))
        self.push_mainui.setText(QCoreApplication.translate("MainWindow", u"\u8fd4\u56de\u4e3b\u754c\u9762", None))
    # retranslateUi

