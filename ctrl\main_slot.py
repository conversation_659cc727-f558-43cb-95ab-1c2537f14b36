"""
主界面控制器

此模块负责处理主界面的所有操作，主要包括：
1. 界面跳转控制
2. 主菜单功能管理
3. 界面状态管理

工作流程：
1. 初始化主界面
2. 连接各个功能按钮
3. 处理界面跳转逻辑

作者: [作者名]
日期: [日期]
"""

from typing import Optional
from PySide6.QtWidgets import QWidget
from window_manager import WindowManager, WindowType
from error_handler import ErrorHandler, AppError, ErrorSeverity
from ctrl.vibration_analysis import show_vibration_analysis
from ctrl.new_project_slot import create_new_project
from core.workflow_state import get_workflow_state, WorkflowStep
from PySide6.QtWidgets import QMessageBox
from datetime import datetime


def to_mesh_slot(window_manager: WindowManager) -> None:
    """跳转到网格无关性验证界面的槽函数

    需要先完成连接设置、分析设置、设置约束三个步骤

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击网格无关性验证按钮，检查前置条件")

        # 获取工作流程状态管理器
        workflow_state = get_workflow_state()

        # 检查是否可以访问网格无关性验证
        if not workflow_state.can_access_step(WorkflowStep.MESH_VALIDATION):
            missing_steps = workflow_state.get_missing_dependencies(WorkflowStep.MESH_VALIDATION)

            # 构建缺失步骤的中文名称
            step_names = {
                WorkflowStep.CONNECTION: "连接设置",
                WorkflowStep.ANALYSIS: "分析设置",
                WorkflowStep.CONSTRAIN: "设置约束"
            }

            missing_names = [step_names.get(step, step.value) for step in missing_steps]

            # 显示警告对话框
            main_window = window_manager.get_window(WindowType.MAIN)
            if main_window:
                msg_box = QMessageBox(main_window)
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("前置条件未满足")
                msg_box.setText("网格无关性验证需要先完成以下步骤：")
                msg_box.setDetailedText(f"缺失的步骤：{', '.join(missing_names)}")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()

            print(f"网格无关性验证前置条件未满足，缺失步骤: {missing_names}")
            return

        print("前置条件满足，准备切换到网格无关性验证窗口")
        # 强制隐藏所有其他窗口
        window_manager.force_hide_all_except(WindowType.MESH)
        window_manager.switch_to(WindowType.MESH)
        print("成功切换到网格无关性验证窗口")

    except Exception as e:
        print(f"切换到网格无关性验证窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到网格无关性验证窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_pre_slot(window_manager: WindowManager) -> None:
    """跳转到前处理界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击前处理按钮，准备切换到前处理窗口")
        window_manager.force_hide_all_except(WindowType.PRE)
        window_manager.switch_to(WindowType.PRE)
        print("成功切换到前处理窗口")
    except Exception as e:
        print(f"切换到前处理窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到前处理窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_connection_slot(window_manager: WindowManager) -> None:
    """跳转到连接设置界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击连接设置按钮，准备切换到连接设置窗口")
        window_manager.force_hide_all_except(WindowType.CONNECTION)
        window_manager.switch_to(WindowType.CONNECTION)
        print("成功切换到连接设置窗口")
    except Exception as e:
        print(f"切换到连接设置窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到连接设置窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_analysis_slot(window_manager: WindowManager) -> None:
    """跳转到分析设置界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击分析设置按钮，准备切换到分析设置窗口")
        window_manager.force_hide_all_except(WindowType.ANALYSIS)
        window_manager.switch_to(WindowType.ANALYSIS)
        print("成功切换到分析设置窗口")
    except Exception as e:
        print(f"切换到分析设置窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到分析设置窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_constrain_slot(window_manager: WindowManager) -> None:
    """跳转到约束设置界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击约束设置按钮，准备切换到约束设置窗口")
        window_manager.force_hide_all_except(WindowType.CONSTRAIN)
        window_manager.switch_to(WindowType.CONSTRAIN)
        print("成功切换到约束设置窗口")
    except Exception as e:
        print(f"切换到约束设置窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到约束设置窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_result_slot(window_manager: WindowManager) -> None:
    """跳转到结果显示界面的槽函数

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击结果显示按钮，准备切换到结果显示窗口")
        window_manager.force_hide_all_except(WindowType.RESULT)
        window_manager.switch_to(WindowType.RESULT)
        print("成功切换到结果显示窗口")
    except Exception as e:
        print(f"切换到结果显示窗口失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"切换到结果显示窗口失败: {str(e)}", ErrorSeverity.ERROR)
        )

def to_vibration_analysis_slot(window_manager: WindowManager) -> None:
    """跳转到振动分析界面的槽函数（后处理）

    需要先完成计算结果步骤

    Args:
        window_manager: 窗口管理器实例
    """
    try:
        print("点击后处理按钮，检查前置条件")

        # 获取工作流程状态管理器
        workflow_state = get_workflow_state()

        # 检查是否可以访问后处理
        if not workflow_state.can_access_step(WorkflowStep.POST_PROCESSING):
            missing_steps = workflow_state.get_missing_dependencies(WorkflowStep.POST_PROCESSING)

            # 构建缺失步骤的中文名称
            step_names = {
                WorkflowStep.RESULT: "计算结果"
            }

            missing_names = [step_names.get(step, step.value) for step in missing_steps]

            # 显示警告对话框
            main_window = window_manager.get_window(WindowType.MAIN)
            if main_window:
                msg_box = QMessageBox(main_window)
                msg_box.setIcon(QMessageBox.Icon.Warning)
                msg_box.setWindowTitle("前置条件未满足")
                msg_box.setText("后处理需要先完成以下步骤：")
                msg_box.setDetailedText(f"缺失的步骤：{', '.join(missing_names)}")
                msg_box.setStandardButtons(QMessageBox.StandardButton.Ok)
                msg_box.exec()

            print(f"后处理前置条件未满足，缺失步骤: {missing_names}")
            return

        print("前置条件满足，启动振动分析器")
        show_vibration_analysis(window_manager)

        # 标记后处理步骤完成
        workflow_state.mark_step_completed(WorkflowStep.POST_PROCESSING, {
            'timestamp': datetime.now().isoformat(),
            'analysis_tool': 'vibration_analyzer'
        })

    except Exception as e:
        print(f"启动振动分析器失败: {e}")
        ErrorHandler().handle_error(
            AppError(f"启动振动分析器失败: {str(e)}", ErrorSeverity.ERROR)
        )

def main_slot(window_manager: WindowManager) -> None:
    """初始化主界面的所有槽函数连接
    
    此函数负责将主界面上的各个功能按钮与对应的槽函数连接起来，
    实现界面跳转功能。
    
    Args:
        window_manager: 窗口管理器实例
    """
    main_window = window_manager.get_window(WindowType.MAIN)
    if not main_window:
        ErrorHandler().handle_error(
            AppError("无法获取主窗口实例", ErrorSeverity.CRITICAL)
        )
        return
        
    # 网格模块按钮连接（保持前置条件检查）
    main_window.ui.mesh.clicked.connect(
        lambda: to_mesh_slot(window_manager))

    # 前处理模块按钮连接
    main_window.ui.preprocessing.clicked.connect(
        lambda: to_pre_slot(window_manager))

    # 连接设置模块按钮连接
    main_window.ui.connection.clicked.connect(
        lambda: to_connection_slot(window_manager))

    # 分析设置模块按钮连接
    main_window.ui.analysis.clicked.connect(
        lambda: to_analysis_slot(window_manager))

    # 约束设置模块按钮连接
    main_window.ui.constrain.clicked.connect(
        lambda: to_constrain_slot(window_manager))

    # 结果显示模块按钮连接
    main_window.ui.result.clicked.connect(
        lambda: to_result_slot(window_manager))

    # 后处理模块按钮连接（保持前置条件检查）
    if hasattr(main_window.ui, "post"):
        main_window.ui.post.clicked.connect(
            lambda: to_vibration_analysis_slot(window_manager))

    # 新建项目菜单动作连接
    main_window.ui.actionnew.triggered.connect(
        lambda: create_new_project(window_manager))

