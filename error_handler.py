"""
错误处理模块

此模块提供统一的错误处理机制，包括：
1. 自定义异常类
2. 错误日志记录
3. 用户界面反馈
4. ANSYS操作错误处理

作者: [作者名]
日期: [日期]
"""

import logging
import os
import traceback
from enum import Enum, auto
from typing import Optional, Any, Dict
from datetime import datetime
from PySide6.QtWidgets import QMessageBox, QWidget


class ErrorSeverity(Enum):
    """错误严重程度枚举"""
    INFO = auto()      # 信息提示
    WARNING = auto()   # 警告
    ERROR = auto()     # 错误
    CRITICAL = auto()  # 严重错误


class ErrorCategory(Enum):
    """错误类别枚举"""
    UI = auto()           # 界面相关错误
    FILE_IO = auto()      # 文件操作错误
    ANSYS = auto()        # ANSYS操作错误
    NETWORK = auto()      # 网络相关错误
    CONFIGURATION = auto() # 配置相关错误
    VALIDATION = auto()    # 数据验证错误
    SYSTEM = auto()       # 系统相关错误


class AppError(Exception):
    """应用程序基础异常类"""
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.ERROR,
                 category: ErrorCategory = ErrorCategory.SYSTEM,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.severity = severity
        self.category = category
        self.details = details or {}
        self.timestamp = datetime.now()


class AnsysError(AppError):
    """ANSYS操作相关异常"""
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.ERROR,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message, severity, ErrorCategory.ANSYS, details)


class ValidationError(AppError):
    """数据验证相关异常"""
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.WARNING,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message, severity, ErrorCategory.VALIDATION, details)


class FileOperationError(AppError):
    """文件操作相关异常"""
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.ERROR,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message, severity, ErrorCategory.FILE_IO, details)


class ConfigurationError(AppError):
    """配置相关异常"""
    def __init__(self, 
                 message: str,
                 severity: ErrorSeverity = ErrorSeverity.ERROR,
                 details: Optional[Dict[str, Any]] = None):
        super().__init__(message, severity, ErrorCategory.CONFIGURATION, details)


class ErrorHandler:
    """错误处理器类
    
    提供统一的错误处理机制，包括日志记录和用户界面反馈。
    实现了单例模式，确保全局只有一个错误处理器实例。
    """
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ErrorHandler, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        # 初始化日志系统
        self._setup_logging()
        self._initialized = True
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        self.logger = logging.getLogger('ErrorHandler')
        self.logger.setLevel(logging.INFO)
        
        # 确保日志目录存在
        from pathlib import Path
        log_dir = Path('logs')
        log_dir.mkdir(exist_ok=True)

        # 创建日志文件处理器
        log_file = log_dir / 'error.log'
        handler = logging.FileHandler(str(log_file), encoding='utf-8')
        handler.setLevel(logging.INFO)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        
        self.logger.addHandler(handler)
    
    def _get_log_level(self, severity: ErrorSeverity) -> int:
        """获取对应的日志级别
        
        Args:
            severity: 错误严重程度
            
        Returns:
            对应的日志级别
        """
        return {
            ErrorSeverity.INFO: logging.INFO,
            ErrorSeverity.WARNING: logging.WARNING,
            ErrorSeverity.ERROR: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(severity, logging.ERROR)
    
    def _get_message_box_icon(self, severity: ErrorSeverity) -> QMessageBox.Icon:
        """获取对应的消息框图标
        
        Args:
            severity: 错误严重程度
            
        Returns:
            对应的消息框图标
        """
        return {
            ErrorSeverity.INFO: QMessageBox.Information,
            ErrorSeverity.WARNING: QMessageBox.Warning,
            ErrorSeverity.ERROR: QMessageBox.Critical,
            ErrorSeverity.CRITICAL: QMessageBox.Critical
        }.get(severity, QMessageBox.Critical)
    
    def handle_error(self, 
                    error: AppError,
                    parent: Optional[QWidget] = None,
                    show_message: bool = True) -> None:
        """处理错误
        
        Args:
            error: 错误对象
            parent: 父窗口对象，用于显示消息框
            show_message: 是否显示消息框
        """
        # 记录错误日志
        log_level = self._get_log_level(error.severity)
        self.logger.log(log_level, f"[{error.category.name}] {str(error)}")
        
        if error.details:
            self.logger.log(log_level, f"Error details: {error.details}")
        
        # 记录堆栈跟踪
        if log_level >= logging.ERROR:
            self.logger.error("Stack trace:", exc_info=True)
        
        # 显示错误消息
        if show_message and parent is not None:
            icon = self._get_message_box_icon(error.severity)
            title = f"{error.category.name} {error.severity.name}"
            
            # 构建详细信息
            detailed_text = f"时间: {error.timestamp}\n"
            detailed_text += f"类型: {error.category.name}\n"
            detailed_text += f"严重程度: {error.severity.name}\n"
            if error.details:
                detailed_text += "\n详细信息:\n"
                for key, value in error.details.items():
                    detailed_text += f"{key}: {value}\n"
            
            # 创建消息框
            msg_box = QMessageBox(parent)
            msg_box.setIcon(icon)
            msg_box.setWindowTitle(title)
            msg_box.setText(str(error))
            msg_box.setDetailedText(detailed_text)
            
            # 根据严重程度添加不同的按钮
            if error.severity in [ErrorSeverity.ERROR, ErrorSeverity.CRITICAL]:
                msg_box.setStandardButtons(QMessageBox.Ok)
            else:
                msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
            
            msg_box.exec_()
    
    def handle_exception(self,
                        exc: Exception,
                        parent: Optional[QWidget] = None,
                        show_message: bool = True) -> None:
        """处理标准异常
        
        Args:
            exc: 异常对象
            parent: 父窗口对象，用于显示消息框
            show_message: 是否显示消息框
        """
        # 将标准异常转换为应用程序异常
        error = AppError(
            str(exc),
            ErrorSeverity.ERROR,
            ErrorCategory.SYSTEM,
            {
                'exception_type': type(exc).__name__,
                'traceback': traceback.format_exc()
            }
        )
        self.handle_error(error, parent, show_message) 