#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格参数持久化测试脚本

此脚本用于验证网格参数的保存和加载机制，确保：
1. 网格参数修改后能正确保存到配置文件
2. 软件重启后能正确加载保存的参数
3. 配置文件的读写机制正常工作

作者: AI Assistant
日期: 2025-08-01
"""

import sys
import os
import logging
import json
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_config_manager_save_load():
    """测试ConfigManager的保存和加载功能"""
    try:
        logger.info("开始测试ConfigManager的保存和加载功能")
        
        from core.config_manager import ConfigManager
        
        # 创建配置管理器
        config_manager = ConfigManager()
        
        # 测试网格参数保存
        test_mesh_data = {
            "test-mesh-1": {
                "id": "test-mesh-1",
                "name": "测试网格1",
                "size": 12.0,
                "element_type": "四面体",
                "status": "未生成"
            },
            "test-mesh-2": {
                "id": "test-mesh-2", 
                "name": "测试网格2",
                "size": 15.0,
                "element_type": "六面体",
                "status": "未生成"
            }
        }
        
        # 保存网格参数
        config_manager.set_mesh_parameters(test_mesh_data)
        config_manager.set_current_mesh_id("test-mesh-1")
        
        # 保存配置到文件
        save_success = config_manager.save_config()
        if not save_success:
            logger.error("配置保存失败")
            return False
        
        logger.info("配置保存成功")
        
        # 创建新的配置管理器实例来测试加载
        config_manager2 = ConfigManager()
        
        # 加载网格参数
        loaded_mesh_data = config_manager2.get_mesh_parameters()
        loaded_current_mesh_id = config_manager2.get_current_mesh_id()
        
        # 验证加载的数据
        if not loaded_mesh_data:
            logger.error("加载的网格参数为空")
            return False
        
        if len(loaded_mesh_data) != len(test_mesh_data):
            logger.error(f"加载的网格数量不匹配，期望: {len(test_mesh_data)}, 实际: {len(loaded_mesh_data)}")
            return False
        
        # 验证具体的网格参数
        for mesh_id, expected_mesh in test_mesh_data.items():
            if mesh_id not in loaded_mesh_data:
                logger.error(f"网格 {mesh_id} 未找到")
                return False
            
            loaded_mesh = loaded_mesh_data[mesh_id]
            for key, expected_value in expected_mesh.items():
                if loaded_mesh.get(key) != expected_value:
                    logger.error(f"网格 {mesh_id} 的 {key} 不匹配，期望: {expected_value}, 实际: {loaded_mesh.get(key)}")
                    return False
        
        # 验证当前网格ID
        if loaded_current_mesh_id != "test-mesh-1":
            logger.error(f"当前网格ID不匹配，期望: test-mesh-1, 实际: {loaded_current_mesh_id}")
            return False
        
        logger.info("✅ ConfigManager保存和加载功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ ConfigManager保存和加载功能测试失败: {str(e)}")
        return False

def test_mesh_manager_persistence():
    """测试MeshManager的持久化功能"""
    try:
        logger.info("开始测试MeshManager的持久化功能")
        
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        
        # 创建网格管理器
        mesh_manager = MeshManager()
        
        # 创建测试网格
        mesh1 = MeshParameter(
            name="持久化测试网格1",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        mesh2 = MeshParameter(
            name="持久化测试网格2", 
            size=18.0,
            element_type=ElementType.HEXAHEDRON
        )
        
        # 添加网格
        success1 = mesh_manager.add_mesh(mesh1)
        success2 = mesh_manager.add_mesh(mesh2)
        
        if not (success1 and success2):
            logger.error("添加网格失败")
            return False
        
        # 设置当前网格
        mesh_manager.set_current_mesh(mesh1.id)
        
        # 导出到字典
        mesh_data = mesh_manager.to_dict()
        
        logger.info(f"导出的网格数据包含 {len(mesh_data.get('mesh_parameters', {}))} 个网格")
        
        # 创建新的网格管理器来测试加载
        mesh_manager2 = MeshManager()
        
        # 从字典加载
        load_success = mesh_manager2.from_dict(mesh_data)
        if not load_success:
            logger.error("从字典加载网格数据失败")
            return False
        
        # 验证加载的数据
        loaded_meshes = mesh_manager2.get_all_meshes()
        if len(loaded_meshes) != 2:
            logger.error(f"加载的网格数量不正确，期望: 2, 实际: {len(loaded_meshes)}")
            return False
        
        # 验证网格参数
        mesh1_loaded = mesh_manager2.get_mesh_by_id(mesh1.id)
        mesh2_loaded = mesh_manager2.get_mesh_by_id(mesh2.id)
        
        if not mesh1_loaded or not mesh2_loaded:
            logger.error("无法找到加载的网格")
            return False
        
        # 验证具体参数
        if mesh1_loaded.name != "持久化测试网格1" or mesh1_loaded.size != 12.0:
            logger.error(f"网格1参数不匹配: {mesh1_loaded.name}, {mesh1_loaded.size}")
            return False
        
        if mesh2_loaded.name != "持久化测试网格2" or mesh2_loaded.size != 18.0:
            logger.error(f"网格2参数不匹配: {mesh2_loaded.name}, {mesh2_loaded.size}")
            return False
        
        # 验证当前网格
        current_mesh = mesh_manager2.current_mesh
        if not current_mesh or current_mesh.id != mesh1.id:
            logger.error("当前网格设置不正确")
            return False
        
        logger.info("✅ MeshManager持久化功能测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ MeshManager持久化功能测试失败: {str(e)}")
        return False

def test_mesh_parameter_update_persistence():
    """测试网格参数更新的持久化"""
    try:
        logger.info("开始测试网格参数更新的持久化")
        
        from core.mesh_manager import MeshManager, MeshParameter, ElementType
        from core.config_manager import ConfigManager
        
        # 创建网格管理器和配置管理器
        mesh_manager = MeshManager()
        config_manager = ConfigManager()
        
        # 创建测试网格
        original_mesh = MeshParameter(
            name="原始网格",
            size=12.0,
            element_type=ElementType.TETRAHEDRON
        )
        
        # 添加网格
        mesh_manager.add_mesh(original_mesh)
        
        # 模拟网格参数更新
        updated_mesh = MeshParameter(
            name="更新后的网格",
            size=15.0,  # 从12.0改为15.0
            element_type=ElementType.HEXAHEDRON  # 从四面体改为六面体
        )
        updated_mesh.id = original_mesh.id  # 保持相同的ID
        
        # 更新网格
        update_success = mesh_manager.update_mesh(updated_mesh)
        if not update_success:
            logger.error("网格更新失败")
            return False
        
        # 模拟保存配置的过程
        mesh_data = {}
        for mesh in mesh_manager.get_all_meshes():
            mesh_data[mesh.id] = mesh.to_dict()
        
        config_manager.set_mesh_parameters(mesh_data)
        
        # 保存当前网格ID
        current_mesh = mesh_manager.current_mesh
        if current_mesh:
            config_manager.set_current_mesh_id(current_mesh.id)
        
        # 保存配置到文件
        save_success = config_manager.save_config()
        if not save_success:
            logger.error("配置保存失败")
            return False
        
        logger.info("网格参数更新并保存成功")
        
        # 模拟软件重启 - 创建新的管理器实例
        mesh_manager2 = MeshManager()
        config_manager2 = ConfigManager()
        
        # 加载配置
        mesh_data_loaded = config_manager2.get_mesh_parameters()
        if mesh_data_loaded:
            load_success = mesh_manager2.from_dict({"mesh_parameters": mesh_data_loaded})
            if not load_success:
                logger.error("加载网格参数失败")
                return False
        
        # 设置当前网格
        current_mesh_id = config_manager2.get_current_mesh_id()
        if current_mesh_id:
            mesh_manager2.set_current_mesh(current_mesh_id)
        
        # 验证加载的网格参数
        loaded_mesh = mesh_manager2.get_mesh_by_id(original_mesh.id)
        if not loaded_mesh:
            logger.error("无法找到加载的网格")
            return False
        
        # 验证更新后的参数是否正确保存和加载
        if loaded_mesh.name != "更新后的网格":
            logger.error(f"网格名称未正确保存，期望: '更新后的网格', 实际: '{loaded_mesh.name}'")
            return False
        
        if loaded_mesh.size != 15.0:
            logger.error(f"网格尺寸未正确保存，期望: 15.0, 实际: {loaded_mesh.size}")
            return False
        
        if loaded_mesh.element_type != ElementType.HEXAHEDRON:
            logger.error(f"单元类型未正确保存，期望: HEXAHEDRON, 实际: {loaded_mesh.element_type}")
            return False
        
        logger.info("✅ 网格参数更新持久化测试通过")
        logger.info(f"  - 网格名称: {loaded_mesh.name}")
        logger.info(f"  - 网格尺寸: {loaded_mesh.size}mm")
        logger.info(f"  - 单元类型: {loaded_mesh.element_type.value}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 网格参数更新持久化测试失败: {str(e)}")
        return False

def test_config_file_existence():
    """测试配置文件是否存在和可读写"""
    try:
        logger.info("开始测试配置文件存在性和可读写性")
        
        from core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        config_path = config_manager.get_config_path()
        
        logger.info(f"配置文件路径: {config_path}")
        
        # 检查配置文件是否存在
        if os.path.exists(config_path):
            logger.info("✅ 配置文件存在")
            
            # 检查文件是否可读
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_content = json.load(f)
                logger.info("✅ 配置文件可读")
                logger.info(f"配置文件包含 {len(config_content)} 个顶级配置项")
                
                # 检查是否包含网格配置
                if 'mesh' in config_content:
                    mesh_config = config_content['mesh']
                    if 'mesh_parameters' in mesh_config:
                        mesh_params = mesh_config['mesh_parameters']
                        logger.info(f"配置文件包含 {len(mesh_params)} 个网格参数")
                    else:
                        logger.info("配置文件中没有网格参数")
                else:
                    logger.info("配置文件中没有网格配置")
                
            except Exception as e:
                logger.error(f"❌ 配置文件读取失败: {str(e)}")
                return False
        else:
            logger.info("⚠️  配置文件不存在，将创建新的配置文件")
        
        # 测试写入权限
        try:
            test_config = {"test": "write_permission"}
            config_manager.set("test_write", test_config)
            save_success = config_manager.save_config()
            if save_success:
                logger.info("✅ 配置文件写入权限正常")
                # 清理测试配置
                config_manager.set("test_write", None)
                config_manager.save_config()
            else:
                logger.error("❌ 配置文件写入失败")
                return False
        except Exception as e:
            logger.error(f"❌ 配置文件写入测试失败: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始网格参数持久化验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 运行测试
    tests = [
        ("配置文件存在性和可读写性测试", test_config_file_existence),
        ("ConfigManager保存和加载功能测试", test_config_manager_save_load),
        ("MeshManager持久化功能测试", test_mesh_manager_persistence),
        ("网格参数更新持久化测试", test_mesh_parameter_update_persistence)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}")
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！网格参数持久化功能正常")
        logger.info("\n📋 验证结果:")
        logger.info("• ✅ 配置文件读写权限正常")
        logger.info("• ✅ ConfigManager保存和加载功能正常")
        logger.info("• ✅ MeshManager持久化功能正常")
        logger.info("• ✅ 网格参数更新能够正确持久化")
        logger.info("\n🔧 如果仍有问题，可能的原因:")
        logger.info("• 网格窗口的_save_configuration()方法未被调用")
        logger.info("• 配置文件权限问题")
        logger.info("• 多个ConfigManager实例导致的同步问题")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
        logger.error("请检查配置文件路径和权限设置")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
