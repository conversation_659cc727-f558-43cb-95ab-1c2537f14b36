# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'mesh_new.ui'
##
## Created by: Qt User Interface Compiler version 6.9.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (<PERSON><PERSON><PERSON>, Q<PERSON><PERSON>r, Q<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>urs<PERSON>,
    <PERSON><PERSON><PERSON>, Q<PERSON>ontData<PERSON>, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QCheckBox, QComboBox,
    QDoubleSpinBox, QFormLayout, QGridLayout, QGroupBox,
    QHBoxLayout, QHeaderView, QLabel, QListWidget,
    QListWidgetItem, QMainWindow, QProgressBar, QPushButton,
    QSizePolicy, QSpacerItem, QSpinBox, QTabWidget,
    QTableWidget, QTableWidgetItem, QTextEdit, QVBoxLayout,
    QWidget)
class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1400, 900)
        MainWindow.setMinimumSize(QSize(1400, 900))
        font = QFont()
        font.setFamilies([u"Microsoft YaHei UI"])
        font.setPointSize(10)
        MainWindow.setFont(font)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout_main = QVBoxLayout(self.centralwidget)
        self.verticalLayout_main.setSpacing(10)
        self.verticalLayout_main.setObjectName(u"verticalLayout_main")
        self.verticalLayout_main.setContentsMargins(15, 15, 15, 15)
        self.label_title = QLabel(self.centralwidget)
        self.label_title.setObjectName(u"label_title")
        self.label_title.setMinimumSize(QSize(0, 60))
        font1 = QFont()
        font1.setFamilies([u"Microsoft YaHei UI"])
        font1.setPointSize(24)
        font1.setBold(True)
        self.label_title.setFont(font1)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_main.addWidget(self.label_title)

        self.tabWidget_main = QTabWidget(self.centralwidget)
        self.tabWidget_main.setObjectName(u"tabWidget_main")
        self.tab_mesh_management = QWidget()
        self.tab_mesh_management.setObjectName(u"tab_mesh_management")
        self.horizontalLayout_mesh_mgmt = QHBoxLayout(self.tab_mesh_management)
        self.horizontalLayout_mesh_mgmt.setSpacing(15)
        self.horizontalLayout_mesh_mgmt.setObjectName(u"horizontalLayout_mesh_mgmt")
        self.widget_left_panel = QWidget(self.tab_mesh_management)
        self.widget_left_panel.setObjectName(u"widget_left_panel")
        self.widget_left_panel.setMinimumSize(QSize(600, 0))
        self.widget_left_panel.setMaximumSize(QSize(650, 16777215))
        self.verticalLayout_left = QVBoxLayout(self.widget_left_panel)
        self.verticalLayout_left.setObjectName(u"verticalLayout_left")
        self.groupBox_mesh_params = QGroupBox(self.widget_left_panel)
        self.groupBox_mesh_params.setObjectName(u"groupBox_mesh_params")
        self.verticalLayout_params = QVBoxLayout(self.groupBox_mesh_params)
        self.verticalLayout_params.setObjectName(u"verticalLayout_params")
        self.horizontalLayout_toolbar = QHBoxLayout()
        self.horizontalLayout_toolbar.setObjectName(u"horizontalLayout_toolbar")
        self.btn_add_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_add_mesh.setObjectName(u"btn_add_mesh")
        self.btn_add_mesh.setMinimumSize(QSize(100, 35))
        icon = QIcon()
        icon.addFile(u"../assets/icons/add.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_add_mesh.setIcon(icon)

        self.horizontalLayout_toolbar.addWidget(self.btn_add_mesh)

        self.btn_import_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_import_mesh.setObjectName(u"btn_import_mesh")
        self.btn_import_mesh.setMinimumSize(QSize(100, 35))

        self.horizontalLayout_toolbar.addWidget(self.btn_import_mesh)

        self.btn_export_mesh = QPushButton(self.groupBox_mesh_params)
        self.btn_export_mesh.setObjectName(u"btn_export_mesh")
        self.btn_export_mesh.setMinimumSize(QSize(100, 35))

        self.horizontalLayout_toolbar.addWidget(self.btn_export_mesh)

        self.horizontalSpacer_toolbar = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_toolbar.addItem(self.horizontalSpacer_toolbar)


        self.verticalLayout_params.addLayout(self.horizontalLayout_toolbar)

        self.tableWidget_mesh_params = QTableWidget(self.groupBox_mesh_params)
        if (self.tableWidget_mesh_params.columnCount() < 6):
            self.tableWidget_mesh_params.setColumnCount(6)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        self.tableWidget_mesh_params.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        self.tableWidget_mesh_params.setObjectName(u"tableWidget_mesh_params")
        self.tableWidget_mesh_params.setMinimumSize(QSize(0, 300))
        self.tableWidget_mesh_params.setAlternatingRowColors(True)
        self.tableWidget_mesh_params.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.tableWidget_mesh_params.setGridStyle(Qt.PenStyle.SolidLine)
        self.tableWidget_mesh_params.setSortingEnabled(True)

        self.verticalLayout_params.addWidget(self.tableWidget_mesh_params)


        self.verticalLayout_left.addWidget(self.groupBox_mesh_params)


        self.horizontalLayout_mesh_mgmt.addWidget(self.widget_left_panel)

        self.widget_right_panel = QWidget(self.tab_mesh_management)
        self.widget_right_panel.setObjectName(u"widget_right_panel")
        self.verticalLayout_right = QVBoxLayout(self.widget_right_panel)
        self.verticalLayout_right.setObjectName(u"verticalLayout_right")
        self.groupBox_mesh_preview = QGroupBox(self.widget_right_panel)
        self.groupBox_mesh_preview.setObjectName(u"groupBox_mesh_preview")
        self.verticalLayout_preview = QVBoxLayout(self.groupBox_mesh_preview)
        self.verticalLayout_preview.setObjectName(u"verticalLayout_preview")
        self.horizontalLayout_preview_ctrl = QHBoxLayout()
        self.horizontalLayout_preview_ctrl.setObjectName(u"horizontalLayout_preview_ctrl")
        self.label_select_mesh = QLabel(self.groupBox_mesh_preview)
        self.label_select_mesh.setObjectName(u"label_select_mesh")

        self.horizontalLayout_preview_ctrl.addWidget(self.label_select_mesh)

        self.comboBox_mesh_select = QComboBox(self.groupBox_mesh_preview)
        self.comboBox_mesh_select.setObjectName(u"comboBox_mesh_select")
        self.comboBox_mesh_select.setMinimumSize(QSize(200, 30))

        self.horizontalLayout_preview_ctrl.addWidget(self.comboBox_mesh_select)

        self.horizontalSpacer_preview = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_preview_ctrl.addItem(self.horizontalSpacer_preview)


        self.verticalLayout_preview.addLayout(self.horizontalLayout_preview_ctrl)

        self.label_mesh_preview = QLabel(self.groupBox_mesh_preview)
        self.label_mesh_preview.setObjectName(u"label_mesh_preview")
        self.label_mesh_preview.setMinimumSize(QSize(400, 300))
        font2 = QFont()
        font2.setFamilies([u"Microsoft YaHei UI"])
        font2.setPointSize(16)
        self.label_mesh_preview.setFont(font2)
        self.label_mesh_preview.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_preview.addWidget(self.label_mesh_preview)

        self.groupBox_mesh_stats = QGroupBox(self.groupBox_mesh_preview)
        self.groupBox_mesh_stats.setObjectName(u"groupBox_mesh_stats")
        self.groupBox_mesh_stats.setMinimumSize(QSize(0, 120))
        self.gridLayout_stats = QGridLayout(self.groupBox_mesh_stats)
        self.gridLayout_stats.setObjectName(u"gridLayout_stats")
        self.label_nodes = QLabel(self.groupBox_mesh_stats)
        self.label_nodes.setObjectName(u"label_nodes")
        font3 = QFont()
        font3.setFamilies([u"Microsoft YaHei UI"])
        font3.setPointSize(12)
        font3.setBold(True)
        self.label_nodes.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_nodes, 0, 0, 1, 1)

        self.label_nodes_value = QLabel(self.groupBox_mesh_stats)
        self.label_nodes_value.setObjectName(u"label_nodes_value")
        font4 = QFont()
        font4.setFamilies([u"Microsoft YaHei UI"])
        font4.setPointSize(12)
        self.label_nodes_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_nodes_value, 0, 1, 1, 1)

        self.label_elements = QLabel(self.groupBox_mesh_stats)
        self.label_elements.setObjectName(u"label_elements")
        self.label_elements.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_elements, 1, 0, 1, 1)

        self.label_elements_value = QLabel(self.groupBox_mesh_stats)
        self.label_elements_value.setObjectName(u"label_elements_value")
        self.label_elements_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_elements_value, 1, 1, 1, 1)

        self.label_quality = QLabel(self.groupBox_mesh_stats)
        self.label_quality.setObjectName(u"label_quality")
        self.label_quality.setFont(font3)

        self.gridLayout_stats.addWidget(self.label_quality, 2, 0, 1, 1)

        self.label_quality_value = QLabel(self.groupBox_mesh_stats)
        self.label_quality_value.setObjectName(u"label_quality_value")
        self.label_quality_value.setFont(font4)

        self.gridLayout_stats.addWidget(self.label_quality_value, 2, 1, 1, 1)


        self.verticalLayout_preview.addWidget(self.groupBox_mesh_stats)


        self.verticalLayout_right.addWidget(self.groupBox_mesh_preview)


        self.horizontalLayout_mesh_mgmt.addWidget(self.widget_right_panel)

        self.tabWidget_main.addTab(self.tab_mesh_management, "")
        self.tab_mesh_generation = QWidget()
        self.tab_mesh_generation.setObjectName(u"tab_mesh_generation")
        self.horizontalLayout_generation = QHBoxLayout(self.tab_mesh_generation)
        self.horizontalLayout_generation.setObjectName(u"horizontalLayout_generation")
        self.groupBox_batch_control = QGroupBox(self.tab_mesh_generation)
        self.groupBox_batch_control.setObjectName(u"groupBox_batch_control")
        self.groupBox_batch_control.setMinimumSize(QSize(400, 0))
        self.groupBox_batch_control.setMaximumSize(QSize(450, 16777215))
        self.verticalLayout_batch = QVBoxLayout(self.groupBox_batch_control)
        self.verticalLayout_batch.setObjectName(u"verticalLayout_batch")
        self.groupBox_intelligent_selection = QGroupBox(self.groupBox_batch_control)
        self.groupBox_intelligent_selection.setObjectName(u"groupBox_intelligent_selection")
        self.verticalLayout_intelligent_selection = QVBoxLayout(self.groupBox_intelligent_selection)
        self.verticalLayout_intelligent_selection.setObjectName(u"verticalLayout_intelligent_selection")
        self.horizontalLayout_selection_buttons = QHBoxLayout()
        self.horizontalLayout_selection_buttons.setObjectName(u"horizontalLayout_selection_buttons")
        self.btn_select_all_meshes = QPushButton(self.groupBox_intelligent_selection)
        self.btn_select_all_meshes.setObjectName(u"btn_select_all_meshes")
        self.btn_select_all_meshes.setMinimumSize(QSize(60, 30))

        self.horizontalLayout_selection_buttons.addWidget(self.btn_select_all_meshes)

        self.btn_select_none_meshes = QPushButton(self.groupBox_intelligent_selection)
        self.btn_select_none_meshes.setObjectName(u"btn_select_none_meshes")
        self.btn_select_none_meshes.setMinimumSize(QSize(60, 30))

        self.horizontalLayout_selection_buttons.addWidget(self.btn_select_none_meshes)

        self.btn_select_inverse_meshes = QPushButton(self.groupBox_intelligent_selection)
        self.btn_select_inverse_meshes.setObjectName(u"btn_select_inverse_meshes")
        self.btn_select_inverse_meshes.setMinimumSize(QSize(60, 30))

        self.horizontalLayout_selection_buttons.addWidget(self.btn_select_inverse_meshes)


        self.verticalLayout_intelligent_selection.addLayout(self.horizontalLayout_selection_buttons)

        self.formLayout_filter = QFormLayout()
        self.formLayout_filter.setObjectName(u"formLayout_filter")
        self.label_size_range = QLabel(self.groupBox_intelligent_selection)
        self.label_size_range.setObjectName(u"label_size_range")

        self.formLayout_filter.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_size_range)

        self.horizontalLayout_size_range = QHBoxLayout()
        self.horizontalLayout_size_range.setObjectName(u"horizontalLayout_size_range")
        self.size_range_min = QDoubleSpinBox(self.groupBox_intelligent_selection)
        self.size_range_min.setObjectName(u"size_range_min")
        self.size_range_min.setMinimum(0.100000000000000)
        self.size_range_min.setMaximum(100.000000000000000)
        self.size_range_min.setValue(1.000000000000000)

        self.horizontalLayout_size_range.addWidget(self.size_range_min)

        self.label_to = QLabel(self.groupBox_intelligent_selection)
        self.label_to.setObjectName(u"label_to")

        self.horizontalLayout_size_range.addWidget(self.label_to)

        self.size_range_max = QDoubleSpinBox(self.groupBox_intelligent_selection)
        self.size_range_max.setObjectName(u"size_range_max")
        self.size_range_max.setMinimum(0.100000000000000)
        self.size_range_max.setMaximum(100.000000000000000)
        self.size_range_max.setValue(50.000000000000000)

        self.horizontalLayout_size_range.addWidget(self.size_range_max)


        self.formLayout_filter.setLayout(0, QFormLayout.ItemRole.FieldRole, self.horizontalLayout_size_range)

        self.label_filter_options = QLabel(self.groupBox_intelligent_selection)
        self.label_filter_options.setObjectName(u"label_filter_options")

        self.formLayout_filter.setWidget(1, QFormLayout.ItemRole.LabelRole, self.label_filter_options)

        self.filter_completed_only = QCheckBox(self.groupBox_intelligent_selection)
        self.filter_completed_only.setObjectName(u"filter_completed_only")

        self.formLayout_filter.setWidget(1, QFormLayout.ItemRole.FieldRole, self.filter_completed_only)

        self.filter_converged_only = QCheckBox(self.groupBox_intelligent_selection)
        self.filter_converged_only.setObjectName(u"filter_converged_only")

        self.formLayout_filter.setWidget(2, QFormLayout.ItemRole.FieldRole, self.filter_converged_only)

        self.btn_apply_filter = QPushButton(self.groupBox_intelligent_selection)
        self.btn_apply_filter.setObjectName(u"btn_apply_filter")
        self.btn_apply_filter.setMinimumSize(QSize(100, 30))

        self.formLayout_filter.setWidget(3, QFormLayout.ItemRole.FieldRole, self.btn_apply_filter)


        self.verticalLayout_intelligent_selection.addLayout(self.formLayout_filter)

        self.selection_preview = QLabel(self.groupBox_intelligent_selection)
        self.selection_preview.setObjectName(u"selection_preview")

        self.verticalLayout_intelligent_selection.addWidget(self.selection_preview)


        self.verticalLayout_batch.addWidget(self.groupBox_intelligent_selection)

        self.listWidget_selected_meshes = QListWidget(self.groupBox_batch_control)
        self.listWidget_selected_meshes.setObjectName(u"listWidget_selected_meshes")
        self.listWidget_selected_meshes.setMinimumSize(QSize(0, 150))

        self.verticalLayout_batch.addWidget(self.listWidget_selected_meshes)

        self.horizontalLayout_batch_buttons = QHBoxLayout()
        self.horizontalLayout_batch_buttons.setObjectName(u"horizontalLayout_batch_buttons")
        self.btn_batch_generate = QPushButton(self.groupBox_batch_control)
        self.btn_batch_generate.setObjectName(u"btn_batch_generate")
        self.btn_batch_generate.setMinimumSize(QSize(120, 40))

        self.horizontalLayout_batch_buttons.addWidget(self.btn_batch_generate)

        self.btn_stop_generation = QPushButton(self.groupBox_batch_control)
        self.btn_stop_generation.setObjectName(u"btn_stop_generation")
        self.btn_stop_generation.setMinimumSize(QSize(120, 40))

        self.horizontalLayout_batch_buttons.addWidget(self.btn_stop_generation)


        self.verticalLayout_batch.addLayout(self.horizontalLayout_batch_buttons)

        self.groupBox_progress = QGroupBox(self.groupBox_batch_control)
        self.groupBox_progress.setObjectName(u"groupBox_progress")
        self.verticalLayout_progress = QVBoxLayout(self.groupBox_progress)
        self.verticalLayout_progress.setObjectName(u"verticalLayout_progress")
        self.progressBar_generation = QProgressBar(self.groupBox_progress)
        self.progressBar_generation.setObjectName(u"progressBar_generation")
        self.progressBar_generation.setValue(0)

        self.verticalLayout_progress.addWidget(self.progressBar_generation)

        self.label_progress_text = QLabel(self.groupBox_progress)
        self.label_progress_text.setObjectName(u"label_progress_text")
        self.label_progress_text.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_progress.addWidget(self.label_progress_text)


        self.verticalLayout_batch.addWidget(self.groupBox_progress)


        self.horizontalLayout_generation.addWidget(self.groupBox_batch_control)

        self.groupBox_generation_log = QGroupBox(self.tab_mesh_generation)
        self.groupBox_generation_log.setObjectName(u"groupBox_generation_log")
        self.verticalLayout_log = QVBoxLayout(self.groupBox_generation_log)
        self.verticalLayout_log.setObjectName(u"verticalLayout_log")
        self.textEdit_generation_log = QTextEdit(self.groupBox_generation_log)
        self.textEdit_generation_log.setObjectName(u"textEdit_generation_log")
        self.textEdit_generation_log.setMinimumSize(QSize(0, 300))
        font5 = QFont()
        font5.setFamilies([u"Consolas"])
        font5.setPointSize(9)
        self.textEdit_generation_log.setFont(font5)
        self.textEdit_generation_log.setReadOnly(True)

        self.verticalLayout_log.addWidget(self.textEdit_generation_log)

        self.tableWidget_mesh_comparison = QTableWidget(self.groupBox_generation_log)
        if (self.tableWidget_mesh_comparison.columnCount() < 10):
            self.tableWidget_mesh_comparison.setColumnCount(10)
        __qtablewidgetitem6 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(0, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(1, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(2, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(3, __qtablewidgetitem9)
        __qtablewidgetitem10 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(4, __qtablewidgetitem10)
        __qtablewidgetitem11 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(5, __qtablewidgetitem11)
        __qtablewidgetitem12 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(6, __qtablewidgetitem12)
        __qtablewidgetitem13 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(7, __qtablewidgetitem13)
        __qtablewidgetitem14 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(8, __qtablewidgetitem14)
        __qtablewidgetitem15 = QTableWidgetItem()
        self.tableWidget_mesh_comparison.setHorizontalHeaderItem(9, __qtablewidgetitem15)
        self.tableWidget_mesh_comparison.setObjectName(u"tableWidget_mesh_comparison")
        self.tableWidget_mesh_comparison.setMinimumSize(QSize(0, 200))
        self.tableWidget_mesh_comparison.setAlternatingRowColors(True)

        self.verticalLayout_log.addWidget(self.tableWidget_mesh_comparison)


        self.horizontalLayout_generation.addWidget(self.groupBox_generation_log)

        self.tabWidget_main.addTab(self.tab_mesh_generation, "")
        self.tab_modal_analysis = QWidget()
        self.tab_modal_analysis.setObjectName(u"tab_modal_analysis")
        self.verticalLayout_modal = QVBoxLayout(self.tab_modal_analysis)
        self.verticalLayout_modal.setObjectName(u"verticalLayout_modal")
        self.groupBox_modal_control = QGroupBox(self.tab_modal_analysis)
        self.groupBox_modal_control.setObjectName(u"groupBox_modal_control")
        self.groupBox_modal_control.setMinimumSize(QSize(0, 150))
        self.horizontalLayout_modal_control = QHBoxLayout(self.groupBox_modal_control)
        self.horizontalLayout_modal_control.setObjectName(u"horizontalLayout_modal_control")
        self.verticalLayout_modal_params = QVBoxLayout()
        self.verticalLayout_modal_params.setObjectName(u"verticalLayout_modal_params")
        self.horizontalLayout_modal_count = QHBoxLayout()
        self.horizontalLayout_modal_count.setObjectName(u"horizontalLayout_modal_count")
        self.label_modal_count = QLabel(self.groupBox_modal_control)
        self.label_modal_count.setObjectName(u"label_modal_count")

        self.horizontalLayout_modal_count.addWidget(self.label_modal_count)

        self.spinBox_modal_count = QSpinBox(self.groupBox_modal_control)
        self.spinBox_modal_count.setObjectName(u"spinBox_modal_count")
        self.spinBox_modal_count.setMinimum(0)
        self.spinBox_modal_count.setMaximum(200)
        self.spinBox_modal_count.setValue(5)

        self.horizontalLayout_modal_count.addWidget(self.spinBox_modal_count)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_modal_count)

        self.horizontalLayout_freq_option = QHBoxLayout()
        self.horizontalLayout_freq_option.setObjectName(u"horizontalLayout_freq_option")
        self.checkBox_limit_freq = QCheckBox(self.groupBox_modal_control)
        self.checkBox_limit_freq.setObjectName(u"checkBox_limit_freq")
        self.checkBox_limit_freq.setChecked(True)

        self.horizontalLayout_freq_option.addWidget(self.checkBox_limit_freq)

        self.horizontalSpacer_freq_option = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_freq_option.addItem(self.horizontalSpacer_freq_option)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_freq_option)

        self.horizontalLayout_freq_range = QHBoxLayout()
        self.horizontalLayout_freq_range.setObjectName(u"horizontalLayout_freq_range")
        self.label_freq_range = QLabel(self.groupBox_modal_control)
        self.label_freq_range.setObjectName(u"label_freq_range")

        self.horizontalLayout_freq_range.addWidget(self.label_freq_range)

        self.doubleSpinBox_freq_min = QDoubleSpinBox(self.groupBox_modal_control)
        self.doubleSpinBox_freq_min.setObjectName(u"doubleSpinBox_freq_min")
        self.doubleSpinBox_freq_min.setMaximum(100000.000000000000000)
        self.doubleSpinBox_freq_min.setValue(0.000000000000000)

        self.horizontalLayout_freq_range.addWidget(self.doubleSpinBox_freq_min)

        self.label_to1 = QLabel(self.groupBox_modal_control)
        self.label_to1.setObjectName(u"label_to1")

        self.horizontalLayout_freq_range.addWidget(self.label_to1)

        self.doubleSpinBox_freq_max = QDoubleSpinBox(self.groupBox_modal_control)
        self.doubleSpinBox_freq_max.setObjectName(u"doubleSpinBox_freq_max")
        self.doubleSpinBox_freq_max.setMaximum(100000.000000000000000)
        self.doubleSpinBox_freq_max.setValue(1000.000000000000000)

        self.horizontalLayout_freq_range.addWidget(self.doubleSpinBox_freq_max)


        self.verticalLayout_modal_params.addLayout(self.horizontalLayout_freq_range)


        self.horizontalLayout_modal_control.addLayout(self.verticalLayout_modal_params)

        self.groupBox_modal_mesh_selection = QGroupBox(self.groupBox_modal_control)
        self.groupBox_modal_mesh_selection.setObjectName(u"groupBox_modal_mesh_selection")
        self.verticalLayout_modal_mesh_selection = QVBoxLayout(self.groupBox_modal_mesh_selection)
        self.verticalLayout_modal_mesh_selection.setObjectName(u"verticalLayout_modal_mesh_selection")
        self.label_modal_mesh_info = QLabel(self.groupBox_modal_mesh_selection)
        self.label_modal_mesh_info.setObjectName(u"label_modal_mesh_info")

        self.verticalLayout_modal_mesh_selection.addWidget(self.label_modal_mesh_info)

        self.horizontalLayout_modal_mesh_selection = QHBoxLayout()
        self.horizontalLayout_modal_mesh_selection.setObjectName(u"horizontalLayout_modal_mesh_selection")
        self.btn_use_selected_meshes = QPushButton(self.groupBox_modal_mesh_selection)
        self.btn_use_selected_meshes.setObjectName(u"btn_use_selected_meshes")
        self.btn_use_selected_meshes.setMinimumSize(QSize(120, 30))

        self.horizontalLayout_modal_mesh_selection.addWidget(self.btn_use_selected_meshes)

        self.btn_use_recommended_mesh = QPushButton(self.groupBox_modal_mesh_selection)
        self.btn_use_recommended_mesh.setObjectName(u"btn_use_recommended_mesh")
        self.btn_use_recommended_mesh.setMinimumSize(QSize(120, 30))
        self.btn_use_recommended_mesh.setEnabled(False)

        self.horizontalLayout_modal_mesh_selection.addWidget(self.btn_use_recommended_mesh)

        self.btn_select_all_for_modal = QPushButton(self.groupBox_modal_mesh_selection)
        self.btn_select_all_for_modal.setObjectName(u"btn_select_all_for_modal")
        self.btn_select_all_for_modal.setMinimumSize(QSize(120, 30))

        self.horizontalLayout_modal_mesh_selection.addWidget(self.btn_select_all_for_modal)


        self.verticalLayout_modal_mesh_selection.addLayout(self.horizontalLayout_modal_mesh_selection)

        self.label_selected_meshes_for_modal = QLabel(self.groupBox_modal_mesh_selection)
        self.label_selected_meshes_for_modal.setObjectName(u"label_selected_meshes_for_modal")

        self.verticalLayout_modal_mesh_selection.addWidget(self.label_selected_meshes_for_modal)


        self.horizontalLayout_modal_control.addWidget(self.groupBox_modal_mesh_selection)

        self.horizontalLayout_modal_buttons = QHBoxLayout()
        self.horizontalLayout_modal_buttons.setObjectName(u"horizontalLayout_modal_buttons")
        self.btn_single_modal = QPushButton(self.groupBox_modal_control)
        self.btn_single_modal.setObjectName(u"btn_single_modal")
        self.btn_single_modal.setMinimumSize(QSize(150, 40))

        self.horizontalLayout_modal_buttons.addWidget(self.btn_single_modal)

        self.btn_start_modal_calculation = QPushButton(self.groupBox_modal_control)
        self.btn_start_modal_calculation.setObjectName(u"btn_start_modal_calculation")
        self.btn_start_modal_calculation.setMinimumSize(QSize(150, 40))
        self.btn_start_modal_calculation.setEnabled(False)

        self.horizontalLayout_modal_buttons.addWidget(self.btn_start_modal_calculation)


        self.horizontalLayout_modal_control.addLayout(self.horizontalLayout_modal_buttons)

        self.horizontalSpacer_modal = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_modal_control.addItem(self.horizontalSpacer_modal)


        self.verticalLayout_modal.addWidget(self.groupBox_modal_control)

        self.groupBox_convergence_chart = QGroupBox(self.tab_modal_analysis)
        self.groupBox_convergence_chart.setObjectName(u"groupBox_convergence_chart")
        self.verticalLayout_chart = QVBoxLayout(self.groupBox_convergence_chart)
        self.verticalLayout_chart.setObjectName(u"verticalLayout_chart")
        self.label_convergence_chart = QLabel(self.groupBox_convergence_chart)
        self.label_convergence_chart.setObjectName(u"label_convergence_chart")
        self.label_convergence_chart.setMinimumSize(QSize(0, 400))
        self.label_convergence_chart.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_chart.addWidget(self.label_convergence_chart)


        self.verticalLayout_modal.addWidget(self.groupBox_convergence_chart)

        self.horizontalLayout_modal_advanced = QHBoxLayout()
        self.horizontalLayout_modal_advanced.setObjectName(u"horizontalLayout_modal_advanced")
        self.groupBox_batch_calculation = QGroupBox(self.tab_modal_analysis)
        self.groupBox_batch_calculation.setObjectName(u"groupBox_batch_calculation")
        self.groupBox_batch_calculation.setMinimumSize(QSize(400, 0))
        self.verticalLayout_batch_calc = QVBoxLayout(self.groupBox_batch_calculation)
        self.verticalLayout_batch_calc.setObjectName(u"verticalLayout_batch_calc")
        self.btn_batch_calculate = QPushButton(self.groupBox_batch_calculation)
        self.btn_batch_calculate.setObjectName(u"btn_batch_calculate")
        self.btn_batch_calculate.setMinimumSize(QSize(0, 40))

        self.verticalLayout_batch_calc.addWidget(self.btn_batch_calculate)

        self.batch_progress = QProgressBar(self.groupBox_batch_calculation)
        self.batch_progress.setObjectName(u"batch_progress")
        self.batch_progress.setVisible(False)
        self.batch_progress.setMinimumSize(QSize(0, 25))

        self.verticalLayout_batch_calc.addWidget(self.batch_progress)

        self.batch_status_label = QLabel(self.groupBox_batch_calculation)
        self.batch_status_label.setObjectName(u"batch_status_label")
        self.batch_status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_batch_calc.addWidget(self.batch_status_label)

        self.current_calculation_label = QLabel(self.groupBox_batch_calculation)
        self.current_calculation_label.setObjectName(u"current_calculation_label")
        self.current_calculation_label.setVisible(False)
        self.current_calculation_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_batch_calc.addWidget(self.current_calculation_label)

        self.horizontalLayout_calc_control = QHBoxLayout()
        self.horizontalLayout_calc_control.setObjectName(u"horizontalLayout_calc_control")
        self.btn_pause_calculation = QPushButton(self.groupBox_batch_calculation)
        self.btn_pause_calculation.setObjectName(u"btn_pause_calculation")
        self.btn_pause_calculation.setEnabled(False)
        self.btn_pause_calculation.setMinimumSize(QSize(80, 30))

        self.horizontalLayout_calc_control.addWidget(self.btn_pause_calculation)

        self.btn_stop_calculation = QPushButton(self.groupBox_batch_calculation)
        self.btn_stop_calculation.setObjectName(u"btn_stop_calculation")
        self.btn_stop_calculation.setEnabled(False)
        self.btn_stop_calculation.setMinimumSize(QSize(80, 30))

        self.horizontalLayout_calc_control.addWidget(self.btn_stop_calculation)


        self.verticalLayout_batch_calc.addLayout(self.horizontalLayout_calc_control)

        self.formLayout_calc_stats = QFormLayout()
        self.formLayout_calc_stats.setObjectName(u"formLayout_calc_stats")
        self.label_stats_total = QLabel(self.groupBox_batch_calculation)
        self.label_stats_total.setObjectName(u"label_stats_total")

        self.formLayout_calc_stats.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_stats_total)

        self.calc_stats_total = QLabel(self.groupBox_batch_calculation)
        self.calc_stats_total.setObjectName(u"calc_stats_total")

        self.formLayout_calc_stats.setWidget(0, QFormLayout.ItemRole.FieldRole, self.calc_stats_total)

        self.label_stats_completed = QLabel(self.groupBox_batch_calculation)
        self.label_stats_completed.setObjectName(u"label_stats_completed")

        self.formLayout_calc_stats.setWidget(1, QFormLayout.ItemRole.LabelRole, self.label_stats_completed)

        self.calc_stats_completed = QLabel(self.groupBox_batch_calculation)
        self.calc_stats_completed.setObjectName(u"calc_stats_completed")

        self.formLayout_calc_stats.setWidget(1, QFormLayout.ItemRole.FieldRole, self.calc_stats_completed)

        self.label_stats_failed = QLabel(self.groupBox_batch_calculation)
        self.label_stats_failed.setObjectName(u"label_stats_failed")

        self.formLayout_calc_stats.setWidget(2, QFormLayout.ItemRole.LabelRole, self.label_stats_failed)

        self.calc_stats_failed = QLabel(self.groupBox_batch_calculation)
        self.calc_stats_failed.setObjectName(u"calc_stats_failed")

        self.formLayout_calc_stats.setWidget(2, QFormLayout.ItemRole.FieldRole, self.calc_stats_failed)

        self.label_stats_time = QLabel(self.groupBox_batch_calculation)
        self.label_stats_time.setObjectName(u"label_stats_time")

        self.formLayout_calc_stats.setWidget(3, QFormLayout.ItemRole.LabelRole, self.label_stats_time)

        self.calc_stats_time = QLabel(self.groupBox_batch_calculation)
        self.calc_stats_time.setObjectName(u"calc_stats_time")

        self.formLayout_calc_stats.setWidget(3, QFormLayout.ItemRole.FieldRole, self.calc_stats_time)


        self.verticalLayout_batch_calc.addLayout(self.formLayout_calc_stats)


        self.horizontalLayout_modal_advanced.addWidget(self.groupBox_batch_calculation)

        self.groupBox_optimal_recommendation = QGroupBox(self.tab_modal_analysis)
        self.groupBox_optimal_recommendation.setObjectName(u"groupBox_optimal_recommendation")
        self.groupBox_optimal_recommendation.setMinimumSize(QSize(500, 0))
        self.verticalLayout_recommendation = QVBoxLayout(self.groupBox_optimal_recommendation)
        self.verticalLayout_recommendation.setObjectName(u"verticalLayout_recommendation")
        self.label_recommendation_title = QLabel(self.groupBox_optimal_recommendation)
        self.label_recommendation_title.setObjectName(u"label_recommendation_title")

        self.verticalLayout_recommendation.addWidget(self.label_recommendation_title)

        self.convergence_analysis = QTextEdit(self.groupBox_optimal_recommendation)
        self.convergence_analysis.setObjectName(u"convergence_analysis")
        self.convergence_analysis.setMaximumSize(QSize(16777215, 120))
        self.convergence_analysis.setReadOnly(True)

        self.verticalLayout_recommendation.addWidget(self.convergence_analysis)

        self.btn_analyze_convergence = QPushButton(self.groupBox_optimal_recommendation)
        self.btn_analyze_convergence.setObjectName(u"btn_analyze_convergence")
        self.btn_analyze_convergence.setMinimumSize(QSize(0, 35))

        self.verticalLayout_recommendation.addWidget(self.btn_analyze_convergence)

        self.recommendation_info = QLabel(self.groupBox_optimal_recommendation)
        self.recommendation_info.setObjectName(u"recommendation_info")
        self.recommendation_info.setMinimumSize(QSize(0, 80))
        self.recommendation_info.setWordWrap(True)

        self.verticalLayout_recommendation.addWidget(self.recommendation_info)

        self.horizontalLayout_recommendation_buttons = QHBoxLayout()
        self.horizontalLayout_recommendation_buttons.setObjectName(u"horizontalLayout_recommendation_buttons")
        self.btn_recommend_mesh = QPushButton(self.groupBox_optimal_recommendation)
        self.btn_recommend_mesh.setObjectName(u"btn_recommend_mesh")
        self.btn_recommend_mesh.setEnabled(False)
        self.btn_recommend_mesh.setMinimumSize(QSize(120, 35))

        self.horizontalLayout_recommendation_buttons.addWidget(self.btn_recommend_mesh)

        self.btn_confirm_recommendation = QPushButton(self.groupBox_optimal_recommendation)
        self.btn_confirm_recommendation.setObjectName(u"btn_confirm_recommendation")
        self.btn_confirm_recommendation.setEnabled(False)
        self.btn_confirm_recommendation.setMinimumSize(QSize(140, 35))

        self.horizontalLayout_recommendation_buttons.addWidget(self.btn_confirm_recommendation)


        self.verticalLayout_recommendation.addLayout(self.horizontalLayout_recommendation_buttons)

        self.formLayout_efficiency_weight = QFormLayout()
        self.formLayout_efficiency_weight.setObjectName(u"formLayout_efficiency_weight")
        self.label_efficiency_weight = QLabel(self.groupBox_optimal_recommendation)
        self.label_efficiency_weight.setObjectName(u"label_efficiency_weight")

        self.formLayout_efficiency_weight.setWidget(0, QFormLayout.ItemRole.LabelRole, self.label_efficiency_weight)

        self.efficiency_weight_spinbox = QDoubleSpinBox(self.groupBox_optimal_recommendation)
        self.efficiency_weight_spinbox.setObjectName(u"efficiency_weight_spinbox")
        self.efficiency_weight_spinbox.setMaximum(1.000000000000000)
        self.efficiency_weight_spinbox.setSingleStep(0.100000000000000)
        self.efficiency_weight_spinbox.setValue(0.300000000000000)

        self.formLayout_efficiency_weight.setWidget(0, QFormLayout.ItemRole.FieldRole, self.efficiency_weight_spinbox)


        self.verticalLayout_recommendation.addLayout(self.formLayout_efficiency_weight)


        self.horizontalLayout_modal_advanced.addWidget(self.groupBox_optimal_recommendation)


        self.verticalLayout_modal.addLayout(self.horizontalLayout_modal_advanced)

        self.tabWidget_main.addTab(self.tab_modal_analysis, "")
        self.tab_result_comparison = QWidget()
        self.tab_result_comparison.setObjectName(u"tab_result_comparison")
        self.horizontalLayout_comparison = QHBoxLayout(self.tab_result_comparison)
        self.horizontalLayout_comparison.setObjectName(u"horizontalLayout_comparison")
        self.groupBox_comparison_control = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_control.setObjectName(u"groupBox_comparison_control")
        self.groupBox_comparison_control.setMinimumSize(QSize(300, 0))
        self.groupBox_comparison_control.setMaximumSize(QSize(350, 16777215))
        self.verticalLayout_comparison_ctrl = QVBoxLayout(self.groupBox_comparison_control)
        self.verticalLayout_comparison_ctrl.setObjectName(u"verticalLayout_comparison_ctrl")
        self.label_select_meshes = QLabel(self.groupBox_comparison_control)
        self.label_select_meshes.setObjectName(u"label_select_meshes")

        self.verticalLayout_comparison_ctrl.addWidget(self.label_select_meshes)

        self.listWidget_comparison_meshes = QListWidget(self.groupBox_comparison_control)
        self.listWidget_comparison_meshes.setObjectName(u"listWidget_comparison_meshes")
        self.listWidget_comparison_meshes.setMinimumSize(QSize(0, 200))
        self.listWidget_comparison_meshes.setSelectionMode(QAbstractItemView.SelectionMode.MultiSelection)

        self.verticalLayout_comparison_ctrl.addWidget(self.listWidget_comparison_meshes)

        self.groupBox_display_options = QGroupBox(self.groupBox_comparison_control)
        self.groupBox_display_options.setObjectName(u"groupBox_display_options")
        self.verticalLayout_display = QVBoxLayout(self.groupBox_display_options)
        self.verticalLayout_display.setObjectName(u"verticalLayout_display")
        self.checkBox_show_frequency = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_frequency.setObjectName(u"checkBox_show_frequency")
        self.checkBox_show_frequency.setChecked(True)

        self.verticalLayout_display.addWidget(self.checkBox_show_frequency)

        self.checkBox_show_convergence = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_convergence.setObjectName(u"checkBox_show_convergence")
        self.checkBox_show_convergence.setChecked(True)

        self.verticalLayout_display.addWidget(self.checkBox_show_convergence)

        self.checkBox_show_grid = QCheckBox(self.groupBox_display_options)
        self.checkBox_show_grid.setObjectName(u"checkBox_show_grid")

        self.verticalLayout_display.addWidget(self.checkBox_show_grid)


        self.verticalLayout_comparison_ctrl.addWidget(self.groupBox_display_options)

        self.btn_export_results = QPushButton(self.groupBox_comparison_control)
        self.btn_export_results.setObjectName(u"btn_export_results")
        self.btn_export_results.setMinimumSize(QSize(0, 40))

        self.verticalLayout_comparison_ctrl.addWidget(self.btn_export_results)


        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_control)

        self.groupBox_comparison_chart = QGroupBox(self.tab_result_comparison)
        self.groupBox_comparison_chart.setObjectName(u"groupBox_comparison_chart")
        self.verticalLayout_comparison_chart = QVBoxLayout(self.groupBox_comparison_chart)
        self.verticalLayout_comparison_chart.setObjectName(u"verticalLayout_comparison_chart")
        self.label_comparison_chart = QLabel(self.groupBox_comparison_chart)
        self.label_comparison_chart.setObjectName(u"label_comparison_chart")
        self.label_comparison_chart.setMinimumSize(QSize(0, 600))
        self.label_comparison_chart.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_comparison_chart.addWidget(self.label_comparison_chart)


        self.horizontalLayout_comparison.addWidget(self.groupBox_comparison_chart)

        self.tabWidget_main.addTab(self.tab_result_comparison, "")

        self.verticalLayout_main.addWidget(self.tabWidget_main)

        self.horizontalLayout_navigation = QHBoxLayout()
        self.horizontalLayout_navigation.setSpacing(15)
        self.horizontalLayout_navigation.setObjectName(u"horizontalLayout_navigation")
        self.btn_generate_mesh = QPushButton(self.centralwidget)
        self.btn_generate_mesh.setObjectName(u"btn_generate_mesh")
        self.btn_generate_mesh.setMinimumSize(QSize(150, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_generate_mesh)

        self.btn_view_results = QPushButton(self.centralwidget)
        self.btn_view_results.setObjectName(u"btn_view_results")
        self.btn_view_results.setMinimumSize(QSize(150, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_view_results)

        self.horizontalSpacer_nav = QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        self.horizontalLayout_navigation.addItem(self.horizontalSpacer_nav)

        self.btn_previous = QPushButton(self.centralwidget)
        self.btn_previous.setObjectName(u"btn_previous")
        self.btn_previous.setMinimumSize(QSize(180, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_previous)

        self.btn_next = QPushButton(self.centralwidget)
        self.btn_next.setObjectName(u"btn_next")
        self.btn_next.setMinimumSize(QSize(180, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_next)

        self.btn_main_menu = QPushButton(self.centralwidget)
        self.btn_main_menu.setObjectName(u"btn_main_menu")
        self.btn_main_menu.setMinimumSize(QSize(120, 50))

        self.horizontalLayout_navigation.addWidget(self.btn_main_menu)


        self.verticalLayout_main.addLayout(self.horizontalLayout_navigation)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        self.tabWidget_main.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u591a\u7f51\u683c\u7ba1\u7406\u7cfb\u7edf", None))
        self.label_title.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u65e0\u5173\u6027\u9a8c\u8bc1\u7cfb\u7edf", None))
        self.label_title.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    color: #34495e;\n"
"    background-color: transparent;\n"
"    border-bottom: 2px solid #3498db;\n"
"    padding-bottom: 10px;\n"
"}", None))
        self.tabWidget_main.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTabWidget::pane {\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 0 6px 6px 6px;\n"
"    background: white;\n"
"    top: -1px;\n"
"}\n"
"\n"
"QTabBar::tab {\n"
"    background: #f5f7fa;\n"
"    border: 1px solid #dcdfe6;\n"
"    border-bottom: none;\n"
"    border-top-left-radius: 4px;\n"
"    border-top-right-radius: 4px;\n"
"    min-width: 120px;\n"
"    padding: 12px 20px;\n"
"    font-weight: 500;\n"
"    font-size: 11px;\n"
"}\n"
"\n"
"QTabBar::tab:selected {\n"
"    background: white;\n"
"    border-bottom-color: white;\n"
"    color: #3498db;\n"
"}\n"
"\n"
"QTabBar::tab:hover:!selected {\n"
"    background: #ecf0f1;\n"
"}", None))
        self.groupBox_mesh_params.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u53c2\u6570\u7ba1\u7406", None))
        self.groupBox_mesh_params.setStyleSheet(QCoreApplication.translate("MainWindow", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.btn_add_mesh.setText(QCoreApplication.translate("MainWindow", u"\u6dfb\u52a0\u7f51\u683c", None))
        self.btn_import_mesh.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u5165\u914d\u7f6e", None))
        self.btn_export_mesh.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u51fa\u914d\u7f6e", None))
        ___qtablewidgetitem = self.tableWidget_mesh_params.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u540d\u79f0", None));
        ___qtablewidgetitem1 = self.tableWidget_mesh_params.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"\u5c3a\u5bf8(mm)", None));
        ___qtablewidgetitem2 = self.tableWidget_mesh_params.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"\u72b6\u6001", None));
        ___qtablewidgetitem3 = self.tableWidget_mesh_params.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570", None));
        ___qtablewidgetitem4 = self.tableWidget_mesh_params.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570", None));
        ___qtablewidgetitem5 = self.tableWidget_mesh_params.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"\u64cd\u4f5c", None));
        self.tableWidget_mesh_params.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTableWidget {\n"
"    border: 1px solid #dcdfe6;\n"
"    background-color: white;\n"
"    gridline-color: #e9eaec;\n"
"    selection-background-color: #3498db;\n"
"    selection-color: white;\n"
"    alternate-background-color: #f5f7fa;\n"
"}\n"
"\n"
"QHeaderView::section {\n"
"    background-color: #f5f7fa;\n"
"    border: 1px solid #dcdfe6;\n"
"    padding: 8px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}", None))
        self.groupBox_mesh_preview.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9884\u89c8\u4e0e\u8be6\u60c5", None))
        self.groupBox_mesh_preview.setStyleSheet(QCoreApplication.translate("MainWindow", u"QGroupBox {\n"
"    background-color: white;\n"
"    border: 1px solid #e9eaec;\n"
"    border-radius: 6px;\n"
"    margin-top: 12px;\n"
"    padding: 15px;\n"
"    font-weight: bold;\n"
"    color: #34495e;\n"
"}\n"
"\n"
"QGroupBox::title {\n"
"    subcontrol-origin: margin;\n"
"    left: 10px;\n"
"    padding: 0 5px;\n"
"    background-color: white;\n"
"}", None))
        self.label_select_mesh.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u7f51\u683c:", None))
        self.label_mesh_preview.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9884\u89c8\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_mesh_preview.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.groupBox_mesh_stats.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u7edf\u8ba1\u4fe1\u606f", None))
        self.label_nodes.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570:", None))
        self.label_nodes_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_nodes_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #3498db;", None))
        self.label_elements.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570:", None))
        self.label_elements_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_elements_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #3498db;", None))
        self.label_quality.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u8d28\u91cf:", None))
        self.label_quality_value.setText(QCoreApplication.translate("MainWindow", u"--", None))
        self.label_quality_value.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #2ecc71;", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_mesh_management), QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u7ba1\u7406", None))
        self.groupBox_batch_control.setTitle(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u64cd\u4f5c\u63a7\u5236", None))
        self.groupBox_intelligent_selection.setTitle(QCoreApplication.translate("MainWindow", u"\u667a\u80fd\u9009\u62e9\u63a7\u5236", None))
        self.btn_select_all_meshes.setText(QCoreApplication.translate("MainWindow", u"\u5168\u9009", None))
        self.btn_select_none_meshes.setText(QCoreApplication.translate("MainWindow", u"\u5168\u4e0d\u9009", None))
        self.btn_select_inverse_meshes.setText(QCoreApplication.translate("MainWindow", u"\u53cd\u9009", None))
        self.label_size_range.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u5c3a\u5bf8\u8303\u56f4:", None))
        self.size_range_min.setSuffix(QCoreApplication.translate("MainWindow", u" mm", None))
        self.label_to.setText(QCoreApplication.translate("MainWindow", u"\u81f3", None))
        self.size_range_max.setSuffix(QCoreApplication.translate("MainWindow", u" mm", None))
        self.label_filter_options.setText(QCoreApplication.translate("MainWindow", u"\u72b6\u6001\u7b5b\u9009:", None))
        self.filter_completed_only.setText(QCoreApplication.translate("MainWindow", u"\u4ec5\u5df2\u5b8c\u6210\u8ba1\u7b97", None))
        self.filter_converged_only.setText(QCoreApplication.translate("MainWindow", u"\u4ec5\u5df2\u6536\u655b\u7f51\u683c", None))
        self.btn_apply_filter.setText(QCoreApplication.translate("MainWindow", u"\u5e94\u7528\u7b5b\u9009", None))
        self.selection_preview.setText(QCoreApplication.translate("MainWindow", u"\u5df2\u9009\u62e9: 0 \u4e2a\u7f51\u683c", None))
        self.selection_preview.setStyleSheet(QCoreApplication.translate("MainWindow", u"font-weight: bold; color: #0078d4;", None))
        self.btn_batch_generate.setText(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u751f\u6210", None))
        self.btn_batch_generate.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.btn_stop_generation.setText(QCoreApplication.translate("MainWindow", u"\u505c\u6b62\u751f\u6210", None))
        self.btn_stop_generation.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #e74c3c;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #c0392b;\n"
"}", None))
        self.groupBox_progress.setTitle(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u8fdb\u5ea6", None))
        self.label_progress_text.setText(QCoreApplication.translate("MainWindow", u"\u7b49\u5f85\u5f00\u59cb...", None))
        self.groupBox_generation_log.setTitle(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u65e5\u5fd7\u4e0e\u8d28\u91cf\u5bf9\u6bd4", None))
        self.textEdit_generation_log.setStyleSheet(QCoreApplication.translate("MainWindow", u"QTextEdit {\n"
"    border: 1px solid #dcdfe6;\n"
"    background-color: #2c3e50;\n"
"    color: #ecf0f1;\n"
"    selection-background-color: #3498db;\n"
"}", None))
        ___qtablewidgetitem6 = self.tableWidget_mesh_comparison.horizontalHeaderItem(0)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u540d\u79f0", None));
        ___qtablewidgetitem7 = self.tableWidget_mesh_comparison.horizontalHeaderItem(1)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u5c3a\u5bf8(mm)", None));
        ___qtablewidgetitem8 = self.tableWidget_mesh_comparison.horizontalHeaderItem(2)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u7c7b\u578b", None));
        ___qtablewidgetitem9 = self.tableWidget_mesh_comparison.horizontalHeaderItem(3)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("MainWindow", u"\u8282\u70b9\u6570", None));
        ___qtablewidgetitem10 = self.tableWidget_mesh_comparison.horizontalHeaderItem(4)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("MainWindow", u"\u5355\u5143\u6570", None));
        ___qtablewidgetitem11 = self.tableWidget_mesh_comparison.horizontalHeaderItem(5)
        ___qtablewidgetitem11.setText(QCoreApplication.translate("MainWindow", u"\u7b2c1\u9636\u9891\u7387(Hz)", None));
        ___qtablewidgetitem12 = self.tableWidget_mesh_comparison.horizontalHeaderItem(6)
        ___qtablewidgetitem12.setText(QCoreApplication.translate("MainWindow", u"\u6536\u655b\u7387(%)", None));
        ___qtablewidgetitem13 = self.tableWidget_mesh_comparison.horizontalHeaderItem(7)
        ___qtablewidgetitem13.setText(QCoreApplication.translate("MainWindow", u"\u63a8\u8350\u5ea6", None));
        ___qtablewidgetitem14 = self.tableWidget_mesh_comparison.horizontalHeaderItem(8)
        ___qtablewidgetitem14.setText(QCoreApplication.translate("MainWindow", u"\u72b6\u6001", None));
        ___qtablewidgetitem15 = self.tableWidget_mesh_comparison.horizontalHeaderItem(9)
        ___qtablewidgetitem15.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9", None));
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_mesh_generation), QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u751f\u6210", None))
        self.groupBox_modal_control.setTitle(QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u8ba1\u7b97\u63a7\u5236", None))
        self.label_modal_count.setText(QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u9636\u6570:", None))
        self.checkBox_limit_freq.setText(QCoreApplication.translate("MainWindow", u"\u9650\u5236\u9891\u7387\u8303\u56f4", None))
        self.label_freq_range.setText(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u8303\u56f4:", None))
        self.doubleSpinBox_freq_min.setSuffix(QCoreApplication.translate("MainWindow", u" Hz", None))
        self.label_to1.setText(QCoreApplication.translate("MainWindow", u"-", None))
        self.doubleSpinBox_freq_max.setSuffix(QCoreApplication.translate("MainWindow", u" Hz", None))
        self.groupBox_modal_mesh_selection.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u9009\u62e9", None))
        self.label_modal_mesh_info.setText(QCoreApplication.translate("MainWindow", u"\u8bf7\u9009\u62e9\u8981\u8fdb\u884c\u6a21\u6001\u8ba1\u7b97\u7684\u7f51\u683c\uff1a", None))
        self.btn_use_selected_meshes.setText(QCoreApplication.translate("MainWindow", u"\u4f7f\u7528\u5df2\u9009\u62e9\u7684\u7f51\u683c", None))
        self.btn_use_recommended_mesh.setText(QCoreApplication.translate("MainWindow", u"\u4f7f\u7528\u63a8\u8350\u7f51\u683c", None))
        self.btn_select_all_for_modal.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u6240\u6709\u7f51\u683c", None))
        self.label_selected_meshes_for_modal.setText(QCoreApplication.translate("MainWindow", u"\u5f53\u524d\u9009\u62e9: 0 \u4e2a\u7f51\u683c", None))
        self.label_selected_meshes_for_modal.setStyleSheet(QCoreApplication.translate("MainWindow", u"font-weight: bold; color: #0078d4;", None))
        self.btn_single_modal.setText(QCoreApplication.translate("MainWindow", u"\u5355\u4e2a\u6a21\u6001\u8ba1\u7b97", None))
        self.btn_start_modal_calculation.setText(QCoreApplication.translate("MainWindow", u"\u5f00\u59cb\u6a21\u6001\u8ba1\u7b97", None))
        self.btn_start_modal_calculation.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}\n"
"QPushButton:disabled {\n"
"    background-color: #bdc3c7;\n"
"}", None))
        self.groupBox_convergence_chart.setTitle(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u6536\u655b\u6027\u5206\u6790", None))
        self.label_convergence_chart.setText(QCoreApplication.translate("MainWindow", u"\u9891\u7387\u6536\u655b\u6027\u56fe\u8868\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_convergence_chart.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.groupBox_batch_calculation.setTitle(QCoreApplication.translate("MainWindow", u"\u6279\u91cf\u8ba1\u7b97\u63a7\u5236", None))
        self.btn_batch_calculate.setText(QCoreApplication.translate("MainWindow", u"\u5f00\u59cb\u6279\u91cf\u8ba1\u7b97", None))
        self.btn_batch_calculate.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.batch_status_label.setText(QCoreApplication.translate("MainWindow", u"\u5c31\u7eea", None))
        self.batch_status_label.setStyleSheet(QCoreApplication.translate("MainWindow", u"font-weight: bold; color: #333333;", None))
        self.current_calculation_label.setText("")
        self.current_calculation_label.setStyleSheet(QCoreApplication.translate("MainWindow", u"color: #0078d4; font-style: italic;", None))
        self.btn_pause_calculation.setText(QCoreApplication.translate("MainWindow", u"\u6682\u505c", None))
        self.btn_stop_calculation.setText(QCoreApplication.translate("MainWindow", u"\u505c\u6b62", None))
        self.label_stats_total.setText(QCoreApplication.translate("MainWindow", u"\u603b\u8ba1:", None))
        self.calc_stats_total.setText(QCoreApplication.translate("MainWindow", u"0", None))
        self.label_stats_completed.setText(QCoreApplication.translate("MainWindow", u"\u5df2\u5b8c\u6210:", None))
        self.calc_stats_completed.setText(QCoreApplication.translate("MainWindow", u"0", None))
        self.label_stats_failed.setText(QCoreApplication.translate("MainWindow", u"\u5931\u8d25:", None))
        self.calc_stats_failed.setText(QCoreApplication.translate("MainWindow", u"0", None))
        self.label_stats_time.setText(QCoreApplication.translate("MainWindow", u"\u7528\u65f6:", None))
        self.calc_stats_time.setText(QCoreApplication.translate("MainWindow", u"0.0s", None))
        self.groupBox_optimal_recommendation.setTitle(QCoreApplication.translate("MainWindow", u"\u6700\u4f18\u7f51\u683c\u63a8\u8350", None))
        self.label_recommendation_title.setText(QCoreApplication.translate("MainWindow", u"\u6536\u655b\u6027\u5206\u6790\u4e0e\u7f51\u683c\u63a8\u8350", None))
        self.label_recommendation_title.setStyleSheet(QCoreApplication.translate("MainWindow", u"font-size: 16px; font-weight: bold; color: #333333; margin: 5px;", None))
        self.convergence_analysis.setPlainText(QCoreApplication.translate("MainWindow", u"\u6682\u65e0\u6536\u655b\u6027\u5206\u6790\u6570\u636e\n"
"\n"
"\u8bf7\u5148\u5b8c\u6210\u7f51\u683c\u7684\u6a21\u6001\u8ba1\u7b97\uff0c\u7136\u540e\u70b9\u51fb'\u5206\u6790\u6536\u655b\u6027'\u6309\u94ae\u3002", None))
        self.btn_analyze_convergence.setText(QCoreApplication.translate("MainWindow", u"\u5206\u6790\u6536\u655b\u6027", None))
        self.recommendation_info.setText(QCoreApplication.translate("MainWindow", u"\u6682\u65e0\u63a8\u8350\u7ed3\u679c", None))
        self.recommendation_info.setStyleSheet(QCoreApplication.translate("MainWindow", u"font-size: 14px; color: #333333; padding: 10px; border: 1px solid #e0e0e0; border-radius: 5px; background-color: #f9f9f9;", None))
        self.btn_recommend_mesh.setText(QCoreApplication.translate("MainWindow", u"\u63a8\u8350\u6700\u4f18\u7f51\u683c", None))
        self.btn_confirm_recommendation.setText(QCoreApplication.translate("MainWindow", u"\u786e\u8ba4\u9009\u62e9\u63a8\u8350\u7f51\u683c", None))
        self.label_efficiency_weight.setText(QCoreApplication.translate("MainWindow", u"\u6548\u7387\u6743\u91cd:", None))
        self.efficiency_weight_spinbox.setSuffix(QCoreApplication.translate("MainWindow", u" (0=\u4ec5\u6536\u655b\u6027, 1=\u4ec5\u6548\u7387)", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_modal_analysis), QCoreApplication.translate("MainWindow", u"\u6a21\u6001\u5206\u6790", None))
        self.groupBox_comparison_control.setTitle(QCoreApplication.translate("MainWindow", u"\u5bf9\u6bd4\u63a7\u5236", None))
        self.label_select_meshes.setText(QCoreApplication.translate("MainWindow", u"\u9009\u62e9\u5bf9\u6bd4\u7f51\u683c:", None))
        self.groupBox_display_options.setTitle(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u9009\u9879", None))
        self.checkBox_show_frequency.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u9891\u7387\u503c", None))
        self.checkBox_show_convergence.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u6536\u655b\u7ebf", None))
        self.checkBox_show_grid.setText(QCoreApplication.translate("MainWindow", u"\u663e\u793a\u7f51\u683c\u7ebf", None))
        self.btn_export_results.setText(QCoreApplication.translate("MainWindow", u"\u5bfc\u51fa\u7ed3\u679c", None))
        self.groupBox_comparison_chart.setTitle(QCoreApplication.translate("MainWindow", u"\u7f51\u683c\u65e0\u5173\u6027\u5206\u6790\u56fe\u8868", None))
        self.label_comparison_chart.setText(QCoreApplication.translate("MainWindow", u"\u591a\u7f51\u683c\u7ed3\u679c\u5bf9\u6bd4\u56fe\u8868\u533a\u57df\n"
"(\u5c06\u96c6\u6210matplotlib\u753b\u5e03)", None))
        self.label_comparison_chart.setStyleSheet(QCoreApplication.translate("MainWindow", u"QLabel {\n"
"    border: 2px dashed #dcdfe6;\n"
"    border-radius: 6px;\n"
"    background-color: #f9f9f9;\n"
"    color: #7f8c8d;\n"
"}", None))
        self.tabWidget_main.setTabText(self.tabWidget_main.indexOf(self.tab_result_comparison), QCoreApplication.translate("MainWindow", u"\u7ed3\u679c\u5bf9\u6bd4", None))
        self.btn_generate_mesh.setText(QCoreApplication.translate("MainWindow", u"\u751f\u6210\u7f51\u683c", None))
        self.btn_generate_mesh.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #2ecc71;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #27ae60;\n"
"}", None))
        self.btn_view_results.setText(QCoreApplication.translate("MainWindow", u"\u67e5\u770b\u7ed3\u679c", None))
        self.btn_view_results.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}", None))
        self.btn_previous.setText(QCoreApplication.translate("MainWindow", u"\u4e0a\u4e00\u6b65(\u524d\u5904\u7406)", None))
        self.btn_previous.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #7f8c8d;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6c7b7d;\n"
"}", None))
        self.btn_next.setText(QCoreApplication.translate("MainWindow", u"\u4e0b\u4e00\u6b65(\u8fde\u63a5\u8bbe\u7f6e)", None))
        self.btn_next.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}", None))
        self.btn_main_menu.setText(QCoreApplication.translate("MainWindow", u"\u4e3b\u83dc\u5355", None))
        self.btn_main_menu.setStyleSheet(QCoreApplication.translate("MainWindow", u"QPushButton {\n"
"    background-color: #7f8c8d;\n"
"    font-size: 14px;\n"
"    font-weight: bold;\n"
"}\n"
"QPushButton:hover {\n"
"    background-color: #6c7b7d;\n"
"}", None))
    # retranslateUi

