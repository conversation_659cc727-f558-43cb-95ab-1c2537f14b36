"""
主窗口模块

此模块定义了应用程序的主窗口类，负责：
1. 应用程序的主界面显示
2. ANSYS Workbench的配置
3. 项目文件的打开和保存
4. 帮助文档的显示
5. 日志查看功能

作者: [作者名]
日期: [日期]
"""

import os
import sys
import logging
from PySide6.QtCore import QSettings, Qt, QCoreApplication, QPropertyAnimation, QEasingCurve, QTimer, QSize, Property, QEvent
from PySide6.QtGui import QCloseEvent, QIcon, QAction, QKeySequence, QColor, QFont, QPalette
from PySide6.QtWidgets import (QMessageBox, QFileDialog, QStyle, QMenu, QPushButton, QGraphicsDropShadowEffect,
                               QFrame, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QWidget, QScrollArea, QSizePolicy)

from ui import ui_main
from window_manager import WindowType
from core import exception_signaler, ErrorDialog, ConfigManager
from .base_window import BaseWindow
from .log_viewer import LogViewer, get_icon

logger = logging.getLogger(__name__)

def tr(text: str) -> str:
    """翻译函数的便捷包装"""
    return QCoreApplication.translate("MainWindow", text)

class MainWindow(BaseWindow):
    """主窗口类"""
    def __init__(self, window_manager, initial_data=None):
        super().__init__(window_manager)
        self.is_main_window = True  # 标记为主窗口
        self.ui = ui_main.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle(tr('振动传递计算软件'))
        
        # 设置窗口样式 - 移除，避免与现代化样式冲突
        # self.setStyleSheet(self.styleSheet() + """
        #     QMainWindow::title {
        #         font-weight: bold;
        #         font-size: 14px;
        #     }
        # """)

        # 设置菜单图标
        self.ui.actionnew.setIcon(self.style().standardIcon(QStyle.SP_FileIcon))
        self.ui.actionSave.setIcon(self.style().standardIcon(QStyle.SP_DialogSaveButton))
        self.ui.actionExit.setIcon(self.style().standardIcon(QStyle.SP_DialogCloseButton))
        self.ui.actionExport.setIcon(self.style().standardIcon(QStyle.SP_ArrowRight))
        self.ui.actionAbout.setIcon(self.style().standardIcon(QStyle.SP_MessageBoxInformation))
        self.ui.actionOpen.setIcon(self.style().standardIcon(QStyle.SP_DialogOpenButton))
        
        # 创建或读取配置
        self.config_manager = ConfigManager()
        self.ANSYS_Start_File = self.config_manager.get("ansys.start_file")
        self.ANSYS_Work_Dir = self.config_manager.get("ansys.work_dir")
        self.WORKBENCH_Project_File = self.config_manager.get("ansys.project_file")

        # 连接菜单动作
        self.ui.actionwb_root_file.triggered.connect(self.ANSYS_Start)
        self.ui.actionworkfile.triggered.connect(self.ANSYS_Work) 
        self.ui.actionOpen.triggered.connect(self.open_workbench_project)
        self.ui.actionAbout.triggered.connect(self.show_help)
        
        # 添加工具菜单和日志查看器选项
        self.setup_tools_menu()
        
        # 如果有初始数据，应用到界面
        if initial_data:
            logger.info("通过配置文件加载初始数据")
            self.apply_initial_data(initial_data)
            
        # 连接全局异常处理信号
        exception_signaler.exception_caught.connect(self.show_exception_dialog)
            
        # 连接配置变更信号
        self.config_manager.signals.config_changed.connect(self.on_config_changed)
        self.config_manager.signals.config_error.connect(self.on_config_error)
        
        # 为所有操作按钮添加动画效果
        self.setup_animated_buttons()

        # 设置现代化布局（在按钮设置完成后）
        self.setup_modern_layout()

        logger.info("主窗口初始化完成")

    # 设置窗口特定的字体样式 - 移除，避免与现代化样式冲突
        # self.setStyleSheet("""
        #     QLabel#label {
        #         font-size: 20pt;
        #         font-family: 'Microsoft YaHei UI';
        #     }
        #     QPushButton {
        #         font-family: 'Microsoft YaHei UI';
        #         min-height: 12pt;
        #     }
        # """)

    def setup_modern_layout(self):
        """设置现代化的卡片式布局"""
        try:
            # 设置窗口大小
            self.setMinimumSize(900, 700)  # 增加最小尺寸
            self.resize(1200, 900)  # 增加默认尺寸

            # 加载样式表
            style_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "styles", "main_window_styles.qss")
            self.modern_stylesheet = ""

            if os.path.exists(style_path):
                with open(style_path, 'r', encoding='utf-8') as f:
                    self.modern_stylesheet = f.read()
                logger.info(f"已加载主窗口样式表: {style_path} ({len(self.modern_stylesheet)} 字符)")
            else:
                logger.warning(f"样式表文件不存在: {style_path}")
                # 使用备用样式
                self.modern_stylesheet = self.get_fallback_stylesheet()
                logger.info("使用备用样式表")

            # 先不应用样式表，等组件创建完成后再应用

            # 保持原有按钮对象的引用，确保信号槽连接不被破坏
            # 不隐藏原有布局，而是创建新的布局覆盖显示

            # 创建新的现代化布局
            self.create_modern_ui()

            # 设置响应式布局
            self.setup_responsive_layout()

            # 强制刷新样式
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            logger.info("现代化布局设置完成")

        except Exception as e:
            logger.error(f"设置现代化布局失败: {e}", exc_info=True)

    def get_fallback_stylesheet(self):
        """获取备用样式表"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
        }

        QWidget {
            font-family: "Microsoft YaHei UI", "Segoe UI", Arial, sans-serif;
            color: #333333;
        }

        QLabel#titleLabel {
            background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 0,
                                        stop: 0 #0078d4, stop: 1 #106ebe);
            color: #ffffff;
            font-size: 24px;
            font-weight: bold;
            padding: 20px;
            border-radius: 8px;
            margin: 10px;
        }

        QLabel#subtitleLabel {
            color: #666666;
            font-size: 14px;
            padding: 10px;
            margin: 5px;
        }

        QFrame[class="functionCard"] {
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 5px;
            min-height: 120px;
            min-width: 180px;
        }

        QFrame[class="functionCard"]:hover {
            border-color: #0078d4;
            background-color: #f8f9fa;
        }

        QLabel[class="cardIcon"] {
            font-size: 32px;
            color: #0078d4;
            background-color: #e3f2fd;
            border-radius: 25px;
            padding: 10px;
            margin: 5px;
            min-width: 50px;
            max-width: 50px;
            min-height: 50px;
            max-height: 50px;
        }

        QLabel[class="cardTitle"] {
            font-size: 14px;
            font-weight: bold;
            color: #333333;
            margin: 5px;
        }

        QLabel[class="cardDescription"] {
            font-size: 11px;
            color: #666666;
            margin: 5px;
        }
        """

    def create_modern_ui(self):
        """创建现代化的用户界面"""
        # 创建主容器
        main_container = QWidget()
        main_container.setObjectName("modernMainContainer")
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建标题区域
        title_widget = self.create_title_section()
        main_layout.addWidget(title_widget)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setObjectName("scrollArea")
        scroll_area.setFrameShape(QFrame.NoFrame)  # 移除边框

        # 创建卡片容器
        cards_container = QWidget()
        cards_container.setObjectName("cardsContainer")
        self.cards_layout = QGridLayout(cards_container)
        self.cards_layout.setSpacing(20)  # 增加间距
        self.cards_layout.setContentsMargins(30, 20, 30, 20)  # 调整边距

        # 创建功能卡片
        self.create_function_cards()

        # 添加垂直弹簧
        from PySide6.QtWidgets import QSpacerItem
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        self.cards_layout.addItem(spacer, 3, 0, 1, 3)  # 跨3列

        scroll_area.setWidget(cards_container)
        main_layout.addWidget(scroll_area)

        # 设置为中央组件
        self.setCentralWidget(main_container)

        # 应用现代化样式表到整个窗口
        if hasattr(self, 'modern_stylesheet') and self.modern_stylesheet:
            self.setStyleSheet(self.modern_stylesheet)
            logger.info("现代化样式表已应用到窗口")

            # 强制刷新样式
            self.style().unpolish(self)
            self.style().polish(self)
            self.update()

            # 递归应用样式到所有子组件
            self.apply_styles_to_children(main_container)

        logger.info("现代化UI创建完成")

    def apply_styles_to_children(self, widget):
        """递归应用样式到所有子组件"""
        try:
            # 强制刷新当前组件的样式
            widget.style().unpolish(widget)
            widget.style().polish(widget)
            widget.update()

            # 递归处理所有子组件
            for child in widget.findChildren(QWidget):
                child.style().unpolish(child)
                child.style().polish(child)
                child.update()

            logger.debug("样式已递归应用到所有子组件")
        except Exception as e:
            logger.warning(f"应用样式到子组件时出现异常: {e}")

    def create_title_section(self):
        """创建标题区域"""
        title_widget = QWidget()
        title_widget.setObjectName("titleWidget")
        title_widget.setMinimumHeight(120)

        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(0)

        # 主标题
        title_label = QLabel("振动传递计算软件")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel("专业的振动分析与计算平台")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(subtitle_label)

        return title_widget

    def create_function_cards(self):
        """创建功能卡片"""
        # 定义卡片数据
        card_data = [
            {
                'id': 'preprocessingCard',
                'icon': '⚙️',
                'title': '前处理',
                'description': '模型导入、几何处理\n材料属性设置',
                'color_theme': 'green'
            },
            {
                'id': 'connectionCard',
                'icon': '🔗',
                'title': '连接设置',
                'description': '接触定义、连接配置\n边界条件设置',
                'color_theme': 'purple'
            },
            {
                'id': 'analysisCard',
                'icon': '📊',
                'title': '分析设置',
                'description': '求解器配置\n分析参数设定',
                'color_theme': 'blue'
            },
            {
                'id': 'constrainCard',
                'icon': '🔒',
                'title': '设置约束',
                'description': '边界约束定义\n载荷条件设置',
                'color_theme': 'red'
            },
            {
                'id': 'meshCard',
                'icon': '🔍',
                'title': '网格无关性验证',
                'description': '网格质量检查\n收敛性分析',
                'color_theme': 'orange'
            },
            {
                'id': 'resultCard',
                'icon': '📈',
                'title': '计算结果',
                'description': '结果查看、数据导出\n后处理分析',
                'color_theme': 'cyan'
            },
            {
                'id': 'postCard',
                'icon': '🎯',
                'title': '后处理',
                'description': '振动传递分析\n可视化展示',
                'color_theme': 'indigo'
            }
        ]

        # 创建卡片并添加到网格布局
        for i, card_info in enumerate(card_data):
            card = self.create_card(card_info)
            row = i // 3  # 每行3个卡片
            col = i % 3
            self.cards_layout.addWidget(card, row, col)

    def create_card(self, card_info):
        """创建单个功能卡片"""
        # 创建卡片容器
        card = QFrame()
        card.setObjectName(card_info['id'])
        card.setProperty("class", "functionCard")
        card.setCursor(Qt.PointingHandCursor)

        # 设置固定尺寸确保显示正常
        card.setMinimumSize(200, 150)
        card.setMaximumSize(300, 200)

        # 设置卡片布局
        card_layout = QVBoxLayout(card)
        card_layout.setAlignment(Qt.AlignCenter)
        card_layout.setSpacing(10)
        card_layout.setContentsMargins(15, 15, 15, 15)

        # 创建图标标签
        icon_label = QLabel(card_info['icon'])
        icon_label.setProperty("class", "cardIcon")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setMinimumSize(60, 60)
        icon_label.setMaximumSize(60, 60)
        card_layout.addWidget(icon_label)

        # 创建标题标签
        title_label = QLabel(card_info['title'])
        title_label.setProperty("class", "cardTitle")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        card_layout.addWidget(title_label)

        # 创建描述标签
        desc_label = QLabel(card_info['description'])
        desc_label.setProperty("class", "cardDescription")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(True)
        card_layout.addWidget(desc_label)

        # 连接点击事件到窗口切换方法
        card.mousePressEvent = lambda event, card_id=card_info['id']: self.on_card_clicked(card_id)

        # 确保组件属性正确设置，样式将由父级统一应用
        logger.debug(f"创建卡片: {card_info['id']}, class={card.property('class')}")

        return card

    def on_card_clicked(self, card_id):
        """处理卡片点击事件"""
        # 根据卡片ID直接调用相应的窗口切换方法
        card_to_window_map = {
            'preprocessingCard': self._switch_to_pre,
            'meshCard': self._switch_to_mesh,
            'connectionCard': self._switch_to_connection,
            'analysisCard': self._switch_to_analysis,
            'constrainCard': self._switch_to_constrain,
            'resultCard': self._switch_to_result,
            'postCard': self._switch_to_post
        }

        switch_method = card_to_window_map.get(card_id)
        if switch_method:
            try:
                switch_method()
                logger.info(f"成功切换窗口: {card_id}")
            except Exception as e:
                logger.error(f"切换窗口失败 {card_id}: {e}", exc_info=True)
        else:
            logger.warning(f"未找到对应的窗口切换方法: {card_id}")

    def setup_responsive_layout(self):
        """设置响应式布局"""
        # 监听窗口大小变化
        self.resizeEvent = self.on_window_resize

    def on_window_resize(self, event):
        """处理窗口大小变化"""
        if hasattr(self, 'cards_layout'):
            width = event.size().width()

            # 根据窗口宽度调整列数
            if width >= 1200:
                # 大屏幕 - 3列
                self.adjust_grid_columns(3)
                self.setProperty("screenSize", "large")
            elif width >= 800:
                # 中等屏幕 - 2列
                self.adjust_grid_columns(2)
                self.setProperty("screenSize", "medium")
            else:
                # 小屏幕 - 1列
                self.adjust_grid_columns(1)
                self.setProperty("screenSize", "small")

            # 刷新样式
            self.style().unpolish(self)
            self.style().polish(self)

        # 调用原有的resizeEvent
        super().resizeEvent(event)

    def adjust_grid_columns(self, columns):
        """调整网格布局的列数"""
        if not hasattr(self, 'cards_layout'):
            return

        # 获取所有卡片
        cards = []
        for i in range(self.cards_layout.count()):
            item = self.cards_layout.itemAt(i)
            if item and item.widget() and item.widget().objectName().endswith('Card'):
                cards.append(item.widget())

        # 清空布局
        for card in cards:
            self.cards_layout.removeWidget(card)

        # 重新排列卡片
        for i, card in enumerate(cards):
            row = i // columns
            col = i % columns
            self.cards_layout.addWidget(card, row, col)

        # 添加垂直弹簧
        from PySide6.QtWidgets import QSpacerItem
        spacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        final_row = (len(cards) - 1) // columns + 1
        self.cards_layout.addItem(spacer, final_row, 0, 1, columns)

    def setup_animated_buttons(self):
        """为主窗口中的按钮添加动画效果"""
        # 获取所有按钮
        buttons = [
            self.ui.preprocessing,
            self.ui.mesh,
            self.ui.connection,
            self.ui.analysis,
            self.ui.constrain,
            self.ui.result,
            self.ui.post
        ]
        
        # 使用基类的方法应用动画效果
        self.apply_animated_buttons(buttons)
        
        # 更新按钮引用
        self.ui.preprocessing = self.findChild(QPushButton, "preprocessing")
        self.ui.mesh = self.findChild(QPushButton, "mesh")
        self.ui.connection = self.findChild(QPushButton, "connection")
        self.ui.analysis = self.findChild(QPushButton, "analysis")
        self.ui.constrain = self.findChild(QPushButton, "constrain")
        self.ui.result = self.findChild(QPushButton, "result")
        self.ui.post = self.findChild(QPushButton, "post")
        
        # 连接按钮点击事件
        self.ui.preprocessing.clicked.connect(self._switch_to_pre)
        self.ui.mesh.clicked.connect(self._switch_to_mesh)
        self.ui.connection.clicked.connect(self._switch_to_connection)
        self.ui.analysis.clicked.connect(self._switch_to_analysis)
        self.ui.constrain.clicked.connect(self._switch_to_constrain)
        self.ui.result.clicked.connect(self._switch_to_result)
        self.ui.post.clicked.connect(self._switch_to_post)
    
    # 按钮切换函数 - 优化性能（修复版）
    def _switch_to_pre(self):
        # 安全地处理按钮动画状态
        self._safe_stop_button_animations(self.ui.preprocessing)
        # 切换窗口
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.PRE))

    def _switch_to_mesh(self):
        self._safe_stop_button_animations(self.ui.mesh)
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.MESH))

    def _switch_to_connection(self):
        self._safe_stop_button_animations(self.ui.connection)
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.CONNECTION))

    def _switch_to_analysis(self):
        self._safe_stop_button_animations(self.ui.analysis)
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.ANALYSIS))

    def _switch_to_constrain(self):
        self._safe_stop_button_animations(self.ui.constrain)
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.CONSTRAIN))

    def _switch_to_result(self):
        self._safe_stop_button_animations(self.ui.result)
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.RESULT))

    def _switch_to_post(self):
        self._safe_stop_button_animations(self.ui.post)
        logger.info("点击后处理按钮，准备切换到后处理窗口")
        QTimer.singleShot(10, lambda: self.window_manager.switch_to(WindowType.VIBRATION))

    def _safe_stop_button_animations(self, btn):
        """安全地停止按钮动画"""
        try:
            if btn and hasattr(btn, 'hover_anim') and btn.hover_anim:
                btn.hover_anim.stop()
            if btn and hasattr(btn, 'reset_anim') and btn.reset_anim:
                btn.reset_anim.stop()
            if btn and hasattr(btn, 'set_elevation'):
                btn.set_elevation(0)
        except Exception as e:
            logger.debug(f"停止按钮动画时出现异常（可忽略）: {e}")

    def setup_tools_menu(self):
        """设置工具菜单"""
        # 创建工具菜单
        self.tools_menu = QMenu(tr("工具(&T)"), self)
        self.ui.menubar.addMenu(self.tools_menu)

        # 添加日志查看器菜单项
        self.log_viewer_action = QAction(tr("日志查看器(&L)"), self)
        self.log_viewer_action.setShortcut("Ctrl+L")
        self.log_viewer_action.setIcon(get_icon("log.png"))
        self.log_viewer_action.triggered.connect(self.show_log_viewer)
        self.tools_menu.addAction(self.log_viewer_action)

        # 添加振动分析工具菜单项
        self.vibration_tool_action = QAction(tr("振动分析工具(&V)"), self)
        self.vibration_tool_action.triggered.connect(self.show_vibration_tool)
        self.tools_menu.addAction(self.vibration_tool_action)

        # 添加分隔符
        self.tools_menu.addSeparator()

        # 添加字体设置菜单项
        self.font_settings_action = QAction(tr("字体设置(&F)"), self)
        self.font_settings_action.setShortcut("Ctrl+Shift+F")
        self.font_settings_action.triggered.connect(self.show_font_settings)
        self.tools_menu.addAction(self.font_settings_action)

        # 添加帮助菜单
        self.help_menu = QMenu(tr("帮助(&H)"), self)
        self.ui.menubar.addMenu(self.help_menu)

        # 添加帮助菜单项
        self.help_action = QAction(tr("查看帮助(&H)"), self)
        self.help_action.setShortcut("F1")
        self.help_action.triggered.connect(self.show_help)
        self.help_menu.addAction(self.help_action)

        # 添加路径调试菜单项
        self.debug_action = QAction(tr("调试路径(&D)"), self)
        self.debug_action.triggered.connect(self.show_debug_paths)
        self.help_menu.addAction(self.debug_action)

        # 添加关于菜单项
        self.about_action = QAction(tr("关于(&A)"), self)
        self.about_action.triggered.connect(self.show_about)
        self.help_menu.addAction(self.about_action)

        # 添加语言设置菜单项
        self.language_action = QAction(tr("语言设置(&L)"), self)
        self.language_action.triggered.connect(self.show_language_settings)
        self.help_menu.addAction(self.language_action)

        logger.debug("工具菜单设置完成")
        
    def show_log_viewer(self):
        """显示日志查看器窗口"""
        try:
            # 使用window_manager切换到日志查看器窗口，并隐藏当前窗口
            self.window_manager.switch_to(WindowType.LOG, hide_current=True)
            logger.info("已打开日志查看器")
        except Exception as e:
            logger.error(f"打开日志查看器失败: {e}", exc_info=True)
            QMessageBox.warning(
                self,
                "错误",
                f"无法打开日志查看器：{str(e)}"
            )

    def show_vibration_tool(self):
        """显示振动分析工具窗口"""
        try:
            # 使用window_manager切换到振动分析工具窗口，并隐藏当前窗口
            self.window_manager.switch_to(WindowType.VIBRATION, hide_current=True)
            logger.info("已打开振动分析工具")
        except Exception as e:
            logger.error(f"打开振动分析工具失败: {e}", exc_info=True)
            QMessageBox.warning(
                self,
                "错误",
                f"无法打开振动分析工具：{str(e)}"
            )

    def show_font_settings(self):
        """显示字体设置对话框"""
        try:
            from views.font_settings_dialog import show_font_settings_dialog
            show_font_settings_dialog(self)
        except Exception as e:
            logger.error(f"显示字体设置对话框失败: {e}")
            QMessageBox.warning(self, "错误", f"无法打开字体设置对话框：{e}")

    def show_debug_paths(self):
        """显示路径调试信息"""
        from debug_paths import show_debug_paths
        show_debug_paths()

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            tr("关于振动传递计算软件"),
            tr("振动传递计算软件 v1.0.0\n\n"
               "本软件用于机械振动传递路径分析计算与可视化。\n"
               "基于ANSYS Workbench和PySide6开发。\n\n"
               "© 2023 版权所有")
        )

    def show_language_settings(self):
        """显示语言设置对话框"""
        try:
            from .language_selector import show_language_settings_dialog
            from core.i18n_manager import get_i18n_manager

            i18n_manager = get_i18n_manager()
            if show_language_settings_dialog(i18n_manager, self):
                # 用户确认了设置，可能需要更新界面
                self._update_ui_language()
        except Exception as e:
            logger.error(f"显示语言设置对话框失败: {e}")
            QMessageBox.warning(
                self,
                tr("错误"),
                tr("无法显示语言设置对话框: ") + str(e)
            )

    def _update_ui_language(self):
        """更新界面语言"""
        try:
            # 重新设置窗口标题
            self.setWindowTitle(tr('振动传递计算软件'))

            # 更新菜单文本
            self.tools_menu.setTitle(tr("工具(&T)"))
            self.help_menu.setTitle(tr("帮助(&H)"))

            # 更新动作文本
            self.log_viewer_action.setText(tr("日志查看器(&L)"))
            self.vibration_tool_action.setText(tr("振动分析工具(&V)"))
            self.font_settings_action.setText(tr("字体设置(&F)"))
            self.help_action.setText(tr("查看帮助(&H)"))
            self.debug_action.setText(tr("调试路径(&D)"))
            self.about_action.setText(tr("关于(&A)"))
            self.language_action.setText(tr("语言设置(&L)"))

            logger.info("界面语言已更新")
        except Exception as e:
            logger.error(f"更新界面语言失败: {e}")

    def apply_initial_data(self, data):
        """应用初始数据到应用程序
        
        Args:
            data: 从启动配置文件中读取的数据
        """
        # 将数据保存到window_manager中，以便在切换窗口时使用
        if hasattr(self, 'window_manager'):
            self.window_manager.initial_data = data
            logger.debug("初始数据已保存到window_manager")

    def update_ui_from_api(self, params):
        """API回调函数，用于从API接收数据并更新UI
        
        Args:
            params: 从API接收的参数
        """
        logger.info(f"通过API实时更新数据: {params}")
        
        # 更新所有相关窗口，不仅是当前可见窗口
        # 更新分析窗口
        analysis_window = self.window_manager.get_window(WindowType.ANALYSIS)
        if analysis_window:
            self.update_window_with_params(analysis_window, params)
            
        # 更新约束窗口
        constrain_window = self.window_manager.get_window(WindowType.CONSTRAIN)
        if constrain_window:
            self.update_window_with_params(constrain_window, params)
            
        # 当前可见窗口可能需要刷新UI
        current_window_type = None
        for window_type, window in self.window_manager._windows.items():
            if window.isVisible():
                current_window_type = window_type
                break
                
        if current_window_type:
            window = self.window_manager.get_window(current_window_type)
            if window:
                window.update()
        
        # 同时更新保存的初始数据，以便在切换窗口时使用
        if hasattr(self.window_manager, 'initial_data'):
            self.window_manager.initial_data.update(params)
        else:
            self.window_manager.initial_data = params

    def update_window_with_params(self, window, params):
        """根据窗口类型更新相应的界面
        
        Args:
            window: 窗口实例
            params: 参数
        """
        # 分析窗口参数更新
        if window.windowTitle() == "分析设置":
            if 'timeStep' in params:
                window.ui.lineEdit_timestep.setText(str(params['timeStep']))
            if 'endTime' in params:
                window.ui.lineEdit_stependline.setText(str(params['endTime']))
            if 'stiffnessCoefficient' in params and params['stiffnessCoefficient'] is not None:
                window.ui.lineEdit_stiffness.setText(str(params['stiffnessCoefficient']))
            if 'massCoefficient' in params and params['massCoefficient'] is not None:
                window.ui.lineEdit_mass.setText(str(params['massCoefficient']))
        
        # 约束窗口参数更新
        elif window.windowTitle() == "约束设置":
            if 'forceOutputFolder' in params and params['forceOutputFolder']:
                force_dir = params['forceOutputFolder']
                if os.path.exists(force_dir):
                    # 设置文本框内容
                    window.ui.lineEdit_force.setText(force_dir)
                    
                    # 自动执行力文件验证和加载
                    from ctrl.constrain_slot import validate_force_file_content
                    
                    # 清空状态显示
                    window.ui.plainTextEdit_force.clear()
                    status_text = []
                    
                    def log_status(msg):
                        status_text.append(msg)
                        window.ui.plainTextEdit_force.setPlainText("\n".join(status_text))
                        window.ui.plainTextEdit_force.repaint()
                    
                    log_status("自动处理力文件...")
                    log_status(f"文件夹路径: {force_dir}")
                    log_status("开始验证文件...")
                    
                    # 定义必需的文件 - 更新为新的命名规范
                    required_files = {
                        "yl-wall": ["yl-fx.out", "yl-fy.out", "yl-fz.out"],
                        "wk-wall": ["wk-fx.out", "wk-fy.out", "wk-fz.out"]
                    }
                    
                    # 检查文件存在性和格式
                    missing_files = []
                    invalid_files = []
                    
                    for group, files in required_files.items():
                        log_status(f"检查 {group} 组文件...")
                        for filename in files:
                            file_path = os.path.join(force_dir, filename)
                            
                            # 检查文件是否存在
                            if not os.path.exists(file_path):
                                log_status(f"  ✗ {filename} - 文件不存在")
                                missing_files.append(filename)
                                continue
                                
                            # 检查文件扩展名
                            if not filename.endswith('.out'):
                                log_status(f"  ✗ {filename} - 不是.out文件")
                                invalid_files.append(f"{filename} (不是.out文件)")
                                continue
                                
                            # 检查文件内容格式
                            if not validate_force_file_content(file_path):
                                log_status(f"  ✗ {filename} - 格式错误")
                                invalid_files.append(f"{filename} (格式错误)")
                                continue
                                
                            log_status(f"  ✓ {filename} - 验证通过")
                    
                    # 处理验证结果
                    if missing_files or invalid_files:
                        error_msg = "力文件自动验证结果:\n"
                        if missing_files:
                            error_msg += "\n缺少以下文件:\n" + "\n".join(missing_files)
                        if invalid_files:
                            error_msg += "\n\n以下文件无效:\n" + "\n".join(invalid_files)
                            
                        log_status("\n验证结果: 失败")
                        log_status(error_msg)
                        log_status("\n请手动点击'选择力文件夹'按钮重新选择有效的力文件夹")
                    else:
                        # 验证通过
                        log_status("\n验证结果: 全部通过 ✓")
                        log_status(f"力文件路径已保存: {force_dir}")
                        
                        # 如果有旋转速度参数，也更新它
                        if 'rotationSpeed' in params and params['rotationSpeed'] is not None:
                            window.ui.lineEdit_rotation_speed.setText(str(params['rotationSpeed']))
                            log_status(f"旋转速度已设置: {params['rotationSpeed']}")
            if 'rotationSpeed' in params and params['rotationSpeed'] is not None:
                window.ui.lineEdit_rotation_speed.setText(str(params['rotationSpeed']))

    def show_help(self):
        """显示帮助文档"""
        from .help_dialog import HelpDialog
        
        help_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "help", "html", "index.html")
        if os.path.exists(help_file):
            try:
                # 创建并显示帮助对话框
                help_dialog = HelpDialog(help_file, self)
                help_dialog.exec()
            except Exception as e:
                QMessageBox.warning(
                    self,
                    "错误",
                    f"无法显示帮助文档：{str(e)}"
                )
        else:
            QMessageBox.warning(
                self,
                "错误",
                "找不到帮助文档文件。\n请确保help/html目录下存在index.html文件。"
            )

    def ANSYS_Start(self):
        """设置ANSYS启动文件路径"""
        try:
            # 使用主窗口self作为父对象，而不是centralwidget
            file_path, _ = QFileDialog.getOpenFileName(
                self,  # 使用主窗口作为父对象
                '指定ANSYS Workbench软件启动文件RunWB2.exe',
                'RunWB2', 'Ansys File(*.exe)')
        except RuntimeError as e:
            # 处理C++对象被删除的错误
            logger.error(f"打开文件对话框时发生错误: {str(e)}")
            QMessageBox.critical(
                None,
                "错误",
                f"无法打开文件选择对话框：\n{str(e)}\n\n请重新启动应用程序。"
            )
            return
        except Exception as e:
            # 处理其他异常
            logger.error(f"打开文件对话框时发生未知错误: {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"打开文件选择对话框时发生错误：\n{str(e)}"
            )
            return

        if file_path:
            self.ANSYS_Start_File = file_path
            self.config_manager.set("ansys.start_file", file_path)
            print(f"ANSYS启动文件为：{self.ANSYS_Start_File}")
        else:
            print("取消选择，保留原有设置。")

    def ANSYS_Work(self):
        """设置工作目录"""
        try:
            # 使用主窗口self作为父对象，而不是centralwidget
            dir_path = QFileDialog.getExistingDirectory(
                self, '指定软件工作目录（注意不能有中文路径）')  # 使用主窗口作为父对象
        except RuntimeError as e:
            # 处理C++对象被删除的错误
            logger.error(f"打开目录选择对话框时发生错误: {str(e)}")
            QMessageBox.critical(
                None,
                "错误",
                f"无法打开目录选择对话框：\n{str(e)}\n\n请重新启动应用程序。"
            )
            return
        except Exception as e:
            # 处理其他异常
            logger.error(f"打开目录选择对话框时发生未知错误: {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"打开目录选择对话框时发生错误：\n{str(e)}"
            )
            return
        if dir_path:
            self.ANSYS_Work_Dir = dir_path
            self.config_manager.set("ansys.work_dir", dir_path)
            print(f"软件工作目录为：{self.ANSYS_Work_Dir}")
        else:
            print("取消选择，保留原有设置。")

    def open_workbench_project(self):
        """设置Workbench项目文件路径"""
        # 添加对象有效性检查，防止C++对象被删除的错误
        try:
            # 使用主窗口self作为父对象，而不是centralwidget
            file_path, _ = QFileDialog.getOpenFileName(
                self,  # 使用主窗口作为父对象
                '选择Workbench项目文件',
                os.path.dirname(self.WORKBENCH_Project_File) if os.path.exists(os.path.dirname(self.WORKBENCH_Project_File)) else '',
                'Workbench项目文件 (*.wbpj);;所有文件 (*)')
        except RuntimeError as e:
            # 处理C++对象被删除的错误
            logger.error(f"打开文件对话框时发生错误: {str(e)}")
            QMessageBox.critical(
                None,  # 使用None作为父对象，创建独立对话框
                "错误",
                f"无法打开文件选择对话框：\n{str(e)}\n\n请重新启动应用程序。"
            )
            return
        except Exception as e:
            # 处理其他异常
            logger.error(f"打开文件对话框时发生未知错误: {str(e)}")
            QMessageBox.critical(
                self,
                "错误",
                f"打开文件选择对话框时发生错误：\n{str(e)}"
            )
            return

        if file_path:
            self.WORKBENCH_Project_File = file_path
            self.config_manager.set("ansys.project_file", file_path)
            print(f"Workbench项目文件设置为：{self.WORKBENCH_Project_File}")
            QMessageBox.information(
                self,
                "成功",
                f"Workbench项目文件已设置为：\n{self.WORKBENCH_Project_File}"
            )
        else:
            print("取消选择，保留原有设置。")

    def closeEvent(self, event: QCloseEvent):
        """处理窗口关闭事件"""
        try:
            reply = QMessageBox.question(
                self, '提示',
                "是否要退出程序？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                # 保存配置
                self.config_manager.save_config()
                
                # 接受关闭事件并退出程序
                event.accept()
                sys.exit(0)
            else:
                # 用户选择不退出，忽略关闭事件
                event.ignore()
        except Exception as e:
            print(f"关闭窗口时出错: {str(e)}")
            # 出现错误时接受关闭事件
            event.accept()

    def show_exception_dialog(self, severity, title, message, details):
        """显示异常对话框
        
        Args:
            severity: 异常严重性
            title: 异常标题
            message: 异常消息
            details: 异常详细信息
        """
        try:
            dialog = ErrorDialog(severity, title, message, details, self)
            dialog.exec()
        except Exception as e:
            # 如果显示错误对话框失败，回退到标准错误处理
            print(f"显示错误对话框失败: {str(e)}")
            QMessageBox.critical(self, f"{severity}: {title}", message)

    def on_config_changed(self, config):
        """处理配置变更事件
        
        Args:
            config: 更新后的配置
        """
        print("配置已更新")
        
    def on_config_error(self, error_msg):
        """处理配置错误事件
        
        Args:
            error_msg: 错误消息
        """
        QMessageBox.warning(
            self,
            "配置错误",
            f"配置操作失败: {error_msg}"
        ) 