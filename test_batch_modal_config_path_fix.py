#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量模态计算配置文件路径修复验证脚本

此脚本用于验证修复后的批量模态计算配置文件生成功能，确保：
1. 输出目录路径格式正确（使用正斜杠）
2. 路径完整且包含时间戳
3. 为每个网格创建独立的输出目录
4. 配置文件格式符合要求

作者: AI Assistant
日期: 2025-08-01
"""

import sys
import os
import logging
import tempfile
import json
from datetime import datetime
from typing import List, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_output_directory_format():
    """测试输出目录格式"""
    try:
        logger.info("开始测试输出目录格式")
        
        # 模拟工作目录
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        
        # 生成时间戳（与修复后的格式保持一致）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建基础输出目录（使用双下划线，与用户示例保持一致）
        base_output_dir = os.path.join(work_dir, "temp", f"modal_output__{timestamp}")
        
        # 转换为正斜杠格式
        base_output_dir = base_output_dir.replace("\\", "/")
        
        # 验证路径格式
        assert "/" in base_output_dir, "路径应该使用正斜杠"
        assert "\\" not in base_output_dir, "路径不应该包含反斜杠"
        assert "modal_output__" in base_output_dir, "应该包含modal_output__前缀"
        assert timestamp in base_output_dir, "应该包含时间戳"
        assert base_output_dir.startswith(work_dir.replace("\\", "/")), "应该以工作目录开始"
        
        # 验证期望的路径格式
        expected_pattern = f"{work_dir}/temp/modal_output__{timestamp}".replace("\\", "/")
        assert base_output_dir == expected_pattern, f"路径格式不正确，期望: {expected_pattern}, 实际: {base_output_dir}"
        
        logger.info(f"✅ 输出目录格式验证通过")
        logger.info(f"  - 基础输出目录: {base_output_dir}")
        logger.info(f"  - 时间戳: {timestamp}")
        logger.info(f"  - 路径格式: 正斜杠 ✓")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 输出目录格式测试失败: {str(e)}", exc_info=True)
        return False

def test_mesh_output_directories():
    """测试网格输出目录创建"""
    try:
        logger.info("开始测试网格输出目录创建")
        
        # 模拟网格参数
        class MockMesh:
            def __init__(self, name, size):
                self.name = name
                self.size = size
        
        test_meshes = [
            MockMesh("a1", 12.0),
            MockMesh("a2", 20.0),
            MockMesh("a3", 15.0)
        ]
        
        # 模拟基础输出目录
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_output_dir = f"{work_dir}/temp/modal_output__{timestamp}".replace("\\", "/")
        
        # 为每个网格创建独立的输出目录路径
        mesh_output_dirs = []
        for mesh in test_meshes:
            # 参考单模态计算的输出目录格式：modal_result_<mesh_name>
            mesh_output_dir = os.path.join(base_output_dir, f"modal_result_{mesh.name}")
            # 转换为正斜杠格式
            mesh_output_dir = mesh_output_dir.replace("\\", "/")
            mesh_output_dirs.append(mesh_output_dir)
        
        # 验证网格输出目录格式
        expected_dirs = [
            f"{base_output_dir}/modal_result_a1",
            f"{base_output_dir}/modal_result_a2",
            f"{base_output_dir}/modal_result_a3"
        ]
        
        assert len(mesh_output_dirs) == len(test_meshes), f"输出目录数量应该等于网格数量"
        assert mesh_output_dirs == expected_dirs, f"输出目录路径不正确"
        
        # 验证路径格式
        for i, output_dir in enumerate(mesh_output_dirs):
            assert "/" in output_dir, f"网格 {i+1} 路径应该使用正斜杠"
            assert "\\" not in output_dir, f"网格 {i+1} 路径不应该包含反斜杠"
            assert f"modal_result_{test_meshes[i].name}" in output_dir, f"网格 {i+1} 路径应该包含网格名称"
            assert output_dir.startswith(base_output_dir), f"网格 {i+1} 路径应该以基础目录开始"
        
        logger.info(f"✅ 网格输出目录创建验证通过")
        logger.info(f"  - 基础输出目录: {base_output_dir}")
        logger.info(f"  - 网格数量: {len(test_meshes)}")
        for i, (mesh, output_dir) in enumerate(zip(test_meshes, mesh_output_dirs)):
            logger.info(f"  - 网格 {i+1}: {mesh.name} -> {output_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 网格输出目录创建测试失败: {str(e)}", exc_info=True)
        return False

def test_batch_config_generation():
    """测试批量配置文件生成"""
    try:
        logger.info("开始测试批量配置文件生成")
        
        # 模拟网格参数
        class MockMesh:
            def __init__(self, name, size):
                self.name = name
                self.size = size
        
        test_meshes = [
            MockMesh("a1", 12.0),
            MockMesh("a2", 20.0),
            MockMesh("a3", 15.0)
        ]
        
        # 模拟计算参数
        calc_params = {
            'modal_count': 10,
            'limit_freq': True,
            'freq_min': 0.0,
            'freq_max': 1000.0
        }
        
        # 模拟输出目录
        work_dir = "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_output_dir = f"{work_dir}/temp/modal_output__{timestamp}".replace("\\", "/")
        
        # 为每个网格创建独立的输出目录路径
        mesh_output_dirs = []
        for mesh in test_meshes:
            mesh_output_dir = f"{base_output_dir}/modal_result_{mesh.name}"
            mesh_output_dirs.append(mesh_output_dir)
        
        # 构建批量配置
        config = {
            "element_size": [mesh.size / 1000.0 for mesh in test_meshes],  # 转换为米
            "output_directory": base_output_dir,  # 基础输出目录
            "mesh_output_directories": mesh_output_dirs,  # 每个网格的具体输出目录
            "batch_mode": True,
            "mesh_names": [mesh.name for mesh in test_meshes],
            "calculation_params": calc_params
        }
        
        # 验证配置格式
        assert "element_size" in config, "应该包含element_size字段"
        assert "output_directory" in config, "应该包含output_directory字段"
        assert "mesh_output_directories" in config, "应该包含mesh_output_directories字段"
        assert "batch_mode" in config, "应该包含batch_mode字段"
        assert "mesh_names" in config, "应该包含mesh_names字段"
        assert "calculation_params" in config, "应该包含calculation_params字段"
        
        # 验证数据格式
        assert isinstance(config["element_size"], list), "element_size应该是列表"
        assert len(config["element_size"]) == len(test_meshes), "element_size长度应该等于网格数量"
        assert config["batch_mode"] == True, "batch_mode应该为True"
        assert len(config["mesh_names"]) == len(test_meshes), "mesh_names长度应该等于网格数量"
        assert len(config["mesh_output_directories"]) == len(test_meshes), "mesh_output_directories长度应该等于网格数量"
        
        # 验证路径格式
        assert "/" in config["output_directory"], "基础输出目录应该使用正斜杠"
        assert "\\" not in config["output_directory"], "基础输出目录不应该包含反斜杠"
        
        for i, output_dir in enumerate(config["mesh_output_directories"]):
            assert "/" in output_dir, f"网格 {i+1} 输出目录应该使用正斜杠"
            assert "\\" not in output_dir, f"网格 {i+1} 输出目录不应该包含反斜杠"
        
        # 保存配置文件进行测试
        os.makedirs("temp", exist_ok=True)
        config_path = "temp/test_batch_config_fixed.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # 验证保存的配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        assert loaded_config == config, "保存和加载的配置应该一致"
        
        logger.info(f"✅ 批量配置文件生成验证通过")
        logger.info(f"  - 配置文件路径: {config_path}")
        logger.info(f"  - 网格数量: {len(test_meshes)}")
        logger.info(f"  - 网格尺寸列表: {config['element_size']}")
        logger.info(f"  - 基础输出目录: {config['output_directory']}")
        logger.info(f"  - 网格输出目录数量: {len(config['mesh_output_directories'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 批量配置文件生成测试失败: {str(e)}", exc_info=True)
        return False

def test_mesh_config_latest_format():
    """测试mesh_config_latest.json格式"""
    try:
        logger.info("开始测试mesh_config_latest.json格式")
        
        # 模拟批量配置
        batch_config = {
            "element_size": [0.012, 0.020, 0.015],
            "output_directory": "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/modal_output__20250801_123456",
            "mesh_output_directories": [
                "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/modal_output__20250801_123456/modal_result_a1",
                "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/modal_output__20250801_123456/modal_result_a2",
                "D:/data/all-XM/autoworkbench/qtauto/qt-cs/qtproject/temp/modal_output__20250801_123456/modal_result_a3"
            ],
            "batch_mode": True,
            "mesh_names": ["a1", "a2", "a3"]
        }
        
        # 创建mesh_config_latest.json文件（批量格式） - 修复输出目录路径格式
        mesh_config = {
            "element_size": batch_config["element_size"],  # 保持列表格式
            "output_directory": batch_config["output_directory"].replace("\\", "/")  # 确保使用正斜杠
        }
        
        # 如果有具体的网格输出目录，也添加到配置中
        if "mesh_output_directories" in batch_config:
            mesh_config["mesh_output_directories"] = [
                path.replace("\\", "/") for path in batch_config["mesh_output_directories"]
            ]
        
        # 验证配置格式
        assert "element_size" in mesh_config, "应该包含element_size字段"
        assert "output_directory" in mesh_config, "应该包含output_directory字段"
        assert "mesh_output_directories" in mesh_config, "应该包含mesh_output_directories字段"
        
        # 验证路径格式
        assert "/" in mesh_config["output_directory"], "基础输出目录应该使用正斜杠"
        assert "\\" not in mesh_config["output_directory"], "基础输出目录不应该包含反斜杠"
        
        for output_dir in mesh_config["mesh_output_directories"]:
            assert "/" in output_dir, "网格输出目录应该使用正斜杠"
            assert "\\" not in output_dir, "网格输出目录不应该包含反斜杠"
        
        # 保存配置文件
        os.makedirs("temp/json", exist_ok=True)
        mesh_config_path = "temp/json/mesh_config_latest_fixed.json"
        with open(mesh_config_path, 'w', encoding='utf-8') as f:
            json.dump(mesh_config, f, indent=4, ensure_ascii=False)
        
        logger.info(f"✅ mesh_config_latest.json格式验证通过")
        logger.info(f"  - 配置文件路径: {mesh_config_path}")
        logger.info(f"  - 网格尺寸列表: {mesh_config['element_size']}")
        logger.info(f"  - 基础输出目录: {mesh_config['output_directory']}")
        logger.info(f"  - 网格输出目录数量: {len(mesh_config['mesh_output_directories'])}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ mesh_config_latest.json格式测试失败: {str(e)}", exc_info=True)
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始批量模态计算配置文件路径修复验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 4
    
    # 运行测试
    tests = [
        ("输出目录格式测试", test_output_directory_format),
        ("网格输出目录创建测试", test_mesh_output_directories),
        ("批量配置文件生成测试", test_batch_config_generation),
        ("mesh_config_latest.json格式测试", test_mesh_config_latest_format)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}", exc_info=True)
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！批量模态计算配置文件路径修复验证成功")
        logger.info("\n📋 修复要点总结:")
        logger.info("• ✅ 输出目录路径格式正确（使用正斜杠）")
        logger.info("• ✅ 路径完整且包含时间戳")
        logger.info("• ✅ 为每个网格创建独立的输出目录")
        logger.info("• ✅ 配置文件格式符合要求")
        logger.info("• ✅ 与单模态计算的路径格式保持一致")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
