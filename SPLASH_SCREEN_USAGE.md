# 🚀 启动画面功能使用指南

## 📋 问题解决

### 导入错误修复

如果遇到 `pyqtSignal` 导入错误，这是因为PySide6使用 `Signal` 而不是 `pyqtSignal`。我已经创建了修复版本。

### 使用修复版本

我已经创建了 `core/splash_screen_fixed.py`，这是完全修复的版本。

## 🚀 快速开始

### 1. 测试启动画面功能

运行修复版本的测试：

```bash
python test_fixed_splash.py
```

### 2. 在主应用程序中使用

修改 `qt_new.py` 中的导入：

```python
# 在 qt_new.py 的顶部，将原来的导入替换为：
try:
    from core.splash_screen_fixed import get_splash_manager
except ImportError:
    # 降级处理
    class DummySplashManager:
        def show_splash(self): return None
        def update_progress_by_percentage(self, *args): pass
        def hide_splash(self): pass
    def get_splash_manager(): return DummySplashManager()
```

### 3. 基本使用示例

```python
from PySide6.QtWidgets import QApplication
from core.splash_screen_fixed import SplashScreenManager

# 创建应用程序
app = QApplication([])

# 创建启动画面管理器
splash_manager = SplashScreenManager()

# 显示启动画面
splash = splash_manager.show_splash()

# 更新进度
splash_manager.update_progress_by_percentage(50, "正在加载...")

# 隐藏启动画面
splash_manager.hide_splash()
```

## ⚙️ 配置选项

### 基本配置

```python
config = {
    "enabled": True,
    "show_fade_in": True,
    "fade_in_duration": 500,
    "fade_out_duration": 300,
    "minimum_display_time": 2000,
    "show_rotation_animation": True,
    "colors": {
        "primary": "#3498db",
        "secondary": "#2ecc71",
        "text": "#34495e",
        "progress_background": "#ecf0f1"
    },
    "fonts": {
        "title_size": 16,
        "version_size": 10,
        "status_size": 9,
        "font_family": "Arial"
    },
    "layout": {
        "width": 480,
        "height": 320
    }
}

# 使用自定义配置
splash_manager = SplashScreenManager(config)
```

### 预设主题

#### 红色主题
```python
red_theme = {
    "colors": {
        "primary": "#e74c3c",
        "secondary": "#f39c12"
    }
}
```

#### 深色主题
```python
dark_theme = {
    "colors": {
        "primary": "#34495e",
        "secondary": "#95a5a6",
        "text": "#ecf0f1",
        "progress_background": "#2c3e50"
    }
}
```

#### 绿色主题
```python
green_theme = {
    "colors": {
        "primary": "#27ae60",
        "secondary": "#2ecc71"
    }
}
```

## 🎨 自定义外观

### 更改窗口大小
```python
config = {
    "layout": {
        "width": 600,
        "height": 400
    }
}
```

### 更改字体
```python
config = {
    "fonts": {
        "title_size": 18,
        "font_family": "Microsoft YaHei"
    }
}
```

### 禁用动画
```python
config = {
    "show_fade_in": False,
    "show_rotation_animation": False
}
```

## 🔧 集成到现有应用

### 方法1：替换原文件

```bash
# 备份原文件
mv core/splash_screen.py core/splash_screen_backup.py

# 使用修复版本
cp core/splash_screen_fixed.py core/splash_screen.py
```

### 方法2：修改导入

在需要使用启动画面的地方：

```python
try:
    from core.splash_screen_fixed import CustomSplashScreen, SplashScreenManager
except ImportError:
    from core.splash_screen import CustomSplashScreen, SplashScreenManager
```

## 📊 完整示例

```python
import sys
import time
from PySide6.QtWidgets import QApplication
from core.splash_screen_fixed import SplashScreenManager

def main():
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 自定义配置
    config = {
        "colors": {
            "primary": "#9b59b6",  # 紫色主题
            "secondary": "#3498db"
        },
        "layout": {
            "width": 520,
            "height": 360
        },
        "fonts": {
            "title_size": 18,
            "font_family": "Microsoft YaHei"
        }
    }
    
    # 创建启动画面管理器
    splash_manager = SplashScreenManager(config)
    
    # 显示启动画面
    splash = splash_manager.show_splash()
    
    # 模拟应用程序初始化
    init_steps = [
        (10, "初始化系统..."),
        (25, "加载配置文件..."),
        (40, "连接数据库..."),
        (55, "加载用户界面..."),
        (70, "初始化插件..."),
        (85, "启动服务..."),
        (100, "启动完成！")
    ]
    
    for progress, status in init_steps:
        splash_manager.update_progress_by_percentage(progress, status)
        time.sleep(0.8)  # 模拟耗时操作
        app.processEvents()
    
    # 等待一下再隐藏
    time.sleep(1)
    splash_manager.hide_splash()
    
    print("应用程序启动完成！")

if __name__ == "__main__":
    main()
```

## 🔍 故障排除

### 问题1：导入错误
**错误**: `ImportError: cannot import name 'pyqtSignal'`

**解决**: 使用修复版本 `core/splash_screen_fixed.py`

### 问题2：启动画面不显示
**可能原因**: 
- 配置中 `enabled` 为 `false`
- 应用程序没有事件循环

**解决**:
```python
# 确保配置正确
config = {"enabled": True}

# 确保处理事件
app.processEvents()
```

### 问题3：动画不流畅
**解决**: 减少动画频率或禁用动画
```python
config = {
    "show_rotation_animation": False,
    "fade_in_duration": 200
}
```

## 📚 API 参考

### CustomSplashScreen 类

#### 构造函数
```python
CustomSplashScreen(pixmap=None, config=None, parent=None)
```

#### 主要方法
- `update_progress(progress, status)` - 更新进度
- `show_with_fade_in(duration)` - 带淡入效果显示
- `hide_with_fade_out(duration)` - 带淡出效果隐藏

### SplashScreenManager 类

#### 构造函数
```python
SplashScreenManager(config=None)
```

#### 主要方法
- `show_splash()` - 显示启动画面
- `hide_splash()` - 隐藏启动画面
- `update_progress_by_percentage(percentage, status)` - 更新进度

## 🎯 最佳实践

1. **使用配置文件**: 将启动画面配置保存在JSON文件中
2. **错误处理**: 始终包含try-catch块处理导入错误
3. **性能考虑**: 在慢速设备上禁用动画
4. **用户体验**: 设置合适的最小显示时间
5. **测试**: 在不同分辨率下测试显示效果

## 🔄 版本信息

- **修复版本**: `core/splash_screen_fixed.py`
- **原版本**: `core/splash_screen.py`
- **测试脚本**: `test_fixed_splash.py`
- **状态**: ✅ 完全可用

---

🎉 **启动画面功能现在完全可用！使用修复版本即可正常工作。**
