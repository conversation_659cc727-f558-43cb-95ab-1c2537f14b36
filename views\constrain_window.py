"""
约束设置窗口模块

此模块定义了应用程序的约束设置窗口类，负责：
1. 显示约束设置界面
2. 处理约束参数输入验证
3. 管理监控点功能（tab_5）

作者: [作者名]
日期: [日期]
"""

import os
import json
import csv
import re
from datetime import datetime
from typing import List, Dict, Any, Optional

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (QMessageBox, QFileDialog, QTableWidgetItem,
                               QPushButton)
from PySide6.QtGui import QDoubleValidator

from ui import ui_constrain
from .base_window import BaseWindow


class ConstrainWindow(BaseWindow):
    """约束设置窗口类 - 包含监控点管理功能"""

    def __init__(self, window_manager):
        super().__init__(window_manager)
        self.ui = ui_constrain.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("约束设置")

        # 监控点数据存储
        self.monitor_points: List[Dict[str, Any]] = []
        self.point_counter = 0

        # 设置窗口样式
        self.setStyleSheet(self.styleSheet() + """
            QMainWindow::title {
                font-weight: bold;
                font-size: 14px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dadce0;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #333;
                background-color: white;
            }
            QLineEdit {
                border: 1px solid #dadce0;
                border-radius: 3px;
                padding: 4px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4285f4;
                outline: none;
            }
            QTableWidget {
                border: 1px solid #dadce0;
                border-radius: 3px;
                background-color: white;
                gridline-color: #e8eaed;
            }
            QTableWidget::item {
                padding: 4px;
                border-bottom: 1px solid #e8eaed;
            }
            QTableWidget::item:selected {
                background-color: #e8f0fe;
                color: #1a73e8;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                border: none;
                border-bottom: 1px solid #dadce0;
                padding: 4px;
                font-weight: bold;
                color: #333;
            }
        """)

        # 设置标签页标题
        self.ui.tabWidget.setTabText(0, "Force")
        self.ui.tabWidget.setTabText(1, "Displacement")
        self.ui.tabWidget.setTabText(2, "Remote Displacement")
        self.ui.tabWidget.setTabText(3, "Rotation")
        self.ui.tabWidget.setTabText(4, "监控点管理")
        self.ui.lineEdit_force.setReadOnly(True)
        self.ui.lineEdit_rotation_speed.setPlaceholderText("请输入旋转速度(r/min):")

        # 设置数值验证器
        double_validator = QDoubleValidator(0.0, 1e6, -1, self)
        for widget in [
            self.ui.lineEdit_rotation_speed
        ]:
            widget.setValidator(double_validator)
            widget.textChanged.connect(self.validate_input)

        # 设置监控点管理的输入验证器
        self._setup_monitor_validators()

        # 连接监控点管理信号槽
        self._connect_monitor_signals()

        # 初始化监控点管理界面状态
        self._init_monitor_ui_state()
        
        # 应用按钮动画效果
        self.setup_animated_buttons()

    def validate_input(self, text):
        """验证输入值"""
        sender = self.sender()
        try:
            value = float(text)
            if value > 1e6:
                sender.setText("1000000")
        except ValueError:
            pass

    def _setup_monitor_validators(self):
        """设置监控点管理的输入验证器"""
        # 为坐标输入框设置数值验证器
        validator = QDoubleValidator()
        validator.setDecimals(6)  # 允许6位小数
        validator.setNotation(QDoubleValidator.Notation.StandardNotation)

        self.ui.monitor_x_input.setValidator(validator)
        self.ui.monitor_y_input.setValidator(validator)
        self.ui.monitor_z_input.setValidator(validator)

    def _connect_monitor_signals(self):
        """连接监控点管理信号槽"""
        # 添加点位按钮
        self.ui.monitor_add_point_btn.clicked.connect(self._add_single_point)

        # 文件导入按钮
        self.ui.monitor_import_file_btn.clicked.connect(self._import_from_file)

        # 清空全部按钮
        self.ui.monitor_clear_all_btn.clicked.connect(self._clear_all_points)

        # 回车键添加点位
        self.ui.monitor_name_input.returnPressed.connect(self._add_single_point)
        self.ui.monitor_x_input.returnPressed.connect(self._add_single_point)
        self.ui.monitor_y_input.returnPressed.connect(self._add_single_point)
        self.ui.monitor_z_input.returnPressed.connect(self._add_single_point)

    def _init_monitor_ui_state(self):
        """初始化监控点管理界面状态"""
        # 设置表格初始状态
        self._update_monitor_table_display()

        # 设置焦点到名称输入框
        self.ui.monitor_name_input.setFocus()

    def _add_single_point(self):
        """添加单个监控点"""
        try:
            # 获取输入数据
            name = self.ui.monitor_name_input.text().strip()
            x_text = self.ui.monitor_x_input.text().strip()
            y_text = self.ui.monitor_y_input.text().strip()
            z_text = self.ui.monitor_z_input.text().strip()

            # 验证输入
            if not name:
                QMessageBox.warning(self, "输入错误", "请输入点位名称")
                self.ui.monitor_name_input.setFocus()
                return

            if not x_text or not y_text or not z_text:
                QMessageBox.warning(self, "输入错误", "请输入完整的坐标信息")
                return

            # 转换坐标值
            try:
                x = float(x_text)
                y = float(y_text)
                z = float(z_text)
            except ValueError:
                QMessageBox.warning(self, "输入错误", "坐标值必须是有效的数字")
                return

            # 检查名称是否重复
            if any(point['name'] == name for point in self.monitor_points):
                QMessageBox.warning(self, "输入错误", f"点位名称 '{name}' 已存在，请使用不同的名称")
                self.ui.monitor_name_input.setFocus()
                self.ui.monitor_name_input.selectAll()
                return

            # 创建监控点数据
            self.point_counter += 1
            point_data = {
                'id': self.point_counter,
                'name': name,
                'x': x,
                'y': y,
                'z': z,
                'created_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            # 添加到列表
            self.monitor_points.append(point_data)

            # 更新界面
            self._update_monitor_table_display()

            # 清空输入框
            self._clear_monitor_input_fields()

            # 显示成功消息
            self.statusBar().showMessage(f"成功添加监控点: {name}", 3000)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"添加监控点时发生错误: {str(e)}")

    def _import_from_file(self):
        """从文件批量导入监控点"""
        try:
            # 选择文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择监控点数据文件",
                "",
                "所有支持格式 (*.txt *.csv *.xlsx);;文本文件 (*.txt);;CSV文件 (*.csv);;Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 根据文件扩展名选择解析方法
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.txt':
                points = self._parse_txt_file(file_path)
            elif file_ext == '.csv':
                points = self._parse_csv_file(file_path)
            elif file_ext == '.xlsx':
                points = self._parse_xlsx_file(file_path)
            else:
                QMessageBox.warning(self, "文件格式错误", "不支持的文件格式")
                return

            if not points:
                QMessageBox.information(self, "导入结果", "文件中没有找到有效的监控点数据")
                return

            # 检查重复名称
            existing_names = {point['name'] for point in self.monitor_points}
            duplicate_names = []
            valid_points = []

            for point in points:
                if point['name'] in existing_names:
                    duplicate_names.append(point['name'])
                else:
                    existing_names.add(point['name'])
                    valid_points.append(point)

            # 显示重复名称警告
            if duplicate_names:
                msg = f"以下点位名称已存在，将被跳过:\n{', '.join(duplicate_names[:10])}"
                if len(duplicate_names) > 10:
                    msg += f"\n... 等共 {len(duplicate_names)} 个重复项"
                QMessageBox.warning(self, "重复名称", msg)

            if not valid_points:
                QMessageBox.information(self, "导入结果", "没有新的监控点可以导入")
                return

            # 添加有效点位
            for point in valid_points:
                self.point_counter += 1
                point['id'] = self.point_counter
                point['created_time'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.monitor_points.append(point)

            # 更新界面
            self._update_monitor_table_display()

            # 显示导入结果
            msg = f"成功导入 {len(valid_points)} 个监控点"
            if duplicate_names:
                msg += f"，跳过 {len(duplicate_names)} 个重复项"

            QMessageBox.information(self, "导入完成", msg)
            self.statusBar().showMessage(f"导入完成: {len(valid_points)} 个新监控点", 5000)

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入文件时发生错误: {str(e)}")

    def _parse_txt_file(self, file_path: str) -> List[Dict[str, Any]]:
        """解析TXT文件"""
        points = []
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    line = line.strip()

                    # 跳过空行和注释行
                    if not line or line.startswith('#'):
                        continue

                    # 尝试解析坐标数据
                    parts = re.split(r'[,\s]+', line)

                    if len(parts) == 3:
                        # 只有坐标，生成默认名称
                        try:
                            x, y, z = map(float, parts)
                            name = f"导入点_{len(points) + 1}"
                            points.append({'name': name, 'x': x, 'y': y, 'z': z})
                        except ValueError:
                            continue
                    elif len(parts) == 4:
                        # 有名称和坐标
                        try:
                            name = parts[0]
                            x, y, z = map(float, parts[1:4])
                            points.append({'name': name, 'x': x, 'y': y, 'z': z})
                        except ValueError:
                            continue
        except Exception as e:
            raise Exception(f"解析TXT文件失败: {str(e)}")

        return points

    def _parse_csv_file(self, file_path: str) -> List[Dict[str, Any]]:
        """解析CSV文件"""
        points = []
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                # 尝试自动检测分隔符
                sample = file.read(1024)
                file.seek(0)

                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.reader(file, delimiter=delimiter)

                for row_num, row in enumerate(reader, 1):
                    if not row or len(row) < 3:
                        continue

                    # 跳过标题行（如果第一行包含非数字）
                    if row_num == 1:
                        try:
                            float(row[-3])  # 尝试转换倒数第三个元素
                        except (ValueError, IndexError):
                            continue  # 跳过标题行

                    try:
                        if len(row) == 3:
                            # 只有坐标
                            x, y, z = map(float, row)
                            name = f"导入点_{len(points) + 1}"
                        elif len(row) >= 4:
                            # 有名称和坐标
                            name = row[0].strip()
                            x, y, z = map(float, row[1:4])
                        else:
                            continue

                        points.append({'name': name, 'x': x, 'y': y, 'z': z})
                    except ValueError:
                        continue

        except Exception as e:
            raise Exception(f"解析CSV文件失败: {str(e)}")

        return points

    def _parse_xlsx_file(self, file_path: str) -> List[Dict[str, Any]]:
        """解析Excel文件"""
        points = []
        try:
            # 尝试导入openpyxl
            try:
                from openpyxl import load_workbook
            except ImportError:
                raise Exception("需要安装 openpyxl 库来支持Excel文件。请运行: pip install openpyxl")

            workbook = load_workbook(file_path, read_only=True)
            worksheet = workbook.active

            for row_num, row in enumerate(worksheet.iter_rows(values_only=True), 1):
                if not row or len(row) < 3:
                    continue

                # 跳过标题行
                if row_num == 1 and any(isinstance(cell, str) and not cell.replace('.', '').replace('-', '').isdigit()
                                       for cell in row[-3:] if cell is not None):
                    continue

                try:
                    # 过滤None值
                    filtered_row = [cell for cell in row if cell is not None]

                    if len(filtered_row) == 3:
                        # 只有坐标
                        x, y, z = map(float, filtered_row)
                        name = f"导入点_{len(points) + 1}"
                    elif len(filtered_row) >= 4:
                        # 有名称和坐标
                        name = str(filtered_row[0]).strip()
                        x, y, z = map(float, filtered_row[1:4])
                    else:
                        continue

                    points.append({'name': name, 'x': x, 'y': y, 'z': z})
                except (ValueError, TypeError):
                    continue

            workbook.close()

        except Exception as e:
            raise Exception(f"解析Excel文件失败: {str(e)}")

        return points

    def _update_monitor_table_display(self):
        """更新监控点表格显示"""
        # 清空表格
        self.ui.monitor_points_table.setRowCount(0)

        # 添加数据行
        for i, point in enumerate(self.monitor_points):
            self.ui.monitor_points_table.insertRow(i)

            # 序号
            self.ui.monitor_points_table.setItem(i, 0, QTableWidgetItem(str(point['id'])))

            # 点位名称
            self.ui.monitor_points_table.setItem(i, 1, QTableWidgetItem(point['name']))

            # 坐标
            self.ui.monitor_points_table.setItem(i, 2, QTableWidgetItem(f"{point['x']:.6f}"))
            self.ui.monitor_points_table.setItem(i, 3, QTableWidgetItem(f"{point['y']:.6f}"))
            self.ui.monitor_points_table.setItem(i, 4, QTableWidgetItem(f"{point['z']:.6f}"))

            # 删除按钮
            delete_btn = QPushButton("🗑️")
            delete_btn.setToolTip(f"删除监控点: {point['name']}")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ea4335;
                    color: white;
                    border: none;
                    border-radius: 2px;
                    padding: 2px 6px;
                    font-size: 10px;
                }
                QPushButton:hover {
                    background-color: #d33b2c;
                }
                QPushButton:pressed {
                    background-color: #b52d20;
                }
            """)
            delete_btn.clicked.connect(lambda checked, idx=i: self._delete_monitor_point(idx))
            self.ui.monitor_points_table.setCellWidget(i, 5, delete_btn)

            # 设置行数据为只读（除了操作列）
            for col in range(5):
                item = self.ui.monitor_points_table.item(i, col)
                if item:
                    item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

        # 调整列宽
        self.ui.monitor_points_table.resizeColumnsToContents()

        # 更新组标题
        count = len(self.monitor_points)
        self.ui.monitor_points_list_group.setTitle(f"已创建监控点列表 (共 {count} 个)")

    def _delete_monitor_point(self, index: int):
        """删除指定索引的监控点"""
        if 0 <= index < len(self.monitor_points):
            point = self.monitor_points[index]

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除监控点 '{point['name']}' 吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                # 删除数据
                del self.monitor_points[index]

                # 更新界面
                self._update_monitor_table_display()

                self.statusBar().showMessage(f"已删除监控点: {point['name']}", 3000)

    def _clear_all_points(self):
        """清空所有监控点"""
        if not self.monitor_points:
            QMessageBox.information(self, "提示", "当前没有监控点需要清空")
            return

        # 确认清空
        reply = QMessageBox.question(
            self,
            "确认清空",
            f"确定要清空所有 {len(self.monitor_points)} 个监控点吗？\n此操作不可撤销！",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 清空数据
            self.monitor_points.clear()
            self.point_counter = 0

            # 更新界面
            self._update_monitor_table_display()

            self.statusBar().showMessage("已清空所有监控点", 3000)

    def _clear_monitor_input_fields(self):
        """清空监控点输入框"""
        self.ui.monitor_name_input.clear()
        self.ui.monitor_x_input.clear()
        self.ui.monitor_y_input.clear()
        self.ui.monitor_z_input.clear()
        self.ui.monitor_name_input.setFocus()

    def get_monitor_points(self) -> List[Dict[str, Any]]:
        """获取当前所有监控点数据"""
        return self.monitor_points.copy()

    def set_monitor_points(self, points: List[Dict[str, Any]]):
        """设置监控点数据"""
        self.monitor_points = points.copy()

        # 更新计数器
        if self.monitor_points:
            self.point_counter = max(point.get('id', 0) for point in self.monitor_points)
        else:
            self.point_counter = 0

        # 更新界面
        self._update_monitor_table_display()

    def save_monitor_points_to_file(self, file_path: str):
        """保存监控点到文件"""
        try:
            data = {
                'monitor_points': self.monitor_points,
                'created_time': datetime.now().isoformat(),
                'total_count': len(self.monitor_points)
            }

            with open(file_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, ensure_ascii=False, indent=2)

        except Exception as e:
            raise Exception(f"保存文件失败: {str(e)}")

    def load_monitor_points_from_file(self, file_path: str):
        """从文件加载监控点"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            if 'monitor_points' in data:
                self.set_monitor_points(data['monitor_points'])
            else:
                raise Exception("文件格式不正确")

        except Exception as e:
            raise Exception(f"加载文件失败: {str(e)}")

    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.push_finish,
            self.ui.push_mainui,
            self.ui.push_analysisui,
            self.ui.push_resultui,
            self.ui.pushButton_force
        ]
        
        # 保存按钮的点击处理函数
        from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step
        from window_manager import WindowType
        # 注意：不在这里连接finish和force按钮，避免重复连接
        # finish和force按钮的连接在ctrl/constrain_slot.py中统一处理
        # 使用统一的导航管理器
        mainui_handler = lambda: navigate_to_main_menu(self.window_manager)
        analysisui_handler = lambda: navigate_to_previous_step(self.window_manager, WindowType.CONSTRAIN)  # 上一步(分析设置)
        resultui_handler = lambda: navigate_to_next_step(self.window_manager, WindowType.CONSTRAIN)  # 下一步(网格验证)

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        # 重新连接信号（不包括finish、force和resultui按钮）
        # 这些按钮的连接在ctrl/constrain_slot.py中统一处理，避免重复连接
        # self.ui.push_finish.clicked.connect(finish_handler)  # 移除重复连接
        self.ui.push_mainui.clicked.connect(mainui_handler)
        self.ui.push_analysisui.clicked.connect(analysisui_handler)
        # self.ui.push_resultui.clicked.connect(resultui_handler)  # 移除重复连接，在slot中处理
        # self.ui.pushButton_force.clicked.connect(force_file_handler)  # 移除重复连接