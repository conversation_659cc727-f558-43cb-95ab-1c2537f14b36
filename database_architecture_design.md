# 数据库架构设计文档

## 🎯 架构概述

### **混合数据库架构**
采用PostgreSQL + Redis + 文件存储的混合架构，充分发挥各种存储技术的优势。

```
应用层 (PySide6/Qt)
    ↓
业务逻辑层 (Python Services)
    ↓
数据访问层 (SQLAlchemy ORM + Repository)
    ↓
数据存储层
├── PostgreSQL (结构化数据)
├── Redis (缓存和会话)
└── 文件系统 (大文件存储)
```

## 🗄️ 数据库选型决策

### **技术选型对比矩阵**

| 特性 | PostgreSQL | MySQL | MongoDB | SQLite |
|------|------------|-------|---------|---------|
| **工程数据支持** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ |
| **数值计算** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ |
| **JSON支持** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **扩展性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐ |
| **ACID特性** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Python生态** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **运维复杂度** | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| **总分** | **32/35** | **26/35** | **25/35** | **21/35** |

### **PostgreSQL选择理由**
```
技术优势:
├── 原生支持JSON/JSONB数据类型
├── 丰富的数值数据类型和函数
├── 强大的索引支持(B-tree, GiST, GIN等)
├── 优秀的并发控制(MVCC)
└── 成熟的Python生态支持

工程数据适配:
├── 支持数组和复合数据类型
├── 自定义数据类型和函数
├── 地理空间数据支持(PostGIS)
├── 全文搜索功能
└── 窗口函数和分析功能
```

## 📊 数据模型设计

### **核心实体关系图**
```
Projects (项目)
    ↓ 1:N
MeshConfigurations (网格配置)
    ↓ 1:N
AnalysisRuns (分析运行)
    ↓ 1:N
Results (结果)
    ↓ 1:N
ResultFiles (结果文件)

Users (用户)
    ↓ 1:N
Projects (项目)
    ↓ 1:N
UserPreferences (用户偏好)
```

### **详细表结构设计**

#### **1. 项目管理表**
```sql
-- 项目表
CREATE TABLE projects (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(50) DEFAULT 'active',
    metadata JSONB,
    CONSTRAINT unique_project_name_per_user UNIQUE(name, created_by)
);

-- 项目索引
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_at ON projects(created_at);
CREATE INDEX idx_projects_metadata_gin ON projects USING GIN(metadata);
```

#### **2. 网格配置表**
```sql
-- 网格配置表
CREATE TABLE mesh_configurations (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    mesh_size DECIMAL(10,6),
    element_type VARCHAR(50),
    node_count INTEGER,
    element_count INTEGER,
    mesh_file_path TEXT,
    mesh_parameters JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_mesh_name_per_project UNIQUE(name, project_id)
);

-- 网格配置索引
CREATE INDEX idx_mesh_project_id ON mesh_configurations(project_id);
CREATE INDEX idx_mesh_size ON mesh_configurations(mesh_size);
CREATE INDEX idx_mesh_element_type ON mesh_configurations(element_type);
CREATE INDEX idx_mesh_parameters_gin ON mesh_configurations USING GIN(mesh_parameters);
```

#### **3. 分析运行表**
```sql
-- 分析运行表
CREATE TABLE analysis_runs (
    id SERIAL PRIMARY KEY,
    project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
    mesh_config_id INTEGER REFERENCES mesh_configurations(id),
    analysis_type VARCHAR(100) NOT NULL,
    parameters JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    computation_time INTERVAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 分析运行索引
CREATE INDEX idx_analysis_project_id ON analysis_runs(project_id);
CREATE INDEX idx_analysis_mesh_config_id ON analysis_runs(mesh_config_id);
CREATE INDEX idx_analysis_type ON analysis_runs(analysis_type);
CREATE INDEX idx_analysis_status ON analysis_runs(status);
CREATE INDEX idx_analysis_created_at ON analysis_runs(created_at);
```

#### **4. 结果数据表**
```sql
-- 模态分析结果表
CREATE TABLE modal_results (
    id SERIAL PRIMARY KEY,
    analysis_run_id INTEGER REFERENCES analysis_runs(id) ON DELETE CASCADE,
    mode_number INTEGER NOT NULL,
    frequency DECIMAL(15,6) NOT NULL,
    damping_ratio DECIMAL(10,6),
    mode_shape_data JSONB,
    participation_factors JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_mode_per_analysis UNIQUE(analysis_run_id, mode_number)
);

-- 频率响应结果表
CREATE TABLE frequency_response_results (
    id SERIAL PRIMARY KEY,
    analysis_run_id INTEGER REFERENCES analysis_runs(id) ON DELETE CASCADE,
    frequency DECIMAL(15,6) NOT NULL,
    response_data JSONB NOT NULL,
    phase_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 结果索引
CREATE INDEX idx_modal_analysis_run_id ON modal_results(analysis_run_id);
CREATE INDEX idx_modal_frequency ON modal_results(frequency);
CREATE INDEX idx_modal_mode_number ON modal_results(mode_number);
CREATE INDEX idx_freq_response_analysis_run_id ON frequency_response_results(analysis_run_id);
CREATE INDEX idx_freq_response_frequency ON frequency_response_results(frequency);
```

#### **5. 文件管理表**
```sql
-- 结果文件表
CREATE TABLE result_files (
    id SERIAL PRIMARY KEY,
    analysis_run_id INTEGER REFERENCES analysis_runs(id) ON DELETE CASCADE,
    file_type VARCHAR(100) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    checksum VARCHAR(64),
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文件索引
CREATE INDEX idx_files_analysis_run_id ON result_files(analysis_run_id);
CREATE INDEX idx_files_file_type ON result_files(file_type);
CREATE INDEX idx_files_created_at ON result_files(created_at);
```

#### **6. 用户和权限表**
```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP
);

-- 用户偏好表
CREATE TABLE user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_preference UNIQUE(user_id, preference_key)
);
```

### **数据分区策略**
```sql
-- 按时间分区的结果表
CREATE TABLE modal_results_partitioned (
    LIKE modal_results INCLUDING ALL
) PARTITION BY RANGE (created_at);

-- 创建月度分区
CREATE TABLE modal_results_2024_01 PARTITION OF modal_results_partitioned
    FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');

CREATE TABLE modal_results_2024_02 PARTITION OF modal_results_partitioned
    FOR VALUES FROM ('2024-02-01') TO ('2024-03-01');

-- 自动分区管理函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    end_date date;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;
```

## 🏗️ 数据访问层架构

### **ORM选择：SQLAlchemy 2.0**
```python
# 核心配置
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import QueuePool

# 数据库连接配置
DATABASE_URL = "postgresql://user:password@localhost:5432/vibration_db"

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
    echo=False  # 生产环境设为False
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()
```

### **模型定义示例**
```python
from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    status = Column(String(50), default="active")
    metadata = Column(JSONB)
    
    # 关系定义
    creator = relationship("User", back_populates="projects")
    mesh_configurations = relationship("MeshConfiguration", back_populates="project", cascade="all, delete-orphan")
    analysis_runs = relationship("AnalysisRun", back_populates="project", cascade="all, delete-orphan")

class MeshConfiguration(Base):
    __tablename__ = "mesh_configurations"
    
    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"))
    name = Column(String(255), nullable=False)
    mesh_size = Column(DECIMAL(10, 6))
    element_type = Column(String(50))
    node_count = Column(Integer)
    element_count = Column(Integer)
    mesh_file_path = Column(Text)
    mesh_parameters = Column(JSONB)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系定义
    project = relationship("Project", back_populates="mesh_configurations")
    analysis_runs = relationship("AnalysisRun", back_populates="mesh_configuration")
```

### **Repository模式实现**
```python
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

class BaseRepository(ABC):
    def __init__(self, db: Session):
        self.db = db
    
    @abstractmethod
    def create(self, obj_in: Dict[str, Any]) -> Any:
        pass
    
    @abstractmethod
    def get(self, id: int) -> Optional[Any]:
        pass
    
    @abstractmethod
    def get_multi(self, skip: int = 0, limit: int = 100) -> List[Any]:
        pass
    
    @abstractmethod
    def update(self, db_obj: Any, obj_in: Dict[str, Any]) -> Any:
        pass
    
    @abstractmethod
    def delete(self, id: int) -> Any:
        pass

class ProjectRepository(BaseRepository):
    def create(self, obj_in: Dict[str, Any]) -> Project:
        db_obj = Project(**obj_in)
        self.db.add(db_obj)
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def get(self, id: int) -> Optional[Project]:
        return self.db.query(Project).filter(Project.id == id).first()
    
    def get_by_name_and_user(self, name: str, user_id: int) -> Optional[Project]:
        return self.db.query(Project).filter(
            Project.name == name,
            Project.created_by == user_id
        ).first()
    
    def get_multi(self, skip: int = 0, limit: int = 100) -> List[Project]:
        return self.db.query(Project).offset(skip).limit(limit).all()
    
    def get_by_user(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Project]:
        return self.db.query(Project).filter(
            Project.created_by == user_id
        ).offset(skip).limit(limit).all()
    
    def update(self, db_obj: Project, obj_in: Dict[str, Any]) -> Project:
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        db_obj.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(db_obj)
        return db_obj
    
    def delete(self, id: int) -> Project:
        obj = self.db.query(Project).get(id)
        self.db.delete(obj)
        self.db.commit()
        return obj
```

### **服务层设计**
```python
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session

class ProjectService:
    def __init__(self, db: Session):
        self.db = db
        self.project_repo = ProjectRepository(db)
        self.mesh_repo = MeshConfigurationRepository(db)
    
    def create_project(self, project_data: Dict[str, Any], user_id: int) -> Project:
        # 验证项目名称唯一性
        existing = self.project_repo.get_by_name_and_user(
            project_data["name"], user_id
        )
        if existing:
            raise ValueError(f"Project '{project_data['name']}' already exists")
        
        # 创建项目
        project_data["created_by"] = user_id
        return self.project_repo.create(project_data)
    
    def get_user_projects(self, user_id: int, skip: int = 0, limit: int = 100) -> List[Project]:
        return self.project_repo.get_by_user(user_id, skip, limit)
    
    def add_mesh_configuration(self, project_id: int, mesh_data: Dict[str, Any]) -> MeshConfiguration:
        # 验证项目存在
        project = self.project_repo.get(project_id)
        if not project:
            raise ValueError(f"Project {project_id} not found")
        
        # 创建网格配置
        mesh_data["project_id"] = project_id
        return self.mesh_repo.create(mesh_data)
```

## 🚀 缓存策略设计

### **Redis缓存架构**
```python
import redis
from typing import Optional, Any
import json
import pickle

class CacheManager:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url, decode_responses=False)
        self.default_ttl = 3600  # 1小时
    
    def get(self, key: str) -> Optional[Any]:
        try:
            data = self.redis_client.get(key)
            if data:
                return pickle.loads(data)
        except Exception as e:
            logging.error(f"Cache get error: {e}")
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        try:
            ttl = ttl or self.default_ttl
            data = pickle.dumps(value)
            return self.redis_client.setex(key, ttl, data)
        except Exception as e:
            logging.error(f"Cache set error: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logging.error(f"Cache delete error: {e}")
            return False
    
    def get_or_set(self, key: str, func, ttl: Optional[int] = None) -> Any:
        # 尝试从缓存获取
        result = self.get(key)
        if result is not None:
            return result
        
        # 缓存未命中，执行函数并缓存结果
        result = func()
        self.set(key, result, ttl)
        return result

# 缓存装饰器
def cached(key_pattern: str, ttl: int = 3600):
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache = CacheManager()
            # 生成缓存键
            key = key_pattern.format(*args, **kwargs)
            
            return cache.get_or_set(
                key, 
                lambda: func(*args, **kwargs), 
                ttl
            )
        return wrapper
    return decorator

# 使用示例
@cached("project:{0}:meshes", ttl=1800)
def get_project_mesh_configurations(project_id: int):
    # 数据库查询逻辑
    pass
```

### **多级缓存策略**
```python
class MultiLevelCache:
    def __init__(self):
        self.l1_cache = {}  # 内存缓存
        self.l1_max_size = 1000
        self.l2_cache = CacheManager()  # Redis缓存
    
    def get(self, key: str) -> Optional[Any]:
        # L1缓存查找
        if key in self.l1_cache:
            return self.l1_cache[key]
        
        # L2缓存查找
        value = self.l2_cache.get(key)
        if value is not None:
            # 回填L1缓存
            self._set_l1(key, value)
            return value
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None):
        # 设置L1和L2缓存
        self._set_l1(key, value)
        self.l2_cache.set(key, value, ttl)
    
    def _set_l1(self, key: str, value: Any):
        # LRU淘汰策略
        if len(self.l1_cache) >= self.l1_max_size:
            # 删除最旧的条目
            oldest_key = next(iter(self.l1_cache))
            del self.l1_cache[oldest_key]
        
        self.l1_cache[key] = value
```

---

## 📝 总结

本数据库架构设计文档提供了完整的技术方案，包括：

**核心特性**:
- ✅ 混合数据库架构设计
- ✅ 完整的数据模型定义
- ✅ 灵活的数据访问层
- ✅ 高效的缓存策略
- ✅ 可扩展的架构设计

**技术优势**:
- 🚀 高性能数据访问
- 🔒 数据一致性保证
- 📈 良好的扩展性
- 🛡️ 健壮的错误处理
- 🔧 易于维护和扩展
