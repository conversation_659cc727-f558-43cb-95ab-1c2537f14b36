<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化支持 - 多语言本地化系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://rsms.me/">
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e3a8a;
            --background-color: #f0f4f8;
            --card-background: #ffffff;
            --text-color: #1f2937;
            --subtle-text-color: #6b7280;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
        }
        .section-card {
            background-color: var(--card-background);
            border-radius: 1rem;
            box-shadow: 0 10px 25px -5px rgba(0,0,0,0.05), 0 4px 6px -2px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 30px -10px rgba(60, 90, 153, 0.15);
        }
        .scroll-reveal {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .scroll-reveal.visible {
            opacity: 1;
            transform: translateY(0);
        }
        .feature-accordion summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            padding: 1.25rem;
            border-radius: 0.75rem;
            transition: background-color 0.2s ease;
        }
        .feature-accordion summary:hover {
            background-color: #f9fafb;
        }
        .feature-accordion summary::after {
            content: '+';
            font-size: 1.5rem;
            font-weight: 300;
            transition: transform 0.3s ease;
        }
        .feature-accordion[open] summary::after {
            transform: rotate(45deg);
        }
        .feature-accordion[open] {
            background-color: var(--card-background);
        }
        .feature-accordion-content {
            padding: 0 1.25rem 1.25rem 1.25rem;
        }
        .language-flag {
            width: 24px;
            height: 18px;
            border-radius: 2px;
            display: inline-block;
            margin-right: 8px;
        }
        .flag-cn { background: linear-gradient(to right, #de2910 0%, #de2910 100%); }
        .flag-en { background: linear-gradient(to right, #012169 0%, #012169 33%, #ffffff 33%, #ffffff 66%, #c8102e 66%, #c8102e 100%); }
        .flag-jp { background: linear-gradient(to right, #ffffff 0%, #ffffff 100%); position: relative; }
        .flag-jp::after { content: ''; position: absolute; top: 3px; left: 6px; width: 12px; height: 12px; background: #bc002d; border-radius: 50%; }
    </style>
</head>
<body class="antialiased">
    <!-- Header -->
    <header class="relative overflow-hidden bg-gradient-to-br from-indigo-600 to-purple-800 text-white py-16 md:py-24">
        <div class="container mx-auto px-6 relative z-10">
            <nav class="mb-8">
                <a href="index.html" class="inline-flex items-center text-indigo-200 hover:text-white transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    返回首页
                </a>
            </nav>
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-extrabold tracking-tighter leading-tight mb-4">
                    🌍 国际化支持
                </h1>
                <p class="text-lg md:text-xl max-w-3xl mx-auto text-indigo-100">
                    多语言本地化系统 | 全球化软件解决方案
                </p>
                <div class="mt-6">
                    <span class="inline-block bg-indigo-500 bg-opacity-20 text-indigo-100 text-sm font-semibold px-4 py-2 rounded-full border border-indigo-400">
                        🇨🇳 中文 | 🇺🇸 English | 🇯🇵 日本語 | 🔄 动态切换
                    </span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8 -mt-16">
        
        <!-- I18n Overview -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🌐 国际化概述</h2>
            <p class="text-gray-600 mb-6 text-lg leading-relaxed">
                振动传递计算软件支持多语言界面，为全球用户提供本地化体验。系统采用现代化的国际化框架，支持动态语言切换和文化适配。
            </p>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-blue-800">多语言支持</h3>
                    </div>
                    <p class="text-sm text-blue-600">中文、英文、日文界面</p>
                </div>
                
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-green-800">动态切换</h3>
                    </div>
                    <p class="text-sm text-green-600">无需重启即可切换语言</p>
                </div>
                
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-purple-800">文化适配</h3>
                    </div>
                    <p class="text-sm text-purple-600">日期、数字格式本地化</p>
                </div>
                
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="flex items-center mb-3">
                        <div class="w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                            </svg>
                        </div>
                        <h3 class="font-semibold text-orange-800">扩展性</h3>
                    </div>
                    <p class="text-sm text-orange-600">支持添加新语言</p>
                </div>
            </div>
        </section>

        <!-- Supported Languages -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">🗣️ 支持的语言</h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-red-50 to-orange-50 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <span class="language-flag flag-cn"></span>
                        <h3 class="text-xl font-semibold text-gray-800">简体中文</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">语言代码:</span>
                            <span class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">zh-CN</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">完成度:</span>
                            <span class="text-sm font-semibold text-green-600">100%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">状态:</span>
                            <span class="text-sm font-semibold text-blue-600">默认语言</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <span class="language-flag flag-en"></span>
                        <h3 class="text-xl font-semibold text-gray-800">English</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">语言代码:</span>
                            <span class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">en-US</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">完成度:</span>
                            <span class="text-sm font-semibold text-yellow-600">85%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">状态:</span>
                            <span class="text-sm font-semibold text-green-600">可用</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-gradient-to-r from-pink-50 to-red-50 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <span class="language-flag flag-jp"></span>
                        <h3 class="text-xl font-semibold text-gray-800">日本語</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">语言代码:</span>
                            <span class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">ja-JP</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">完成度:</span>
                            <span class="text-sm font-semibold text-orange-600">60%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">状态:</span>
                            <span class="text-sm font-semibold text-purple-600">开发中</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section-card p-6 md:p-8 mb-12 scroll-reveal">
            <h2 class="text-3xl font-bold text-gray-800 mb-6">❓ 常见问题</h2>

            <div class="space-y-4">
                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔄</span>如何切换界面语言？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">软件提供多种语言切换方式，操作简单便捷。</p>
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">切换步骤：</h4>
                            <ul class="text-sm text-blue-600 space-y-1">
                                <li>• 方法1：主菜单 → 设置 → 语言 → 选择目标语言</li>
                                <li>• 方法2：按下快捷键 Ctrl+Shift+L</li>
                                <li>• 方法3：点击工具栏的语言图标</li>
                                <li>• 语言切换立即生效，无需重启软件</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🌍</span>支持哪些语言？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">目前支持三种主要语言，未来将扩展更多语言。</p>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 mb-2">语言支持状态：</h4>
                            <ul class="text-sm text-green-600 space-y-1">
                                <li>• 简体中文 (zh-CN) - 100% 完成，默认语言</li>
                                <li>• English (en-US) - 85% 完成，可正常使用</li>
                                <li>• 日本語 (ja-JP) - 60% 完成，开发中</li>
                                <li>• 更多语言正在规划中</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">⚙️</span>语言设置会保存吗？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">是的，语言设置会自动保存并在下次启动时生效。</p>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-purple-800 mb-2">设置保存机制：</h4>
                            <ul class="text-sm text-purple-600 space-y-1">
                                <li>• 语言选择自动保存到配置文件</li>
                                <li>• 下次启动软件时自动应用</li>
                                <li>• 支持用户配置文件备份和恢复</li>
                                <li>• 可以重置为系统默认语言</li>
                            </ul>
                        </div>
                    </div>
                </details>

                <details class="feature-accordion section-card overflow-hidden">
                    <summary class="font-bold text-lg text-gray-800">
                        <span class="flex items-center">
                            <span class="text-2xl mr-4">🔧</span>如何添加新的语言支持？
                        </span>
                    </summary>
                    <div class="feature-accordion-content text-gray-600">
                        <p class="mb-3">软件采用模块化的国际化架构，支持扩展新语言。</p>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">扩展步骤：</h4>
                            <ul class="text-sm text-orange-600 space-y-1">
                                <li>• 创建新的语言资源文件</li>
                                <li>• 翻译所有界面文本和消息</li>
                                <li>• 配置本地化格式（日期、数字等）</li>
                                <li>• 测试和验证翻译质量</li>
                                <li>• 联系开发团队提交语言包</li>
                            </ul>
                        </div>
                    </div>
                </details>
            </div>
        </section>

        <!-- Important Notes -->
        <section class="grid md:grid-cols-2 gap-8 mb-12 scroll-reveal">
            <div class="section-card p-6 border-l-4 border-red-400">
                <h3 class="text-xl font-bold text-red-800 mb-4">⚠️ 注意事项</h3>
                <ul class="space-y-2 text-sm text-red-700">
                    <li>• 部分语言可能存在翻译不完整的情况</li>
                    <li>• 语言切换后某些对话框需要重新打开</li>
                    <li>• 帮助文档可能不支持所有语言</li>
                    <li>• 数字格式切换可能影响数据显示</li>
                    <li>• 建议在重要操作前确认语言设置</li>
                </ul>
            </div>

            <div class="section-card p-6 border-l-4 border-blue-400">
                <h3 class="text-xl font-bold text-blue-800 mb-4">💡 使用建议</h3>
                <ul class="space-y-2 text-sm text-blue-700">
                    <li>• 首次使用时选择最熟悉的语言</li>
                    <li>• 定期检查语言包更新</li>
                    <li>• 遇到翻译问题可切换到中文界面</li>
                    <li>• 备份个人配置文件</li>
                    <li>• 向开发团队反馈翻译建议</li>
                </ul>
            </div>
        </section>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8">
        <div class="container mx-auto px-6 text-center">
            <p class="text-gray-300">© 2025 振动传递计算软件团队 |
                <a href="mailto:<EMAIL>" class="text-blue-400 hover:text-blue-300 transition duration-300">技术支持</a>
            </p>
            <p class="text-gray-400 text-sm mt-2">多语言本地化系统 - 全球化软件解决方案</p>
        </div>
    </footer>

    <!-- Scroll Reveal Animation Script -->
    <script>
        // Scroll reveal animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
    </script>
</body>
</html>
