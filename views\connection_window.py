"""
连接设置窗口模块

此模块定义了应用程序的连接设置窗口类，负责：
1. 显示连接设置界面
2. 处理连接参数设置

作者: [作者名]
日期: [日期]
"""

from ui import ui_connection
from .base_window import BaseWindow


class ConnectionWindow(BaseWindow):
    """连接设置窗口类"""
    def __init__(self, window_manager):
        super().__init__(window_manager)
        self.ui = ui_connection.Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle("连接设置")
        
        # 设置窗口样式
        self.setStyleSheet(self.styleSheet() + """
            QMainWindow::title {
                font-weight: bold;
                font-size: 14px;
            }
        """)
        
        # 设置标签页标题
        self.ui.tabWidget.setTabText(0, "bearing")
        self.ui.tabWidget.setTabText(1, "bushing")
        self.ui.tabWidget_2.setTabText(0, "bushing1-2")
        self.ui.tabWidget_2.setTabText(1, "bushing3-4")
        
        # 应用按钮动画效果
        self.setup_animated_buttons()
    
    def setup_animated_buttons(self):
        """为窗口中的按钮添加动画效果"""
        buttons = [
            self.ui.push_generateconnection,
            self.ui.push_meshui,
            self.ui.push_analysisui,
            self.ui.push_mainui
        ]
        
        # 保存按钮的点击处理函数
        from core.navigation_manager import navigate_to_main_menu, navigate_to_next_step, navigate_to_previous_step
        from window_manager import WindowType
        # 注意：不在这里连接generateconnection按钮，避免重复连接
        # generateconnection按钮的连接在ctrl/connection_slot.py中统一处理
        # 使用统一的导航管理器
        meshui_handler = lambda: navigate_to_previous_step(self.window_manager, WindowType.CONNECTION)  # 上一步(前处理)
        analysisui_handler = lambda: navigate_to_next_step(self.window_manager, WindowType.CONNECTION)  # 下一步(分析设置)
        mainui_handler = lambda: navigate_to_main_menu(self.window_manager)

        # 应用动画效果
        self.apply_animated_buttons(buttons)

        # 重新连接信号（不包括generateconnection按钮）
        # self.ui.push_generateconnection.clicked.connect(generateconnection_handler)  # 移除重复连接
        self.ui.push_meshui.clicked.connect(meshui_handler)
        self.ui.push_analysisui.clicked.connect(analysisui_handler)
        self.ui.push_mainui.clicked.connect(mainui_handler)