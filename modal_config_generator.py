"""
模态分析配置生成器

此模块实现从现有网格界面参数到 modal.py 脚本所需配置文件的转换。
基于对 originscript/modal.py 的分析，生成符合 IronPython 脚本期望的配置格式。

作者: 振动传递计算软件开发团队
日期: 2025-01-28
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# 假设这些是现有的模块
# from core.mesh_manager import MeshManager
# from core.config_manager import ConfigManager
# from models.mesh_parameter import MeshParameter

logger = logging.getLogger(__name__)

class ModalConfigGenerator:
    """
    模态分析配置生成器
    
    负责从现有的复杂网格配置中提取 modal.py 脚本所需的简单参数，
    并生成符合 IronPython 脚本期望格式的配置文件。
    """
    
    def __init__(self, mesh_manager=None, config_manager=None):
        """
        初始化配置生成器
        
        Args:
            mesh_manager: 网格管理器实例
            config_manager: 配置管理器实例
        """
        self.mesh_manager = mesh_manager
        self.config_manager = config_manager
        
        # modal.py 脚本期望的默认配置路径
        self.default_modal_config_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
        
    def generate_modal_config(self, output_path: Optional[str] = None) -> bool:
        """
        生成 modal.py 专用的配置文件
        
        Args:
            output_path: 输出文件路径，如果为None则使用默认路径
            
        Returns:
            bool: 生成成功返回True，否则返回False
        """
        try:
            if output_path is None:
                output_path = self.default_modal_config_path
                
            logger.info(f"开始生成模态分析配置文件: {output_path}")
            
            # 1. 提取网格尺寸列表
            element_sizes = self._extract_element_sizes()
            if not element_sizes:
                logger.warning("未找到有效的网格尺寸，使用默认值")
                element_sizes = [0.001, 0.002, 0.005, 0.01, 0.02]  # 默认尺寸列表
            
            # 2. 确定输出目录
            output_directory = self._get_output_directory()
            
            # 3. 生成配置
            modal_config = {
                "element_size": element_sizes,
                "output_directory": output_directory
            }
            
            # 4. 确保输出目录存在
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # 5. 保存配置文件
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(modal_config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"模态分析配置文件生成成功")
            logger.info(f"  - 网格尺寸数量: {len(element_sizes)}")
            logger.info(f"  - 尺寸范围: {min(element_sizes):.4f}m - {max(element_sizes):.4f}m")
            logger.info(f"  - 输出目录: {output_directory}")
            
            return True
            
        except Exception as e:
            logger.error(f"生成模态配置失败: {str(e)}", exc_info=True)
            return False
    
    def _extract_element_sizes(self) -> List[float]:
        """
        从网格管理器中提取网格尺寸列表（转换为米）
        
        Returns:
            List[float]: 网格尺寸列表，单位为米
        """
        try:
            sizes = []
            
            if self.mesh_manager is None:
                logger.warning("网格管理器未初始化，无法提取网格尺寸")
                return sizes
            
            # 获取所有网格参数
            all_meshes = self.mesh_manager.get_all_meshes()
            logger.debug(f"找到 {len(all_meshes)} 个网格参数")
            
            for mesh in all_meshes:
                if hasattr(mesh, 'size') and mesh.size > 0:
                    # 转换单位：mm -> m
                    size_in_meters = mesh.size / 1000.0
                    sizes.append(size_in_meters)
                    logger.debug(f"网格 '{mesh.name}': {mesh.size}mm -> {size_in_meters}m")
                else:
                    logger.warning(f"网格 '{getattr(mesh, 'name', 'Unknown')}' 尺寸无效: {getattr(mesh, 'size', 'N/A')}")
            
            # 去重并排序
            unique_sizes = sorted(list(set(sizes)))
            
            if len(unique_sizes) != len(sizes):
                logger.info(f"去重后网格尺寸数量: {len(sizes)} -> {len(unique_sizes)}")
            
            return unique_sizes
            
        except Exception as e:
            logger.error(f"提取网格尺寸失败: {str(e)}", exc_info=True)
            return []
    
    def _get_output_directory(self) -> str:
        """
        获取模态分析结果的输出目录
        
        Returns:
            str: 输出目录路径
        """
        try:
            # 尝试从配置管理器获取工作目录
            if self.config_manager:
                work_dir = self.config_manager.get("ansys.work_dir", "")
                if work_dir and os.path.exists(work_dir):
                    output_dir = os.path.join(work_dir, "modal_results")
                    logger.debug(f"使用配置的工作目录: {output_dir}")
                    return output_dir
            
            # 使用默认目录
            default_dir = r"D:/data/all-XM/autoworkbench/csdaima/modal_results"
            logger.debug(f"使用默认输出目录: {default_dir}")
            return default_dir
            
        except Exception as e:
            logger.error(f"获取输出目录失败: {str(e)}", exc_info=True)
            # 返回安全的默认值
            return r"D:/data/all-XM/autoworkbench/csdaima/modal_results"
    
    def validate_modal_config(self, config_path: str) -> Dict[str, Any]:
        """
        验证生成的模态配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            Dict[str, Any]: 验证结果，包含 'valid' 和 'errors' 字段
        """
        result = {
            'valid': False,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 检查文件是否存在
            if not os.path.exists(config_path):
                result['errors'].append(f"配置文件不存在: {config_path}")
                return result
            
            # 读取配置文件
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 验证必需字段
            required_fields = ['element_size', 'output_directory']
            for field in required_fields:
                if field not in config:
                    result['errors'].append(f"缺少必需字段: {field}")
            
            # 验证 element_size
            if 'element_size' in config:
                element_size = config['element_size']
                if not isinstance(element_size, list):
                    result['errors'].append("element_size 必须是数组")
                elif len(element_size) == 0:
                    result['warnings'].append("element_size 数组为空")
                else:
                    for i, size in enumerate(element_size):
                        if not isinstance(size, (int, float)) or size <= 0:
                            result['errors'].append(f"element_size[{i}] 必须是正数: {size}")
            
            # 验证 output_directory
            if 'output_directory' in config:
                output_dir = config['output_directory']
                if not isinstance(output_dir, str):
                    result['errors'].append("output_directory 必须是字符串")
                elif not output_dir.strip():
                    result['errors'].append("output_directory 不能为空")
            
            # 设置验证结果
            result['valid'] = len(result['errors']) == 0
            
            if result['valid']:
                logger.info("模态配置文件验证通过")
            else:
                logger.warning(f"模态配置文件验证失败: {result['errors']}")
            
            if result['warnings']:
                logger.warning(f"模态配置文件验证警告: {result['warnings']}")
            
        except json.JSONDecodeError as e:
            result['errors'].append(f"JSON 格式错误: {str(e)}")
        except Exception as e:
            result['errors'].append(f"验证过程出错: {str(e)}")
        
        return result
    
    def get_modal_config_template(self) -> Dict[str, Any]:
        """
        获取模态配置文件的模板
        
        Returns:
            Dict[str, Any]: 配置文件模板
        """
        return {
            "element_size": [0.001, 0.002, 0.005, 0.01, 0.02],
            "output_directory": "D:/data/all-XM/autoworkbench/csdaima/modal_results"
        }
    
    def preview_modal_config(self) -> Dict[str, Any]:
        """
        预览将要生成的模态配置（不实际保存文件）
        
        Returns:
            Dict[str, Any]: 预览的配置内容
        """
        try:
            element_sizes = self._extract_element_sizes()
            output_directory = self._get_output_directory()
            
            return {
                "element_size": element_sizes,
                "output_directory": output_directory,
                "_preview_info": {
                    "mesh_count": len(element_sizes),
                    "size_range": f"{min(element_sizes):.4f}m - {max(element_sizes):.4f}m" if element_sizes else "无",
                    "generated_at": "预览模式"
                }
            }
        except Exception as e:
            logger.error(f"预览模态配置失败: {str(e)}", exc_info=True)
            return self.get_modal_config_template()


def create_modal_config_generator(mesh_manager=None, config_manager=None) -> ModalConfigGenerator:
    """
    工厂函数：创建模态配置生成器实例
    
    Args:
        mesh_manager: 网格管理器实例
        config_manager: 配置管理器实例
        
    Returns:
        ModalConfigGenerator: 配置生成器实例
    """
    return ModalConfigGenerator(mesh_manager, config_manager)


# 使用示例
if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建生成器（在实际使用中需要传入真实的管理器实例）
    generator = create_modal_config_generator()
    
    # 预览配置
    preview = generator.preview_modal_config()
    print("预览模态配置:")
    print(json.dumps(preview, indent=2, ensure_ascii=False))
    
    # 生成配置文件
    success = generator.generate_modal_config("test_mesh_config.json")
    if success:
        print("配置文件生成成功")
        
        # 验证配置文件
        validation = generator.validate_modal_config("test_mesh_config.json")
        print(f"验证结果: {'通过' if validation['valid'] else '失败'}")
        if validation['errors']:
            print(f"错误: {validation['errors']}")
        if validation['warnings']:
            print(f"警告: {validation['warnings']}")
    else:
        print("配置文件生成失败")
