"""
窗口工厂模块

此模块提供窗口的懒加载工厂函数，包括：
1. 各种窗口类型的工厂函数
2. 延迟导入机制
3. 窗口创建优化

作者: [作者名]
日期: [日期]
"""

from typing import Callable, Dict
from window_manager import WindowType
from PySide6.QtWidgets import QMainWindow


def create_mesh_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建网格窗口工厂函数"""
    def factory():
        from views.mesh_window_merged import MeshWindow
        return MeshWindow(window_manager=window_manager)
    return factory


def create_pre_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建前处理窗口工厂函数"""
    def factory():
        from views.pre_window import PreWindow
        return PreWindow(window_manager=window_manager)
    return factory


def create_connection_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建连接窗口工厂函数"""
    def factory():
        from views.connection_window import ConnectionWindow
        return ConnectionWindow(window_manager=window_manager)
    return factory


def create_analysis_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建分析窗口工厂函数"""
    def factory():
        from views.analysis_window import AnalysisWindow
        return AnalysisWindow(window_manager=window_manager)
    return factory


def create_constrain_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建约束窗口工厂函数"""
    def factory():
        from views.constrain_window import ConstrainWindow
        return ConstrainWindow(window_manager=window_manager)
    return factory


def create_result_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建结果窗口工厂函数"""
    def factory():
        from views.result_window import ResultWindow
        return ResultWindow(window_manager=window_manager)
    return factory


def create_vibration_window_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建振动分析窗口工厂函数"""
    def factory():
        from ctrl.vibration_analysis import VibrationAnalysisWindow
        return VibrationAnalysisWindow(window_manager=window_manager)
    return factory


def create_log_viewer_factory(window_manager) -> Callable[[], QMainWindow]:
    """创建日志查看器工厂函数"""
    def factory():
        from views.log_viewer import LogViewer
        return LogViewer(window_manager=window_manager)
    return factory


def register_all_window_factories(window_manager) -> None:
    """注册所有窗口工厂函数

    Args:
        window_manager: 窗口管理器实例
    """
    import logging
    logger = logging.getLogger(__name__)

    try:
        # 注册各种窗口的工厂函数
        logger.info("开始注册网格窗口工厂...")
        window_manager.register_window_factory(
            WindowType.MESH,
            create_mesh_window_factory(window_manager)
        )
        logger.info("网格窗口工厂注册成功")
    except Exception as e:
        logger.error(f"网格窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册前处理窗口工厂...")
        window_manager.register_window_factory(
            WindowType.PRE,
            create_pre_window_factory(window_manager)
        )
        logger.info("前处理窗口工厂注册成功")
    except Exception as e:
        logger.error(f"前处理窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册连接窗口工厂...")
        window_manager.register_window_factory(
            WindowType.CONNECTION,
            create_connection_window_factory(window_manager)
        )
        logger.info("连接窗口工厂注册成功")
    except Exception as e:
        logger.error(f"连接窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册分析窗口工厂...")
        window_manager.register_window_factory(
            WindowType.ANALYSIS,
            create_analysis_window_factory(window_manager)
        )
        logger.info("分析窗口工厂注册成功")
    except Exception as e:
        logger.error(f"分析窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册约束窗口工厂...")
        window_manager.register_window_factory(
            WindowType.CONSTRAIN,
            create_constrain_window_factory(window_manager)
        )
        logger.info("约束窗口工厂注册成功")
    except Exception as e:
        logger.error(f"约束窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册结果窗口工厂...")
        window_manager.register_window_factory(
            WindowType.RESULT,
            create_result_window_factory(window_manager)
        )
        logger.info("结果窗口工厂注册成功")
    except Exception as e:
        logger.error(f"结果窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册振动分析窗口工厂...")
        window_manager.register_window_factory(
            WindowType.VIBRATION,
            create_vibration_window_factory(window_manager)
        )
        logger.info("振动分析窗口工厂注册成功")
    except Exception as e:
        logger.error(f"振动分析窗口工厂注册失败: {e}", exc_info=True)
        raise

    try:
        logger.info("开始注册日志查看器工厂...")
        window_manager.register_window_factory(
            WindowType.LOG,
            create_log_viewer_factory(window_manager)
        )
        logger.info("日志查看器工厂注册成功")
    except Exception as e:
        logger.error(f"日志查看器工厂注册失败: {e}", exc_info=True)
        raise

    logger.info("所有窗口工厂注册完成")


def get_window_factory_map(window_manager) -> Dict[WindowType, Callable[[], QMainWindow]]:
    """获取窗口工厂映射表
    
    Args:
        window_manager: 窗口管理器实例
        
    Returns:
        窗口类型到工厂函数的映射
    """
    return {
        WindowType.MESH: create_mesh_window_factory(window_manager),
        WindowType.PRE: create_pre_window_factory(window_manager),
        WindowType.CONNECTION: create_connection_window_factory(window_manager),
        WindowType.ANALYSIS: create_analysis_window_factory(window_manager),
        WindowType.CONSTRAIN: create_constrain_window_factory(window_manager),
        WindowType.RESULT: create_result_window_factory(window_manager),
        WindowType.VIBRATION: create_vibration_window_factory(window_manager),
        WindowType.LOG: create_log_viewer_factory(window_manager),
    }
