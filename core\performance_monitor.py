"""
性能监控模块

此模块提供启动性能监控和测量功能，包括：
1. 启动时间测量
2. 内存使用监控
3. 性能数据记录和分析
4. 优化效果对比

作者: [作者名]
日期: [日期]
"""

import time
import logging
import psutil
import os
from functools import wraps
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    duration: float
    memory_before: float
    memory_after: float
    timestamp: datetime


class StartupProfiler:
    """启动性能分析器"""
    
    def __init__(self):
        self.timings: Dict[str, float] = {}
        self.metrics: List[PerformanceMetric] = []
        self.start_time = time.time()
        self.process = psutil.Process(os.getpid())
        self.baseline_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        # 设置性能日志
        self.logger = logging.getLogger("performance")
        if not self.logger.handlers:
            # 确保日志目录存在
            log_dir = Path('logs')
            log_dir.mkdir(exist_ok=True)

            log_file = log_dir / 'performance.log'
            handler = logging.FileHandler(str(log_file))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def measure(self, name: str):
        """性能测量装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                memory_before = self.process.memory_info().rss / 1024 / 1024
                start = time.time()
                
                try:
                    result = func(*args, **kwargs)
                    success = True
                except Exception as e:
                    self.logger.error(f"Error in {name}: {e}")
                    success = False
                    raise
                finally:
                    end = time.time()
                    memory_after = self.process.memory_info().rss / 1024 / 1024
                    duration = end - start
                    
                    # 记录性能指标
                    metric = PerformanceMetric(
                        name=name,
                        duration=duration,
                        memory_before=memory_before,
                        memory_after=memory_after,
                        timestamp=datetime.now()
                    )
                    self.metrics.append(metric)
                    self.timings[name] = duration
                    
                    # 记录日志
                    status = "SUCCESS" if success else "FAILED"
                    self.logger.info(
                        f"{name}: {duration:.3f}s, "
                        f"Memory: {memory_before:.1f}MB -> {memory_after:.1f}MB "
                        f"({memory_after - memory_before:+.1f}MB), "
                        f"Status: {status}"
                    )
                
                return result
            return wrapper
        return decorator
    
    def log_checkpoint(self, name: str):
        """记录检查点"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        current_memory = self.process.memory_info().rss / 1024 / 1024
        memory_diff = current_memory - self.baseline_memory
        
        self.logger.info(
            f"CHECKPOINT {name}: {elapsed:.3f}s from start, "
            f"Memory: {current_memory:.1f}MB (+{memory_diff:.1f}MB)"
        )
    
    def get_total_startup_time(self) -> float:
        """获取总启动时间"""
        return time.time() - self.start_time
    
    def get_summary(self) -> Dict:
        """获取性能摘要"""
        total_time = self.get_total_startup_time()
        current_memory = self.process.memory_info().rss / 1024 / 1024
        
        return {
            'total_startup_time': total_time,
            'baseline_memory': self.baseline_memory,
            'current_memory': current_memory,
            'memory_increase': current_memory - self.baseline_memory,
            'measured_operations': len(self.timings),
            'timings': self.timings.copy(),
            'slowest_operations': sorted(
                self.timings.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        }
    
    def print_summary(self):
        """打印性能摘要"""
        summary = self.get_summary()
        
        print("\n" + "="*60)
        print("启动性能分析报告")
        print("="*60)
        print(f"总启动时间: {summary['total_startup_time']:.3f}秒")
        print(f"基线内存: {summary['baseline_memory']:.1f}MB")
        print(f"当前内存: {summary['current_memory']:.1f}MB")
        print(f"内存增长: {summary['memory_increase']:+.1f}MB")
        print(f"测量操作数: {summary['measured_operations']}")
        
        print("\n最耗时的5个操作:")
        for name, duration in summary['slowest_operations']:
            print(f"  {name}: {duration:.3f}秒")
        
        print("="*60)


# 全局性能分析器实例
_global_profiler: Optional[StartupProfiler] = None


def get_profiler() -> StartupProfiler:
    """获取全局性能分析器实例"""
    global _global_profiler
    if _global_profiler is None:
        _global_profiler = StartupProfiler()
    return _global_profiler


def measure_performance(name: str):
    """性能测量装饰器的便捷函数"""
    return get_profiler().measure(name)


def log_checkpoint(name: str):
    """记录性能检查点的便捷函数"""
    get_profiler().log_checkpoint(name)


def print_performance_summary():
    """打印性能摘要的便捷函数"""
    if _global_profiler:
        _global_profiler.print_summary()
