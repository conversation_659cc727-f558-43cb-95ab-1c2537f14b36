#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复exc_info=True递归错误的脚本

此脚本用于批量修复views\mesh_window_merged.py中所有的exc_info=True调用，
避免在日志记录时出现递归错误。

作者: AI Assistant
日期: 2025-08-01
"""

import re
import os

def fix_exc_info_recursion(file_path):
    """修复文件中的exc_info=True递归问题"""
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已创建备份文件: {backup_path}")
        
        # 统计修复前的exc_info=True数量
        original_count = len(re.findall(r'exc_info=True', content))
        print(f"📊 发现 {original_count} 个 exc_info=True 调用")
        
        # 替换所有的 exc_info=True
        # 模式1: logger.error(f"...", exc_info=True)
        pattern1 = r'logger\.error\(f"([^"]*)", exc_info=True\)'
        replacement1 = r'logger.error(f"\1")'
        content = re.sub(pattern1, replacement1, content)
        
        # 模式2: logger.error("...", exc_info=True)
        pattern2 = r'logger\.error\("([^"]*)", exc_info=True\)'
        replacement2 = r'logger.error("\1")'
        content = re.sub(pattern2, replacement2, content)
        
        # 模式3: logger.warning(f"...", exc_info=True)
        pattern3 = r'logger\.warning\(f"([^"]*)", exc_info=True\)'
        replacement3 = r'logger.warning(f"\1")'
        content = re.sub(pattern3, replacement3, content)
        
        # 模式4: logger.warning("...", exc_info=True)
        pattern4 = r'logger\.warning\("([^"]*)", exc_info=True\)'
        replacement4 = r'logger.warning("\1")'
        content = re.sub(pattern4, replacement4, content)
        
        # 统计修复后的exc_info=True数量
        remaining_count = len(re.findall(r'exc_info=True', content))
        fixed_count = original_count - remaining_count
        
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成:")
        print(f"  - 修复了 {fixed_count} 个 exc_info=True 调用")
        print(f"  - 剩余 {remaining_count} 个 exc_info=True 调用")
        
        if remaining_count > 0:
            print(f"⚠️  仍有 {remaining_count} 个 exc_info=True 未修复，可能需要手动处理")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("修复exc_info=True递归错误")
    print("=" * 60)
    
    file_path = "views\\mesh_window_merged.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    print(f"🔧 开始修复文件: {file_path}")
    
    success = fix_exc_info_recursion(file_path)
    
    if success:
        print("\n🎉 修复完成！")
        print("\n📋 修复说明:")
        print("• 移除了所有logger.error()和logger.warning()中的exc_info=True参数")
        print("• 这将避免在异常处理时出现递归错误")
        print("• 错误信息仍会被记录，但不包含详细的堆栈跟踪")
        print("• 如果需要详细的错误信息，可以在关键位置手动添加print()语句")
    else:
        print("\n❌ 修复失败")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
