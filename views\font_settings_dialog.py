"""
字体设置对话框

此模块提供字体设置的图形界面，包括：
1. 字体大小调整界面
2. 字体族选择
3. 实时预览
4. 设置保存和重置

作者: [作者名]
日期: [日期]
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QSpinBox, QComboBox, QPushButton, QGroupBox,
    QSlider, QApplication, QMessageBox, QTabWidget, QWidget,
    QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from core.font_manager import get_font_manager


class FontSettingsDialog(QDialog):
    """字体设置对话框"""
    
    # 信号：字体设置已更改
    font_changed = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.font_manager = get_font_manager()
        self.setup_ui()
        self.load_current_settings()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("字体设置")
        self.setModal(True)
        self.resize(500, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # 基础设置选项卡
        basic_tab = self.create_basic_settings_tab()
        tab_widget.addTab(basic_tab, "基础设置")
        
        # 组件设置选项卡
        components_tab = self.create_components_settings_tab()
        tab_widget.addTab(components_tab, "组件设置")
        
        # 预览选项卡
        preview_tab = self.create_preview_tab()
        tab_widget.addTab(preview_tab, "预览")
        
        main_layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_btn = QPushButton("预览")
        self.apply_btn = QPushButton("应用")
        self.reset_btn = QPushButton("重置")
        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.preview_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.reset_btn)
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.ok_btn)
        button_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(button_layout)
        
        # 连接信号
        self.preview_btn.clicked.connect(self.preview_fonts)
        self.apply_btn.clicked.connect(self.apply_fonts)
        self.reset_btn.clicked.connect(self.reset_fonts)
        self.ok_btn.clicked.connect(self.accept_and_apply)
        self.cancel_btn.clicked.connect(self.reject)
    
    def create_basic_settings_tab(self):
        """创建基础设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 字体族设置
        font_family_group = QGroupBox("字体族")
        font_family_layout = QHBoxLayout(font_family_group)
        
        font_family_layout.addWidget(QLabel("字体:"))
        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems([
            "Microsoft YaHei UI",
            "Microsoft YaHei",
            "SimSun",
            "SimHei",
            "Arial",
            "Tahoma",
            "Verdana",
            "Calibri"
        ])
        font_family_layout.addWidget(self.font_family_combo)
        font_family_layout.addStretch()
        
        layout.addWidget(font_family_group)
        
        # 基础字体大小设置
        base_size_group = QGroupBox("基础字体大小")
        base_size_layout = QGridLayout(base_size_group)
        
        # 基础大小滑块
        base_size_layout.addWidget(QLabel("基础大小:"), 0, 0)
        self.base_size_slider = QSlider(Qt.Horizontal)
        self.base_size_slider.setRange(6, 24)
        self.base_size_slider.setValue(9)
        base_size_layout.addWidget(self.base_size_slider, 0, 1)
        
        self.base_size_spinbox = QSpinBox()
        self.base_size_spinbox.setRange(6, 24)
        self.base_size_spinbox.setValue(9)
        base_size_layout.addWidget(self.base_size_spinbox, 0, 2)
        
        # 连接滑块和数字框
        self.base_size_slider.valueChanged.connect(self.base_size_spinbox.setValue)
        self.base_size_spinbox.valueChanged.connect(self.base_size_slider.setValue)
        
        # 预设大小按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设:"))
        
        small_btn = QPushButton("小 (8)")
        normal_btn = QPushButton("正常 (9)")
        large_btn = QPushButton("大 (11)")
        extra_large_btn = QPushButton("特大 (13)")
        
        small_btn.clicked.connect(lambda: self.set_base_size(8))
        normal_btn.clicked.connect(lambda: self.set_base_size(9))
        large_btn.clicked.connect(lambda: self.set_base_size(11))
        extra_large_btn.clicked.connect(lambda: self.set_base_size(13))
        
        preset_layout.addWidget(small_btn)
        preset_layout.addWidget(normal_btn)
        preset_layout.addWidget(large_btn)
        preset_layout.addWidget(extra_large_btn)
        preset_layout.addStretch()
        
        base_size_layout.addLayout(preset_layout, 1, 0, 1, 3)
        
        layout.addWidget(base_size_group)
        layout.addStretch()
        
        return widget
    
    def create_components_settings_tab(self):
        """创建组件设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        
        # 组件字体大小设置
        self.component_spinboxes = {}
        
        components = [
            ("按钮", "QPushButton"),
            ("标签", "QLabel"),
            ("输入框", "QLineEdit"),
            ("下拉框", "QComboBox"),
            ("分组框", "QGroupBox"),
            ("选项卡", "QTabWidget"),
            ("树形控件", "QTreeWidget"),
            ("表格控件", "QTableWidget"),
            ("文本编辑器", "QTextEdit"),
            ("纯文本编辑器", "QPlainTextEdit"),
            ("菜单栏", "QMenuBar"),
            ("菜单", "QMenu"),
            ("状态栏", "QStatusBar"),
            ("工具提示", "QToolTip")
        ]
        
        for i, (display_name, component_name) in enumerate(components):
            label = QLabel(f"{display_name}:")
            spinbox = QSpinBox()
            spinbox.setRange(6, 24)
            spinbox.setValue(9)
            
            self.component_spinboxes[component_name] = spinbox
            
            scroll_layout.addWidget(label, i, 0)
            scroll_layout.addWidget(spinbox, i, 1)
        
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        return widget
    
    def create_preview_tab(self):
        """创建预览选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 预览区域
        preview_group = QGroupBox("字体预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 示例文本
        self.preview_label = QLabel("这是预览文本 - This is preview text")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("border: 1px solid gray; padding: 20px; background-color: white;")
        
        self.preview_button = QPushButton("示例按钮")
        
        preview_layout.addWidget(self.preview_label)
        preview_layout.addWidget(self.preview_button)
        
        layout.addWidget(preview_group)
        
        # 当前设置信息
        info_group = QGroupBox("当前设置")
        info_layout = QVBoxLayout(info_group)
        
        self.info_label = QLabel()
        self.info_label.setWordWrap(True)
        info_layout.addWidget(self.info_label)
        
        layout.addWidget(info_group)
        layout.addStretch()
        
        return widget
    
    def set_base_size(self, size):
        """设置基础字体大小"""
        self.base_size_slider.setValue(size)
        self.base_size_spinbox.setValue(size)
    
    def load_current_settings(self):
        """加载当前字体设置"""
        config = self.font_manager.get_font_config_info()
        current_config = config['current_config']
        
        # 设置字体族
        font_family = current_config['font_family']
        index = self.font_family_combo.findText(font_family)
        if index >= 0:
            self.font_family_combo.setCurrentIndex(index)
        
        # 设置基础大小
        base_size = current_config['base_size']
        self.set_base_size(base_size)
        
        # 设置组件大小
        for component, spinbox in self.component_spinboxes.items():
            size = current_config['components'].get(component, base_size)
            spinbox.setValue(size)
        
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        font_family = self.font_family_combo.currentText()
        base_size = self.base_size_spinbox.value()
        
        # 更新预览标签
        preview_font = QFont(font_family, base_size)
        self.preview_label.setFont(preview_font)
        
        # 更新预览按钮
        button_size = self.component_spinboxes.get('QPushButton', self.base_size_spinbox).value()
        button_font = QFont(font_family, button_size)
        self.preview_button.setFont(button_font)
        
        # 更新信息
        info_text = f"""
字体族: {font_family}
基础大小: {base_size}pt
按钮大小: {button_size}pt
        """.strip()
        self.info_label.setText(info_text)
    
    def preview_fonts(self):
        """预览字体设置"""
        self.update_preview()
        
        # 临时应用字体设置
        self.apply_current_settings_to_manager()
        self.font_manager.apply_fonts_to_application()
        
        QMessageBox.information(self, "预览", "字体设置已临时应用，您可以查看效果。\n点击'应用'保存设置，或'重置'恢复原设置。")
    
    def apply_current_settings_to_manager(self):
        """将当前设置应用到字体管理器"""
        # 设置字体族
        font_family = self.font_family_combo.currentText()
        self.font_manager.set_font_family(font_family)
        
        # 设置基础大小
        base_size = self.base_size_spinbox.value()
        self.font_manager.set_base_font_size(base_size)
        
        # 设置组件大小
        for component, spinbox in self.component_spinboxes.items():
            size = spinbox.value()
            self.font_manager.set_component_font_size(component, size)
    
    def apply_fonts(self):
        """应用字体设置"""
        self.apply_current_settings_to_manager()
        self.font_manager.apply_fonts_to_application()
        self.font_manager.save_font_config()
        
        self.font_changed.emit()
        QMessageBox.information(self, "应用成功", "字体设置已应用并保存。")
    
    def reset_fonts(self):
        """重置字体设置"""
        reply = QMessageBox.question(
            self, "确认重置", 
            "确定要重置为默认字体设置吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.font_manager.reset_to_default()
            self.load_current_settings()
            QMessageBox.information(self, "重置成功", "字体设置已重置为默认值。")
    
    def accept_and_apply(self):
        """确定并应用"""
        self.apply_fonts()
        self.accept()


def show_font_settings_dialog(parent=None):
    """显示字体设置对话框的便捷函数"""
    dialog = FontSettingsDialog(parent)
    return dialog.exec()
