<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口文档 - 振动传递计算软件</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="header">
        <h1>API接口文档</h1>
    </div>

    <div class="container">
        <div class="section">
            <h2>🌐 API概述</h2>
            <p>振动传递计算软件提供了完整的RESTful API接口，支持外部应用程序与软件进行集成。API采用线程安全设计，支持高并发调用。</p>
            
            <div class="feature-highlight">
                <h3>API特性</h3>
                <ul>
                    <li><strong>RESTful设计</strong> - 标准的HTTP方法和状态码</li>
                    <li><strong>JSON格式</strong> - 统一的数据交换格式</li>
                    <li><strong>线程安全</strong> - 支持高并发调用</li>
                    <li><strong>错误处理</strong> - 详细的错误信息和状态码</li>
                    <li><strong>文档完整</strong> - 完整的API文档和示例</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔧 基础信息</h2>
            
            <div class="feature-card">
                <h3>服务器配置</h3>
                <ul>
                    <li><strong>基础URL</strong>: <code>http://127.0.0.1:8000</code></li>
                    <li><strong>协议</strong>: HTTP/1.1</li>
                    <li><strong>数据格式</strong>: JSON</li>
                    <li><strong>字符编码</strong>: UTF-8</li>
                </ul>
            </div>

            <div class="feature-card">
                <h3>认证方式</h3>
                <p>当前版本使用本地服务，无需认证。未来版本将支持：</p>
                <ul>
                    <li>API密钥认证</li>
                    <li>JWT令牌认证</li>
                    <li>OAuth 2.0认证</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📋 API端点</h2>
            
            <div class="feature-card">
                <h3>健康检查</h3>
                <p><strong>GET</strong> <code>/health</code></p>
                <p>检查API服务器状态</p>
                
                <h4>响应示例：</h4>
                <pre><code>
{
    "status": "healthy",
    "timestamp": "2023-12-01T10:30:00Z",
    "version": "1.2.0",
    "uptime": 3600
}
                </code></pre>
            </div>

            <div class="feature-card">
                <h3>仿真参数更新</h3>
                <p><strong>POST</strong> <code>/api/v1/simulation-params</code></p>
                <p>更新仿真计算参数</p>
                
                <h4>请求体示例：</h4>
                <pre><code>
{
    "mesh_size": 0.01,
    "time_step": 0.001,
    "analysis_type": "transient",
    "solver_settings": {
        "max_iterations": 1000,
        "convergence_tolerance": 1e-6
    }
}
                </code></pre>
                
                <h4>响应示例：</h4>
                <pre><code>
{
    "success": true,
    "message": "参数更新成功",
    "updated_params": {
        "mesh_size": 0.01,
        "time_step": 0.001,
        "analysis_type": "transient"
    }
}
                </code></pre>
            </div>

            <div class="feature-card">
                <h3>窗口切换</h3>
                <p><strong>POST</strong> <code>/api/v1/switch-window</code></p>
                <p>切换到指定的功能窗口</p>
                
                <h4>请求体示例：</h4>
                <pre><code>
{
    "window_name": "mesh",
    "params": {
        "auto_mesh": true,
        "element_size": 0.01
    }
}
                </code></pre>
                
                <h4>支持的窗口类型：</h4>
                <ul>
                    <li><code>main</code> - 主界面</li>
                    <li><code>mesh</code> - 网格划分</li>
                    <li><code>pre</code> - 前处理</li>
                    <li><code>connection</code> - 连接设置</li>
                    <li><code>analysis</code> - 分析设置</li>
                    <li><code>constrain</code> - 约束设置</li>
                    <li><code>result</code> - 结果显示</li>
                    <li><code>vibration</code> - 振动分析</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>📊 状态码</h2>
            
            <table>
                <thead>
                    <tr>
                        <th>状态码</th>
                        <th>含义</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>200</td>
                        <td>OK</td>
                        <td>请求成功</td>
                    </tr>
                    <tr>
                        <td>201</td>
                        <td>Created</td>
                        <td>资源创建成功</td>
                    </tr>
                    <tr>
                        <td>400</td>
                        <td>Bad Request</td>
                        <td>请求参数错误</td>
                    </tr>
                    <tr>
                        <td>404</td>
                        <td>Not Found</td>
                        <td>资源不存在</td>
                    </tr>
                    <tr>
                        <td>500</td>
                        <td>Internal Server Error</td>
                        <td>服务器内部错误</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>💻 使用示例</h2>
            
            <div class="feature-card">
                <h3>Python示例</h3>
                <pre><code>
import requests
import json

# 基础URL
base_url = "http://127.0.0.1:8000"

# 检查服务器状态
response = requests.get(f"{base_url}/health")
print(f"服务器状态: {response.json()}")

# 更新仿真参数
params = {
    "mesh_size": 0.01,
    "time_step": 0.001,
    "analysis_type": "transient"
}

response = requests.post(
    f"{base_url}/api/v1/simulation-params",
    json=params,
    headers={"Content-Type": "application/json"}
)

if response.status_code == 200:
    print("参数更新成功")
    print(response.json())
else:
    print(f"更新失败: {response.status_code}")
                </code></pre>
            </div>

            <div class="feature-card">
                <h3>JavaScript示例</h3>
                <pre><code>
// 使用fetch API
const baseUrl = "http://127.0.0.1:8000";

// 检查服务器状态
fetch(`${baseUrl}/health`)
    .then(response => response.json())
    .then(data => console.log('服务器状态:', data))
    .catch(error => console.error('错误:', error));

// 更新仿真参数
const params = {
    mesh_size: 0.01,
    time_step: 0.001,
    analysis_type: "transient"
};

fetch(`${baseUrl}/api/v1/simulation-params`, {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(params)
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('参数更新成功:', data);
    } else {
        console.error('更新失败:', data.message);
    }
})
.catch(error => console.error('错误:', error));
                </code></pre>
            </div>
        </div>

        <div class="section">
            <h2>🛡️ 线程安全</h2>
            
            <div class="note">
                <h3>并发处理</h3>
                <p>API服务器采用线程安全设计，支持多个客户端同时调用：</p>
                <ul>
                    <li><strong>请求隔离</strong> - 每个请求在独立的线程中处理</li>
                    <li><strong>资源保护</strong> - 共享资源使用互斥锁保护</li>
                    <li><strong>状态同步</strong> - UI状态更新通过信号槽机制同步</li>
                    <li><strong>错误隔离</strong> - 单个请求的错误不会影响其他请求</li>
                </ul>
            </div>

            <div class="tip">
                <h3>性能建议</h3>
                <ul>
                    <li>避免频繁的小请求，尽量批量处理</li>
                    <li>使用适当的超时设置</li>
                    <li>实现客户端重试机制</li>
                    <li>监控API响应时间</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>🔍 错误处理</h2>
            
            <div class="warning">
                <h3>错误响应格式</h3>
                <pre><code>
{
    "success": false,
    "error": {
        "code": "INVALID_PARAMETER",
        "message": "参数验证失败",
        "details": {
            "field": "mesh_size",
            "reason": "值必须大于0"
        }
    },
    "timestamp": "2023-12-01T10:30:00Z"
}
                </code></pre>
            </div>

            <div class="interpretation">
                <h3>常见错误代码</h3>
                <ul>
                    <li><code>INVALID_PARAMETER</code> - 参数验证失败</li>
                    <li><code>WINDOW_NOT_FOUND</code> - 指定的窗口不存在</li>
                    <li><code>OPERATION_FAILED</code> - 操作执行失败</li>
                    <li><code>THREAD_SAFETY_ERROR</code> - 线程安全错误</li>
                    <li><code>INTERNAL_ERROR</code> - 内部服务器错误</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>© 2023 振动传递计算软件团队 | <a href="mailto:<EMAIL>">技术支持</a></p>
            <p><a href="index.html" class="back-link">← 返回主页</a></p>
        </div>
    </div>
</body>
</html>
