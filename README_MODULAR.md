# 模块化重构说明

本文档描述了对振动传递计算软件的模块化重构方案，以提高代码的可维护性和可扩展性。

## 重构目标

1. 将 qt.py 的职责精简为应用程序的启动器和协调器
2. 将窗口的定义和逻辑完全分离到专门的模块中
3. 提高代码的可读性和可维护性
4. 便于后续功能扩展和维护

## 模块化结构

重构后的项目结构如下：

```
project/
├── qt.py                # 应用程序启动器
├── window_manager.py    # 窗口管理器
├── core/                # 核心功能模块
│   ├── __init__.py
│   ├── exception_handler.py
│   ├── error_dialog.py
│   └── config_manager.py
├── views/               # 视图模块
│   ├── __init__.py
│   ├── base_window.py   # 基础窗口类
│   ├── main_window.py   # 主窗口
│   ├── mesh_window.py   # 网格窗口
│   ├── pre_window.py    # 前处理窗口
│   ├── connection_window.py # 连接设置窗口
│   ├── analysis_window.py  # 分析设置窗口
│   ├── constrain_window.py # 约束设置窗口
│   ├── result_window.py    # 结果显示窗口
│   └── help_dialog.py      # 帮助对话框
├── ctrl/                # 控制器模块
│   ├── main_slot.py
│   ├── mesh_slot.py
│   ├── pre_slot.py
│   ├── connection_slot.py
│   ├── analysis_slot.py
│   ├── constrain_slot.py
│   ├── result_slot.py
│   ├── vibration_analysis.py
│   ├── api_server.py
│   └── startup_config.py
└── ui/                  # UI定义文件
    ├── ui_main.py
    ├── ui_mesh.py
    ├── ui_pre.py
    ├── ui_connection.py
    ├── ui_analysis.py
    ├── ui_constrain.py
    └── ui_result.py
```

## 主要改进

1. **基础窗口类 (BaseWindow)**：
   - 提供了所有窗口共享的基本功能
   - 处理窗口关闭事件
   - 提供帮助显示功能

2. **窗口类**：
   - 每个窗口都有自己的模块
   - 继承自 BaseWindow
   - 只包含与该窗口相关的逻辑

3. **应用程序启动器 (qt.py)**：
   - 负责初始化应用程序
   - 创建和注册窗口
   - 启动API服务器
   - 连接信号和槽
   - 显示主窗口

## 如何使用

1. 导入窗口类：
```python
from views import MainWindow, MeshWindow, PreWindow, ...
```

2. 创建窗口实例：
```python
main_window = MainWindow(initial_data=initial_data)
mesh_window = MeshWindow()
# ...
```

3. 注册到窗口管理器：
```python
window_manager.register_window(WindowType.MAIN, main_window)
window_manager.register_window(WindowType.MESH, mesh_window)
# ...
```

4. 初始化槽函数：
```python
main_slot.main_slot(window_manager)
mesh_slot.mesh_slot(window_manager)
# ...
```

5. 显示主窗口：
```python
window_manager.switch_to(WindowType.MAIN)
```

## 注意事项

目前由于环境配置问题，模块化版本可能无法正常运行。需要确保：

1. PySide6 库正确安装并可导入
2. 项目路径正确设置在 Python 路径中
3. 所有依赖项都已安装

## 后续改进

1. 进一步优化窗口类的接口
2. 添加更多单元测试
3. 改进错误处理机制
4. 优化配置管理 