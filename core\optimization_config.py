"""
优化配置管理模块

此模块提供启动优化的配置管理，包括：
1. 优化开关控制
2. 回滚机制支持
3. 性能配置管理
4. 兼容性控制

作者: [作者名]
日期: [日期]
"""

import os
import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class OptimizationConfig:
    """优化配置数据类"""
    # 窗口懒加载配置
    enable_lazy_loading: bool = True
    preload_critical_windows: bool = True
    lazy_loading_delay_ms: int = 100
    
    # 样式表优化配置
    enable_style_cache: bool = True
    enable_style_compression: bool = True
    enable_critical_styles_first: bool = True
    
    # 动画优化配置
    enable_lazy_animations: bool = True
    animation_creation_delay_ms: int = 50
    simplified_animations: bool = True
    
    # 性能监控配置
    enable_performance_monitoring: bool = True
    detailed_logging: bool = False
    
    # 兼容性配置
    fallback_to_legacy: bool = False
    legacy_mode_timeout_ms: int = 5000
    
    # 调试配置
    debug_mode: bool = False
    verbose_logging: bool = False


class OptimizationManager:
    """优化管理器"""
    
    def __init__(self, config_path: str = "config/optimization.json"):
        self.config_path = config_path
        self.config = OptimizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> None:
        """加载优化配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置对象
                for key, value in config_data.items():
                    if hasattr(self.config, key):
                        setattr(self.config, key, value)
                
                self.logger.info(f"优化配置已从 {self.config_path} 加载")
            else:
                # 创建默认配置文件
                self.save_config()
                self.logger.info(f"创建默认优化配置文件: {self.config_path}")
                
        except Exception as e:
            self.logger.error(f"加载优化配置失败: {e}")
            # 使用默认配置
            self.config = OptimizationConfig()
    
    def save_config(self) -> None:
        """保存优化配置"""
        try:
            config_data = asdict(self.config)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"优化配置已保存到 {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"保存优化配置失败: {e}")
    
    def enable_legacy_mode(self) -> None:
        """启用传统模式（回滚所有优化）"""
        self.config.enable_lazy_loading = False
        self.config.enable_style_cache = False
        self.config.enable_lazy_animations = False
        self.config.fallback_to_legacy = True
        
        self.save_config()
        self.logger.warning("已启用传统模式，所有优化已禁用")
    
    def enable_all_optimizations(self) -> None:
        """启用所有优化"""
        self.config.enable_lazy_loading = True
        self.config.enable_style_cache = True
        self.config.enable_lazy_animations = True
        self.config.fallback_to_legacy = False
        
        self.save_config()
        self.logger.info("已启用所有优化")
    
    def is_optimization_enabled(self, optimization_name: str) -> bool:
        """检查特定优化是否启用"""
        if self.config.fallback_to_legacy:
            return False
        
        return getattr(self.config, f"enable_{optimization_name}", False)
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return getattr(self.config, key, default)
    
    def set_config_value(self, key: str, value: Any) -> None:
        """设置配置值"""
        if hasattr(self.config, key):
            setattr(self.config, key, value)
            self.save_config()
        else:
            self.logger.warning(f"未知的配置键: {key}")
    
    def get_debug_info(self) -> Dict[str, Any]:
        """获取调试信息"""
        return {
            'config_path': self.config_path,
            'config_exists': os.path.exists(self.config_path),
            'current_config': asdict(self.config),
            'optimizations_status': {
                'lazy_loading': self.is_optimization_enabled('lazy_loading'),
                'style_cache': self.is_optimization_enabled('style_cache'),
                'lazy_animations': self.is_optimization_enabled('lazy_animations'),
            }
        }


# 全局优化管理器实例
_global_optimization_manager: Optional[OptimizationManager] = None


def get_optimization_manager() -> OptimizationManager:
    """获取全局优化管理器实例"""
    global _global_optimization_manager
    if _global_optimization_manager is None:
        _global_optimization_manager = OptimizationManager()
    return _global_optimization_manager


def is_optimization_enabled(optimization_name: str) -> bool:
    """检查优化是否启用的便捷函数"""
    return get_optimization_manager().is_optimization_enabled(optimization_name)


def get_config_value(key: str, default: Any = None) -> Any:
    """获取配置值的便捷函数"""
    return get_optimization_manager().get_config_value(key, default)


def enable_legacy_mode():
    """启用传统模式的便捷函数"""
    get_optimization_manager().enable_legacy_mode()


def enable_all_optimizations():
    """启用所有优化的便捷函数"""
    get_optimization_manager().enable_all_optimizations()
