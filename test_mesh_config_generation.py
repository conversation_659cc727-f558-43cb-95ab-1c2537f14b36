#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网格配置文件生成功能测试脚本

此脚本用于验证网格配置文件生成功能，确保：
1. 基于用户选择的结果能正确生成mesh_config_last.json
2. 配置文件格式符合finalscript的期望
3. 动态路径替换功能正常工作
4. 与现有网格生成流程兼容

作者: AI Assistant
日期: 2025-08-01
"""

import sys
import os
import logging
import json
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置基本日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_mesh_config_generator():
    """测试MeshConfigGenerator基本功能"""
    try:
        logger.info("开始测试MeshConfigGenerator基本功能")
        
        from core.mesh_config_generator import MeshConfigGenerator
        
        # 创建配置生成器
        work_dir = os.getcwd()
        generator = MeshConfigGenerator(work_dir)
        
        # 模拟用户选择的结果
        selected_result = {
            'mesh_name': '测试网格',
            'element_size': 15.0,  # 毫米
            'element_type': '四面体',
            'frequencies': [12.5, 78.3, 156.7, 234.1, 345.6],
            'calculation_time': 125.8,
            'output_directory': 'temp/modal_output_test'
        }
        
        # 生成配置文件
        config_path = generator.generate_config_from_selection(selected_result)
        
        # 验证配置文件
        if not os.path.exists(config_path):
            logger.error(f"配置文件未生成: {config_path}")
            return False
        
        # 读取并验证配置内容
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = json.load(f)
        
        # 验证必需字段
        if 'element_size' not in config_content:
            logger.error("配置文件缺少element_size字段")
            return False
        
        expected_element_size = 0.015  # 15mm转换为米
        actual_element_size = config_content['element_size']
        
        if abs(actual_element_size - expected_element_size) > 0.001:
            logger.error(f"element_size值不正确，期望: {expected_element_size}, 实际: {actual_element_size}")
            return False
        
        logger.info("✅ MeshConfigGenerator基本功能测试通过")
        logger.info(f"  - 配置文件路径: {config_path}")
        logger.info(f"  - element_size: {actual_element_size} (米)")
        logger.info(f"  - 配置内容: {config_content}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MeshConfigGenerator基本功能测试失败: {str(e)}")
        return False

def test_mesh_parameter_integration():
    """测试与MeshParameter的集成"""
    try:
        logger.info("开始测试与MeshParameter的集成")
        
        from core.mesh_manager import MeshParameter, ElementType, MeshStatus, ModalResults
        from core.mesh_config_generator import MeshConfigGenerator
        
        # 创建测试网格参数
        mesh_param = MeshParameter(
            name="集成测试网格",
            size=20.0,  # 毫米
            element_type=ElementType.HEXAHEDRON
        )
        mesh_param.status = MeshStatus.COMPLETED
        
        # 创建模态结果
        modal_results = ModalResults()
        modal_results.frequencies = [15.2, 89.7, 167.3, 245.8, 378.1]
        modal_results.calculation_time = 156.4
        mesh_param.modal_results = modal_results
        
        # 使用静态方法生成配置文件
        config_path = MeshConfigGenerator.create_from_mesh_parameter(mesh_param)
        
        # 验证配置文件
        if not os.path.exists(config_path):
            logger.error(f"配置文件未生成: {config_path}")
            return False
        
        # 读取配置内容
        with open(config_path, 'r', encoding='utf-8') as f:
            config_content = json.load(f)
        
        # 验证配置内容
        expected_element_size = 0.020  # 20mm转换为米
        actual_element_size = config_content['element_size']
        
        if abs(actual_element_size - expected_element_size) > 0.001:
            logger.error(f"element_size值不正确，期望: {expected_element_size}, 实际: {actual_element_size}")
            return False
        
        if config_content.get('mesh_name') != "集成测试网格":
            logger.error(f"mesh_name不正确，期望: '集成测试网格', 实际: '{config_content.get('mesh_name')}'")
            return False
        
        logger.info("✅ MeshParameter集成测试通过")
        logger.info(f"  - 网格名称: {config_content.get('mesh_name')}")
        logger.info(f"  - element_size: {actual_element_size} (米)")
        logger.info(f"  - 单元类型: {config_content.get('element_type')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ MeshParameter集成测试失败: {str(e)}")
        return False

def test_dynamic_path_functionality():
    """测试动态路径功能"""
    try:
        logger.info("开始测试动态路径功能")
        
        from core.mesh_config_generator import get_dynamic_config_path, replace_hardcoded_paths
        
        # 测试动态路径获取
        work_dir = os.getcwd()
        dynamic_path = get_dynamic_config_path(work_dir)
        
        expected_filename = "mesh_config_last.json"
        if expected_filename not in dynamic_path:
            logger.error(f"动态路径不包含期望的文件名: {expected_filename}")
            return False
        
        # 测试路径替换功能
        test_script_content = '''
# 测试脚本内容
cfg_path = r"D:/data/all-XM/autoworkbench/csdaima/mesh_config.json"
other_path = r"C:/some/other/path.json"

def run_mesh_setting():
    with open(cfg_path, 'r') as f:
        config = json.load(f)
    return config
'''
        
        # 执行路径替换
        updated_content = replace_hardcoded_paths(test_script_content, work_dir)
        
        # 验证替换结果
        if "D:/data/all-XM/autoworkbench/csdaima/mesh_config.json" in updated_content:
            logger.error("硬编码路径未被替换")
            return False
        
        if dynamic_path not in updated_content:
            logger.error(f"动态路径未正确插入: {dynamic_path}")
            return False
        
        logger.info("✅ 动态路径功能测试通过")
        logger.info(f"  - 动态路径: {dynamic_path}")
        logger.info(f"  - 路径替换成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 动态路径功能测试失败: {str(e)}")
        return False

def test_config_file_format():
    """测试配置文件格式兼容性"""
    try:
        logger.info("开始测试配置文件格式兼容性")
        
        from core.mesh_config_generator import MeshConfigGenerator
        
        # 创建配置生成器
        generator = MeshConfigGenerator()
        
        # 生成测试配置
        test_result = {
            'element_size': 12.0,  # 毫米
            'mesh_name': '格式测试网格'
        }
        
        config_path = generator.generate_config_from_selection(test_result)
        
        # 读取配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证与finalscript期望格式的兼容性
        required_fields = ['element_size']
        for field in required_fields:
            if field not in config:
                logger.error(f"配置文件缺少必需字段: {field}")
                return False
        
        # 验证element_size是数值类型
        element_size = config['element_size']
        if not isinstance(element_size, (int, float)):
            logger.error(f"element_size应该是数值类型，实际类型: {type(element_size)}")
            return False
        
        # 验证element_size在合理范围内（米单位）
        if element_size <= 0 or element_size > 1:
            logger.error(f"element_size值超出合理范围: {element_size}")
            return False
        
        # 验证JSON格式正确
        try:
            json.dumps(config)
        except Exception as e:
            logger.error(f"配置文件JSON格式无效: {str(e)}")
            return False
        
        logger.info("✅ 配置文件格式兼容性测试通过")
        logger.info(f"  - element_size: {element_size} (米)")
        logger.info(f"  - JSON格式有效")
        logger.info(f"  - 包含字段: {list(config.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置文件格式兼容性测试失败: {str(e)}")
        return False

def test_multiple_mesh_selection():
    """测试多网格选择场景"""
    try:
        logger.info("开始测试多网格选择场景")
        
        from core.mesh_config_generator import MeshConfigGenerator
        
        # 模拟多个网格选择
        selected_results = [
            {
                'mesh_name': '网格1',
                'element_size': 10.0,  # 毫米
                'frequencies': [10.1, 25.3, 45.7]
            },
            {
                'mesh_name': '网格2', 
                'element_size': 15.0,  # 毫米
                'frequencies': [8.9, 22.1, 41.2]
            },
            {
                'mesh_name': '网格3',
                'element_size': 20.0,  # 毫米
                'frequencies': [7.5, 19.8, 38.6]
            }
        ]
        
        # 使用第一个网格作为主要配置（符合finalscript期望）
        primary_result = selected_results[0]
        primary_result['selected_count'] = len(selected_results)
        primary_result['all_selected_meshes'] = [r['mesh_name'] for r in selected_results]
        
        # 生成配置文件
        generator = MeshConfigGenerator()
        config_path = generator.generate_config_from_selection(primary_result)
        
        # 验证配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证主要网格的element_size
        expected_element_size = 0.010  # 10mm转换为米
        actual_element_size = config['element_size']
        
        if abs(actual_element_size - expected_element_size) > 0.001:
            logger.error(f"element_size值不正确，期望: {expected_element_size}, 实际: {actual_element_size}")
            return False
        
        # 验证多选信息是否保存
        if 'selected_count' in config and config['selected_count'] != 3:
            logger.error(f"选择数量不正确，期望: 3, 实际: {config.get('selected_count')}")
            return False
        
        logger.info("✅ 多网格选择场景测试通过")
        logger.info(f"  - 主要网格element_size: {actual_element_size} (米)")
        logger.info(f"  - 选择的网格数量: {config.get('selected_count', 1)}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 多网格选择场景测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("开始网格配置文件生成功能验证测试")
    logger.info("=" * 60)
    
    success_count = 0
    total_tests = 5
    
    # 运行测试
    tests = [
        ("MeshConfigGenerator基本功能测试", test_mesh_config_generator),
        ("MeshParameter集成测试", test_mesh_parameter_integration),
        ("动态路径功能测试", test_dynamic_path_functionality),
        ("配置文件格式兼容性测试", test_config_file_format),
        ("多网格选择场景测试", test_multiple_mesh_selection)
    ]
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                success_count += 1
                logger.info(f"✅ {test_name} 通过")
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {str(e)}")
    
    # 输出测试结果
    logger.info("\n" + "=" * 60)
    logger.info(f"测试完成: {success_count}/{total_tests} 通过")
    if success_count == total_tests:
        logger.info("🎉 所有测试通过！网格配置文件生成功能正常")
        logger.info("\n📋 功能验证结果:")
        logger.info("• ✅ 基于用户选择自动生成mesh_config_last.json")
        logger.info("• ✅ 配置文件格式符合finalscript期望")
        logger.info("• ✅ 动态路径替换功能正常")
        logger.info("• ✅ 与MeshParameter集成正常")
        logger.info("• ✅ 支持多网格选择场景")
        logger.info("\n🔧 实现的功能:")
        logger.info("• 用户选择结果后自动生成配置文件")
        logger.info("• 硬编码路径动态替换")
        logger.info("• 跨平台路径兼容性")
        logger.info("• 与现有网格生成流程兼容")
    else:
        logger.error(f"❌ 有 {total_tests - success_count} 个测试失败")
    logger.info("=" * 60)
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
