"""
简化的启动画面测试脚本

用于快速验证启动画面功能是否正常工作
"""

import sys
import os
import time

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QIcon
    
    print("✅ PySide6 导入成功")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序图标
    icon_path = os.path.join("assets", "icons", "vibration_transfer_icon_alt.ico")
    if os.path.exists(icon_path):
        app.setWindowIcon(QIcon(icon_path))
        print(f"✅ 应用图标加载成功: {icon_path}")
    else:
        print(f"⚠️ 应用图标文件不存在: {icon_path}")
    
    # 测试启动画面导入
    try:
        from core.splash_screen import CustomSplashScreen, get_splash_manager
        print("✅ 启动画面模块导入成功")
        
        # 创建启动画面管理器
        splash_manager = get_splash_manager()
        print("✅ 启动画面管理器创建成功")
        
        # 显示启动画面
        splash = splash_manager.show_splash()
        print("✅ 启动画面显示成功")
        
        # 模拟进度更新
        steps = [
            (20, "正在初始化..."),
            (40, "正在加载配置..."),
            (60, "正在创建界面..."),
            (80, "正在完成设置..."),
            (100, "启动完成！")
        ]
        
        for progress, status in steps:
            splash_manager.update_progress_by_percentage(progress, status)
            print(f"✅ 进度更新: {progress}% - {status}")
            time.sleep(1)
            app.processEvents()
        
        # 等待一下再隐藏
        time.sleep(2)
        splash_manager.hide_splash()
        print("✅ 启动画面隐藏成功")
        
        print("\n🎉 启动画面测试完全成功！")
        print("所有功能都正常工作。")
        
    except Exception as e:
        print(f"❌ 启动画面测试失败: {e}")
        import traceback
        traceback.print_exc()
    
except ImportError as e:
    print(f"❌ PySide6 导入失败: {e}")
    print("请确保已安装 PySide6: pip install PySide6")

except Exception as e:
    print(f"❌ 测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()

print("\n测试完成。")
